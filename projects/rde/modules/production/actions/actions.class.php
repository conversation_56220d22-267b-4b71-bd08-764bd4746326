<?php

  use domain\production\service\ProductionPdfGenerator;

  class productionRdeActions extends gsdActions {

    public function executeWork() {

      $query = "SELECT * FROM " . Quotations::getTablename() . " ";
      $query .= "JOIN " . Stones::getTablename() . " ON quotations.stoneId = stones.stoneId ";
      $query .= "JOIN " . QuotationsExtra::getTablename() . " ON quotations.quotationId = quotations_extra.quotationId ";
      $query .= "JOIN " . CrmCompanies::getTablename() . " ON quotations.companyId = crm_companies.companyId ";
      $query .= "JOIN " . ProductionOrder::getTablename() . " ON quotations.quotationId = production_order.quotationId ";
      $query .= "JOIN " . StoneBrands::getTablename() . " ON stones.brandId = stone_brands.brandId ";

      if (isset($_POST['filter_both'])) {
        $query .= " ";
      }
      elseif (isset($_POST['filter_has_date'])) {
        $query .= "JOIN " . GpsbuddyRde::getTablename() . " ON quotations.quotationId = gpsbuddy_rde.quotationId ";
        $query .= "JOIN " . GpsbuddyRoutes::getTablename() . " ON gpsbuddy_routes.routeId = gpsbuddy_rde.routeId ";
        $query .= " WHERE gpsbuddy_routes.date IS NOT NULL ";
      }
      else {
        $query .= "JOIN " . GpsbuddyRde::getTablename() . " ON quotations.quotationId = gpsbuddy_rde.quotationId ";
        $query .= "JOIN " . GpsbuddyRoutes::getTablename() . " ON gpsbuddy_routes.routeId = gpsbuddy_rde.routeId ";
        $query .= " WHERE gpsbuddy_routes.date IS NOT NULL ";
      }

      $query .= "AND statusId IN (30, 35, 38) ";

      // only stone brands that are produced at raamdorpel (no belgium or zimbabwe)
      $query .= "AND stones.brandId NOT IN (5, 6, 7, 8, 12, 13, 15, 50) ";

      // Ensure unique quotationId values
      $query .= "GROUP BY quotations.quotationId ";

      $query .= "ORDER BY crm_companies.companyId ASC, quotations.productionDate DESC LIMIT 1500 ";
      $quotations = [];
      $result = DBConn::db_link()->query($query);

      while ($row = $result->fetch_row()) {
        $quotation = new Quotations();
        $quotation->hydrate($row);
        $stone = new Stones();
        $stone->hydrate($row, count(Quotations::columns));
        $quotation_extra = new QuotationsExtra();
        $quotation_extra->hydrate($row, count(Stones::columns) + count(Quotations::columns));
        $company = new CrmCompanies();
        $company->hydrate($row, count(Stones::columns) + count(Quotations::columns) + count(QuotationsExtra::columns));
        $production_order = new ProductionOrder();
        $production_order->hydrate($row, count(Stones::columns) + count(Quotations::columns) + count(QuotationsExtra::columns) + count(CrmCompanies::columns));

        //kleur voeg
        $seam_color = SeamColor::find_by(['seamColorId' => $quotation_extra->seamColorId], ' AND seamColorId != 1 ');
        $seam_color_name = '';
        if ($seam_color) {
          $seam_color_name = $seam_color->name;
        }

        //steen merk, kleur en afmetingen
        $stone_size = StoneSizes::find_by(['sizeId' => $stone->sizeId]);
        $stone_color = StoneColors::find_by(['colorId' => $stone->colorId]);
        $stone_brand = StoneBrands::find_by(['brandId' => $stone->brandId]);

        //verstek
        $order_elements = OrderElements::find_all_by(['quotationId' => $quotation->quotationId]);
        $hasVerstek = $quotation->hasVerstek($order_elements, $stone);

        //endstone
        $endstone = '';
        if ($quotation->endstone == 'true_grooves') {
          $endstone = 'Ja, groeven';
        }
        elseif ($quotation->endstone == 'true') {
          $endstone = 'Ja, opstaande zijkanten';
        }

        $gpsbuddy_rde = GpsbuddyRde::find_by(['quotationId' => $quotation->quotationId]);
        $day_before_production = '';
        $date_before_production = '';
        if ($gpsbuddy_rde) {
          $gpsbuddy_route = GpsbuddyRoutes::find_by(['routeId' => $gpsbuddy_rde->routeId]);
          if ($gpsbuddy_route->date) {

            $day_name = $gpsbuddy_route->getDate("l");

            if ($day_name === 'Tuesday' || $day_name === 'Monday') {
              $min3Days = strtotime($gpsbuddy_route->date . '-3 days');
              $day_before_production = date('l', $min3Days);
              $date_before_production = date('d-m-Y', $min3Days);
            }
            else {
              $min2Days = strtotime($gpsbuddy_route->date . '-2 days');
              $day_before_production = date('l', $min2Days);
              $date_before_production = date('d-m-Y', $min2Days);
            }

          }
        }

        $quotation->company_name = $company->name;
        $quotation->has_endstone = $endstone;
        $quotation->stone_size = $stone_size->name;
        $quotation->stone_color = $stone_color->name;
        $quotation->stone_brand = $stone_brand->name;
        $quotation->seam_color = $seam_color_name;
        $quotation->has_verstek = $hasVerstek;
        $quotation->prod_day = $day_before_production;
        $quotation->prod_date = $date_before_production;

        // Calculate meters for each product type
        $quotation->meters_k = $stone->isKeramiek() ? $quotation->meters : 0;
        $quotation->meters_n = $stone->isNatuursteen() ? $quotation->meters : 0;
        $quotation->meters_b = $stone->isBeton() ? $quotation->meters : 0;
        $quotation->meters_i = $stone->isIsosill() ? $quotation->meters : 0;

        $quotations[] = $quotation;
      }

      $quotations_has_gps = [];
      $quotations_no_gps = [];
      $meters_total_30_k = 0;

      foreach ($quotations as $quotation) {
        if ($quotation->prod_date != '') {
          $quotations_has_gps[] = $quotation;
        }
        else {
          $quotations_no_gps[] = $quotation;
          // Add keramiek meters to total
          $meters_total_30_k += $quotation->meters_k;
        }
      }
      // Only store the total for keramiek meters
      $quotations_no_gps['meters_total_k'] = round($meters_total_30_k, 2);

      $arr_has_gps = [];
      foreach ($quotations_has_gps as $key => $quotation_gps) {
        if (array_key_exists($quotation_gps->prod_date, $arr_has_gps)) {
          $arr_has_gps[$quotation_gps->prod_date][] = $quotation_gps;
        }
        else {
          $arr_has_gps[$quotation_gps->prod_date] = [];
          $arr_has_gps[$quotation_gps->prod_date][] = $quotation_gps;
        }
      }

      $arr_has_gps_with_total = [];
      foreach ($arr_has_gps as $key => $arr) {
        $meters_total_35_k = 0;
        $quotation_ids = [];

        foreach ($arr as $quot) {
          // Add keramiek meters to total
          $meters_total_35_k += $quot->meters_k;

          if ($quot->statusId == Status::STATUS_CHECKED) {
            $quotation_ids[] = $quot->quotationId;
          }
        }

        // Only store the total for keramiek meters
        $arr['meters_total_k'] = round($meters_total_35_k, 2);
        $arr['quotationIds'] = implode(",", $quotation_ids);
        $arr_has_gps_with_total[$key] = $arr;
      }

      function compare_date_keys($dt1, $dt2) {
        return strtotime($dt1) - strtotime($dt2);
      }

      uksort($arr_has_gps_with_total, "compare_date_keys");
      $this->quotations_35 = $arr_has_gps_with_total;
      $this->quotations_30 = $quotations_no_gps;
    }

    public function executeChangewidth() {

      if (isset($_POST['show_width'])) {
        if (!empty($_POST['quotation_number'])) {
          ResponseHelper::redirect('?action=widthoptions&quotation_number=' . $_POST['quotation_number']);
        }
        ResponseHelper::redirectAlertMessage('U heeft geen offerte nummer ingevuld', reconstructQueryAdd());
      }
    }

    public function executeWidthoptions() {

      if (isset($_POST['go_back'])) {
        ResponseHelper::redirect('?action=changewidth');
      }
      $quotation_number_full = $_GET['quotation_number'];

      $quotationNumberExplode = explode("-", $quotation_number_full);
      if (count($quotationNumberExplode) == 1) {
        ResponseHelper::redirectAlertMessage('U heeft geen geldig offerte nummer ingevuld', reconstructQueryAdd());
      }
      $quotationNumber = $quotationNumberExplode[0];
      $quotationVersion = $quotationNumberExplode[1];

      $quotation = Quotations::find_by(['quotationNumber' => $quotationNumber, 'quotationVersion' => $quotationVersion]);
      if (!$quotation) {
        ResponseHelper::redirectAlertMessage('Offerte niet gevonden', reconstructQueryAdd());
      }
      $quotation_extra = QuotationsExtra::find_by(['quotationId' => $quotation->quotationId]);

      if (isset($_POST['change_width'])) {

        $aantal_mm = substr($_POST['standard_width'], -3);
        $stone_width = $quotation_extra->stoneSizeWidth * 10;

        if (str_starts_with($_POST['standard_width'], 'min')) {
          $new_width = $stone_width - (float)$aantal_mm;
        }
        elseif (str_starts_with($_POST['standard_width'], 'plus')) {
          $new_width = $stone_width + (float)$aantal_mm;
        }
        else {
          ResponseHelper::redirectMessage('De breedte is succesvol gewijzigd', reconstructQuery());
        }

        $quotation_extra->stoneSizeWidth = $new_width / 10;
        $quotation_extra->save();

        ResponseHelper::redirectMessage('De breedte is succesvol gewijzigd', reconstructQuery());
      }

      $this->quotation = $quotation;
      $this->width = $quotation_extra->stoneSizeWidth * 10;
    }

    public function executeShowpdf() {
      $pdfGenerator = new ProductionPdfGenerator($_GET['quotation_id']);
      $pdfGenerator->generatePdf();
    }

    public function executePrintrack() {

      if (isset($_POST['generate_pdf'])) {
        if (empty($_POST['amount'])) {
          ResponseHelper::redirectAlertMessage('U heeft geen aantal opgegeven', reconstructQueryAdd());
        }
        elseif ($_POST['amount'] > 200 || $_POST['amount'] < 1) {
          ResponseHelper::redirectAlertMessage('U heeft geen geldig aantal opgegeven, probeer het opnieuw met een aantal tussen de 1 en de 200', reconstructQueryAdd());
        }
        else {
          $generateRackPdf = new GenerateRackPdf($_POST['amount']);
          $generateRackPdf->generatePdf();
        }
      }

    }

    public function executeWebshop() {
      $projects = Projects::find_all_by(['webshopOnly' => 1]);

      $quotation_ids = [];
      foreach ($projects as $project) {
        $quotation_ids[] = $project->quotationId;
      }

      $quotations = Quotations::find_all_by([], 'WHERE statusId >= 30 AND statusId < 41 AND quotationId IN (' . implode(',', $quotation_ids) . ')');

      $quotations_complete = [];
      foreach ($quotations as $quotation) {
        $order_element = OrderElements::find_all_by(['quotationId' => $quotation->quotationId]);
        if ($order_element) continue;

        $projects_quotation = Projects::find_by(['quotationId' => $quotation->quotationId], 'AND showOnProductionPage = 1');

        if (!$projects_quotation) continue;

        $company = CrmCompanies::find_by(['companyId' => $quotation->companyId]);
        $name = $quotation->projectName;
        if ($company) {
          $name = $company->name;
        }

        $quotation->name = $name;
        $quotation->status = Status::getName($quotation->statusId);
        $quotation->quotation_link = '<a href="' . PageMap::getUrl("M_RDE_ORDERS_GENERAL") . '?id=' . $quotation->quotationId . '">' . $quotation->getQuotationNumberFull() . '</a>';
        $quotations_complete[] = $quotation;
      }

      $this->quotations = $quotations_complete;
    }

    public function executeWebshopOptions() {

      if (isset($_POST['go_back'])) {
        ResponseHelper::redirect('?action=webshop');
      }

      $quotation = Quotations::find_by(['quotationId' => $_GET['quotationId']]);
      if (!$quotation) {
        ResponseHelper::redirectNotFound();
      }

      if (isset($_POST['production_pdf'])) {
        ResponseHelper::redirect('?action=showproductionreceiptwebshop&quotationId=' . $quotation->quotationId);
      }

      if (isset($_POST['change_status'])) {
        $quotation->statusId = Status::STATUS_PREPARED;
        $quotation->save();

        ResponseHelper::redirectMessage('Status succesvol gewijzigd', reconstructQuery());
      }
    }

    public function executeShowproductionreceiptwebshop() {
      $pdf = new ProductionReceiptWebshopPdf(DbHelper::escape($_GET['quotationId']));
      $pdf->generate();
    }

    public function executePrintpdfmultiple() {
      if (isset($_POST['go_back'])) {
        ResponseHelper::redirect('?action=work');
      }

      if (isset($_POST['production_pdf_multiple'])) {
        $quotationIdsArr = explode(',', $_GET['quotation_ids']);

        $isAdmin = $_SESSION['userObject']->usergroup == User::USERGROUP_SUPERADMIN || $_SESSION['userObject']->usergroup == User::USERGROUP_ADMIN;
        $pdf = new ProductionReceiptMultiplePdf($quotationIdsArr);
        $pdf->setIsAdmin($isAdmin);
        $pdf->generate();
      }

      if (isset($_POST['change_status_multiple'])) {
        $quotations = Quotations::find_all_by(['quotationId' => explode(',', $_GET['quotation_ids'])]);
        foreach ($quotations as $quotation) {
          if ($quotation->statusId != 30) continue;
          $quotation->statusId = 35;

          $quotation->save();
        }

        ResponseHelper::redirectMessage('Status succesvol gewijzigd', reconstructQuery());
      }
    }

  }