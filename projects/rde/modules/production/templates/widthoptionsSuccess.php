<?php TemplateHelper::includePartial("_tabs.php",'production') ?>
<h2><PERSON><PERSON><PERSON><PERSON> breedte</h2>

<form method="post">
  <input type="submit" name="go_back" value="Annuleer">
  <a href="?action=showpdf&quotation_id=<?php echo $quotation->quotationId ?>" class="gsd-btn gsd-btn-secondary" target="_blank">Productie PDF</a>

  <h4>Breedte opties</h4>

  <div style="display: flex; flex-direction: column;">
    <label><input type="radio" name="standard_width" value="plus_2.5"> +2.5 mm</label>
    <label><input type="radio" name="standard_width" value="plus_2.0"> +2 mm</label>
    <label><input type="radio" name="standard_width" value="plus_1.5"> +1.5 mm</label>
    <label><input type="radio" name="standard_width" value="plus_1.0"> +1 mm</label>
    <label><input type="radio" name="standard_width" value="plus_0.5"> +0.5 mm</label>
    <label><input type="radio" name="standard_width" checked> <?php echo $width; ?> mm (vorig gekozen maat in quotation_extra)</label>
    <label><input type="radio" name="standard_width" value="min_0.5"> -0.5 mm</label>
    <label><input type="radio" name="standard_width" value="min_1.0"> -1 mm</label>
    <label><input type="radio" name="standard_width" value="min_1.5"> -1.5 mm</label>
    <label><input type="radio" name="standard_width" value="min_2.0"> -2 mm</label>
    <label><input type="radio" name="standard_width" value="min_2.5"> -2.5 mm</label>
  </div>
  <br>
  <input type="submit" name="change_width" value="Wijzig breedte" class="gsd-btn gsd-btn-primary">
</form>

<style>
  .pdf-btn {
    display: inline-block;
    border: 1px solid transparent;
    cursor: pointer;
    padding: 7px 10px;
    text-align: center;
    text-decoration: none;
    border-radius: 3px;
    transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    background-color: var(--gsd-btn-bg-color);
    color: var(--gsd-btn-text-color);
    font-size: 1.4rem;
  }
</style>