<?php

  use domain\elements\service\SplitCalculator;
  use domain\firebase\service\PushNotification;
  use domain\reviews\service\PrepareReviews;
  use gsdfw\domain\gpsbuddy\service\GpsbuddyApi;


  class developerRdeActions extends developerActions {

    public function executeScripts() {

//    DrawElements::generatedMitres();


      echo '<pre>';

      //$this->buildCargoQuotations();

//    $this->renameStones();

//    $this->gpsbuddyTest();

//    sendSwiftMail("<EMAIL>", "test 2", "inhoud 2");

//    $this->fillWorkerProfile();

//    echo file_get_contents("http://api.raamdorpel.nl/nl/external?action=trucklocations");

//    $this->fillProductsShop();

//    $this->snippet();
//    $this->migrateSocialUrls();

//    $this->buildCatProdUrls();

//    $this->migrateCategoryKeywords();

//    CronjobsFactory::calculateProductionStats(true);


//    $this->ontdubbeleQuotationExtra();

//    $this->cleanIbans();

//    $this->checkViaPostcodeApi();
//    $this->correctStock();

//    $this->rebuildModelClasses();

//    $this->correctQuotationStatus();

//    $this->correctCargoReceiptQuotations();

//    $packs_api = new PacksApi();
//    $response = $packs_api->downloadlabel("6827BJ", "21085953");
//    ResponseHelper::exitAsPdf($response);

//    $this->fillMollie();

//    $this->encryptPasswords();
//    $this->correctPasswords();

//    $this->correctMollie();

//    $this->correctHours();

//    $this->importMetas();

//    MailsFactory::sendDamaged();

//    $this->correctStoneImages();

//    $this->setElementLengthTotal();
//    $this->correctMaxOrderSize();

//    ProductionOrder::create(new Quotations());

//    $this->correctImageFilenames();

//    $this->correctInvoiceParties();

//    $this->correctMuurafdekkersEindsteen();

//    GsdMailer::build("<EMAIL>", "test swiftmailer", "test swiftmailer inhoud")->send();
//    mail("<EMAIL>","php mail","test php mail inhoud");

//    $this->naturalStoneMigrate();
//      $this->correctProjectsOrdernumber();

//    $this->ontdubbelenSandbox();

//    $this->checkSanboxVsPerson();

//   MailsFactory::sendQuotationsProduced();

//    $this->correctVensterbanken();

//    CronjobsFactory::importStandaardlengtes(true);

//    $this->resizeStoneImages();

//    $this->setStoneCategories();
//    $this->setQuotationStoneCat();
//    $this->setQuotationStoneCatNatuursteen();

//    $this->compressContainerImages();

//    $this->checkWhois();

//    $this->setStartdateEmployeeQuotations();

//    $this->cleanupGeneratedRackIdsEmployees();

      //MailFileBox::fetchFilesFromMailbox();
      //Files::fetchFilesFromMailbox();

//      GsdMailer::build("<EMAIL>","test32 RDE DKIM","test nieuwe mailser")->send();

//      $_GET["robert"]="<EMAIL>";
//      $quotation = Quotations::getById(169699);
//      $elements = OrderElements::find_all_by(["quotationId" => $quotation->quotationId]);
//      MailsFactory::sendTenderEmail($quotation, $elements);

//      $this->repairGsdBlokeditorFilepath();

//      $this->fixReviewRecords();

//      $this->sendDeliveredMail();

//      $this->sendPickupMail();

//      $quot = Quotations::find_by(['quotationId' => 196794]);
//      $qe = QuotationsExtra::find_by(['quotationId' => $quot->id]);
//      $stone = Stones::find_by(["stoneId" => $quot->stoneId]);
//      $stone_size = StoneSizes::find_by(["sizeId" => $stone->sizeId]);
//      $element = OrderElements::find_by(['elementId' => 524947]);
//
//      (new DrawElements)->generate(196135,524947);

//      $f = $element->calculateValuesCeramic($quot,$qe,$stone_size,$stone);

//      $this->cleanupSandBoxUserAddresses();

//      PushNotification::sendTestPushNotification(15); // test with user id of a subscribed user profile
//      $this->calculateTotalPriceForProjects();
//      $this->setPriceOnRequestForProducts();
      $this->setProductUrls();
      $this->runSplitCalculation();

      echo 'Done';
      echo '</pre>';
      ResponseHelper::exit();
    }

    private function runSplitCalculation() {
      $quotation = Quotations::find_by(['quotationId' => 212886]);
      dump($quotation);
      $quotationExtra = QuotationsExtra::find_by(['quotationId' => $quotation->quotationId]);
      dump($quotationExtra);
      $calculator = new SplitCalculator($quotation, $quotationExtra);
      $calculator->run();
      $element = OrderElements::find_by(['quotationId' => $quotation->quotationId]);
      $parts = OrderElementparts::find_by(['elementId' => $element->elementId]);
      dump($parts);
    }

    // Updates product URLs for all product contents, if they don't already have a URL set.
    private function setProductUrls(): void {
      $contents = ProductContent::find_all();
      foreach ($contents as $content) {
        // If the content already has a URL, skip it
        if ($content->url) continue;

        // Generate a URL and make sure it is unique
        $content->buildDefaultUrl();
        $content->buildUrlUnique();

        try {
          $content->save();
          echo "Updated product content URL for product ID: {$content->product_id} - URL: {$content->url}\n";
        } catch (Exception $e) {
          echo "Failed to update product content URL for product ID: {$content->product_id} - Error: {$e->getMessage()}\n";
        }
      }
    }

    private function setPriceOnRequestForProducts() {
      // get products with price 0 or 1
      $products = Product::find_all("WHERE price_part = 0 OR price_part = 1");
      $productsUpdated = 0;
      foreach ($products as $product) {
        $product->price_on_request = 1;
        try {
          $product->save();
          $productsUpdated++;
        } catch (Exception) {
          echo "Product $product->id could not be saved, its data is most likely corrupted\n";
        }
      }
      echo "Products updated: $productsUpdated\n";
    }

    private function calculateTotalPriceForProjects(): void {
      $projectsUpdated = 0;

      foreach (Projects::find_all("WHERE euro IS NULL") as $project) {
        if (!is_numeric($project->pieceprice) || !is_numeric($project->size)) {
          echo "Project $project->projectId has invalid pieceprice or size\n";
          continue;
        }

        $project->euro = $project->pieceprice * $project->size;

        try {
          $project->save();
          $projectsUpdated++;
        } catch (Exception) {
          echo "Project $project->projectId could not be saved, its data is most likely corrupted\n";
        }
      }

      echo "Projects updated: $projectsUpdated\n";
    }
    // Sets any user with a delivery address that doesn't exist to null, 0 should also be set to null
    private function cleanupSandBoxUserAddresses() {
      $usersCleaned = 0;
      foreach (SandboxUsers::find_all() as $su) {
        // If the last delivery address is null, we don't need to do anything
        if ($su->lastDeliveryAddressId === null) continue;

        if ($su->lastDeliveryAddressId !== 0) {
          $address = CrmAddresses::find_by(["addressId" => $su->lastDeliveryAddressId]);

          // If the address exists, the id is valid and should not be changed
          if ($address) continue;
        }

        // In this case, the id is either 0 or doesn't exist
        $su->lastDeliveryAddressId = null;

        try {
          $su->save();
          $usersCleaned++;
        } catch (Exception) {
          echo "User {$su->userId} could not be cleaned, its data is likely corrupted\n";
        }
      }
      echo "Users cleaned: $usersCleaned\n";
    }

    private function fixReviewRecords() {
      $invoices = Invoices::find_all("WHERE dateInvoice>'2023-07-20'");
      $count = 0;
      foreach ($invoices as $invoice) {
        $count += (new PrepareReviews($invoice))->execute();
      }
      dump($count);
    }


    private function cleanupGeneratedRackIdsEmployees() {
      $found = [];
      $doubles = 0;
      foreach (GeneratedRackIdsEmployees::find_all() as $gre) {
        $key = $gre->employeeId . "_" . $gre->washerEmployeeId . "_" . $gre->rackId . "_" . $gre->datetime;
        if (isset($found[$key])) {
          $doubles++;
          $gre->destroy();
        }
        $found[$key] = true;
      }
      echo $doubles;
    }

    private function setStartdateEmployeeQuotations() {
      $startdate = date("Y-m-d", strtotime("-1 WEEK"));
      $enddate = date("Y-m-d");
      if (DEVELOPMENT) {
        $startdate = date("Y-m-d", strtotime("-22 WEEK"));
        $enddate = date("Y-m-d", strtotime("-21 WEEK"));
      }
      $filter = "";
//      $filter = "WHERE enddate>'".$startdate."' AND enddate<='".$enddate."' ";
      $filter .= "ORDER BY id DESC ";
//      $filter .= "LIMIT 5000 ";
      $equotations = EmployeeQuotation::find_all($filter);

      foreach ($equotations as $equotation) {
        $eqs = EmployeeQuotation::find_all_by(["quotationId" => $equotation->quotationId]);

        if (count($eqs) == 1) {
          //er heeft maar 1 employee aan deze quotation gewerkt
          $eq = $eqs[0];
          //pak van deze employee de laatst afwerkte order
          $eq_prev = EmployeeQuotation::find_by(["employeeId" => $eq->employeeId], "AND enddate<'" . $eq->enddate . "' ORDER BY enddate DESC LIMIT 1");
          if (!$eq_prev) continue;
//            pd($eq);
//            pd($eq_prev);
          if ($eq->getEnddateFormatted("dmY") == $eq_prev->getEnddateFormatted("dmY")) {
            //de vorige order is op dezelfde dag verwerkt, als deze.
            //we gaan de startdatum zetten.

            $quotation = Quotations::find_by(["quotationId" => $equotation->quotationId]);
            if ($quotation->meters == 0) continue;//overslaan

            $minutes = ($eq->getEnddateFormatted("U") - $eq_prev->getEnddateFormatted("U")) / 60;
            $prevTime = intval($eq_prev->getEnddateFormatted("Hi"));
            $curTime = intval($eq->getEnddateFormatted("Hi"));
            if (($prevTime <= 1005 && $curTime >= 1010) || ($prevTime <= 1505 && $curTime >= 1510)) {
              //er zit koffie pauze tussen
              $minutes -= 15;
            }
            elseif ($prevTime <= 1235 && $curTime >= 1255) {
              //er zit lunch pauze tussen
              $minutes -= 30;
            }

            $speed_calc = floor(($quotation->meters / ($minutes / 60)) * 100);

            if ($speed_calc > 1700 || $speed_calc < 200) {
              //meer dan 17 meter per uur/minder dan 2m/uur overslaan
              continue;
            }

            //alles goed
            if ($eq->getStartdateFormatted() != $eq_prev->getEnddateFormatted()) {
              $eq->startdate = $eq_prev->getEnddateFormatted("Y-m-d H:i:s");
              $eq->save();
              //pd($eq_prev->getEnddateFormatted() .' tot ' . $eq->getEnddateFormatted());
            }

          }

        }

      }

    }


    private function compressContainerImages() { //891 mappen

      set_time_limit(3600);
      ini_set('memory_limit', '1200M');
      ini_set('max_execution_time', 3600); // 300 sec = 5 min

      $dir = DIR_ROOT_HTTPDOCS . "filesystem/raamdorpel/clients/container";
      $h = opendir($dir);
      $start = 514;
      $run = 500;
      $tel = -1;
      while ($file = readdir($h)) {
        if ($file == "." || $file == "..") continue;
        $tel++;
        if ($tel < $start) continue;
        if ($tel >= $start + $run) break;

        $path = $dir . '/' . $file;
        //pd($path);
        if (is_dir($path)) {
          //directory overslaan
          $sub = opendir($path);
          while ($file_in_dir = readdir($sub)) {
            if ($file_in_dir == "." || $file_in_dir == "..") continue;
            $filepath = $path . '/' . $file_in_dir;
            //pd($filepath);
            $res = ImageHelper::compress($filepath);
          }
        }

        logToFile("compress", $tel . " - " . $path);

        echo $path . "\n";

      }
      logToFile("compress", "DONE");
      closedir($h);
    }

    private function setQuotationStoneCatNatuursteen() {
      foreach (Stones::find_all_by(["category_id" => [10, 19, 20, 21]]) as $stone) {
        if (stripos($stone->name, "Basic+") !== false || $stone->brandId == 5) {
          $stone->category_id = 21;
        }
        elseif (stripos($stone->name, "Basic") !== false || $stone->brandId == 12) {
          $stone->category_id = 20;
        }
        else {
          $stone->category_id = 19;
        }
        $stone->save();
      }

      //corectie quotations
      $stones = AppModel::mapObjectIds(Stones::find_all(), "stoneId");
      foreach (Quotations::find_all("WHERE stoneId!='' AND stoneCategoryId IN (10,19,20,21) ") as $quot) {
        if (isset($stones[$quot->stoneId])) {
          if ($quot->priceDate == "0000-00-00") $quot->priceDate = $quot->quotationDate;
          if ($quot->pickup == "") $quot->pickup = "false";
          if ($quot->stoneCategoryId != $stones[$quot->stoneId]->category_id) {
            $quot->stoneCategoryId = $stones[$quot->stoneId]->category_id;
            $quot->save();
            echo $quot->quotationId . "\n";
          }
        }
      }


    }

    private function setQuotationStoneCat() {
      $stones = AppModel::mapObjectIds(Stones::find_all(), "stoneId");
      foreach (Quotations::find_all("WHERE stoneId!='' AND stoneCategoryId IS NULL ") as $quot) {
        if (isset($stones[$quot->stoneId])) {
          if ($quot->priceDate == "0000-00-00") $quot->priceDate = $quot->quotationDate;
          if ($quot->pickup == "") $quot->pickup = "false";
          $quot->stoneCategoryId = $stones[$quot->stoneId]->category_id;
          $quot->save();
        }
      }
    }

    private function setStoneCategories() {
      foreach (Stones::find_all() as $stone) {
        if ($stone->short == "") {
          $stone->short = $stone->name;
          $stone->save();
        }
        if (in_array($stone->brandId, [1, 2]) && $stone->type == "raamdorpel") {
          $stone->category_id = 4;
          $stone->material = Stones::MATERIAL_KERAMIEK;
          $stone->save();
        }
        elseif (in_array($stone->brandId, [1, 2]) && $stone->type == "spekband") {
          $stone->material = Stones::MATERIAL_KERAMIEK;
          $stone->category_id = 5;
          $stone->save();
        }
        elseif ($stone->brandId == 3 && $stone->type == "spekband") {
          $stone->material = Stones::MATERIAL_BETON;
          $stone->category_id = 8;
          $stone->save();
        }
        elseif ($stone->brandId == 3 && $stone->type == "raamdorpel") {
          $stone->material = Stones::MATERIAL_BETON;
          $stone->category_id = 7;
          $stone->save();
        }
        elseif (in_array($stone->brandId, [1, 2]) && $stone->type == "muurafdekker") {
          $stone->material = Stones::MATERIAL_KERAMIEK;
          $stone->category_id = 6;
          $stone->save();
        }
        elseif ($stone->brandId == 14 && $stone->type == "raamdorpel") {
          $stone->material = Stones::MATERIAL_ISOSILL;
          $stone->category_id = 15;
          $stone->save();
        }
        elseif (in_array($stone->brandId, [4, 5, 6, 7, 8, 9, 10, 11, 12]) && $stone->type == "raamdorpel") {
          $stone->material = Stones::MATERIAL_NATUURSTEEN;
          $stone->category_id = 10;
          $stone->save();
        }
        elseif (in_array($stone->brandId, [4, 5, 6, 7, 8, 9, 10, 11, 12]) && $stone->type == "spekband") {
          $stone->material = Stones::MATERIAL_NATUURSTEEN;
          $stone->category_id = 11;
          $stone->save();
        }
        elseif (in_array($stone->brandId, [4, 5, 6, 7, 8, 9, 10, 11, 12]) && $stone->type == "muurafdekker") {
          $stone->material = Stones::MATERIAL_NATUURSTEEN;
          //eenzijdig/ezelsrug/plat
          if (stripos($stone->name, "eenzijdig") !== false) {
            $stone->category_id = 16;
          }
          elseif (stripos($stone->name, "plat") !== false) {
            $stone->category_id = 17;
          }
          elseif (stripos($stone->name, "Ezelsrug") !== false) {
            $stone->category_id = 18;
          }
          else {
            dump($stone);
            dumpe(StoneBrands::find_by(["brandId" => $stone->brandId]));
          }

          $stone->save();
        }
        elseif (in_array($stone->brandId, [4, 5, 6, 7, 8, 9, 10, 11, 12]) && $stone->type == "vensterbank") {
          $stone->material = Stones::MATERIAL_NATUURSTEEN;
          $stone->category_id = 13;
          $stone->save();
        }
        else {
          dump($stone);
          dumpe(StoneBrands::find_by(["brandId" => $stone->brandId]));
        }

      }
    }

    private function resizeStoneImages() {
      $dir = DIR_ROOT_HTTPDOCS . 'images/thresholds/';
      foreach (Stones::find_all("WHERE image!='' AND NOT image IS NULL AND brandId!=1 AND brandId!=2") as $stone) {
        pd($stone->image);
        ImageHelper::resizeImageGD($dir . $stone->image, $dir . $stone->image, 600, 600);
      }

    }


    private function correctVensterbanken() {
      foreach (Stones::find_all_by(["type" => Stones::TYPE_VENSTERBANK]) as $stone) {
        foreach (StonePrices::find_all_by(["stoneId" => $stone->stoneId]) as $price) {
          $price->price_m2 = $price->price;
          $price->price = 8;
          $price->save();
        }
      }
    }

    private function ontdubbelenSandbox() {
//
//    foreach(SandboxUsers::find_all_by(["blocked"=>"false"]) as $su) {
//      if(!ValidationHelper::isEmail($su->email)) {
//        echo $su->userId.'-'.$su->companyId.'-'.$su->personId.' '.$su->email.' '.$su->blockedbyadmin.' '.$su->getCreated()."\n";
//        //$su->email = trim($su->email);
//        $su->blocked = "true";
//        $su->blockedbyadmin = "true";
//        $su->save();
//      }
//    }
//
//    ResponseHelper::exit();
      $sus = [];
      foreach (SandboxUsers::find_all_by(["blocked" => "false"]) as $su) {
        $sus[$su->email][] = $su;
      }
      foreach ($sus as $email => $l_users) {
        if (count($l_users) <= 1) continue;
        $tel = 0;
        /** @var SandboxUsers $su */
        foreach ($l_users as $su) {
          echo $su->userId . '-' . $su->companyId . '-' . $su->personId . ' ' . $su->email . ' ' . $su->blockedbyadmin . ' ' . $su->getCreated() . "\n";
          //pd($su);
          if ($tel >= 1) {
            $su->blocked = "true";
            $su->blockedbyadmin = "true";
            $su->save();
            echo $su->userId . "- blocked\n";
          }
          $tel++;
        }
        echo "\n";
      }

    }

    private function checkSanboxVsPerson() {
      foreach (SandboxUsers::find_all("WHERE NOT companyId IS NULL AND companyId!=0 AND NOT personId IS NULL AND personId!=0 ORDER BY userId DESC") as $su) {
        $other = SandboxUsers::find_by(["companyId" => $su->companyId, "personId" => $su->personId], "AND userId!=" . $su->userId);
        if ($other) {
          pd($su);
          pd($other);
          ResponseHelper::exit();
        }
      }
    }

    private function correctProjectsOrdernumber() {
      foreach (Quotations::find_all_by(["createdVia" => "webshop"]) as $q) {
        $projects = Projects::find_all_by(["quotationId" => $q->quotationId], "ORDER BY orderNr");
        $orderNr = 1;
        foreach ($projects as $project) {
          if ($project->orderNr != $orderNr) {
            $project->orderNr = $orderNr;
            $project->save();
            pd($q->quotationId . " - " . $project->product_id);
          }
          $orderNr++;
        }
      }
    }

    private function correctMuurafdekkersEindsteen() {
      foreach (Quotations::find_all_by(["offerteVariant" => [Quotations::VARIANT_KM, Quotations::VARIANT_NM, Quotations::VARIANT_KS, Quotations::VARIANT_BS, Quotations::VARIANT_NS]]) as $quot) {
        if ($quot->brandId == StoneBrands::BRAND_ID_STJORIS && in_array($quot->offerteVariant, [Quotations::VARIANT_KS, Quotations::VARIANT_BS, Quotations::VARIANT_NS])) {
          continue;
        }
        pd($quot->quotationId . ' ' . $quot->offerteVariant);
        foreach (OrderElements::find_all_by(["quotationId" => $quot->quotationId]) as $oe) {
          if ($oe->flagWindowSide != "none") {
            pd("- " . $oe->elementId . " - " . $oe->leftEndstone . " - " . $oe->rightEndstone . " - " . $oe->flagWindow . " - " . $oe->flagWindowSide);

            if ($oe->flagWindowSide == "both") {
              $oe->leftEndstone = "1";
              $oe->rightEndstone = "1";
            }
            elseif ($oe->flagWindowSide == "left") {
              $oe->leftEndstone = "1";
              $oe->rightEndstone = "0";
            }
            elseif ($oe->flagWindowSide == "right") {
              $oe->leftEndstone = "0";
              $oe->rightEndstone = "1";
            }
            else {
              $oe->leftEndstone = "0";
              $oe->rightEndstone = "0";
            }

            $oe->flagWindow = "false";
            $oe->flagWindowSide = "none";
            $oe->save();

          }
        }

      }
    }

//  private function correctInvoiceParties() {
//    foreach(CrmCompanies::find_all_by(["invoicePartyId"=>null]) as $company) {
//      $ip = CrmInvoiceparties::find_by(["companyId"=>$company->companyId]);
//      if($ip) {
//        echo $company->companyId." ".$ip->invoicePartyId."\n";
//      }
//    }
//  }

    private function correctMaxOrderSize() {
      foreach (Category::find_all_by(["id" => [6, 27, 28]]) as $cat) {
        $selfcatprods = CategoryProduct::find_all_by(['category_id' => $cat->id]);
        foreach ($selfcatprods as $cp) {
          $product = Product::find_by_id($cp->product_id);
          $product->order_size_max = 1;
          $product->save();
        }
      }
    }


    private function setElementLengthTotal() {
      $mitres = AppModel::mapObjectIds(Mitres::find_all(), "mitreId");
      $stones = AppModel::mapObjectIds(Stones::find_all(), "stoneId");
      $sizes = AppModel::mapObjectIds(StoneSizes::find_all(), "sizeId");
      foreach (Quotations::find_all("ORDER BY quotationId") as $quotation) {
        $quotations_extra = QuotationsExtra::find_by(["quotationId" => $quotation->quotationId]);
        echo($quotation->quotationId . "\n");
        $elements = OrderElements::find_all_by(["quotationId" => $quotation->quotationId]);
        if ($quotation->stoneId == 0) continue;
        $stone = $stones[$quotation->stoneId];
        $size = $sizes[$stone->sizeId];
        foreach ($elements as $element) {
          if ($element->elementLengthTotal == 0) {
            //fallback
            $leftMitre = $rightMitre = false;
            if ($element->leftMitreId != "" && $element->leftMitreId != 0)
              $leftMitre = $mitres[$element->leftMitreId];
            if ($element->rightMitreId != "" && $element->rightMitreId != 0)
              $rightMitre = $mitres[$element->rightMitreId];

            $element->elementLengthTotal = $element->getTotalElementlength($quotation, $quotations_extra, $leftMitre, $rightMitre, $size, $stone);
            if ($element->referenceName == "") {
              $element->referenceName = "A";
            }
            if ($element->flagWindow == "") {
              $element->flagWindow = "false";
            }
            $element->save();
          }
        }
      }
    }

    private function correctStoneImages() {
      $dir = DIR_ROOT_HTTPDOCS . 'images/thresholds/';
      foreach (Stones::find_all("WHERE image!='' AND NOT image IS NULL") as $stone) {
        if (!file_exists($dir . $stone->image)) {
          echo $dir . $stone->image . "\n";
          $stone->image = null;
          $stone->save();
        }
      }

    }


    private function importMetas() {
      $content = file_get_contents(DIR_ROOT . 'docs/rde/imports/metas_rde.csv');
      foreach (explode("\n", $content) as $tel => $row) {
        $cols = str_getcsv($row, ";", escape: '\\');
        if (trim($cols[0]) == "") continue;

//      pd($cols);
        $url = trim($cols[0]);
        $meta = trim(($cols[1]));
//      echo $meta."\n";

        $found = false;
        $pc = PageContent::find_by(["url" => $url]);
        if ($pc) {
          $found = true;
          $pc->seo_desc = $meta;
          $pc->save();
          echo "Updated pagecontent: " . $pc->page_id . "\n";
        }
        if (!$found) {
          $cc = CategoryContent::find_by(["url" => $url]);
          if ($cc) {
            $found = true;
            $cc->seo_desc = $meta;
            $cc->save();
            echo "Updated categorycontent: " . $cc->id . "\n";
          }
        }
        if (!$found) {
          $prc = ProductContent::find_by(["url" => $url]);
          if ($prc) {
            $found = true;
            $prc->seo_desc = $meta;
            $prc->save();
            echo "Updated productcontent: " . $prc->id . "\n";
          }
        }
        if (!$found) {
          echo "NOT FOUND: " . $url . "\n";
        }


      }
    }

    public function correctHours() {
      foreach (WorkerCustWeek::find_all() as $wcw) {
        $days = WorkerCustDay::find_all_by(["worker_cust_week_id" => $wcw->id], "ORDER BY date");
        if ($wcw->week == 1 && $wcw->year == 2019) {
          pd($days);
        }
        continue;
        if (count($days) != 7) {
          pd($days);
          die("Geen 7 dagen");
        }
        $firstday = $days[0];
        if ($wcw->year != $firstday->getDate("Y")) {
          //year datum komt niet overeen. vul dagen opnieuw
          $mondaytime = DateTimeHelper::getMondayFromWeeknr($wcw->week, $wcw->year, "U");
          foreach ($days as $tel => $day) {
            $newdate = date("Y-m-d", strtotime("+" . " " . $tel . "DAYS", $mondaytime));
            echo $day->getDate("%A, %d %B %Y", true) . " word ";
            $day->date = $newdate;
            echo $day->getDate("%A, %d %B %Y", true);
            echo "\n";
            $day->save();
          }
          echo "\n";
        }


      }
    }


    private function correctMollie() {
      foreach (Quotations::find_all("WHERE NOT mollie_id IS NULL") as $quotation) {
        $m = Mollie::find_by_id($quotation->mollie_id);
        $m->insertTS = $quotation->productionDate;
        $m->save();
        echo $quotation->quotationNumber . "\n";
      }

      foreach (Invoices::find_all("WHERE NOT mollie_id IS NULL") as $invoice) {
        $m = Mollie::find_by_id($invoice->mollie_id);
        $m->insertTS = $invoice->paid;
        $m->save();
        echo $invoice->invoiceNumber . "\n";
      }

    }

    private function fillMollie() {
      foreach (Quotations::find_all("WHERE NOT paymentId IS NULL") as $quotation) {
        $m = Mollie::find_by(['paymentId' => $quotation->paymentId]);
        if (!$m) {
          $m = new Mollie();
          $m->paymentId = $quotation->paymentId;
          $m->amount = $quotation->getTotalPrice();
        }
        else {
          echo "SAME " . $m->id . "\n";
          $m->amount += $quotation->getTotalPrice();
        }
        $m->save();
        $quotation->mollie_id = $m->id;
        $quotation->save();
        echo $quotation->quotationNumber . "\n";
      }

      foreach (Invoices::find_all("WHERE NOT paid_with_id IS NULL") as $invoice) {
        $m = Mollie::find_by(['paymentId' => $invoice->paid_with_id]);
        if (!$m) {
          $m = new Mollie();
          $m->paymentId = $invoice->paid_with_id;
          $m->amount = $invoice->totalProjectValue;
        }
        else {
          echo "SAME???????? " . $m->id . "\n";
        }
        $m->save();
        $invoice->mollie_id = $m->id;
        $invoice->save();
        echo $invoice->invoiceNumber . "\n";
      }

    }

    private function correctCargoReceiptQuotations() {
      $unique = [];
      foreach (CargoReceiptQuotation::find_all() as $crq) {
        if (isset($unique[$crq->cargoReceiptId . '_' . $crq->quotationId])) {
          echo "dubbel: " . $crq->cargoReceiptId . '_' . $crq->quotationId . "\n";
          $crq->destroy();
        }
        $unique[$crq->cargoReceiptId . '_' . $crq->quotationId] = true;
      }
    }

//    private function correctQuotationStatus() {
//      foreach (Invoices::find_all("WHERE YEAR(iSent)=2019 AND NOT paid IS NULL") as $invoice) {
//        $quotations = Quotations::find_all_by(["invoiceId" => $invoice->invoiceId], "AND statusId!=62");
//        foreach ($quotations as $quotation) {
//          if ($quotation->statusId != 62) {
//            pd($quotation->quotationNumber . ' ' . $quotation->statusId);
//            $quotation->statusId = Status::STATUS_PAID;
//            $quotation->save();
//          }
//        }
//
//      }
//    }

    private function correctStock() {

      foreach ([238, 292, 38, 13] as $stoneId) {
        foreach (StoneOrderItem::find_all_by(["stone_id" => $stoneId]) as $soi) {
          if ($soi->receiveddate != "") continue;
          $stoneorder = StoneOrder::find_by_id($soi->stone_order_id);
          if ($stoneorder->receiveddate != "") {
            $soi->senddate = $stoneorder->senddate;
            $soi->receiveddate = $stoneorder->receiveddate;
            $soi->receivedsize = $soi->size;
            $soi->save();
          }

        }
      }


    }

    private function checkViaPostcodeApi() {
//    MailsFactory::sendMissingDataEmail(Quotations::find_by(["quotationId"=>112189]));
//    ResponseHelper::exit();
      $result = PostcodeApi::getAll("5531 SG", 27);

    }

    private function cleanIbans() {
      foreach (CrmBankaccounts::find_all() as $ba) {
        $ba->IBAN = strtoupper(str_replace(" ", "", $ba->IBAN));
        if ($ba->IBAN != "" && !ValidationHelper::isIban($ba->IBAN)) {
          pd($ba->IBAN);
          $ba->IBAN = null;
        }
        if ($ba->accountnumber == "") {
          $ba->accountnumber = substr($ba->IBAN, -9);
        }
        $ba->save();
      }
    }

    public function ontdubbeleQuotationExtra() {
      $query = "SELECT count(quotationId) as c, id, quotationId FROM " . QuotationsExtra::getTablename() . " GROUP BY quotationId ORDER BY c DESC";
      $oResult4 = DBConn::db_link()->query($query);
      while ($row = $oResult4->fetch_assoc()) {
//      pd($row);
        if ($row["c"] <= 1) break;

        $qexs = QuotationsExtra::find_all_by(["quotationId" => $row["quotationId"]], "order by id DESC");
        foreach ($qexs as $qe) {
          if ($qe->stoneDeliveryDate == "0000-00-00") {
            $qe->stoneDeliveryDate = null;
          }
          if ($qe->quotationAltPriceYear == "0000-00-00") {
            $qe->quotationAltPriceYear = null;
          }
          if ($qe->stoneOrderedDate == "0000-00-00") {
            $qe->stoneOrderedDate = null;
          }
        }
//      pd($qexs);
        $diff = (array_diff((array)$qexs[0], (array)$qexs[1]));
//      pd($diff);
        if (count($diff) == 1 && isset($diff["id"])) {
          pd("id anders");
          //de eerste gooien we weg
          $qexs[0]->destroy();
        }
        elseif (count($diff) == 2 && isset($diff["id"]) && isset($diff["totalMiddlesStones"])) {
          pd("id/totalMiddlesStones anders");
          //de eerste gooien we weg
          $qexs[0]->destroy();
        }
        else {
//        $qexs[0]->destroy();
          pd($diff);
          pd($qexs);
        }

      }
    }

    public function snippet() {
      $url = "https://www.raamdorpel.nl/prefab-raamdorpels";
      $username = 'iclicks';
      $password = 'welkom2013';
      $context = stream_context_create([
        'http' => [
          'header' => "Authorization: Basic " . base64_encode("$username:$password"),
        ],
      ]);
      $content = file_get_contents($url, false, $context);

      $start = strpos($content, "<header");
      $header = substr($content, $start, strpos($content, '<div class="container-wrapper">', $start) - $start);
      CacheHelper::putCacheFile("header.raamdorpel.nl.html", $header);

      $start = strpos($content, "<footer");
      $footer = substr($content, $start, strpos($content, '</footer>', $start) - $start) . '</footer>';
      CacheHelper::putCacheFile("footer.raamdorpel.nl.html", $footer);
    }

    private function fillProductsShop() {

      $allproducts = Product::getAllProducts("");
      foreach ($allproducts as $pr) {
        $pr->destroy();
      }

      if (true) {
        logToFile("cron", "importStones start");

        //kleuren
        $prodcatid = 3;
        $allproducts = AppModel::mapObjectIds(Product::getAllProducts('AND category_product.category_id=' . $prodcatid, 'nl'), "supplier_code");
        foreach (StoneColors::getDisplayColors() as $color) {
          $supplier_code = "COLOR_" . $color->colorId;
          $product = new Product();
          $content = new ProductContent();
          if (isset($allproducts[$supplier_code])) {
            $product = $allproducts[$supplier_code];
            $content = $product->content;
          }
          else {
            $content->name = $color->name;
            $product->online_custshop = 1;
            $product->online_uc = 1;
            $product->online_admin = 1;
            $product->brand_id = $color->brandId;
            $product->supplier_code = $supplier_code;
            $product->vatgroup = 1;
            $product->discountgroup_id = 3; //stenen
            $content->locale = 'nl';
            $product->price_part = 1; //even een lege prijs
          }

          $product->code = $color->short;


          $product->save();

          $content->product_id = $product->id;
          $content->save();

          $catprod = CategoryProduct::getCategoryProduct($product->id, $prodcatid);
          if (!$catprod) {
            $catprod = new CategoryProduct();
            $catprod->product_id = $product->id;
            $catprod->category_id = $prodcatid;
            $catprod->online = 1;
            $catprod->save();
          }
        }

        //stenen
        $prodcatid = 1;
        $allproducts = AppModel::mapObjectIds(Product::getAllProducts('AND category_product.category_id=' . $prodcatid, 'nl'), "supplier_code");
        $stoneprices = StonePrices::getLatestPrices();

        foreach (Stones::getDisplayStones() as $stone) {
          $supplier_code = "STONE_" . $stone->stoneId;
          $product = new Product();
          $content = new ProductContent();
          if (isset($allproducts[$supplier_code])) {
            $product = $allproducts[$supplier_code];
            $content = $product->content;
          }
          else {
            $content->name = $stone->name;
            $product->online_custshop = 1;
            $product->online_uc = 1;
            $product->online_admin = 1;
            $product->brand_id = $stone->brandId;
            $product->supplier_code = $supplier_code;
            $product->vatgroup = 1;
            $product->discountgroup_id = 1; //stenen
            $content->locale = 'nl';
          }

          $product->code = $stone->short;

          if (!isset($stoneprices[$stone->stoneId])) continue; //geen prijs gedefineerd, overlaan.
          $product->price_part = round($stoneprices[$stone->stoneId]->price, 2);

          //images
          $filesource = DIR_ROOT_HTTPDOCS . 'images/thresholds/' . $stone->image;
          if ($stone->image != "" && file_exists($filesource)) {
            $path_parts = pathinfo($filesource);
            $ext = strtolower($path_parts['extension']);

            $thumb = 'pr_' . $product->id . '_' . time() . '_main_thumb.' . $ext;
            $orig = 'pr_' . $product->id . '_' . time() . '_main_orig.' . $ext;

            //opruimen oude beelden
            if ($product->main_image_thumb != "" && file_exists(DIR_UPLOAD_CAT . $product->main_image_thumb)) {
              unlink(DIR_UPLOAD_CAT . $product->main_image_thumb);
            }
            if ($product->main_image_orig != "" && file_exists(DIR_UPLOAD_CAT . $product->main_image_orig)) {
              unlink(DIR_UPLOAD_CAT . $product->main_image_orig);
            }

            $product->main_image_thumb = $thumb;
            $product->main_image_orig = $orig;

            ImageHelper::resizeToFixedSize($filesource, DIR_UPLOAD_CAT . $thumb, IMAGES_THUMB_WIDTH, IMAGES_THUMB_HEIGHT);
            ImageHelper::resizeImageGD($filesource, DIR_UPLOAD_CAT . $orig, IMAGES_ORIG_WIDTH, IMAGES_ORIG_HEIGHT, true);
          }

          $product->save();

          $content->product_id = $product->id;
          $content->save();

          $catprod = CategoryProduct::getCategoryProduct($product->id, $prodcatid);
          if (!$catprod) {
            $catprod = new CategoryProduct();
            $catprod->product_id = $product->id;
            $catprod->category_id = $prodcatid;
            $catprod->online = 1;
            $catprod->save();
          }
        }

        logToFile("cron", "importStones end");
      }
    }

    public function fillWorkerProfile() {
      foreach (ProductionEmployees::find_all() as $pe) {

        $worker = Worker::getWorkerByExtId($pe->employeeId);
//      if($pe->name=="Test Persoon") {
//        $worker = Worker::find_by_id(28);
//      }
        if (!$worker) {
          pd($pe->name);
          continue;
        }
        if ($pe->password != "") {
          $wp = new WorkerProfile();
          $wp->worker_id = $worker->id;
          $wp->type = WorkerProfile::TYPE_APP;
          $wp->code = "password";
          $wp->value = $pe->password;
          $wp->save();
        }
        if ($pe->passwordnr != "") {
          $wp = new WorkerProfile();
          $wp->worker_id = $worker->id;
          $wp->type = WorkerProfile::TYPE_APP;
          $wp->code = "passwordnr";
          $wp->value = $pe->passwordnr;
          $wp->save();
        }
        for ($tel = 1; $tel <= 15; $tel++) {
          if ($pe->{"app" . $tel} == 1) {
            $wp = new WorkerProfile();
            $wp->worker_id = $worker->id;
            $wp->type = WorkerProfile::TYPE_APP;
            $wp->code = 'app' . $tel;
            $wp->value = 1;
            $wp->save();
          }
        }
      }
    }

    public function gpsbuddyTest() {
      $gpsbuddy = GpsbuddyApi::getInstance();
      $gpsbuddy->init();
      foreach (GpsbuddyTrucks::getActiveTrucks() as $tr) {
        $gpsbuddy->addTruck($tr->truckId, $tr->gpsbuddyId);
      }
      if ($gpsbuddy->login()) {
        //      $gpsbuddy->getFunctionlist();
        pd($gpsbuddy->getCurrenttruckLocations(false));

        //$taskId = $gpsbuddy->addTask(3, "Onderwerp 1", "Tekst 1", 5.22571, 51.349);
        //$statusId = $gpsbuddy->getTaskStatus(926867);
        //      echo $gpsbuddy->addTask(2, "Ambiani", "Alaaf !", 5.21524, 51.3655057);
      }
    }

    public function renameStones() {

      $colors = AppModel::mapObjectIds(StoneColors::find_all(), 'colorId');
      $sizes = AppModel::mapObjectIds(StoneSizes::find_all(), 'sizeId');

      foreach (Stones::find_all() as $stone) {
        $newname = '';
        if ($stone->brandId == StoneBrands::BRAND_ID_STJORIS) {
          $newname .= "StJoris ";
        }
        else {
          $newname .= "Wienerberger ";
        }

        if (isset($colors[$stone->colorId])) {
          $newname .= $colors[$stone->colorId]->name . " ";
        }
        else {
          pd("Onbekende kleur: " . $stone->colorId);
        }

        if (isset($sizes[$stone->sizeId])) {
          $newname .= $sizes[$stone->sizeId]->name . " ";
        }
        else {
          pd("Onbekende kleur: " . $stone->sizeId);
        }

        if ($stone->endstone == "left") {
          $newname .= "Links";
        }
        elseif ($stone->endstone == "right") {
          $newname .= "Rechts";
        }
        elseif ($stone->endstone == "leftg" || $stone->endstone == "rightg") {
          $newname .= "Groef";
        }

        $stone->name = trim($newname);
        $stone->save();

      }

    }

    public function buildCargoQuotations() {


      foreach (CargoReceipt::find_all("WHERE marksAndNums!=''") as $cr) {
        pd("[" . $cr->cargoReceiptId . "]");
        $rows = explode("\n", $cr->marksAndNums);
        foreach ($rows as $row) {
          $nr = substr($row, 0, 8);
          $nr_full = explode(" - ", $row);
          $nr_full = $nr_full[0];
          //pd($cr->marksAndNums);
          pd($nr_full);

          $sql = "SELECT * FROM " . Quotations::getTablename() . "  ";
          $sql .= "WHERE quotationNumber='" . $nr . "' ";
          $oResult4 = DBConn::db_link()->query($sql);
          $quotations = [];
          while ($l_quotation = $oResult4->fetch_object()) {
            $name = $l_quotation->getQuotationNumberFull();
            if ($nr_full == $name) {
              pd("Gevonden");
              $crq = new CargoReceiptQuotation();
              $crq->cargoReceiptId = $cr->cargoReceiptId;
              $crq->quotationId = $l_quotation->quotationId;
              $crq->save();
              break;
            }
          }

        }

      }

    }

    private function bretiQuotationsToInvoices() {

      $filt = "SELECT * FROM " . Quotations::getTablename() . " ";
      $filt .= "JOIN " . QuotationsExtra::getTablename() . " ON quotations_extra.quotationId=quotations.quotationId  ";
      $filt .= "WHERE NOT supplier_show IS NULL AND supplier_show!='' ";
      $filt .= "AND brandId IN (" . implode(",", StoneBrands::getBretiBrandIds()) . ") ";
      $filt .= "AND statusId>=" . Status::STATUS_PRODUCED . " AND statusId<" . Status::STATUS_SPLIT . " ";

      $filt .= "ORDER BY produceDate DESC ";


      $result = DBConn::db_link()->query($filt);

      $quotations = [];
      while ($row = $result->fetch_row()) {
        $quotation = new Quotations();
        $quotation->hydrate($row);

        $breti_invoice = new BretiInvoice();

        $breti_invoice->breti_invoice_number = $last_invoice_number += 1;
        $breti_invoice->breti_invoice_date = $quotation->quotationDate;
        $breti_invoice->insertTS = date("Y-m-d H:i:s");
        $breti_invoice->save();

        $breti_quotation = new BretiQuotation();

        $breti_quotation->quotation_id = $quotation->quotationId;
        $breti_quotation->breti_invoice_id = $breti_invoice->id;
        $breti_quotation->save();
      }
    }

    public function sendDeliveredMail() {
//      $tos, $cargo_receipt_id, $quotationIds

      $tos = [];
      $tos['<EMAIL>'] = '<EMAIL>';
      $tos['<EMAIL>'] = '<EMAIL>';

      $cargoReceiptId = 27111;

      $quotations = Quotations::find_all_by(['companyId' => 21896, 'statusId' => 53]);
      $quotationIds = [];
      foreach ($quotations as $quot) {
        $quotationIds[$quot->quotationId] = $quot->quotationId;
      }

      MailsFactory::sendDeliveredEmail($tos, $cargoReceiptId, $quotationIds);
    }

    public function sendPickupMail() {
      $tos = [];
      $tos['<EMAIL>'] = '<EMAIL>';
      $tos['<EMAIL>'] = '<EMAIL>';

      $cargoReceiptId = 27111;

      $quotations = Quotations::find_all_by(['companyId' => 21896, 'statusId' => 53]);
      $quotationIds = [];
      foreach ($quotations as $quot) {
        $quotationIds[$quot->quotationId] = $quot->quotationId;
      }

      MailsFactory::sendPickupEmail($tos, $cargoReceiptId, $quotationIds);
    }

  }