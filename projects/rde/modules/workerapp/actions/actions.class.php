<?php

  class workerappRdeActions extends workerappActions {

    /**
     * Overzicht van planning
     * @return void
     */
    public function executePlanning() {

      //omdat we de time doorsturen naar android, even met UTC tijden werken.
      //in toekomst tijd doorsturen.
      date_default_timezone_set("UTC");

      $inputdata = $this->data->getData();

      $workerscreen = WorkerScreen::find_by(['id' => 4]);
      if (!$workerscreen) {
        RestUtils::sendResponseError("Scherm niet gevonden: " . $inputdata["screenid"]);
      }

      if (!DEVELOPMENT && !in_array(IpHelper::getIpAdress(), $workerscreen->getIps())) {
        RestUtils::sendResponseError("U heeft geen toegang tot dit scherm: " . $inputdata["screenid"]);
      }

      $startimeMonday = DateTimeHelper::getFirstOfWeekDate(null, 'U');
      if (isset($inputdata["weeknr"])) {
        $year = substr($inputdata["weeknr"], 0, 4);
        $weeknr = intval(substr($inputdata["weeknr"], 4));
        $dto = new DateTime();
        $dto->setISODate($year, $weeknr);
        $startimeMonday = $dto->format('U');
      }

      $workers = Worker::getWorkersForScreen($workerscreen->id);
      //alleen werknemers met planning op aan
      foreach ($workers as $k => $worker) {
        if ($worker->planning != 1 || $worker->disabled == 1) unset($workers[$k]);
      }
      $worker_ids = [];
      foreach ($workers as $worker) {
        $worker_ids[$worker->id] = $worker->id;
      }

      $workersplanning = WorkerPlanhour::getPlanningForWeek($startimeMonday);

      foreach ($workers as $k => $worker) {
        $worker = AppModel::plainObject($worker);
        $workers[$k] = $worker;

        $worker->planning = [];
        if (isset($workersplanning[$worker->id])) {
          foreach ($workersplanning[$worker->id] as $time => $hours) {
            $plan = new stdClass();
            $plan->time = $time * 1000; //java time
            $plan->date = date("Y-m-d H:i:s", $time);
            $plan->hours = floatval($hours);
            $plan->weeknr = date("W", $time);
            $worker->planhours[] = $plan;


          }

        }

      }

      RestUtils::sendResponseOK("WERKNEMERS OPGEHAALD", $workers);

    }

    /**
     * In of uitklokken door medewerker
     * @return void
     */
    public function executeSendWorkerRegisters() {

      $request_vars = $this->data->getData();
      $registers = RestUtils::getPostvaluesByKey("json", $request_vars);

      //logToFile('workers', print_r($registers, true));
      $workersCheckedInProductionLane = [];

      foreach ($registers as $register) {

        $worker = Worker::find_by_id($register->workerId);
        if (!$worker) continue;

        $time = strtotime($register->time);
        $hour = new WorkerHours();
        if ($register->checkin == "1") { //checkin

          if ($worker->ischeckedin == 1) {
            continue; //is al inchecked
          }

          $lasthour = WorkerHours::find_by(['worker_id' => $worker->id], " ORDER BY worker_hours.in DESC LIMIT 1");
          if ($lasthour && strtotime($lasthour->out . ' +2 MINUTES') > $time) {
            //binnen 2 minuten weer ingeklokt. Deze tijd word niet geteld.
            $lasthour->out = null;
            $lasthour->minutes = 0;
            $lasthour->save();
            $worker->ischeckedin = 1;
            $worker->save();
            // $worker->firstname.' is binnen 2 minuten weer ingeklokt. Tijd is niet geregistreerd.';
          }
          else {
            //inklokken maar
            $hour->worker_id = $worker->id;
            $hour->in = date('Y-m-d H:i', $time);
            $hour->save();
            $worker->ischeckedin = 1;
            $worker->save();
          }

        }
        else { //checkout

          if ($worker->ischeckedin == 0) {
            continue; //al uitgeklokt....
          }

          $hour = WorkerHours::find_by(['worker_id' => $worker->id], " AND (worker_hours.out='' OR worker_hours.out IS NULL)");
          if (!$hour) {
            continue; //Je probeert uit te klokken, maar je bent nog niet ingeklokt. overslaan
          }

          $hour->out = date('Y-m-d H:i', $time);
          //        $hour->out = '2011-08-12 17:00';
          if (strtotime($hour->out . ' -2 MINUTES') < strtotime($hour->in)) {
            //binnen 2 minuten uitgeklokt
            $hour->destroy();
            $worker->ischeckedin = 0;
            $worker->save();
            //$worker->firstname.' is binnen 2 minuten weer uitgeklokt. Tijd is niet geregistreerd.';
          }
          elseif (strtotime($hour->out . ' -1 DAY') > strtotime($hour->in)) {
            //meer als 24 uur gewerkt. Afblokken op 24 uur, waarschijnlijk vergeten uit te klokken.
            $hour->out = date('Y-m-d H:i', strtotime($hour->in . " +1 DAYS"));
            $hour->minutes = (strtotime($hour->out) - strtotime($hour->in)) / 60;
            $hour->save();
            $worker->ischeckedin = 0;
            $worker->save();
          }
          else {
            //uitklokken maar
            $hour->minutes = (strtotime($hour->out) - strtotime($hour->in)) / 60;
            $hour->save();
            $worker->ischeckedin = 0;
            $worker->save();

          }

          //een checkout kan ook pauzes bevatten....
          if (!empty($register->pauze)) {
            //splits de geregistreerde uren in 2.
            $hour_second = new WorkerHours();
            $hour_second->worker_id = $worker->id;
            $minutes_first = ceil($hour->minutes / 2);
            $start_second = strtotime($hour->in . " + " . ($minutes_first + $register->pauze) . " MINUTES");
            $hour_second->in = date('Y-m-d H:i', $start_second);
            $hour_second->out = $hour->out;
            $hour_second->minutes = (strtotime($hour_second->out) - strtotime($hour_second->in)) / 60;
            $hour_second->save();

            $hour->out = date('Y-m-d H:i', strtotime($hour->in . " + " . $minutes_first . " MINUTES"));
            $hour->minutes = $minutes_first;
            $hour->save();
          }


          if (intval(date("Hi")) >= 1630 && intval(date("Hi")) <= 1900) { // tussen 16:30 en 19:00
            $eq = EmployeeQuotation::find_by(["employeeId" => $worker->external_id], "AND DATE(startdate)='" . date("Y-m-d") . "' AND enddate IS NULL");
            if (!empty($eq)) {
              $workersCheckedInProductionLane[$worker->id] = $worker;
            }
          }

        }

      }

      $message = "WERKNEMERS OPGEHAALD";
      if (!empty($workersCheckedInProductionLane)) {
        $workerNames = [];
        foreach ($workersCheckedInProductionLane as $wne) {
          $workerNames[] = $wne->getNaam();
        }
        $message = "De volgende werknemers moeten de productie nog afsluiten:\n" . implode("\n", $workerNames);
      }

      RestUtils::sendResponseOK($message, [count($registers)]);

    }


  }

