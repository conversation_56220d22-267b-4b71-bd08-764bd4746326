<?php TemplateHelper::includePartial("_tabs.php", 'invoiceadmin') ?>

<?php if (isset($company) && $company): ?>
  <h3>Facturen van <?php echo $company->name ?> <a href="<?php echo reconstructQuery(['organid', 'pageNum']) ?>"><?php echo IconHelper::getCross() ?></a></h3>
<?php endif; ?>

<form method="post">
  <div class="box">
    <input type="text" name="i_search" id="i_search" value="<?php echo $_SESSION['i_search'] ?>" style="width: 200px;" placeholder="Zoeken...">
    <select name="i_year" id="i_year">
      <option value="">Filter op jaar...</option>
      <?php for($year=intval(date("Y"))+1;$year>=2011;$year--): ?>
        <option value="<?php echo $year ?>" <?php writeIfSelectedVal($_SESSION['i_year'], $year) ?>><?php echo $year ?></option>
      <?php endfor; ?>
    </select>
    <select name="i_status" id="i_status">
      <option value="">Status...</option>
      <?php foreach($stati as $status): ?>
        <option value="<?php echo $status->statusId ?>" <?php writeIfSelectedVal($_SESSION['i_status'], $status->statusId) ?>><?php echo $status->bedrijvengidsName ?></option>
      <?php endforeach; ?>
    </select>
    &nbsp;Facturatiedatum van
    <?php echo getDateSelector("i_invoice_from", $_SESSION["i_invoice_from"], false, "", "Van") ?>
    Tot
    <?php echo getDateSelector("i_invoice_to", $_SESSION["i_invoice_to"], false, "", "Tot") ?>
<!--    <label>-->
<!--      Betaald-->
<!--      <input type="checkbox" name="i_paid" id="i_paid" value="--><?php //echo $_SESSION['i_paid'] ?><!--">-->
<!--    </label>-->
    <label><input type="checkbox" value="1" name="i_paid" <?php if($_SESSION['i_paid']==1) echo 'checked'; ?>/> Betaald</label>
    <input type="submit" value="Zoeken" name="go" id="go"/>
  </div>

  <?php if (count($invoices) == 0): ?>
    <br/>
    Geen facturen gevonden.
  <?php else: ?>

    <?php $pager->writePreviousNext(); ?>

    <table class="default_table default_table_center" id="invoice_list_table">
      <tr class="dataTableHeadingRow">
        <td>Factuurnummer</td>
        <td>Bedrijfsnaam</td>
        <td>Bedrag</td>
        <td>Dagen</td>
        <td>Factuurdatum</td>
        <td>Betaaldatum</td>
        <td>Aanmaning 1</td>
        <td>Aanmaning 2</td>
        <td>Aanmaning 3</td>
        <td>Opmerking</td>
        <td>Pdf</td>
        <td>Actie</td>
      </tr>
      <?php
        /** @var Invoices $invoice */
        foreach ($invoices as $invoice): ?>
          <tr class="dataTableRow trhover">
            <td><?php echo $invoice->invoiceNumber ?></td>
            <td>
              <?php if ($invoice->user->company): ?>
                <a href="?organid=<?php echo $invoice->user->companyId ?>">
                  <?php echo $invoice->user->company->name ?>
                </a>
              <?php endif; ?>
            </td>
            <td style="text-align: right">€ <?php echo getLocalePrice($invoice->totalProjectValue) ?></td>
            <td style="text-align: right"><?php echo $invoice->getInvoicedateDays() ?> / <?php if ($invoice->user->company) echo $invoice->user->company->paymentTerm ?></td>
            <td><?php echo $invoice->getDateInvoice() ?></td>
            <td><?php echo $invoice->getPaid() ?></td>
            <td><?php echo $invoice->getReminder1() ?></td>
            <td><?php echo $invoice->getReminder2() ?></td>
            <td><?php echo $invoice->getReminder3() ?></td>
            <td>
              <?php if (!empty(trim($invoice->invoiceNotes))): ?>
                <img src="/gsdfw/images/note.png" class="qtipa" data-caption="Opmerking" title="<?php echo escapeForJS(nl2br($invoice->invoiceNotes)) ?>"/>
              <?php endif; ?>
            </td>
            <td>
              <?php echo BtnHelper::getPrintPDF("?action=createinvoicepdf&id=" . $invoice->invoiceId) ?>
            </td>
            <td>
              <?php echo BtnHelper::getEdit('?action=invoiceedit&id=' . $invoice->invoiceId) ?>
            </td>
          </tr>
        <?php endforeach; ?>
    </table>
  <?php endif; ?>
</form>

