<?php TemplateHelper::includePartial("_tabs.php", 'invoiceadmin') ?>

<?php
  /** @var Invoices $invoice */
?>

<h3>Bewerk factuur <?php echo $invoice->invoiceNumber ?></h3>

<?php writeErrors($form->getErrors(), true); ?>

<form method="post" class="edit-form">
  <table class="default_table">
    <tr class="dataTableHeadingRow">
      <td>Item</td>
      <td>Instelling</td>
    </tr>
    <tr class="dataTableRow trhover">
      <td>Bedrijfsnaam</td>
      <td><?php echo $iuser->company->name ?></td>
    </tr>
    <tr class="dataTableRow trhover">
      <td>Factuurnummer</td>
      <td><?php echo $invoice->invoiceNumber ?></td>
    </tr>
    <tr class="dataTableRow trhover">
      <td><?php echo $form->getElement("irrecoverable")->getLabel(); ?></td>
      <td><?php $form->getElement("irrecoverable")->render(); ?></td>
    </tr>
    <tr class="dataTableRow trhover">
      <td>Factuurdatum</td>
      <td><?php echo $invoice->getDateInvoice() ?></td>
    </tr>
    <tr class="dataTableRow trhover">
      <td><?php echo $form->getElement("reminder1")->getLabel(); ?></td>
      <td>
        <?php $form->getElement("reminder1")->render(); ?>
      </td>
    </tr>
    <tr class="dataTableRow trhover">
      <td><?php echo $form->getElement("reminder2")->getLabel(); ?></td>
      <td><?php $form->getElement("reminder2")->render(); ?></td>
    </tr>
    <tr class="dataTableRow trhover">
      <td><?php echo $form->getElement("reminder3")->getLabel(); ?></td>
      <td><?php $form->getElement("reminder3")->render(); ?></td>
    </tr>
    <tr class="dataTableRow trhover">
      <td><?php echo $form->getElement("paid")->getLabel(); ?></td>
      <td><?php $form->getElement("paid")->render(); ?></td>
    </tr>
    <tr class="dataTableRow trhover">
      <td><?php echo $form->getElement("invoiceNotes")->getLabel(); ?></td>
      <td><?php $form->getElement("invoiceNotes")->render(); ?></td>
    </tr>
    <tr class="dataTableRow trhover">
      <td>Gemiddelde betaal termijn</td>
      <td>
        <?php $pay = $invoice->averageDaysOpenOfCompany($iuser->company->companyId); ?>
        <?php if($pay["count"]>0): ?>
          <?php echo $pay["days"] ?>
          dagen
          op <?php echo $pay["count"] ?> facturen
        <?php else: ?>
          Er zijn nog geen facturen verzonden naar deze klant.
        <?php endif; ?>
      </td>
    </tr>
    <tr class="dataTableRow trhover">
      <td>
        Uitvoerdersbon
        <?php echo showHelpButton("Als dit vinkje gezet is, moet er een uitvoerdersbon zijn geupload bij de factuurbestanden.") ?>
      </td>
      <td>
        <?php echo $hasExecutorTicket?"Ja":"Nee" ?>
      </td>
    </tr>
    <tr class="dataTableRow trhover">
      <td>Bestanden</td>
      <td>
        <?php if(empty($files)): ?>
          Geen bestanden gekoppeld
        <?php else: ?>
          <?php foreach($files as  $file): ?>
            <?php echo BtnHelper::getPrintPDF("?action=docopen&id=" . $file->fileId, $file->title) ?>
          <?php endforeach; ?>
        <?php endif; ?>
      </td>
    </tr>

    <?php if($showalert): ?>
      <tr class="dataTableRow trhover">
        <td>Notities melding</td>
        <td>
          <?php $form->getElement("invoiceNotesAlert")->render(); ?>
          - zet dit vinkje uit als u het bericht hebt gelezen.
        </td>
      </tr>
    <?php endif; ?>

  </table>

  <input type="submit" name="go" value="Opslaan" class="gsd-btn gsd-btn-primary"/>
  <input type="submit" name="go_list" value="Opslaan en naar lijst" class="gsd-btn gsd-btn-secondary"/>

</form>