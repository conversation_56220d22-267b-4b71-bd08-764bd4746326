<?php TemplateHelper::includePartial("_tabs.php", 'invoiceadmin') ?>
  <div class="box">
    <form method="post">
      <input type="text" name="i_search" id="i_search" value="<?php echo $_SESSION['i_search'] ?>" style="width: 200px;" placeholder="Zoeken...">
      <select name="i_year" id="i_year">
        <option value="">Filter op jaar...</option>
        <?php for ($year = intval(date("Y")) + 1; $year >= 2011; $year--): ?>
          <option value="<?php echo $year ?>" <?php writeIfSelectedVal($_SESSION['i_year'], $year) ?>><?php echo $year ?></option>
        <?php endfor; ?>
      </select>
      &nbsp;Facturatiedatum van
      <?php echo getDateSelector("i_invoice_from", $_SESSION["i_invoice_from"], false, "", "Van") ?>
      Tot
      <?php echo getDateSelector("i_invoice_to", $_SESSION["i_invoice_to"], false, "", "Tot") ?>
      <input type="submit" value="Zoeken" name="go" id="go"/>
    </form>
  </div>
<?php if (count($invoices) == 0): ?>
  <br/>
  Geen facturen gevonden.
<?php else: ?>

  <table class="default_table default_table_center" id="invoice_list_table">
    <tr class="dataTableHeadingRow">
      <td>Factuurnummer</td>
      <td>Bedrijfsnaam</td>
      <td>Bedrag</td>
      <td>Dagen</td>
      <td>Factuurdatum</td>
      <td>Betaaldatum</td>
      <td>Aanmaning 1</td>
      <td>Aanmaning 2</td>
      <td>Aanmaning 3</td>
      <td>Opmerking</td>
      <td>Alert</td>
      <td>Reden</td>
      <td>Pdf</td>
      <td>Actie</td>
    </tr>
    <?php
      /** @var Invoices $invoice */
      foreach ($invoices as $invoice): ?>
        <tr class="dataTableRow trhover">
          <td><?php echo $invoice->invoiceNumber ?></td>
          <td><?php if ($invoice->user->company) echo $invoice->user->company->name ?></td>
          <td style="text-align: right">€ <?php echo getLocalePrice($invoice->totalProjectValue) ?></td>
          <td><?php echo $invoice->getInvoicedateDays() ?>/<?php if ($invoice->user->company) echo $invoice->user->company->paymentTerm ?></td>
          <td><?php echo $invoice->getDateInvoice() ?></td>
          <td><?php echo $invoice->getPaid() ?></td>
          <td><?php echo $invoice->getReminder1() ?></td>
          <td><?php echo $invoice->getReminder2() ?></td>
          <td><?php echo $invoice->getReminder3() ?></td>
          <td>
            <?php if ($invoice->invoiceNotes != ""): ?>
              <img src="/gsdfw/images/note.png" class="qtipa" data-caption="Opmerking" title="<?php echo escapeForJS(nl2br($invoice->invoiceNotes)) ?>"/>
            <?php endif; ?>
          </td>
          <td>
            <?php if ($invoice->invoiceNotesAlert == 1): ?>
              <i class="material-icons" style="color: #982e1a; font-size: 22px;" title="Melding">error</i>
            <?php endif; ?>
          </td>
          <td><?php echo $irrecoverable[$invoice->irrecoverable]->name ?></td>
          <td>
            <?php echo BtnHelper::getPrintPDF("?action=pdf&id=" . $invoice->invoiceId) ?>
          </td>
          <td>
            <?php echo BtnHelper::getEdit('?action=invoiceedit&id=' . $invoice->invoiceId . '&showalert=1') ?>
          </td>
        </tr>
      <?php endforeach; ?>
  </table>
<?php endif; ?>