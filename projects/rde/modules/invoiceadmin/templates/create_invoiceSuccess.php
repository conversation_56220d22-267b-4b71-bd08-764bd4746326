<?php TemplateHelper::includePartial("_tabs.php",'invoiceadmin') ?>

<section class="module-description">
  <p>Op deze pagina kun je facturen aanmaken.</p>
</section>

<?php foreach ($users as $key => $user): ?>

  <div>
    <form method="post" id="create_invoice_form">
    <h3 class="user_title" id="user_title_<?php echo $key ?>">
      <?php echo $user->name ?>
      <?php echo BtnHelper::getOpenPage(PageMap::getUrl("M_BEDRIJVENGIDS_EDIT") . '?companyid=' . $user->companyId) ?>
      <?php if ($user->orders_separate) echo showHelpButton("Deze orders moeten apart worden gefactureerd","Let op!"); ?>
    </h3>
    <table class="default_table default_table_center quotation_table visible" id="quotation_table_<?php echo $key ?>">
      <tr class="dataTableHeadingRow">
        <td><input name="check_all_<?php echo $key ?>" type="checkbox" id="check_all_<?php echo $key ?>">Status</td>
        <td>Offerte nummer</td>
        <td>Datum</td>
        <td>Meters</td>
        <td>Plaats</td>
        <td>Bak nummer</td>
        <td>Uitvoerdersbon</td>
        <td>Betaald</td>
      </tr>
      <?php foreach ($user->quotations as $nr => $quotation): ?>
      <tr class="dataTableRow trhover">
        <td class="status_row"><input id="add_quotation_<?php echo $nr ?>" name="add_quotation_<?php echo $nr ?>" type="checkbox" value="<?php echo $quotation->quotationId ?>"> <?php echo $quotation->getIconHTML() ?> </td>
        <td>
          <a href="<?php echo PageMap::getUrl("M_RDE_ORDERS_GENERAL") . '?id=' . $quotation->quotationId ?>" target="_blank"><?php echo $quotation->getQuotationNumberFull() ?></a>
        </td>
        <td><?php echo $quotation->quotationDate ?></td>
        <td><?php echo $quotation->meters ?></td>
        <td><?php echo $quotation->domestic ?></td>
        <td><?php echo $quotation->container ?></td>
        <td><?php echo $quotation->has_executor_ticket==1?'Ja':'' ?></td>
        <td><?php echo $quotation->payedFlag==1?'Ja':'' ?></td>
      </tr>
      <?php if (isset($quotation->invoice_notes)): ?>
      <tr class="dataTableRow invoice_note" style="">
        <td colspan="8">Let op! Factuur melding: <?php echo $quotation->invoice_notes ?></td>
      </tr>
      <?php endif; ?>
      <?php endforeach; ?>
      <?php if ($user->invoice_party_emailConfirmed != 1): ?>
      <tr class="confirm_email">
        <td colspan="6">Let op: <?php echo $user->invoice_party_email ?> is nog niet bevestigd!</td>
        <td><input type="submit" name="confirm_invoice_email" value="Stuur bevestigingsmail"></td>
      </tr>
      <?php endif; ?>
      <tr>
        <td style="padding: 5px"><button type="submit" name="create_invoice" class="gsd-btn gsd-btn-primary">Opslaan</button></td>
      </tr>
    </table>
    </form>
  </div>

  <script>

    $(document).ready(function () {

      let checked = false;
      $('#quotation_table_<?php echo $key ?> input').each(function () {
        if ($(this).is(':checked')) {
          checked = true;
        }
      });
      if (checked) {
        $('#quotation_table_<?php echo $key ?>').toggleClass("visible");
      }

      $("#user_title_<?php echo $key ?>").on('click', function (event) {
        $('#quotation_table_<?php echo $key ?>').toggleClass("visible");
      });

      $('#check_all_<?php echo $key ?>').change(function () {

        var checkboxes = $(this).closest('form').find(':checkbox').not('#check_all_<?php echo $key ?>');
        checkboxes.prop('checked', this.checked);

      });
    });
  </script>

<?php endforeach; ?>


<style>
  .quotation_table tr.dataTableHeadingRow td {
    width: 100px;
  }

  .quotation_table .dataTableRow td {
    padding: 10px 5px;
  }

  .visible {
    display: none;
  }

  .user_title {
    padding: 10px;
    background-color: #EAEBEC;
    margin-bottom: 0;
  }

  .user_title:hover {
    background-color: #dddfe1;
    border-radius: 5px;
    cursor: pointer;
  }

  .status_row {
    display: flex;
  }

  .invoice_note {
    width: 100%;
    background-color: red;
    color: white;
  }

  .confirm_email {
    background-color: yellow
  }

  .gsd-svg-icon svg {
    color: #c22a21;
  }
</style>



