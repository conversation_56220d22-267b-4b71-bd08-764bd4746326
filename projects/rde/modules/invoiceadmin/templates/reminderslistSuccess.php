<?php TemplateHelper::includePartial("_tabs.php", 'invoiceadmin') ?>

<h2>Aanmaning 1</h2>
<form method="post" id="send_reminders1_form">
  <input type="hidden" name="invoice_ids" value="">
  <input type="hidden" name="reminder1" value="1">
  <input type="submit" id="send_reminders1" class="gsd-btn gsd-btn-primary"  value="<?php echo __('Verzend 1ste aanmaningen') ?>" name="send_reminders1" style="margin-bottom: 15px">
</form>
<?php $dataTable->render(); ?>
<h2>Aanmaning 2</h2>
<form method="post" id="send_reminders2_form">
  <input type="hidden" name="invoice_ids2" value="">
  <input type="hidden" name="reminder2" value="2">
  <input type="submit" id="send_reminders2" class="gsd-btn gsd-btn-primary"  value="<?php echo __('Verzend 2e aanmaningen') ?>" name="send_reminders2" style="margin-bottom: 15px">
</form>
<?php $dataTable2->render(); ?>
<h2>Aanmaning 3</h2>
<form method="post" id="send_reminders3_form">
  <input type="hidden" name="invoice_ids3" value="">
  <input type="hidden" name="reminder3" value="3">
  <input type="submit" id="send_reminders3" class="gsd-btn gsd-btn-primary"  value="<?php echo __('Verzend 3de aanmaningen') ?>" name="send_reminders3" style="margin-bottom: 15px">
</form>
<?php $dataTable3->render(); ?>

<script type="text/javascript">

  $(document).ready(function () {
    <?php echo $dataTable->renderJSconfig() ?>
    <?php echo $dataTable2->renderJSconfig() ?>
    <?php echo $dataTable3->renderJSconfig() ?>

    $(document).on('click', '#send_reminders1', function(event) {
      $('input[name="invoice_ids"]').val(<?php echo $dataTable->getTableVariableName()  ?>.rows().ids().join());
    });

    $(document).on('click', '#send_reminders2', function(event) {
      $('input[name="invoice_ids2"]').val(<?php echo $dataTable2->getTableVariableName()  ?>.rows().ids().join());
    });

    $(document).on('click', '#send_reminders3', function(event) {
      $('input[name="invoice_ids3"]').val(<?php echo $dataTable3->getTableVariableName()  ?>.rows().ids().join());
    });
  });

</script>

<style>
  h2 {
    color: #982e1a;
  }
</style>