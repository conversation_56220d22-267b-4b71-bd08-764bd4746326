<?php TemplateHelper::includePartial("_tabs.php",'invoiceadmin') ?>
<br/>Hieronder ziet u een overzicht van de facturen en bestellingen betaald via mollie, gegroepeerd per dag.<br/>
<br/>

  <div class="box">
    <form method="post">
      <input type="text" name="i_search" id="i_search" value="<?php echo $_SESSION['i_search'] ?>" style="width: 200px;" placeholder="Zoeken...">
      <select name="i_year" id="i_year">
        <option value="">Filter op jaar...</option>
        <?php for($year=intval(date("Y"))+1;$year>=2011;$year--): ?>
          <option value="<?php echo $year ?>" <?php writeIfSelectedVal($_SESSION['i_year'], $year) ?>><?php echo $year ?></option>
        <?php endfor; ?>
      </select>
      &nbsp;Betaaldatum van
      <?php echo getDateSelector("i_invoice_from",$_SESSION["i_invoice_from"],false, "","Van") ?>
      Tot
      <?php echo getDateSelector("i_invoice_to",$_SESSION["i_invoice_to"],false, "","Tot") ?>
      <input type="submit" value="Zoeken" name="go" id="go"/>
    </form>
  </div>

<?php if(count($days)==0): ?>
  <br/>
  Geen betalingen gevonden.
<?php else: ?>
  <table class="default_table default_table_center" id="invoice_list_table">
    <tr class="dataTableHeadingRow">
      <td>Bedrijfsnaam</td>
      <td>Betaald online op</td>
      <td style="text-align: right;">Bedrag</td>
      <td>Factuurnummer</td>
      <td>Factuurdatum</td>
      <td>Factuur pdf</td>
      <td>Order info</td>
    </tr>
    <?php foreach($days as $datumst=>$mollies):
      $first_mollie = false;
      $total = 0;
      foreach($mollies as $mollie):
        $first_mollie = $mollie;
        break;
      endforeach;
      ?>
      <tr class="dataTableHeadingRow">
        <td colspan="7"><?php echo $first_mollie->getInsertTS("d-m-Y") ?> - TOTAAL € <?php echo getLocalePrice($day_totals[$datumst]) ?></td>
      </tr>
      <?php foreach($mollies as $mollie):
        $total += $mollie->amount;
        ?>
        <?php if(count($mollie->invoices)>0): ?>
          <?php foreach($mollie->invoices as $invoice):
            if(isset($mollies[$invoice->mollie_id])) { //mollie afgerekend vanuit factuur
              $mollie = $mollies[$invoice->mollie_id];
            }
            else {
              //mollie afgerekend vanuit quotation(s)
              foreach($invoice->quotations as $quotation) {
                if(isset($mollies[$quotation->mollie_id])) {
                  $mollie = $mollies[$quotation->mollie_id];
                  break;
                }
              }
            }
            ?>
            <tr class="dataTableRow trhover">
              <td><?php if($invoice->user->company) echo $invoice->user->company->name ?></td>
              <td><?php echo $mollie->getInsertTS() ?></td>
              <td style="text-align: right">€ <?php echo getLocalePrice($invoice->totalProjectValue) ?>

                <?php
  //                $som = 0;
  //                foreach($invoice->quotations as $quotation) {
  //                  echo '['.$quotation->quotationId.' ';
  //                  $t = $quotation->getTotalPrice();
  //                  echo ' ('.getLocalePrice($quotation->freightCosts).') ';
  //                  echo ' ('.getLocalePrice($quotation->specialFreightCost).') ';
  //                  echo getLocalePrice($t).']';
  //                  $som += $t;
  //                }
  //                echo getLocalePrice($som);
  //                if(getLocalePrice($som)!=getLocalePrice($invoice->totalProjectValue)) echo ' <span style="color: red;">'.getLocalePrice($invoice->totalProjectValue-$som).'</span> ';
  //                echo ' ('.getLocalePrice($invoice->freightCosts).') ';
  //
  //                if($invoice->mollie_id!="") echo ' {'.getLocalePrice($mollies[$invoice->mollie_id]->amount).'} ';
                ?>
              </td>
              <td><?php echo $invoice->invoiceNumber ?></td>
              <td><?php echo $invoice->getDateInvoice() ?></td>
              <td>
                <?php echo BtnHelper::getPrintPDF("?action=pdf&id=".$invoice->invoiceId) ?>
              </td>
              <td>
                <?php foreach ($invoice->quotations as $quotation): ?>
                  <a target="_blank" href="?action=quotationpdf&id=<?php echo $quotation->quotationId ?>" title="Bekijk de PDF van deze bestelling"><?php echo $quotation->quotationNumber ?></a>
                  <br/>
                <?php endforeach; ?>
              </td>
            </tr>
          <?php endforeach; ?>
        <?php endif; ?>
        <?php if(count($mollie->quotations)): ?>
          <?php foreach($mollie->quotations as $quotation): ?>
              <tr class="dataTableRow trhover">
                <td>
                  <?php
                    if($quotation->user->company) echo $quotation->user->company->name;
                    if($quotation->user && $quotation->user->getNaam()!="") echo " - ".$quotation->user->getNaam();
                  ?>
                </td>
                <td><?php echo $mollies[$quotation->mollie_id]->getInsertTS() ?></td>
                <td style="text-align: right">€ <?php echo getLocalePrice($mollies[$quotation->mollie_id]->amount); ?></td>
                <td></td>
                <td></td>
                <td></td>
                <td>
                  <a target="_blank" href="?action=quotationpdf&id=<?php echo $quotation->quotationId ?>" title="Bekijk de PDF van deze bestelling" ><?php echo $quotation->quotationNumber ?></a>
                  - <?php echo $quotation->getProductionDate() ?>
                </td>
              </tr>
          <?php endforeach; ?>
        <?php endif; ?>
      <?php endforeach; ?>
      <?php if($total!=$day_totals[$datumst]): ?>
        <tr class="dataTableRow">
          <td colspan="7" style="color: red;">Totaal bedrag facturen/quotations komt niet overeen met overgemaakt bedrag via mollie. Mollie: € <?php echo getLocalePrice($total) ?> != totaal € <?php echo getLocalePrice($day_totals[$datumst]) ?></td>
        </tr>
    <?php endif; ?>
    <?php endforeach; ?>
  </table>
<?php endif; ?>