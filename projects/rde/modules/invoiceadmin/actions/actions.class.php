<?php

  use domain\multivers\service\InvoiceSave;
  use Gsd\DataTable\DataTable;
  use Gsd\Form\Elements\Select;
  use Gsd\Form\Elements\Submit;
  use Gsd\Form\ModelForm;
  use gsdfw\domain\multivers\exception\MultiversException;
  use gsdfw\domain\multivers\service\MultiversApi;

  class invoiceadminRdeActions extends gsdActions {

    public function preExecute() {
      if (!isset($_SESSION['invoice_not_send'])) {
        //ophalen dat aantal als sessie leeg is. $_SESSION["invoice_not_send"] = 0;
        $filter_query = "LEFT JOIN " . SandboxUsers::getTablename() . " ON sandbox_users.userId = invoices.userId ";
        $filter_query .= "LEFT JOIN " . CrmCompanies::getTablename() . " ON sandbox_users.companyId = crm_companies.companyId ";
        $filter_query .= "WHERE invoices.paid IS NULL AND crm_companies.executorTicket = 1 AND invoices.totalProjectValue > 0 AND invoices.send_multivers = 0 AND invoices.dateInvoice IS NULL ";
        $query = "SELECT COUNT(*) AS count FROM " . Invoices::getTablename() . " ";
        $query .= $filter_query;

        $result = DBConn::db_link()->query($query);
        $row = $result->fetch_assoc();
        $_SESSION['invoice_not_send'] = $row['count'];
      }
    }

    public function executeInvoicelist() {

      if (!isset($_SESSION['i_search'])) $_SESSION['i_search'] = '';
      if (!isset($_SESSION['i_paid'])) $_SESSION['i_paid'] = 0;
      if (!isset($_SESSION['i_year'])) $_SESSION['i_year'] = "";
      if (!isset($_SESSION['i_status'])) $_SESSION['i_status'] = "";
      if (!isset($_SESSION['i_invoice_from'])) $_SESSION['i_invoice_from'] = "";
      if (!isset($_SESSION['i_invoice_to'])) $_SESSION['i_invoice_to'] = "";
      if (!isset($_SESSION['i_rows_per_page'])) $_SESSION['i_rows_per_page'] = 30;
      if (isset($_POST['rows_per_page'])) $_SESSION['i_rows_per_page'] = $_POST['rows_per_page'];

      if (isset($_POST['go'])) {
        $_SESSION['i_search'] = trim($_POST['i_search']);
        $_SESSION['i_paid'] = isset($_POST['i_paid']) ? 1 : 0;
        $_SESSION['i_year'] = trim($_POST['i_year']);
        $_SESSION['i_status'] = trim($_POST['i_status']);
        $_SESSION['i_invoice_from'] = trim($_POST['i_invoice_from']);
        $_SESSION['i_invoice_to'] = trim($_POST['i_invoice_to']);

        // ignore the company filter if user uses the search field
        $paramsToIgnore = !empty($_SESSION['i_search']) ? ['organid'] : [];
        ResponseHelper::redirect(reconstructQuery($paramsToIgnore));
      }

      //pager properties
      $this->pager = new Pager();
      $this->pager->setWriteCount(true);
      $this->pager->setRowsPerPageOptions([
        30     => 30,
        100    => 100,
        250    => 250,
        999999 => 'Alles',
      ]);
      $this->pager->setWriteRowsPerPageOptions(true, $_SESSION['i_rows_per_page']);
      $this->pager->handle();
      //einde pager props

      $filt = "JOIN " . SandboxUsers::getTablename() . " ON invoices.userId = sandbox_users.userId ";
      $filt .= "JOIN " . CrmCompanies::getTablename() . " ON sandbox_users.companyId = crm_companies.companyId ";
//      $filt .= "JOIN " . Quotations::getTablename() . " ON invoices.invoiceId = quotations.invoiceId ";
      $filt .= "JOIN " . Quotations::getTablename() . " ON quotations.invoiceId = (SELECT invoiceId FROM " . Invoices::getTablename() . " LIMIT 1) ";
      $filt .= "WHERE 1 ";
      if ($_SESSION['i_year'] != "") {
        $filt .= "AND YEAR(dateInvoice)=" . $_SESSION['i_year'] . " ";
      }
      if ($_SESSION['i_status'] != "") {
        $filt .= "AND quotations.statusId=" . $_SESSION['i_status'] . " ";
      }
      if ($_SESSION['i_invoice_from'] && $_SESSION['i_invoice_to']) {
        $filt .= " AND invoices.dateInvoice >= '" . escapeForDB(getTSFromStr($_SESSION['i_invoice_from'])) . "' AND invoices.dateInvoice <= '" . getTSFromStr(escapeForDB($_SESSION['i_invoice_to'])) . "' ";
      }
      if ($_SESSION['i_paid'] !== 0) {
        $filt .= "AND invoices.paid IS NOT NULL ";
      }
      if ($_SESSION['i_search'] != "") {
        $searchval = escapeForDB($_SESSION['i_search']);
        $filt .= " AND (";
        $filt .= "invoices.invoiceNumber='" . $searchval . "' ";
        $filt .= "OR invoices.invoiceId LIKE '%" . $searchval . "%' ";
        $filt .= "OR crm_companies.name LIKE '%" . $searchval . "%' ";
        $filt .= ") ";
      }

      // optional query filter for company
      if ($_GET['organid'] ?? false) {
        // set query filter and search company to display
        $filt .= " AND crm_companies.companyId = '" . escapeForDB($_GET['organid']) . "' ";
        $this->company = CrmCompanies::find_by(['companyId' => $_GET['organid']]);
      }

      $count = "SELECT COUNT(invoices.invoiceId) AS count FROM " . Invoices::getTablename() . " " . $filt;
      $result = DBConn::db_link()->query($count);
      $row = $result->fetch_assoc();
      if ($row) {
        $this->pager->count = $row['count'];
      }

      $query = "SELECT * FROM " . Invoices::getTablename() . " ";
      $query .= $filt;
      $query .= "GROUP BY invoices.invoiceId ";
      $query .= "ORDER BY dateInvoice DESC, invoices.invoiceNumber DESC ";
      $query .= $this->pager->getLimitQuery();

      $result = DBConn::db_link()->query($query);
      $invoices = [];
      while ($row = $result->fetch_row()) {
        $invoice = new Invoices();
        $invoice->hydrate($row);
        $cuser = new SandboxUsers();
        $cuser->hydrate($row, count(Invoices::columns));

        $ccompany = new CrmCompanies();
        $ccompany->hydrate($row, count(Invoices::columns) + count(SandboxUsers::columns));

        $cuser->company = $ccompany;
        $invoice->user = $cuser;

        $invoices[] = $invoice;
      }

      $this->stati = Status::find_all();
      $this->invoices = $invoices;

    }

    public function executeInvoiceedit() {

      $invoice = Invoices::find_by(["invoiceId" => $_GET["id"]]);
      $iuser = SandboxUsers::getUserAndCompany($invoice->userId);
      $showalert = isset($_GET["showalert"]);

      $form = new ModelForm();
      $form->addClass("edit-form");
      $form->buildElementsFromModel($invoice);
      $form->setElementsLabel([
        "payment_1"         => "1e betaling",
        "payment_2"         => "2e betaling",
        "payment_3"         => "3e betaling",
        "payment_4"         => "4e betaling",
        "paid"              => "Betaaldatum",
        "reminder1"         => "Aanmaning1",
        "reminder2"         => "Aanmaning2",
        "reminder3"         => "Aanmaning3",
        "invoiceNotes"      => "Notities factuur",
        "invoiceNotesAlert" => "Notities melding",
      ], true)->getElement("invoiceNotes")->addAtribute("rows", "8");

      $form->getElement("paid")->addClass("datepicker_weeks");
      $form->getElement("reminder1")->addClass("datepicker_weeks");
      $form->getElement("reminder2")->addClass("datepicker_weeks");
      $form->getElement("reminder3")->addClass("datepicker_weeks");

      if (!isset($_GET["showalert"])) {
        $form->getElement("invoiceNotesAlert")->setReadonly(true);
      }
      if (empty($invoice->payment_1)) {
        $form->getElement('payment_2')->setReadonly(true);
        $form->getElement('payment_3')->setReadonly(true);
        $form->getElement('payment_4')->setReadonly(true);
      }
      elseif (empty($invoice->payment_2)) {
        $form->getElement('payment_3')->setReadonly(true);
        $form->getElement('payment_4')->setReadonly(true);
      }
      elseif (empty($invoice->payment_3)) {
        $form->getElement('payment_4')->setReadonly(true);
      }

      if (isset($_POST['go']) || isset($_POST['go_list'])) {

        $form->setElementsAndObjectValue($_POST);

        if ($form->isValid()) {
          if ($form->getElement('paid')) {
            $quotation = Quotations::find_by(['invoiceId' => $invoice->invoiceId]);
            if ($quotation) {
              $quotation->statusId = Status::STATUS_PAID;
              $quotation->save();
            }
          }
          if (!$form->getElement('payment_1')->isEmpty()) {
            $invoice->payment_1_date = date('Y-m-d');
          }
          if (!$form->getElement('payment_2')->isEmpty()) {
            $invoice->payment_2_date = date('Y-m-d');
          }
          if (!$form->getElement('payment_3')->isEmpty()) {
            $invoice->payment_3_date = date('Y-m-d');
          }
          if (!$form->getElement('payment_4')->isEmpty()) {
            $invoice->payment_4_date = date('Y-m-d');
          }
          $form->getModelobject()->save();
          if (isset($_POST['go_list'])) {
            ResponseHelper::redirectMessage("Gegevens opgeslagen", reconstructQueryAdd());
          }
          ResponseHelper::redirectMessage("Gegevens opgeslagen", reconstructQuery());
        }

      }

      //nog te betalen bedrag
      $payment_remainder = $invoice->totalProjectValue;
      if (!empty($invoice->payment_1)) {
        $payment_remainder -= $invoice->payment_1;
      }
      if (!empty($invoice->payment_2)) {
        $payment_remainder -= $invoice->payment_2;
      }
      if (!empty($invoice->payment_3)) {
        $payment_remainder -= $invoice->payment_3;
      }
      if (!empty($invoice->payment_4)) {
        $payment_remainder -= $invoice->payment_4;
      }

      //heeft uitvoerders ticket
      $quotationIds = ArrayHelper::getValuesOfNestedObjectsAsArray(Quotations::find_all_by(["invoiceId" => $invoice->invoiceId]), "quotationId");
      $qexs = QuotationsExtra::find_all_by(["quotationId" => $quotationIds]);
      $hasExecutorTicket = false;
      foreach ($qexs as $quotationExtra) {
        if ($quotationExtra->executorTicket == 1) {
          $hasExecutorTicket = true;
        }
      }

      //bestanden
      $this->files = Files::getFilesByQuotationidsCategoryid($quotationIds, Files::CATEGORY_INVOICE);

      $this->remainder = StringHelper::asMoney($payment_remainder);
      $this->form = $form;
      $this->invoice = $invoice;
      $this->iuser = $iuser;
      $this->showalert = $showalert;
      $this->hasExecutorTicket = $hasExecutorTicket;
    }

    public function executeUnpayable() {

      if (!isset($_SESSION['i_search'])) $_SESSION['i_search'] = '';
      if (!isset($_SESSION['i_year'])) $_SESSION['i_year'] = date('Y');
      if (!isset($_SESSION['i_invoice_from'])) $_SESSION['i_invoice_from'] = "";
      if (!isset($_SESSION['i_invoice_to'])) $_SESSION['i_invoice_to'] = "";

      if (isset($_POST['go'])) {
        $_SESSION['i_search'] = trim($_POST['i_search']);
        $_SESSION['i_year'] = trim($_POST['i_year']);
        $_SESSION['i_invoice_from'] = trim($_POST['i_invoice_from']);
        $_SESSION['i_invoice_to'] = trim($_POST['i_invoice_to']);
        ResponseHelper::redirect(reconstructQuery());
      }

      $query = "SELECT * FROM " . Invoices::getTablename() . " ";
      $query .= "JOIN " . SandboxUsers::getTablename() . " ON invoices.userId = sandbox_users.userId ";
      $query .= "JOIN " . CrmCompanies::getTablename() . " ON sandbox_users.companyId = crm_companies.companyId ";
      $query .= "WHERE irrecoverable!=0 ";
      if ($_SESSION['i_year'] != "") {
        $query .= "AND YEAR(dateInvoice)=" . $_SESSION['i_year'] . " ";
      }
      if ($_SESSION['i_invoice_from'] && $_SESSION['i_invoice_to']) {
        $query .= " AND invoices.dateInvoice >= '" . escapeForDB(getTSFromStr($_SESSION['i_invoice_from'])) . "' AND invoices.dateInvoice <= '" . getTSFromStr(escapeForDB($_SESSION['i_invoice_to'])) . "' ";
      }
      if ($_SESSION['i_search'] != "") {
        $searchval = escapeForDB($_SESSION['i_search']);
        $query .= " AND (";
        $query .= "invoiceNumber='" . $searchval . "' ";
        $query .= "OR crm_companies.name LIKE '%" . $searchval . "%' ";
        $query .= ") ";
      }
      $query .= "ORDER BY dateInvoice DESC, invoiceNumber DESC ";
      $query .= "LIMIT 500 ";

      $result = DBConn::db_link()->query($query);
      $invoices = [];
      while ($row = $result->fetch_row()) {
        $invoice = new Invoices();
        $invoice->hydrate($row);
        $cuser = new SandboxUsers();
        $cuser->hydrate($row, count(Invoices::columns));

        $ccompany = new CrmCompanies();
        $ccompany->hydrate($row, count(Invoices::columns) + count(SandboxUsers::columns));

        $cuser->company = $ccompany;
        $invoice->user = $cuser;

        $invoices[] = $invoice;
      }


      $this->invoices = $invoices;
      $this->irrecoverable = AppModel::mapObjectIds(InvoicesIrrecoverable::find_all());
    }

    public function executeMollie() {

      if (!isset($_SESSION['i_search'])) $_SESSION['i_search'] = '';
      if (!isset($_SESSION['i_year'])) $_SESSION['i_year'] = date('Y');
      if (!isset($_SESSION['i_invoice_from'])) $_SESSION['i_invoice_from'] = "";
      if (!isset($_SESSION['i_invoice_to'])) $_SESSION['i_invoice_to'] = "";

      if (isset($_POST['go'])) {
        $_SESSION['i_search'] = trim($_POST['i_search']);
        $_SESSION['i_year'] = trim($_POST['i_year']);
        $_SESSION['i_invoice_from'] = trim($_POST['i_invoice_from']);
        $_SESSION['i_invoice_to'] = trim($_POST['i_invoice_to']);
        ResponseHelper::redirect(reconstructQuery());
      }

      $filt = 'WHERE NOT insertTS IS NULL ';
      if ($_SESSION['i_year'] != "") {
        $filt .= "AND YEAR(insertTS)=" . $_SESSION['i_year'] . " ";
      }
      if ($_SESSION['i_invoice_from'] && $_SESSION['i_invoice_to']) {
        $filt .= " AND DATE(insertTS) >= '" . escapeForDB(getTSFromStr($_SESSION['i_invoice_from'])) . "' AND DATE(insertTS) <= '" . getTSFromStr(escapeForDB($_SESSION['i_invoice_to'])) . "' ";
      }
      $filt .= " ORDER BY insertTS DESC ";

      $mollies = AppModel::mapObjectIds(Mollie::find_all($filt));
      $days = [];
      $day_totals = [];
      $mollie_ids = [];
      foreach ($mollies as $mollie) {
        $mollie_ids[$mollie->id] = $mollie->id;
        $mollie->quotations = [];
        $mollie->invoices = [];
        $days[$mollie->getInsertTS("Ymd")][$mollie->id] = $mollie;
        $day_totals[$mollie->getInsertTS("Ymd")] = 0;
      }


      if (count($mollie_ids) > 0) {
        //zoek quotations die betaald zijn, echter zonder factuur
        $filt = "AND mollie_id IN (" . implode(",", $mollie_ids) . ") ";
        if ($_SESSION['i_search'] != "") {
          $searchval = escapeForDB($_SESSION['i_search']);
          $filt .= " AND (";
          $filt .= "quotationNumber='" . $searchval . "' ";
          $filt .= ") ";
        }

        $extraInvoiceIds = [];
        $quotations = Quotations::find_all_by(["payedFlag" => 1, "paymentMethod" => "mollie"], $filt);
        foreach ($quotations as $quotation) {

          $quotation->user = SandboxUsers::getUserAndCompany($quotation->userId);
          if ($quotation->invoiceId != "") {
            $extraInvoiceIds[$quotation->invoiceId] = $quotation->mollie_id;
          }
          else {
            //gegroepeerd op mollie_id
            $mollie = $mollies[$quotation->mollie_id];
            $days[$mollie->getInsertTS("Ymd")][$mollie->id]->quotations[] = $quotation;
            $day_totals[$mollie->getInsertTS("Ymd")] += $mollie->amount;
          }
        }

        //invoices
        $query = "SELECT * FROM " . Invoices::getTablename() . " ";
        $query .= "JOIN " . SandboxUsers::getTablename() . " ON invoices.userId = sandbox_users.userId ";
        $query .= "JOIN " . CrmCompanies::getTablename() . " ON sandbox_users.companyId = crm_companies.companyId ";
        $query .= "WHERE (mollie_id IN (" . implode(",", $mollie_ids) . ") ";
        if ($_SESSION['i_search'] != "") {
          $searchval = escapeForDB($_SESSION['i_search']);
          $query .= " AND (";
          $query .= "invoiceNumber='" . $searchval . "' ";
          $query .= "OR crm_companies.name LIKE '%" . $searchval . "%' ";
          $query .= ") ";
        }
        $query .= ") ";
        if (count($extraInvoiceIds) > 0) {
          $query .= "OR invoiceId IN (" . implode(",", array_flip($extraInvoiceIds)) . ") ";
        }
        $query .= "LIMIT 500 ";

        $result = DBConn::db_link()->query($query);
        while ($row = $result->fetch_row()) {
          $invoice = new Invoices();
          $invoice->hydrate($row);
          $cuser = new SandboxUsers();
          $cuser->hydrate($row, count(Invoices::columns));

          $ccompany = new CrmCompanies();
          $ccompany->hydrate($row, count(Invoices::columns) + count(SandboxUsers::columns));

          $cuser->company = $ccompany;
          $invoice->user = $cuser;
          $invoice->quotations = Quotations::find_all_by(["invoiceId" => $invoice->invoiceId]);

          $mollie_id = $invoice->mollie_id;
          if ($mollie_id == "") {
            $mollie_id = $extraInvoiceIds[$invoice->invoiceId];
          }
          $mollie = $mollies[$mollie_id];
          $days[$mollie->getInsertTS("Ymd")][$mollie->id]->invoices[] = $invoice;
          $day_totals[$mollie->getInsertTS("Ymd")] += $invoice->totalProjectValue;

        }
      }

      //      ksort($days);
      //      $days = array_reverse($days,true);


      $this->days = $days;
      $this->mollies = $mollies;
      $this->day_totals = $day_totals;
    }

    public function executeQuotationpdf() {

      $quotation = Quotations::find_by(["quotationId" => $_GET["id"]]);
      if (!$quotation) {
        MessageFlashCoordinator::addMessageAlert(__("Bestelling niet gevonden."));
        ResponseHelper::redirect(reconstructQueryAdd());
      }
      $pdf = new QuotationPdf($quotation->quotationId);
      $pdf->setShow(true);
      $pdf->generatePdf();
    }


    public function executePdf() {

      $invoice = Invoices::find_by(["invoiceId" => $_GET["id"]]);

      if (!$invoice) {
        MessageFlashCoordinator::addMessageAlert(__("Factuur niet geopend. Factuur is niet gevonden, of u heeft geen rechten om deze te bekijken."));
        ResponseHelper::redirect(reconstructQueryAdd());
      }

      $pdf = new InvoicePdf($invoice->invoiceId);
      $pdf->setShow(true);
      $pdf->generatePdf();
      ResponseHelper::exit();
    }

    // wanbetaling
    public function executeNon_paymentlist() {
      $this->executeNon_paymentlistFilters();
    }

    public function executeNon_paymentlistFilters() {

      $dataTable = new DataTable('non_payment');
      $dataTable->setRequestUrl(reconstructQueryAdd() . "action=non_paymentListajax");
      $dataTable->addColumnHelper("invoiceNumber", "Factuur nr.");
      $dataTable->addColumnHelper("name", "Bedrijfsnaam");
      $dataTable->addColumnHelper("totalProjectValue", "Bedrag");
      $dataTable->addColumnHelper("nr_of_days", "Aantal dagen");
      $dataTable->addColumnHelper("dateInvoice", "Datum");
      $dataTable->addColumnHelper("reminder1", "Aanmaning 1");
      $dataTable->addColumnHelper("reminder2", "Aanmaning 2");
      $dataTable->addColumnHelper("reminder3", "Aanmaning 3");
      $dataTable->addColumnHelper("invoiceNotes", "Opmerking");
      $dataTable->addColumnHelper("actions", "Acties");

      $dataTable->getColumn("actions")->setSortable(false);
      $dataTable->getColumn("nr_of_days")->setSortable(false);

      $dataTable->setDefaultSort("dateInvoice", "Desc");
      $dataTable->addSearchInput();
      $dataTable->addSearchReset();

      $dataTable->handleRequest($_POST);
      $this->dataTable = $dataTable;
    }

    public function executeNon_paymentListajax() {
      $this->executeNon_paymentlistFilters();

      $filter_query = "";
      $filter_query .= "LEFT JOIN " . SandboxUsers::getTablename() . " ON sandbox_users.userId = invoices.userId ";
      $filter_query .= "LEFT JOIN " . CrmCompanies::getTablename() . " ON sandbox_users.companyId = crm_companies.companyId ";
      $filter_query .= "WHERE invoices.paid IS NULL AND invoices.reminder3 IS NOT NULL ";
      $filter_query .= "AND invoices.irrecoverable = 0 ";

      if ($this->dataTable->hasFormElementValue("search")) {
        $searchstr = DbHelper::escape($this->dataTable->getFormElementValue("search"));
        $filter_query .= "AND ( ";
        $filter_query .= "crm_companies.name LIKE '%" . $searchstr . "%' ";
        $filter_query .= ") ";
      }

      /** TOTALS */
      $total_count = Invoices::count_all_by([]);
      $total_count_filtered = Invoices::count_all_by([], $filter_query);

      /** GET DATA */
      $query = "SELECT * FROM " . Invoices::getTablename() . " ";
      $query .= $filter_query;
      $query .= $this->dataTable->getSortQuery();
      $query .= $this->dataTable->getPager()->getLimitQuery();

      $result = DBConn::db_link()->query($query);

      $table_data = [];
      while ($row = $result->fetch_array()) {
        $column_count = 0;

        $invoice = (new Invoices())->hydrateNext($row, $column_count);
        $user = (new SandboxUsers())->hydrateNext($row, $column_count);
        $company = (new CrmCompanies())->hydrateNext($row, $column_count);

        $invoice_notes = "";
        if ($invoice->invoiceNotes != "") {
          $invoice_notes .= '<img src="/gsdfw/images/note.png" class="qtipa" data-caption="Opmerking" title=" ' . escapeForJS(nl2br($invoice->invoiceNotes)) . '"/>';
        }

        $pay = $invoice->averageDaysOpenOfCompany($company->companyId);
        $nr_of_days = round($pay['days'] / $pay['count']) . ' / ' . $company->paymentTerm;

        $actions_html = "";
        $actions_html .= " " . BtnHelper::getEdit('?action=non_paymentedit&id=' . $invoice->invoiceId);
        $actions_html .= " " . BtnHelper::getPrintPDF('?action=pdf&id=' . $invoice->invoiceId, __('Bekijk pdf'), '_blank');

        $table_data[] = [
          'DT_RowId'          => $invoice->invoiceId,
          'invoiceNumber'     => $invoice->invoiceNumber,
          'name'              => $company->name,
          'totalProjectValue' => $invoice->totalProjectValue,
          'nr_of_days'        => $nr_of_days,
          'dateInvoice'       => $invoice->dateInvoice,
          'reminder1'         => $invoice->reminder1,
          'reminder2'         => $invoice->reminder2,
          'reminder3'         => $invoice->reminder3,
          'invoiceNotes'      => $invoice_notes,
          'actions'           => $actions_html,
        ];
      }

      /** RETURN DATA AS JSON */
      ResponseHelper::exitAsJson([
        'data'            => $table_data,
        'recordsTotal'    => $total_count,
        'recordsFiltered' => $total_count_filtered,
        'draw'            => (int)$_POST['draw'],
      ]);
    }

    public function executeNon_paymentedit() {
      $invoice = Invoices::find_by(["invoiceId" => $_GET["id"]]);
      $iuser = SandboxUsers::getUserAndCompany($invoice->userId);
      $showalert = isset($_GET["showalert"]);

      $form = new ModelForm();
      $form->addClass("edit-form");
      $form->buildElementsFromModel($invoice);
      $form->setElementsLabel([
        "paid"              => "Betaaldatum",
        "irrecoverable"     => "Reden oninbaar",
        "reminder1"         => "Aanmaning1",
        "reminder2"         => "Aanmaning2",
        "reminder3"         => "Aanmaning3",
        "invoiceNotes"      => "Notities factuur",
        "invoiceNotesAlert" => "Notities melding",
      ], true)->getElement("invoiceNotes")->addAtribute("rows", "8");
      if (!isset($_GET["showalert"])) {
        $form->getElement("invoiceNotesAlert")->setReadonly(true);
      }

      $opt_irrecoverable = AppModel::mapObjectIds(InvoicesIrrecoverable::find_all());
      $select = new Select("Reden oninbaar", "irrecoverable", $invoice->irrecoverable);
      $select->addOptionHelper(0, "Selecteer reden");
      foreach ($opt_irrecoverable as $opt_id => $opt_name) {
        $select->addOptionHelper($opt_id, $opt_name->name);
      }
      $form->addElement($select);

      if (isset($_POST['go']) || isset($_POST['go_list'])) {

        $form->setElementsAndObjectValue($_POST);

        if ($form->isValid()) {
          if ($form->getElement('paid')) {
            $quotation = Quotations::find_by(['invoiceId' => $invoice->invoiceId]);
            $quotation->statusId = Status::STATUS_PAID;
            $quotation->save();
          }
          $form->getModelobject()->save();
          if (isset($_POST['go_list'])) {
            ResponseHelper::redirectMessage("Gegevens opgeslagen", reconstructQueryAdd());
          }
          ResponseHelper::redirectMessage("Gegevens opgeslagen", reconstructQuery());
        }

      }

      //heeft uitvoerders ticket
      $quotationIds = ArrayHelper::getValuesOfNestedObjectsAsArray(Quotations::find_all_by(["invoiceId" => $invoice->invoiceId]), "quotationId");
      $qexs = QuotationsExtra::find_all_by(["quotationId" => $quotationIds]);
      $hasExecutorTicket = false;
      foreach ($qexs as $quotationExtra) {
        if ($quotationExtra->executorTicket == 1) {
          $hasExecutorTicket = true;
        }
      }

      //bestanden
      $this->files = Files::getFilesByQuotationidsCategoryid($quotationIds, Files::CATEGORY_INVOICE);

      $this->form = $form;
      $this->invoice = $invoice;
      $this->iuser = $iuser;
      $this->showalert = $showalert;
      $this->hasExecutorTicket = $hasExecutorTicket;
    }

    //Factuur betaald
    public function executeInvoice_paidlist() {
      $this->getTotal();
      $this->executeInvoice_paidlistFilters();

      if (isset($_POST['paid'])) {
        foreach ($_POST['paid'] as $id => $date) {
          if (empty($date) || empty($id)) continue;
          $invoice = Invoices::find_by(["invoiceId" => $id]);
          $quotation = Quotations::find_by(["invoiceId" => $invoice->invoiceId]);

          $invoice->paid = $date;
          $quotation->statusId = Status::STATUS_PAID;
          $quotation->save();
          $invoice->save();
        }
      }
    }

    public function executeInvoice_paidlistFilters() {

      $dataTable = new DataTable('invoice_paid');
      $dataTable->setRequestUrl(reconstructQueryAdd() . "action=invoice_paidListajax");
      $dataTable->addColumnHelper("invoiceNumber", "Factuur nr.");
      $dataTable->addColumnHelper("name", "Bedrijfsnaam");
      $dataTable->addColumnHelper("dateInvoice", "Datum");
      $dataTable->addColumnHelper("totalProjectValue", "Bedrag");
      $dataTable->addColumnHelper("paid", "Betaald");

      $dataTable->setDefaultSort("dateInvoice", "Desc");

      $save_btn = new Submit("Opslaan", "save_paid", "Opslaan");
      $dataTable->getForm()->addElement($save_btn);
      $dataTable->addSearchInput();
      $dataTable->addSearchReset();

      $dataTable->handleRequest($_POST);
      $this->dataTable = $dataTable;
    }

    public function executeInvoice_paidListajax() {

      $this->executeInvoice_paidlistFilters();

      $filter_query = "";
      $filter_query .= "LEFT JOIN " . SandboxUsers::getTablename() . " ON sandbox_users.userId = invoices.userId ";
      $filter_query .= "LEFT JOIN " . CrmCompanies::getTablename() . " ON sandbox_users.companyId = crm_companies.companyId ";
      $filter_query .= "WHERE invoices.paid IS NULL AND invoices.irrecoverable = 0 ";

      if ($this->dataTable->hasFormElementValue("search")) {
        $searchstr = DbHelper::escape($this->dataTable->getFormElementValue("search"));
        $filter_query .= "AND ( ";
        $filter_query .= "crm_companies.name LIKE '%" . $searchstr . "%' ";
        $filter_query .= ") ";
      }

      /** TOTALS */
      $total_count = Invoices::count_all_by([]);
      $total_count_filtered = Invoices::count_all_by([], $filter_query);

      /** GET DATA */
      $query = "SELECT * FROM " . Invoices::getTablename() . " ";
      $query .= $filter_query;
      $query .= $this->dataTable->getSortQuery();
      $query .= $this->dataTable->getPager()->getLimitQuery();

      $result = DBConn::db_link()->query($query);

      $table_data = [];
      while ($row = $result->fetch_array()) {
        $column_count = 0;

        $invoice = (new Invoices())->hydrateNext($row, $column_count);
        $user = (new SandboxUsers())->hydrateNext($row, $column_count);
        $company = (new CrmCompanies())->hydrateNext($row, $column_count);

        $table_data[] = [
          'DT_RowId'          => $invoice->invoiceId,
          'invoiceNumber'     => $invoice->invoiceNumber,
          'name'              => $company->name,
          'dateInvoice'       => $invoice->dateInvoice,
          'totalProjectValue' => $invoice->totalProjectValue,
          'paid'              => '<input type="date" name="paid[' . $invoice->invoiceId . ']">',
        ];
      }

      /** RETURN DATA AS JSON */
      ResponseHelper::exitAsJson([
        'data'            => $table_data,
        'recordsTotal'    => $total_count,
        'recordsFiltered' => $total_count_filtered,
        'draw'            => (int)$_POST['draw'],
      ]);
    }

    public function getTotal() {
      $invoices = Invoices::find_all_by(['paid' => null, 'irrecoverable' => 0]);

      $total = 0;
      foreach ($invoices as $invoice) {
        $total += $invoice->totalProjectValue;
      }

      $this->total = StringHelper::asMoney($total);
    }

    //mail versturen
    public function executeSend_mail() {
      $invoice = Invoices::find_by(['invoiceId' => $_GET['id']]);
      if (!$invoice) ResponseHelper::redirectNotFound("Factuur niet gevonden");
      $user = SandboxUsers::find_by(['userId' => $invoice->userId]);
      //$invoice_party = CrmInvoiceparties::find_by(['companyId' => $user->companyId]);

      if ($user->private) {
        $extension = 'PRIVATE';
      }
      else {
        $extension = 'COMMERCIAL';
      }

      if ($_GET['reminder'] == 1) {
        $mail_template = 'INVOICE_REMINDER1_' . $extension;
      }
      if ($_GET['reminder'] == 2) {
        $mail_template = 'INVOICE_REMINDER2_' . $extension;
      }
      if ($_GET['reminder'] == 3) {
        $mail_template = 'INVOICE_REMINDER3_' . $extension;
      }

      MailsFactory::sendInvoiceReminder($invoice->invoiceId, $_GET['reminder'], $mail_template);

      ResponseHelper::redirect(reconstructQueryAdd());

    }

    public function send_reminders_multiple($invoice_ids, $reminder) {
      //haal alle invoices uit de post data
      $invoice_ids_arr = explode(',', $invoice_ids);
      //loop door alle invoices heen
      foreach ($invoice_ids_arr as $invoice_id) {
        $invoice = Invoices::find_by(['invoiceId' => $invoice_id]);
        $user = SandboxUsers::find_by(['userId' => $invoice->userId]);

        if ($user->private) {
          $extension = 'PRIVATE';
        }
        else {
          $extension = 'COMMERCIAL';
        }

        if ($reminder == 1) {
          $mail_template = 'INVOICE_REMINDER1_' . $extension;
        }
        if ($reminder == 2) {
          $mail_template = 'INVOICE_REMINDER2_' . $extension;
        }
        if ($reminder == 3) {
          $mail_template = 'INVOICE_REMINDER3_' . $extension;
        }

        //verstuur mail via MailsFactory
        MailsFactory::sendInvoiceReminder($invoice_id, $reminder, $mail_template);
      }

      ResponseHelper::redirect(reconstructQueryAdd());
    }

    //aanmaningen
    public function executeReminderslist() {
      $this->executeReminderslistFilters();
      $this->executeReminders2listFilters();
      $this->executeReminders3listFilters();

      //aanmaning 1
      if (isset($_POST['send_reminders1'])) {
        $this->send_reminders_multiple($_POST['invoice_ids'], $_POST['reminder1']);
      }
      //aanmaning 2
      if (isset($_POST['send_reminders2'])) {
        $this->send_reminders_multiple($_POST['invoice_ids2'], $_POST['reminder2']);
      }
      //aanmaning 3
      if (isset($_POST['send_reminders3'])) {
        $this->send_reminders_multiple($_POST['invoice_ids3'], $_POST['reminder3']);
      }
    }

    public function executeReminderslistFilters() {

      $dataTable = new DataTable('reminders');
      $dataTable->setRequestUrl(reconstructQueryAdd() . "action=reminderslistajax");
      $dataTable->addColumnHelper("invoiceNumber", "Factuur nr.");
      $dataTable->addColumnHelper("name", "Bedrijfsnaam");
      $dataTable->addColumnHelper("dateInvoice", "Datum");
      $dataTable->addColumnHelper("totalProjectValue", "Bedrag");
      $dataTable->addColumnHelper("partly_paid", "gedeeltelijk betaald");
      $dataTable->addColumnHelper("payment_remainder", "Nog te betalen");
      $dataTable->addColumnHelper("email", "E-mail");

      $dataTable->addColumnHelper("actions", "Acties");

      $dataTable->getColumn("actions")->setSortable(false);
      $dataTable->setDefaultSort("dateInvoice", "Desc");

      $dataTable->addSearchInput();
      $dataTable->addSearchReset();

      $dataTable->handleRequest($_POST);
      $this->dataTable = $dataTable;
    }

    public function executeReminderslistajax() {
      $this->executeReminderslistFilters();

      $filter_query = "LEFT JOIN " . SandboxUsers::getTablename() . " ON sandbox_users.userId = invoices.userId ";
      $filter_query .= "LEFT JOIN " . CrmCompanies::getTablename() . " ON sandbox_users.companyId = crm_companies.companyId ";
      $filter_query .= "LEFT JOIN " . CrmInvoiceparties::getTablename() . " ON crm_companies.companyId = crm_invoiceparties.companyId ";
      $filter_query .= "WHERE invoices.paid IS NULL AND invoices.reminder1 IS NULL AND invoices.totalProjectValue > 0 ";
      $filter_query .= "AND (DATEDIFF(invoices.dateInvoice, CURRENT_DATE) < -sandbox_users.payterm) ";

      if ($this->dataTable->hasFormElementValue("search")) {
        $searchstr = DbHelper::escape($this->dataTable->getFormElementValue("search"));
        $filter_query .= "AND ( ";
        $filter_query .= "crm_companies.name LIKE '%" . $searchstr . "%' ";
        $filter_query .= ") ";
      }

      /** TOTALS */
      $total_count = Invoices::count_all_by([]);
      $total_count_filtered = Invoices::count_all_by([], $filter_query);

      /** GET DATA */
      $query = "SELECT * FROM " . Invoices::getTablename() . " ";
      $query .= $filter_query;
      $query .= $this->dataTable->getSortQuery();
      $query .= $this->dataTable->getPager()->getLimitQuery();

      $result = DBConn::db_link()->query($query);

      $table_data = [];
      while ($row = $result->fetch_array()) {
        $column_count = 0;

        $invoice = (new Invoices())->hydrateNext($row, $column_count);
        $user = (new SandboxUsers())->hydrateNext($row, $column_count);
        $company = (new CrmCompanies())->hydrateNext($row, $column_count);
        $invoice_party = (new CrmInvoiceparties())->hydrateNext($row, $column_count);

        //gedeeltelijk betaald
        $total_project_value = $invoice->totalProjectValue;
        $partly_paid_check = false;
        $payment_remainder = $invoice->totalProjectValue;
        $nog_te_betalen = $invoice->totalProjectValue;
        if ($invoice->payment_1 > 0) {
          $partly_paid_check = true;
          $payment_remainder = StringHelper::asMoney($invoice->payment_1) . ' op: ' . DateTimeHelper::formatDateTime($invoice->payment_1_date, "dd-MM-Y");
          $nog_te_betalen -= $invoice->payment_1;
        }
        if ($invoice->payment_2 > 0) {
          $payment_remainder = StringHelper::asMoney($invoice->payment_2) . ' op: ' . DateTimeHelper::formatDateTime($invoice->payment_2_date, "dd-MM-Y");
          $nog_te_betalen -= $invoice->payment_2;
        }
        if ($invoice->payment_3 > 0) {
          $payment_remainder = StringHelper::asMoney($invoice->payment_3) . ' op: ' . DateTimeHelper::formatDateTime($invoice->payment_3_date, "dd-MM-Y");
          $nog_te_betalen -= $invoice->payment_3;
        }
        if ($invoice->payment_4 > 0) {
          $payment_remainder = StringHelper::asMoney($invoice->payment_4) . ' op: ' . DateTimeHelper::formatDateTime($invoice->payment_4_date, "dd-MM-Y");
          $nog_te_betalen -= $invoice->payment_4;
        }

        if ($partly_paid_check) {
          $partly_paid = '<a style="color: #982e1a;" href="javascript: void(0);" class="qtipa fa_info fa fa-info-circle" title="Laatst betaald: ' . ($payment_remainder) . '" partly_paid="' . $payment_remainder . '"
                  monster_id="' . $invoice->invoiceId . '" data-caption="Gedeeltelijk betaald"></a>';
        }
        else {
          $partly_paid = '';
        }

        $actions_html = "";
        $actions_html .= " " . BtnHelper::getPrintPDF('?action=createinvoicepdf&id=' . $invoice->invoiceId, __('Bekijk pdf'), '_blank');
        $actions_html .= " " . BtnHelper::getEmailByUrl("?action=send_mail&reminder=1&id=" . $invoice->invoiceId);

        $table_data[] = [
          'DT_RowId'          => $invoice->invoiceId,
          'invoiceNumber'     => $invoice->invoiceNumber,
          'name'              => $company->name,
          'dateInvoice'       => $invoice->dateInvoice,
          'totalProjectValue' => $total_project_value,
          'partly_paid'       => $partly_paid,
          'payment_remainder' => $nog_te_betalen,
          'email'             => $invoice_party->email,
          'actions'           => $actions_html,
        ];
      }

      /** RETURN DATA AS JSON */
      ResponseHelper::exitAsJson([
        'data'            => $table_data,
        'recordsTotal'    => $total_count,
        'recordsFiltered' => $total_count_filtered,
        'draw'            => (int)$_POST['draw'],
      ]);
    }

    public function executeReminders2listFilters() {

      $dataTable = new DataTable('reminders2');
      $dataTable->setRequestUrl(reconstructQueryAdd() . "action=reminders2listajax");
      $dataTable->addColumnHelper("invoiceNumber", "Factuur nr.");
      $dataTable->addColumnHelper("name", "Bedrijfsnaam");
      $dataTable->addColumnHelper("dateInvoice", "Datum");
      $dataTable->addColumnHelper("totalProjectValue", "Bedrag");
      $dataTable->addColumnHelper("partly_paid", "gedeeltelijk betaald");
      $dataTable->addColumnHelper("payment_remainder", "Nog te betalen");
      $dataTable->addColumnHelper("email", "E-mail");

      $dataTable->addColumnHelper("actions", "Acties");

      $dataTable->getColumn("actions")->setSortable(false);
      $dataTable->setDefaultSort("dateInvoice", "Desc");

      $dataTable->addSearchInput();
      $dataTable->addSearchReset();

      $dataTable->handleRequest($_POST);
      $this->dataTable2 = $dataTable;
    }

    public function executeReminders2listajax() {
      $this->executeReminders2listFilters();

      $filter_query = "";
      $filter_query .= "LEFT JOIN " . SandboxUsers::getTablename() . " ON sandbox_users.userId = invoices.userId ";
      $filter_query .= "LEFT JOIN " . CrmCompanies::getTablename() . " ON sandbox_users.companyId = crm_companies.companyId ";
      $filter_query .= "WHERE invoices.paid IS NULL AND invoices.reminder1 IS NOT NULL AND invoices.reminder2 IS NULL AND invoices.totalProjectValue > 0 ";
      $filter_query .= "AND (DATEDIFF(invoices.reminder1, CURRENT_DATE) < -8) ";

      if ($this->dataTable2->hasFormElementValue("search")) {
        $searchstr = DbHelper::escape($this->dataTable2->getFormElementValue("search"));
        $filter_query .= "AND ( ";
        $filter_query .= "crm_companies.name LIKE '%" . $searchstr . "%' ";
        $filter_query .= ") ";
      }

      /** TOTALS */
      $total_count = Invoices::count_all_by([]);
      $total_count_filtered = Invoices::count_all_by([], $filter_query);

      /** GET DATA */
      $query = "SELECT * FROM " . Invoices::getTablename() . " ";
      $query .= $filter_query;
      $query .= $this->dataTable2->getSortQuery();
      $query .= $this->dataTable2->getPager()->getLimitQuery();

      $result = DBConn::db_link()->query($query);

      $table_data = [];
      while ($row = $result->fetch_array()) {
        $column_count = 0;

        $invoice = (new Invoices())->hydrateNext($row, $column_count);
        $user = (new SandboxUsers())->hydrateNext($row, $column_count);
        $company = (new CrmCompanies())->hydrateNext($row, $column_count);

        //gedeeltelijk betaald
        $total_project_value = $invoice->totalProjectValue;
        $partly_paid_check = false;
        $payment_remainder = $invoice->totalProjectValue;
        $nog_te_betalen = $invoice->totalProjectValue;
        if ($invoice->payment_1 > 0) {
          $partly_paid_check = true;
          $payment_remainder = StringHelper::asMoney($invoice->payment_1) . ' op: ' . DateTimeHelper::formatDateTime($invoice->payment_1_date, "dd-MM-Y");
          $nog_te_betalen -= $invoice->payment_1;
        }
        if ($invoice->payment_2 > 0) {
          $payment_remainder = StringHelper::asMoney($invoice->payment_2) . ' op: ' . DateTimeHelper::formatDateTime($invoice->payment_2_date, "dd-MM-Y");
          $nog_te_betalen -= $invoice->payment_2;
        }
        if ($invoice->payment_3 > 0) {
          $payment_remainder = StringHelper::asMoney($invoice->payment_3) . ' op: ' . DateTimeHelper::formatDateTime($invoice->payment_3_date, "dd-MM-Y");
          $nog_te_betalen -= $invoice->payment_3;
        }
        if ($invoice->payment_4 > 0) {
          $payment_remainder = StringHelper::asMoney($invoice->payment_4) . ' op: ' . DateTimeHelper::formatDateTime($invoice->payment_4_date, "dd-MM-Y");
          $nog_te_betalen -= $invoice->payment_4;
        }

        if ($partly_paid_check) {
          $partly_paid = '<a style="color: #982e1a;" href="javascript: void(0);" class="qtipa fa_info fa fa-info-circle" title="Laatst betaald: ' . ($payment_remainder) . '" partly_paid="' . $payment_remainder . '"
                  monster_id="' . $invoice->invoiceId . '" data-caption="Gedeeltelijk betaald"></a>';
        }
        else {
          $partly_paid = '';
        }


        $actions_html = "";
        $actions_html .= " " . BtnHelper::getPrintPDF('?action=createinvoicepdf&id=' . $invoice->invoiceId, __('Bekijk pdf'), '_blank');
        $actions_html .= " " . '<a style="vertical-align: middle;" href=?action=send_mail&reminder=2&id=' . $invoice->invoiceId . ' >' . IconHelper::getEmail() . '</a>';

        $table_data[] = [
          'DT_RowId'          => $invoice->invoiceId,
          'invoiceNumber'     => $invoice->invoiceNumber,
          'name'              => $company->name,
          'dateInvoice'       => $invoice->dateInvoice,
          'totalProjectValue' => $total_project_value,
          'partly_paid'       => $partly_paid,
          'payment_remainder' => $nog_te_betalen,
          'email'             => $company->email,
          'actions'           => $actions_html,
        ];
      }

      /** RETURN DATA AS JSON */
      ResponseHelper::exitAsJson([
        'data'            => $table_data,
        'recordsTotal'    => $total_count,
        'recordsFiltered' => $total_count_filtered,
        'draw'            => (int)$_POST['draw'],
      ]);
    }

    public function executeReminders3listFilters() {

      $dataTable = new DataTable('reminders3');
      $dataTable->setRequestUrl(reconstructQueryAdd() . "action=reminders3listajax");
      $dataTable->addColumnHelper("invoiceNumber", "Factuur nr.");
      $dataTable->addColumnHelper("name", "Bedrijfsnaam");
      $dataTable->addColumnHelper("dateInvoice", "Datum");
      $dataTable->addColumnHelper("totalProjectValue", "Bedrag");
      $dataTable->addColumnHelper("partly_paid", "gedeeltelijk betaald");
      $dataTable->addColumnHelper("payment_remainder", "Nog te betalen");
      $dataTable->addColumnHelper("email", "E-mail");

      $dataTable->addColumnHelper("actions", "Acties");

      $dataTable->getColumn("actions")->setSortable(false);
      $dataTable->setDefaultSort("dateInvoice", "Desc");

      $dataTable->addSearchInput();
      $dataTable->addSearchReset();

      $dataTable->handleRequest($_POST);
      $this->dataTable3 = $dataTable;
    }

    public function executeReminders3listajax() {
      $this->executeReminders3listFilters();

      $filter_query = "";
      $filter_query .= "LEFT JOIN " . SandboxUsers::getTablename() . " ON sandbox_users.userId = invoices.userId ";
      $filter_query .= "LEFT JOIN " . CrmCompanies::getTablename() . " ON sandbox_users.companyId = crm_companies.companyId ";
      $filter_query .= "WHERE invoices.paid IS NULL AND invoices.reminder1 IS NOT NULL AND invoices.reminder2 IS NOT NULL AND invoices.reminder3 IS NULL AND invoices.totalProjectValue > 0 ";
      $filter_query .= "AND (DATEDIFF(invoices.reminder2, CURRENT_DATE) < -5) ";

      if ($this->dataTable3->hasFormElementValue("search")) {
        $searchstr = DbHelper::escape($this->dataTable3->getFormElementValue("search"));
        $filter_query .= "AND ( ";
        $filter_query .= "crm_companies.name LIKE '%" . $searchstr . "%' ";
        $filter_query .= ") ";
      }

      /** TOTALS */
      $total_count = Invoices::count_all_by([]);
      $total_count_filtered = Invoices::count_all_by([], $filter_query);

      /** GET DATA */
      $query = "SELECT * FROM " . Invoices::getTablename() . " ";
      $query .= $filter_query;
      $query .= $this->dataTable3->getSortQuery();
      $query .= $this->dataTable3->getPager()->getLimitQuery();

      $result = DBConn::db_link()->query($query);

      $table_data = [];
      while ($row = $result->fetch_array()) {
        $column_count = 0;

        $invoice = (new Invoices())->hydrateNext($row, $column_count);
        $user = (new SandboxUsers())->hydrateNext($row, $column_count);
        $company = (new CrmCompanies())->hydrateNext($row, $column_count);

        //gedeeltelijk betaald
        $total_project_value = $invoice->totalProjectValue;
        $partly_paid_check = false;
        $payment_remainder = $invoice->totalProjectValue;
        $nog_te_betalen = $invoice->totalProjectValue;
        if ($invoice->payment_1 > 0) {
          $partly_paid_check = true;
          $payment_remainder = StringHelper::asMoney($invoice->payment_1) . ' op: ' . DateTimeHelper::formatDateTime($invoice->payment_1_date, "dd-MM-Y");
          $nog_te_betalen -= $invoice->payment_1;
        }
        if ($invoice->payment_2 > 0) {
          $payment_remainder = StringHelper::asMoney($invoice->payment_2) . ' op: ' . DateTimeHelper::formatDateTime($invoice->payment_2_date, "dd-MM-Y");
          $nog_te_betalen -= $invoice->payment_2;
        }
        if ($invoice->payment_3 > 0) {
          $payment_remainder = StringHelper::asMoney($invoice->payment_3) . ' op: ' . DateTimeHelper::formatDateTime($invoice->payment_3_date, "dd-MM-Y");
          $nog_te_betalen -= $invoice->payment_3;
        }
        if ($invoice->payment_4 > 0) {
          $payment_remainder = StringHelper::asMoney($invoice->payment_4) . ' op: ' . DateTimeHelper::formatDateTime($invoice->payment_4_date, "dd-MM-Y");
          $nog_te_betalen -= $invoice->payment_4;
        }

        if ($partly_paid_check) {
          $partly_paid = '<a style="color: #982e1a;" href="javascript: void(0);" class="qtipa fa_info fa fa-info-circle" title="Laatst betaald: ' . ($payment_remainder) . '" partly_paid="' . $payment_remainder . '"
                  monster_id="' . $invoice->invoiceId . '" data-caption="Gedeeltelijk betaald"></a>';
        }
        else {
          $partly_paid = '';
        }


        $actions_html = "";
        $actions_html .= " " . BtnHelper::getPrintPDF('?action=createinvoicepdf&id=' . $invoice->invoiceId, __('Bekijk pdf'), '_blank');
        $actions_html .= " " . '<a style="vertical-align: middle;" href=?action=send_mail&reminder=3&id=' . $invoice->invoiceId . ' >' . IconHelper::getEmail() . '</a>';

        $table_data[] = [
          'DT_RowId'          => $invoice->invoiceId,
          'invoiceNumber'     => $invoice->invoiceNumber,
          'name'              => $company->name,
          'dateInvoice'       => $invoice->dateInvoice,
          'totalProjectValue' => $total_project_value,
          'partly_paid'       => $partly_paid,
          'payment_remainder' => $nog_te_betalen,
          'email'             => $company->email,
          'actions'           => $actions_html,
        ];
      }

      /** RETURN DATA AS JSON */
      ResponseHelper::exitAsJson([
        'data'            => $table_data,
        'recordsTotal'    => $total_count,
        'recordsFiltered' => $total_count_filtered,
        'draw'            => (int)$_POST['draw'],
      ]);
    }

    public function executeCreate_invoice() {
      $query = "SELECT * FROM " . CrmCompanies::getTablename() . " ";
      $query .= "WHERE companyId IN (SELECT quotations.companyId FROM " . Quotations::getTablename() . " WHERE quotations.statusId = '55') ";

      $companies = [];
      $result = DBConn::db_link()->query($query);

      while ($row = $result->fetch_row()) {
        $column_count = 0;
        $company = (new CrmCompanies())->hydrateNext($row, $column_count);
        $invoice_party = CrmInvoiceparties::find_by(['companyId' => $company->companyId]);

        $quotations = Quotations::find_all_by(['companyId' => $company->companyId, 'statusId' => 55]);
        foreach ($quotations as $quotation) {
          $quotation_extra = QuotationsExtra::find_by(['quotationId' => $quotation->quotationId]);
          $container_quotation = ContainersQuotations::find_by(['quotationId' => $quotation->quotationId]);
          $container = false;
          if ($container_quotation) {
            $container = Containers::find_by(['containerId' => $container_quotation->containerId]);
          }

          if ($quotation_extra->executorTicket > 0) {
            $executor_ticket = "Ja";
          }
          else {
            $executor_ticket = "";
          }
          $quotation->has_executor_ticket = $executor_ticket;

          if ($container) {
            if ($container->type === 'bak') {
              $container_type = 'B';
            }
            elseif ($container->type === 'rek') {
              $container_type = 'R';
            }
            elseif ($container->type === 'pallet') {
              $container_type = 'P';
            }
            elseif ($container->type === 'ophaalrek') {
              $container_type = 'O';
            }
            $quotation->container = $container_type . '.' . $container->containerNumber;
          }
          else {
            $quotation->container = '';
          }

          if ($quotation_extra->quoteInvoiceAlertFlag == 1) {
            $quotation->invoice_notes = $quotation_extra->quoteInvoiceAlertInfo;
          }
        }

        $email = $invoice_party->email;
        $email_confirmed = $invoice_party->emailConfirmed;

        if (isset($_POST['confirm_invoice_email'])) {
          if ($invoice_party && $sandbox_user->invoice_email != "" && $sandbox_user->invoice_email_confirmed == 0) {
            MailsFactory::sendConfirmInvoiceEmailadres($sandbox_user, $invoice_party);
            ResponseHelper::redirect(reconstructQuery());
          }
        }

        $company->invoice_party_emailConfirmed = $email_confirmed;
        $company->invoice_party_email = $email;
        $company->quotations = $quotations;
        $company->orders_separate = $invoice_party->orders_separate;
        $companies[] = $company;
      }
      if (isset($_POST['create_invoice'])) {
        $quotationIds = [];
        foreach ($_POST as $key => $value) {
          if (str_starts_with($key, 'add_quotation')) $quotationIds[] = $value;
        }

        if (count($quotationIds) == 0) {
          ResponseHelper::redirectAlertMessage('Geen order geselecteerd. Kies een offerte om te factureren.', reconstructQueryAdd());
        }

        // Get all quotation objects for the selected quotation IDs
        $quotations = array_map(function ($id) {
          $quotation = Quotations::find_by(['quotationId' => $id]);
          $quotation->quotation_extra = QuotationsExtra::find_by(['quotationId' => $id]);
          return $quotation;
        }, $quotationIds);

        // all submitted quotations have the same companyId, so we can use the first one to determine the invoice party
        $invoice_party = CrmInvoiceparties::find_by(['companyId' => $quotations[0]->companyId]);
        if ($invoice_party->orders_separate && count($quotations) > 1) {
          ResponseHelper::redirectAlertMessage('U kunt maximaal 1 order per keer factureren', reconstructQueryAdd());
        }

        // check if within submitted quotations there are different payedFlags, all must either be paid or not
        $payedFlags = array_unique(array_map(fn($q) => $q->payedFlag, $quotations));
        if (count($payedFlags) > 1) {
          ResponseHelper::redirectAlertMessage('U kunt alleen orders met dezelfde betaalstatus tegelijk factureren', reconstructQueryAdd());
        }

        // check if within submitted quotations there are different delivery addresses, all must be the same
        $deliveryAddresses = array_unique(array_map(fn($q) => $q->quotation_extra->addressDeliveryId, $quotations));
        if (count($deliveryAddresses) > 1) {
          ResponseHelper::redirectAlertMessage('U kunt alleen orders met dezelfde afleveradres tegelijk factureren', reconstructQueryAdd());
        }

        ResponseHelper::redirect('?action=createinvoiceform&quotation_ids=' . implode(',', $quotationIds));
      }

      $this->users = $companies;
    }

    public function executeCreateInvoiceForm() {
      $quotation_ids = explode(",", $_GET['quotation_ids']);
      if (empty($quotation_ids)) {
        ResponseHelper::redirectAlertMessage('Er zijn geen offertes geselecteerd', reconstructQueryAdd());
      }
      $quotations = Quotations::find_all_by(["quotationId" => $quotation_ids]);

      $check_for_webshop = true;
      $all_quotations_paid = true;
      $mollie_id = null;
      $meters = [];
      $firstFoundUserId = -1;
      $projectValueSum = 0;
      $special_freight_costs = 0;
      $projects_sum = 0;
      $projects_arr = [];
      $webshop_freight_costs = 0;
      $ophalen = false;
      $foreign = false;
      $companyId = 0;
      $prod_date = null;
      foreach ($quotations as $quotation) {

        $prod_date = $quotation->productionDate;

        if ($firstFoundUserId == -1) {
          $firstFoundUserId = $quotation->userId;
        }

        $projects = Projects::find_all_by(['quotationId' => $quotation->quotationId]);
        if ($projects) {
          foreach ($projects as $project) {
            $projects_sum += (float)$project->euro;
            $projects_arr[] = $project;
          }
        }

        //all quotations paid
        if ($quotation->payedFlag == 0) {
          $all_quotations_paid = false;
        }

        if ($projects && $quotation->meters == 0.00) {
          $webshop_freight_costs = (float)$quotation->freightCosts;
        }
        //check mollie id
        if ($quotation->paymentMethod == 'mollie') {
          $mollie_id = $quotation->mollie_id;
        }

        if ($quotation->meters == 0.00) continue;
        $meters[] = $quotation->meters;
        if (!empty($quotation->projectValue)) {
          $projectValueSum += (float)$quotation->projectValue;
        }

        //check for webshop
        if (!empty($quotation->stoneId)) {
          $check_for_webshop = false;
        }

        //haal alle subtotalen op
        $subtotal_arr[] = $quotation->projectValue;

        if ($quotation->specialFreightCost && !$special_freight_costs > 0) {
          $special_freight_costs = (float)$quotation->specialFreightCostPrice;
        }

        if (str_starts_with($quotation->zipcode, '5531')) {
          $ophalen = true;
        }

        $quotation_extra = QuotationsExtra::find_by(['quotationId' => $quotation->quotationId]);
        $delivery_address = CrmAddresses::find_by(['addressId' => $quotation_extra->addressDeliveryId]);
        if ($delivery_address->country != 'NL') {
          $foreign = true;
        }

        if (!empty($quotation->companyId)) {
          $companyId = $quotation->companyId;
        }

      }

      $meters_total = array_sum($meters);
      $lastInvoiceNumber = intval(Invoices::getLastInvoiceNumber());

      $user = SandboxUsers::find_by(['userId' => $firstFoundUserId]);

      $subtotal_arr[] = $projects_sum;

      //introductie koring
      $intro_discount = 0;
      $intro_discount_project = false;
      if ($companyId !== 0) {
        $company = CrmCompanies::find_by(['companyId' => $companyId]);
        if ($company) {
          if ($company->introDiscount == 1) {
            $intro_discount = 50;
            $intro_discount_project = new Projects();
            $intro_discount_project->name = 'Introductie korting';
            $intro_discount_project->size = 1;
            $intro_discount_project->euro = -50;
            $intro_discount_project->quotationId = $quotation->quotationId;
            $intro_discount_project->orderNr = 8;
            $intro_discount_project->showOnProductionPage = 0;
            $intro_discount_project->productFromWebshop = 0;
          }
        }
      }

      //freightcosts
      if ($meters_total > 40) {
        $freight_costs = 0.00;
        $freight_costs_orig = 0.00;
      }
      else {
        if ($check_for_webshop || $ophalen) {
          $freight_costs = 0.00;
          $freight_costs_orig = 0.00;
        }
        else {
          $freight_costs = 50.00;
          $freight_costs_orig = 50.00;
          if ($special_freight_costs > 0) {
            $freight_costs = $special_freight_costs;
          }
        }
      }
      $subtotal_arr[] = $freight_costs;

      $invoice_party = CrmInvoiceparties::find_by(['companyId' => $user->companyId]);
      $vatRegShifted = 3;
//      if (!empty($invoice_party->vatRegNo) && $company->country !== "NL") {
//        $vatRegShifted = 1;
//      }
      if ($foreign) {
        $vatRegShifted = 1;
      }

      $vat_rate = VatRates::find_by(['id' => $vatRegShifted]);
      $btw = $vat_rate->vatRate / 100;
      $project_value_freight = ($projectValueSum - $intro_discount) + $freight_costs + $projects_sum;
      $total_btw = $project_value_freight * $btw;
      $project_value_total = $project_value_freight + $total_btw;

      $this->invoice_number = $lastInvoiceNumber + 1;
      $this->meters_total = $meters_total;
      $this->intro_discount = 0;
      if ($intro_discount > 0) {
        $this->intro_discount = -50;
      }
      $this->freight_costs = $freight_costs;
      $this->subtotal = array_sum($subtotal_arr) - $intro_discount;
      $this->total_btw = round($total_btw, 2);
      $this->total_project_value = round($project_value_total, 2);
      $this->quotations = $quotations;
      $this->projects = $projects_arr;

      if (isset($_POST['go'])) {
        if ($intro_discount_project) {
          $intro_discount_project->save();
          $company->introDiscount = 0;
          $company->save();
        }
        $new_invoice = new Invoices();

        $project_value_total_new = $project_value_total;

        $new_freight_costs = (float)$_POST['freight_cost'];
        if ($special_freight_costs) {
          $new_freight_costs = $special_freight_costs;
        }

        if (isset($_POST['freight_cost'])) {
          $project_value_freight = ($projectValueSum - $intro_discount) + $new_freight_costs + $projects_sum;
          $total_btw = $project_value_freight * $btw;
          $project_value_total_new = $project_value_freight + $total_btw;
        }

        if ($webshop_freight_costs > 0) {
          $project_value_freight = ($projectValueSum - $intro_discount) + $webshop_freight_costs + $projects_sum;
          $total_btw = $project_value_freight * $btw;
          $project_value_total_new = $project_value_freight + $total_btw;
        }

        $new_invoice->userId = $user->userId;
        $new_invoice->vatRegShifted = $vatRegShifted;
        $new_invoice->invoiceNumber = $lastInvoiceNumber += 1;
        $new_invoice->freightCosts = $freight_costs_orig;
        if (isset($_POST['freight_cost'])) {
          $new_invoice->freightCosts = $_POST['freight_cost'];
        }
        $new_invoice->totalProjectValue = round($project_value_total_new, 2);
        if ($webshop_freight_costs > 0) {
          $new_invoice->freightCosts = $webshop_freight_costs;
        }
        $new_invoice->meterAmount = $meters_total;
        if (!empty($_POST['invoice_notes'])) {
          $new_invoice->invoiceNotes = trim($_POST['invoice_notes']);
        }

        if ($all_quotations_paid) {
          $new_invoice->paid = $prod_date;
        }
        if ($mollie_id != null) {
          $new_invoice->paid_with = 'online';
          $new_invoice->mollie_id = $mollie_id;
        }

        $new_invoice->save();

        //update quotations
        foreach ($quotations as $quotation) {
          $quotation->invoiceId = $new_invoice->invoiceId;
          $quotation->invoiceDate = date("Y-m-d");
          $quotation->invoiceNumber = $new_invoice->invoiceNumber;
          $quotation->statusId = 60;
          $quotation->save();
        }

        if ($user->private) {
          $extension = 'PRIVATE';
        }
        else {
          $extension = 'COMMERCIAL';
        }

        //@TODO checken of dit werkt
//        if (!DEVELOPMENT) {
//          $this->executeInvoicesend($new_invoice->invoiceId);
//        }

        MailsFactory::sendNewInvoice($new_invoice, "MAIL_INVOICE_" . $extension);
        ResponseHelper::redirectMessage('De factuur is succesvol aangemaakt en verzonden', reconstructQueryAdd());
      }
    }

    /**
     * Verzend specifiek factuur
     * @throws Exception
     */
    public function executeInvoicesend($invoice_id) {

      $user = User::getUserWithOrganById(ADMIN_DEFAULT_ID);
      $multivers = Multivers::find_by(["organisation_id" => $user->organisation_id]);
      $multivers_api = new MultiversApi($multivers);
      $invoice_service = new InvoiceSave($multivers_api);

      if (!isset($invoice_id)) {
        RestUtils::sendResponseError("InvoiceId niet gezet.");
      }

      $invoice = Invoices::find_by(["invoiceId" => $invoice_id]);
      if (!$invoice) {
        RestUtils::sendResponseError("Factuur niet gevonden");
      }
      $result = false;
      try {
        if (!DEVELOPMENT) {
          $result = $invoice_service->add($invoice);
        }
        if ($result === false) {
          RestUtils::sendResponseError("Niet verzonden, factuurnummer " . $invoice->invoiceNumber . ': onbekende fout');
        }
        RestUtils::sendResponseOK("Factuur met factuurnummer " . $invoice->invoiceNumber . " verzonden");
      }
      catch (MultiversException $e) {
        RestUtils::sendResponseError("Niet verzonden, factuurnummer " . $invoice->invoiceNumber . ': ' . $e->getMessage());
        logToFile("multivers_error", "Niet verzonden, factuurnummer " . $invoice->invoiceNumber . "\n" . $e->getMessage() . ' ' . $e->getTraceAsString());
      }
    }

    public function executeCreateInvoicePdf() {
      $invoice = Invoices::find_by(["invoiceId" => $_GET["id"]]);

      $pdf = new InvoicePdf($invoice->invoiceId);
      $pdf->setShow(true);
      $pdf->generatePdf();
    }

    public function executeInvoices_not_sendlist() {
      $this->executeInvoices_not_sendlistFilters();
    }

    public function executeInvoices_not_sendlistFilters() {
      $dataTable = new DataTable('invoices_not_send');
      $dataTable->setRequestUrl(reconstructQueryAdd() . "action=invoices_not_sendlistajax");
      $dataTable->addColumnHelper("invoiceNumber", "Factuur nr.");
      $dataTable->addColumnHelper("name", "Bedrijfsnaam");
      $dataTable->addColumnHelper("dateInvoice", "Datum");
      $dataTable->addColumnHelper("totalProjectValue", "Bedrag");
      $dataTable->addColumnHelper("partly_paid", "gedeeltelijk betaald");
      $dataTable->addColumnHelper("payment_remainder", "Nog te betalen");
      $dataTable->addColumnHelper("email", "E-mail");

      $dataTable->addColumnHelper("actions", "Acties");

      $dataTable->getColumn("actions")->setSortable(false);
      $dataTable->setDefaultSort("dateInvoice", "Desc");

      $dataTable->addSearchInput();
      $dataTable->addSearchReset();

      $dataTable->handleRequest($_POST);
      $this->dataTable = $dataTable;
    }

    public function executeInvoices_not_sendlistajax() {
      $this->executeInvoices_not_sendlistFilters();

      $filter_query = "";
      $filter_query .= "LEFT JOIN " . SandboxUsers::getTablename() . " ON sandbox_users.userId = invoices.userId ";
      $filter_query .= "LEFT JOIN " . CrmCompanies::getTablename() . " ON sandbox_users.companyId = crm_companies.companyId ";
      $filter_query .= "WHERE invoices.paid IS NULL AND crm_companies.executorTicket = 1 AND invoices.totalProjectValue > 0 AND invoices.send_multivers = 0 AND invoices.dateInvoice IS NULL ";

      if ($this->dataTable->hasFormElementValue("search")) {
        $searchstr = DbHelper::escape($this->dataTable->getFormElementValue("search"));
        $filter_query .= "AND ( ";
        $filter_query .= "crm_companies.name LIKE '%" . $searchstr . "%' ";
        $filter_query .= ") ";
      }

      /** TOTALS */
      $total_count = Invoices::count_all_by([]);
      $total_count_filtered = Invoices::count_all_by([], $filter_query);

      /** GET DATA */
      $query = "SELECT * FROM " . Invoices::getTablename() . " ";
      $query .= $filter_query;
      $query .= $this->dataTable->getSortQuery();
      $query .= $this->dataTable->getPager()->getLimitQuery();

      $result = DBConn::db_link()->query($query);

      $table_data = [];
      while ($row = $result->fetch_array()) {
        $column_count = 0;

        $invoice = (new Invoices())->hydrateNext($row, $column_count);
        $user = (new SandboxUsers())->hydrateNext($row, $column_count);
        $company = (new CrmCompanies())->hydrateNext($row, $column_count);

        //gedeeltelijk betaald
        $total_project_value = $invoice->totalProjectValue;
        $partly_paid_check = false;
        $payment_remainder = $invoice->totalProjectValue;
        $nog_te_betalen = $invoice->totalProjectValue;
        if ($invoice->payment_1 > 0) {
          $partly_paid_check = true;
          $payment_remainder = StringHelper::asMoney($invoice->payment_1) . ' op: ' . DateTimeHelper::formatDateTime($invoice->payment_1_date, "dd-MM-Y");
          $nog_te_betalen -= $invoice->payment_1;
        }
        if ($invoice->payment_2 > 0) {
          $payment_remainder = StringHelper::asMoney($invoice->payment_2) . ' op: ' . DateTimeHelper::formatDateTime($invoice->payment_2_date, "dd-MM-Y");
          $nog_te_betalen -= $invoice->payment_2;
        }
        if ($invoice->payment_3 > 0) {
          $payment_remainder = StringHelper::asMoney($invoice->payment_3) . ' op: ' . DateTimeHelper::formatDateTime($invoice->payment_3_date, "dd-MM-Y");
          $nog_te_betalen -= $invoice->payment_3;
        }
        if ($invoice->payment_4 > 0) {
          $payment_remainder = StringHelper::asMoney($invoice->payment_4) . ' op: ' . DateTimeHelper::formatDateTime($invoice->payment_4_date, "dd-MM-Y");
          $nog_te_betalen -= $invoice->payment_4;
        }


        if ($partly_paid_check) {
          $partly_paid = '<a style="color: #982e1a;" href="javascript: void(0);" class="qtipa fa_info fa fa-info-circle" title="Laatst betaald: ' . ($payment_remainder) . '" partly_paid="' . $payment_remainder . '"
                  monster_id="' . $invoice->invoiceId . '" data-caption="Gedeeltelijk betaald"></a>';
        }
        else {
          $partly_paid = '';
        }


        $actions_html = "";
        $actions_html .= " " . BtnHelper::getPrintPDF('?action=createinvoicepdf&id=' . $invoice->invoiceId, __('Bekijk pdf'), '_blank');
        $actions_html .= " " . '<a style="vertical-align: middle;" href=?action=send_invoice&id=' . $invoice->invoiceId . ' >' . IconHelper::getEmail() . '</a>';


        $table_data[] = [
          'DT_RowId'          => $invoice->invoiceId,
          'invoiceNumber'     => $invoice->invoiceNumber,
          'name'              => $company->name,
          'dateInvoice'       => $invoice->dateInvoice,
          'totalProjectValue' => $total_project_value,
          'partly_paid'       => $partly_paid,
          'payment_remainder' => $nog_te_betalen,
          'email'             => $company->email,
          'actions'           => $actions_html,
        ];
      }

      /** RETURN DATA AS JSON */
      ResponseHelper::exitAsJson([
        'data'            => $table_data,
        'recordsTotal'    => $total_count,
        'recordsFiltered' => $total_count_filtered,
        'draw'            => (int)$_POST['draw'],
      ]);
    }

    public function executeSend_invoice() {
      $invoice = Invoices::find_by(['invoiceId' => $_GET['id']]);
      $user = SandboxUsers::find_by(['userId' => $invoice->userId]);
      //$invoice_party = CrmInvoiceparties::find_by(['companyId' => $user->companyId]);

      if ($user->private) {
        $extension = 'PRIVATE';
      }
      else {
        $extension = 'COMMERCIAL';
      }

      MailsFactory::sendNewInvoice($invoice, 'MAIL_INVOICE_' . $extension);
      ResponseHelper::redirectMessage('De factuur is succesvol verzonden', reconstructQueryAdd());
    }

    public function executeDocopen() {
      $file = Files::find_by(["fileId" => $_GET["id"]]);
      if (!$file) {
        MessageFlashCoordinator::addMessageAlert("Bestand niet gevonden, of u heeft geen rechten.");
        ResponseHelper::redirect(reconstructQueryAdd());
      }
      $filepath = DIR_ROOT_HTTPDOCS . $file->folder . '/' . $file->filename;
      if (!file_exists($filepath)) {
        MessageFlashCoordinator::addMessageAlert("Bestand niet gevonden.");
        ResponseHelper::redirect(reconstructQueryAdd());
      }
      FileHelper::getFileAndOuput($filepath);
      ResponseHelper::exit();
    }

  }