<style>
  td:last-child, th:last-child {
    text-align: right;
  }
  .pickup {
    padding: .3em 1em !important;
    margin-right: .5em;
  }

  .form-group,
  form > div {
    margin-bottom: 1rem;
  }

  label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: bold;
  }

  input[type="text"],
  select {
    width: 300px;
    height: 34px;
    border: 1px solid #ccc;
    border-radius: 4px;
  }
</style>

<div style="margin: 5em auto; width: 1200px;">
  <h1>Inkomend gesprek</h1>
  <?php if (!empty($calledAt)): ?>
    <p><strong>Gebeld op:</strong> <?php echo date('d-m-Y H:i', strtotime($calledAt)); ?></p>
  <?php endif; ?>
  <br>
  <form id="search-form" method="get">
    <?php if ($phone && empty($companies) && empty($unlinked)): ?>
      <input id="phone" type="hidden" name="phone" value="<?php echo $_GET['phone'] ?? ''; ?>"/>
      <div class="form-group">
        <label for="company">Bedrijfsnaam:</label>
        <input id="company" type="text" name="company" value="<?php echo $_GET['company'] ?? ''; ?>" placeholder="naam"/>
      </div>
      <div class="form-group">
        <label for="domestic">Plaats:</label>
        <select id="domestic" name="domestic">
          <?php if (isset($_GET['domestic']) && $_GET['domestic']): ?>
            <option value="<?php echo $_GET['domestic'] ?>"><?php echo $_GET['domestic'] ?></option>
          <?php else: ?>
            <option value="">Kies een plaats</option>
          <?php endif; ?>
        </select>
      </div>
    <?php else: ?>
      <div class="form-group">
        <label for="phone">Telefoonnummer:</label>
        <input id="phone" type="text" name="phone" value="<?php echo $_GET['phone'] ?? ''; ?>" placeholder="telefoonnummer"/>
      </div>
    <?php endif; ?>
    <input type="submit" value="Zoek"/>
  </form>

  <?php if (empty($_GET['phone'])): ?>
    <p>Vul een telefoonnummer in</p>
  <?php elseif (empty($companies) && empty($unlinked) && empty($companiesFromNameDomesticSearch)): ?>
    <p style="margin-bottom: 2em; max-width: 600px;">
      Geen bedrijven gevonden met nummer <?php echo $phone ?>.
      <a href="<?php echo PageMap::getUrl("M_BEDRIJVENGIDS_EDIT", ['phone' => $phone]) ?>"><u>Maak bedrijf aan in bedrijvengids</u></a>
      of zoek op naam of plaats naar een bedrijf om dit nummer aan te koppelen.
    </p>
  <?php endif; ?>

  <?php if (!empty($companiesFromNameDomesticSearch)): ?>
    <table class="default_table">
      <tr class="dataTableHeadingRow">
        <td>Naam</td>
        <td>Plaats</td>
        <td>Email</td>
        <td>Telefoon</td>
        <td>Acties</td>
      </tr>
      <?php foreach ($companiesFromNameDomesticSearch as $company): ?>
        <tr class="dataTableRow trhover">
          <td><?php echo $company->name ?></td>
          <td><?php echo $company->address->domestic ?></td>
          <td><a href="mailto:<?php echo $company->email ?>"><?php echo $company->email ?></a></td>
          <td><a href="tel:<?php echo $company->phone ?>"><?php echo $company->phone ?></a></td>
          <td style="text-align: right">
            <?php echo BtnHelper::getEdit(PageMap::getUrl('M_BEDRIJVENGIDS_EDIT', ['companyid' => $company->companyId])); ?>
            <?php echo BtnHelper::getEmail($company->email) ?>
            <?php echo BtnHelper::getPlus(PageMap::getUrl('M_BEDRIJVENGIDS_PERSONS', ['action' => 'personedit', 'companyid' => $company->companyId, 'phone' => $phone])); ?>
          </td>
        </tr>
      <?php endforeach; ?>
    </table>
  <?php endif; ?>

  <?php if (!empty($companies)): ?>
    <?php foreach ($companies as $company): ?>
      <h2>
        <a href="<?php echo PageMap::getUrl("M_BEDRIJVENGIDS_EDIT", ['companyid' => $company->companyId]) ?>"><?php echo $company->name; ?></a>
      </h2>

      <strong>Personen</strong>
      <?php if (count($company->users)): ?>
      <table class="default_table">
        <tr class="dataTableHeadingRow">
          <td>Naam</td>
          <td>Email</td>
          <td>Telefoon</td>
          <td>Mobiel</td>
          <td>Plaatsnaam</td>
          <td>Acties</td>
        </tr>
        <?php foreach ($company->users as $user): ?>
          <tr class="dataTableRow trhover">
            <td>
              <?php echo $user->getNaam() ?>
              <?php if ($user->isBlocked) echo IconHelper::getCrossRed("geblokkeerd") ?>
            </td>
            <td><a href="mailto:<?php echo $user->email ?>"><?php echo $user->email ?></a></td>
            <td><a href="tel:<?php echo $user->phone ?>"><?php echo $user->phone ?></a></td>
            <td><a href="tel:<?php echo $user->mobile ?>"><?php echo $user->mobile ?></a></td>
            <td><?php echo $user->domestic ?? $company->address?->domestic ?? '' ?></td>
            <td style="text-align: right">
              <?php if ($user instanceof CrmPersons): ?>
                <?php echo BtnHelper::getEdit(PageMap::getUrl('M_BEDRIJVENGIDS_PERSONS', ['action' => 'personedit', 'companyid' => $company->companyId, 'personid' => $user->personId])); ?>
              <?php else: ?>
                <?php echo BtnHelper::getEdit(PageMap::getUrl('M_SANDBOXUSERS', ['action' => 'useredit', 'id' => $user->userId])); ?>
              <?php endif; ?>
              <?php echo BtnHelper::getEmail($user->email) ?>
              <?php if (!$user->isBlocked) echo BtnHelper::getLogin($user->directLoginLink) ?>
            </td>
          </tr>
        <?php endforeach; ?>
      </table>
      <?php else: ?>
        <p>Geen personen gevonden.</p>
      <?php endif; ?>

      <br><br>

      <strong>Actieve bestellingen</strong>
      <?php if (count($company->quotations)): ?>
      <table class="default_table">
        <tr class="dataTableHeadingRow">
          <td>Status</td>
          <td>Geplanned</td>
          <td>Offertenummer</td>
          <td>Offertedatum</td>
          <td>Meters</td>
          <td>Plaats</td>
          <td>Week</td>
          <td>Acties</td>
        </tr>
        <?php foreach ($company->quotations as $quotation): ?>
          <tr class="dataTableRow trhover">
            <td><?php echo Status::getIconHTML($quotation->statusId) ?></td>
            <td><?php echo $quotation->plannedDate ? date('d-m-Y', strtotime($quotation->plannedDate)) : '' ?></td>
            <td>
              <a href="<?php echo PageMap::getUrl("M_RDE_ORDERS_GENERAL", ['id' => $quotation->quotationId]) ?>"><?php echo $quotation->getQuotationNumberFull() ?></a>
            </td>
            <td><?php echo $quotation->getQuotationDate() ?></td>
            <td><?php echo $quotation->meters ?></td>
            <td><?php echo $quotation->domestic ?></td>
            <td><?php echo $quotation->dueDateWeek ?></td>
            <td>
              <?php echo BtnHelper::getEdit(PageMap::getUrl("M_RDE_ORDERS_GENERAL", ['id' => $quotation->quotationId])) ?>
              <?php echo BtnHelper::getPrintPDF(PageMap::getUrl('M_RDE_ORDERS_GENERAL', ['action' => 'showpdf', 'quotation_id' => $quotation->quotationId])) ?>
            </td>
          </tr>
        <?php endforeach; ?>
      </table>
      <?php else: ?>
        <p>Geen bestellingen gevonden.</p>
      <?php endif; ?>

      <br><br>

      <strong>Openstaande Facturen</strong>
      <?php if (count($company->openInvoices)): ?>
      <table class="default_table">
        <tr class="dataTableHeadingRow">
          <td>Factuurnummer</td>
          <td>Bedrag</td>
          <td>Dagen</td>
          <td>Factuurdatum</td>
          <td>Betaaldatum</td>
          <td>Acties</td>
        </tr>
        <?php foreach ($company->openInvoices as $invoice): ?>
          <tr class="dataTableRow trhover">
            <td><?php echo $invoice->invoiceNumber ?></td>
            <td>€ <?php echo getLocalePrice($invoice->totalProjectValue) ?></td>
            <td><?php echo $invoice->getInvoicedateDays() ?> / <?php echo $company->paymentTerm ?></td>
            <td><?php echo $invoice->getDateInvoice() ?></td>
            <td>N.V.T</td>
            <td>
              <?php echo BtnHelper::getEdit( PageMap::getUrl('M_INVOICES_RDE', ['action' => 'invoiceedit', 'id' => $invoice->invoiceId])) ?>
              <?php echo BtnHelper::getPrintPDF(PageMap::getUrl('M_INVOICES_RDE', ['action' => 'createinvoicepdf', 'id' => $invoice->invoiceId])) ?>
            </td>
          </tr>
        <?php endforeach; ?>
      </table>
      <?php else: ?>
        <p>Geen openstaande facturen gevonden.</p>
      <?php endif; ?>

      <br><br>

      <strong>Recent betaalde facturen</strong>
      <?php if (count($company->paidInvoices)): ?>
      <table class="default_table">
        <tr class="dataTableHeadingRow">
          <td>Factuurnummer</td>
          <td>Bedrag</td>
          <td>Dagen</td>
          <td>Factuurdatum</td>
          <td>Betaaldatum</td>
          <td>Acties</td>
        </tr>
        <?php foreach ($company->paidInvoices as $invoice): ?>
          <tr class="dataTableRow trhover">
            <td><?php echo $invoice->invoiceNumber ?></td>
            <td>€ <?php echo getLocalePrice($invoice->totalProjectValue) ?></td>
            <td><?php echo $invoice->getInvoicedateDays() ?> / <?php echo $company->paymentTerm ?></td>
            <td><?php echo $invoice->getDateInvoice() ?></td>
            <td><?php echo $invoice->getPaid() ?></td>
            <td>
              <?php echo BtnHelper::getEdit( PageMap::getUrl('M_INVOICES_RDE', ['action' => 'invoiceedit', 'id' => $invoice->invoiceId])) ?>
              <?php echo BtnHelper::getPrintPDF(PageMap::getUrl('M_INVOICES_RDE', ['action' => 'createinvoicepdf', 'id' => $invoice->invoiceId])) ?>
            </td>
          </tr>
        <?php endforeach; ?>
      </table>
      <?php else: ?>
        <p>Geen recent betaalde facturen gevonden.</p>
      <?php endif; ?>

      <br><br>

      <strong>Bakken en rekken</strong>
      <?php if (count($company->containers)): ?>
      <table class="default_table">
        <tr class="dataTableHeadingRow">
          <td>Ophalen</td>
          <td>Bak nummer</td>
          <td>Offerte nummer</td>
          <td>Plaats</td>
          <td>Adres</td>
          <td>Acties</td>
        </tr>
        <?php foreach ($company->containers as $container): ?>
          <tr class="dataTableRow trhover">
            <td><?php echo $container->containerQuotation->nextroute === 'true' ? 'Ja' : 'Nee' ?></td>
            <td><?php echo $container->containerNumber ?></td>
            <td>
              <a href="<?php echo PageMap::getUrl("M_RDE_ORDERS_GENERAL", ['id' => $container->quotation->quotationId]) ?>"><?php echo $container->quotation->getQuotationNumberFull() ?></a>
            </td>
            <td><?php echo $container->quotation->domestic ?></td>
            <td><?php echo $container->quotation->getAddress() ?></td>
            <td>
              <form method="post">
                <input type="hidden" value="<?php echo $container->containerQuotation->containerQuotationId ?>" name="containerQuotationId"/>
                <input type="submit" value="<?php if($container->containerQuotation->nextroute === 'true') echo 'niet ' ?>ophalen" name="pickup" class="pickup"/>
              </form>
              <?php echo BtnHelper::getEdit(PageMap::getUrl('M_RDE_ORDERS_CONTAINERS_LIST', ['action' => 'containerOverview', 'containerid' => $container->containerId])) ?>
            </td>
          </tr>
        <?php endforeach; ?>
      </table>
      <?php else: ?>
        <p>Geen bakken en rekken gevonden.</p>
      <?php endif; ?>

      <?php if ($company !== end($companies)): ?>
        <br><br>
      <?php endif; ?>
    <?php endforeach; ?>
  <?php endif; ?>

  <?php if (!empty($unlinked)): ?>
    <h2>Personen zonder bedrijf</h2>
    <table class="default_table">
      <tr class="dataTableHeadingRow">
        <td>Naam</td>
        <td>Telefoon</td>
        <td>Mobiel</td>
        <td>Acties</td>
      </tr>
      <?php foreach ($unlinked as $user): ?>
        <tr class="dataTableRow trhover">
          <td><?php echo $user->getNaam() ?></td>
          <td><a href="tel:<?php echo $user->phone ?>"><?php echo $user->phone ?></a></td>
          <td><a href="tel:<?php echo $user->mobile ?>"><?php echo $user->mobile ?></a></td>
          <td style="text-align: right">
            <?php echo BtnHelper::getEdit(PageMap::getUrl('M_SANDBOXUSERS', ['action' => 'useredit', 'id' => $user->userId])) ?>
            <?php echo BtnHelper::getSearch(PageMap::getUrl('M_SANDBOXUSERS', ['search' => $user->companyName])) ?>
          </td>
        </tr>
      <?php endforeach; ?>
  <?php endif; ?>
</div>

<script type="text/javascript">
  $(document).ready(function () {
    $('#domestic').select2({
      ajax: {
        url: '?action=domesticSearchAjax',
        dataType: 'json',
        delay: 250,
        data: (params) => ({ search: params.term, type: "public" }),
        processResults: (data, params) => {
          if (!params.term) data = [{ id: 0, text: 'Kies een plaats' }, ...data];
          return { results: data };
        },
      }
    });
  });
</script>