<?php
  TemplateHelper::includePartial('_tabs.php', 'sandbox');
  include("_header.php")
?>

<?php writeErrors(array_merge($form_invoiceparty_address->getErrors(),$form_invoiceparty->getErrors(),$form_invoiceparty_company->getErrors()), true); ?>

<form method="post" name="form" id="form" class="edit-form">

  <table class="default_table">
    <tr class="dataTableHeadingRow">
      <td colspan="2">Factuurgegevens</td>
    </tr>
    <?php foreach ($form_invoiceparty_address->getElements() as $element): ?>
      <?php $element->renderRow() ?>
    <?php endforeach; ?>
    <?php foreach ($form_invoiceparty->getElements() as $element): ?>
      <?php $element->renderRow() ?>
    <?php endforeach; ?>
    <?php foreach ($form_invoiceparty_company->getElements() as $element): ?>
      <?php $element->renderRow() ?>
    <?php endforeach; ?>
    <td><b>Gem. aantal dagen factuur</b></td>
    <td><?php echo $quotation_days ?? '' ?></td>

  </table>

  <input type="submit" name="go" value="Opslaan" class="gsd-btn gsd-btn-primary"/>
  <input type="submit" name="go_list" value="Opslaan en naar lijst" class="gsd-btn gsd-btn-secondary"/>

</form>

<h2>Adresgegevens</h2>

<form method="post" name="adres_form" id="adres_form" class="edit-form">
  <!--  <p>Verzendmethode:</p>-->
  <label class="label-address">
    <p style="padding-left: 15px;" class="checkbox-address">Bezoekadres - <?php echo $visitAddress->street . ' ' . $visitAddress->nr .  ', ' . $visitAddress->zipcode . ' ' . $visitAddress->domestic  ?></p>
    <input style="margin-left: 15px;" type="radio" checked value="1" name="address_radio" class="address_radio" id="checkbox_visit">
  </label>
  <label class="label-address">
    <p style="padding-left: 15px;" class="checkbox-address">Postadres - <?php echo $postAddress->street . ' ' . $postAddress->nr .  ', ' . $postAddress->zipcode . ' ' . $postAddress->domestic  ?></p>
    <input style="margin-left: 15px; margin-bottom: 15px;" type="radio" value="2" name="address_radio" class="address_radio" id="checkbox_post">
  </label>
  <div class="form1-container">
    <div id="form">
      <table class="default_table table-border default_table_center" style="border: 1px solid var(--gray-200); border-radius: 12px;">
        <tr class="dataTableHeadingRow">
          <td>Eigenschap</td>
          <td>Waarde</td>
        </tr>
        <tr class="dataTableRow">
          <td>Straat</td>
          <td><input id="street_2" name="street_2" type="text" value="<?php echo $visitAddress->street ?>"></td>
        </tr>
        <tr class="dataTableRow">
          <td>Nummer</td>
          <td><input id="number_2" name="number_2" type="number" value="<?php echo $visitAddress->nr ?>"></td>
        </tr>
        <tr class="dataTableRow">
          <td>Extensie</td>
          <td><input id="extension_2" name="extension_2" type="text" value="<?php echo $visitAddress->extension ?>"></td>
        </tr>
        <tr class="dataTableRow">
          <td>Postcode</td>
          <td><input id="zip_2" name="zip_2" type="text" value="<?php echo $visitAddress->zipcode ?>"></td>
        </tr>
        <tr class="dataTableRow">
          <td>Plaats</td>
          <td><input id="city_2" name="city_2" type="text" value="<?php echo $visitAddress->domestic ?>"></td>
        </tr>
        <tr class="dataTableRow">
          <td>Land</td>
          <td><input id="country_2" name="country_2" type="text" value="<?php echo $visitAddress->country ?>"></td>
        </tr>
        <!--    <tr class="dataTableRow">-->
        <!--      <td>Locaties</td>-->
        <!--      <td>--><?php //echo '?' ?><!--</td>-->
        <!--    </tr>-->
      </table>
      <br/>
      <input type="submit" name="save_address" value="Opslaan" class="gsd-btn gsd-btn-primary" style="margin: 16px 0 16px 8px;">
    </div>
  </div>
</form>

<h1 style="margin: 30px 0 30px 0">Betaalrekeningen</h1>

<form>
  <a href="?action=bankaccountedit&companyid=<?php echo $company->companyId ?>" class="gsd-btn gsd-btn-primary" style="margin-bottom: 17px">Nieuwe rekening toevoegen</a>
</form>

<table class="default_table">
  <tr class="dataTableHeadingRow">
    <td>IBAN</td>
    <td>Bewerken</td>
    <td>Verwijderen</td>
  </tr>
  <?php foreach ($bankaccounts as $bankaccount): ?>
    <tr class="dataTableRow trhover">
      <td><?php echo $bankaccount->IBAN ?></td>
      <td><?php echo BtnHelper::getEdit(reconstructQueryAdd(['pageId']) . 'action=bankaccountedit&bankaccountid=' . $bankaccount->accountId, __('Bewerk locatie')) ?></td>
      <td><?php echo BtnHelper::getRemove(reconstructQueryAdd(['pageId']) . 'action=bankaccountdelete&bankaccountid=' . $bankaccount->accountId, __('Verwijder locatie')) ?></td>
    </tr>
  <?php endforeach; ?>
</table>

<script>
  var checkbox_visit    = $("#checkbox_visit");
  var checkbox_post   = $("#checkbox_post");

  var street      = $("#street_2");
  var number      = $("#number_2");
  var extension   = $("#extension_2");
  var zip         = $("#zip_2");
  var city        = $("#city_2");

  if ($(".address_radio").change(function (event) {

    if (checkbox_visit.is(':checked')) {
      street.val('<?php echo $visitAddress->street ?>');
      console.log(street.val());
      number.val('<?php echo $visitAddress->nr ?>');
      extension.val('<?php echo $visitAddress->extension ?>');
      zip.val('<?php echo $visitAddress->zipcode ?>');
      city.val('<?php echo $visitAddress->domestic ?>');
    }
    if (checkbox_post.is(':checked')) {
      street.val('<?php echo $postAddress->street ?>');
      console.log(street.val());
      number.val('<?php echo $postAddress->nr ?>');
      extension.val('<?php echo $postAddress->extension ?>');
      zip.val('<?php echo $postAddress->zipcode ?>');
      city.val('<?php echo $postAddress->domestic ?>');
    }
  }));
</script>