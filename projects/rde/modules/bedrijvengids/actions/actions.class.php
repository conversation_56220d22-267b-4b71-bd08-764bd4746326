<?php

  use Gsd\DataTable\DataTable;
  use Gsd\Form\Elements\A;
  use Gsd\Form\Elements\Checkbox;
  use Gsd\Form\Elements\Option;
  use Gsd\Form\Elements\Select;
  use Gsd\Form\ModelForm;

  class bedrijvengidsRdeActions extends gsdActions {

    public function executeCompanyedit() {

//      $quotations = Quotations::find_all_by(['statusId' => [Status::STATUS_ORDER, Status::STATUS_CHECKED, Status::STATUS_PREPARED, Status::STATUS_IN_PRODUCTION, Status::STATUS_PRODUCED, Status::STATUS_PACKED, Status::STATUS_LOADED, Status::STATUS_DELIVERED]], ' AND dueDate >  DATE_ADD(NOW(), INTERVAL -1 WEEK) ');
//
//
//      $quotationsWithSameZip = [];
//      foreach ($quotations as $quotation) {
//        $zipcode = substr($quotation->zipcode,0,-2);
//
//        if (!in_array($zipcode, $quotationsWithSameZip)) {
//          $quotationsWithSameZip[$zipcode][] = $quotation;
//        }
//      }

//      dumpe($quotationsWithSameZip);

      $company = new CrmCompanies();
      $company->phone = $_GET['phone'] ?? '';

      $visitAddress = new CrmAddresses();
      $invoiceParty = new CrmInvoiceparties();
      $post_address = new CrmAddresses();
      $is_double_address = false;
      if (isset($_GET['companyid'])) {
        $company = CrmCompanies::find_by(['companyId' => $_GET['companyid']]);
        if (!$company) {
          ResponseHelper::redirectNotFound("Bedrijf niet gevonden.");
        }
        $visitAddress = CrmAddresses::find_by(["companyId" => $company->companyId, "type" => 'visit']);
        if (!$visitAddress) $visitAddress = new CrmAddresses();
        $invoiceParty = CrmInvoiceparties::find_by(["companyId" => $company->companyId]);
        if (!$invoiceParty) $invoiceParty = new CrmInvoiceparties();
        $post_address = CrmAddresses::find_by(["companyId" => $company->companyId]);
        if (!$post_address) $post_address = new CrmAddresses();

        $is_double_address = true;
        $hasDoubleAddress = CrmAddresses::find_by(['companyId' => $company->companyId, 'type' => 'post']);
        if ($hasDoubleAddress) {
          $post_address = $hasDoubleAddress;
          $is_double_address = false;
        }

      }

      $form_company = $this->createCompanyForm($company);
      $form_visit = $this->createAddressForm($visitAddress);
      $form_post = $this->createAddressPostForm($post_address, $is_double_address);

      if (isset($_POST['go']) || isset($_POST['go_list'])) {
        $form_company->setElementsAndObjectValue($_POST, $company);
        $form_visit->setElementsAndObjectValue($_POST, $visitAddress);
        $form_post->setElementsAndObjectValue($_POST, $post_address);

        if ($form_company->isValid() && $form_visit->isValid() && $form_post->isValid()) {

          $company->save();
          $visitAddress->type = CrmAddresses::TYPE_VISIT;
          $visitAddress->companyId = $company->companyId;
          $visitAddress->save();
          if ($post_address->type == CrmAddresses::TYPE_POST || !isset($_GET['companyid'])) {
            $post_address->type = CrmAddresses::TYPE_POST;
            $post_address->companyId = $company->companyId;
            $post_address->save();
          }
          $invoiceParty->companyId = $company->companyId;
          $invoiceParty->addressId = $visitAddress->addressId;
          $invoiceParty->invoiceTerm = $company->paymentTerm;
          $invoiceParty->save();
          $company->visitAddressId = $visitAddress->addressId;
          if ($post_address->type == CrmAddresses::TYPE_POST) {
            $company->postAddressId = $post_address->addressId;
          }
          $company->noDelivery = 0;
          if (isset($_POST['noDelivery']) && $_POST['noDelivery'] == 'on') {
            $company->noDelivery = true;
          }
          $company->save();

          MessageFlashCoordinator::addMessage("Bedrijf opgeslagen.");
          if (isset($_POST['go_list'])) {
            ResponseHelper::redirect(PageMap::getUrl('M_BEDRIJVENGIDS_LIST'));
          }
          ResponseHelper::redirect(reconstructQuery());

        }
      }

      $this->no_delivery = $company->noDelivery;
      $this->form_company = $form_company;
      $this->is_double_address = $is_double_address;
      $this->form_visit = $form_visit;
      $this->form_post = $form_post;

    }

    public function executePersondelete() {

//      dumpe(isset($_GET['personid']));
      if (!isset($_GET['personid'])) {
        ResponseHelper::redirectNotFound("Geen person id gezet", pageMap::getUrl('M_BEDRIJVENGIDS_PERSONS'));
      }

      $person = CrmPersons::find_by(["personId" => $_GET["personid"]]);
      if (!$person) {
        ResponseHelper::redirectNotFound("Persoon niet gevonden.");
      }

      $person->flagForDeletion = 1;
      $person->save();

      ResponseHelper::redirectMessage("Persoon verwijderd.", PageMap::getUrl('M_BEDRIJVENGIDS_PERSONS'));

    }

    public function executePersonedit() {

      $person = new CrmPersons();
      $person->phone = $_GET['phone'] ?? '';
      $person->flagForExecutor = $_GET['executor'] ?? 0;

      $sandboxuser = new SandboxUsers();

      if (isset($_GET['personid'])) {
        $person = CrmPersons::find_by(['personId' => $_GET['personid']]);
        if (!$person) {
          ResponseHelper::redirectNotFound("Persoon niet gevonden.");
        }
        $sandboxuser = SandboxUsers::find_by(['personId' => $person->personId]);
      }
      $company = CrmCompanies::find_by(['companyId' => $_GET['companyid']]);
      $persons = CrmPersons::find_all_by(['companyId' => $company->companyId]);
      $address = CrmAddresses::find_by(['addressId' => $company->visitAddressId]);

      $form_person = $this->createPersonForm($person, $sandboxuser);

      if (isset($_POST['go']) || isset($_POST['go_list'])) {
        $form_person->setElementsAndObjectValue($_POST, $person);

        if ($form_person->isValid()) {
          $person->companyId = $_GET['companyid'];
          $person->save();

          if ($sandboxuser && $sandboxuser->personId == $person->personId) {
            $sandboxuser->invoice_email_confirmed = isset($_POST['invoice_email_confirmed']) ? 1 : 0;
            $sandboxuser->lastDeliveryAddressId = $_POST['person']['lastDeliveryAddressId'] ?? null;

            $sandboxuser->save();
          }

          MessageFlashCoordinator::addMessage("Persoon opgeslagen.");
          if (isset($_POST['go_list'])) {
            ResponseHelper::redirect(PageMap::getUrl('M_BEDRIJVENGIDS_PERSONS'));
          }
          ResponseHelper::redirect(reconstructQuery());
        }
      }

      $this->form_person = $form_person;
      $this->persons = $persons;
      $this->sandboxuser = $sandboxuser;
      $this->company = $company;
      $this->address = $address;
    }

    /**
     * @param $company
     * @return ModelForm
     */
    private function createCompanyForm($company) {

      $form_company = new ModelForm("company");
      $form_company->addClass("edit-form");
      $form_company->buildElementsFromModel($company);

      $select = new Select("Categorie", "categoryId", $company->categoryId);
      $select->addOption(new Option("", "Selecteer categorie..."));
      foreach (CrmCategories::getCategories() as $cat) {
        $select->addOption(new Option($cat->categoryId, $cat->name));
      }
      $form_company->addElement($select);

      $select = new Select("Klantgroep", "customerGroupId", $company->customerGroupId);
      $select->addOption(new Option("", "Selecteer klantgroep..."));
      foreach (CustomerGroups::find_all("ORDER BY name") as $cgroup) {
        $select->addOption(new Option($cgroup->groupId, $cgroup->name));
      }
      $form_company->addElement($select);

      $form_company->setElementsLabel([
        "companyId"           => "Company id",
        "name"                => "Bedrijfsnaam",
        "phone"               => "Telefoonnummer",
        "email"               => "E-mailadres",
        "tradeRegNo"          => "KVK-nummer",
        "establishmentNumber" => "Vestigingsnummer",
        "receiveDM"           => "Ontvangt nieuwsbrief",
        "blocked"             => "Geblokkeerd",
        "url"                 => "Website",
        "categoryId"          => "Categorie",
        "customerGroupId"     => "Klantgroep",
        "paymentTerm"         => "Betaaltermijn",
        "introDiscount"       => "Introductie korting",
        "stJoris"             => "Merk St. Joris",
        "terca"               => "Merk Wienerberger",
        "notes"               => "Notities",
      ], true);
      $form_company->setElementsRequired([
        "name", "paymentTerm"
      ])->getElement("tradeRegNo")->setTextAfterElement('<a target="_blank" href="https://www.kvk.nl/zoeken/handelsregister/?kvknummer=' . $company->tradeRegNo . '" style="padding: 5px;" class="searchkvk" title="Zoek op kvk.nl...">' . IconHelper::getOpenPage() . '</a>');
      $form_company->getElement("companyId")->setReadonly(true);

      return $form_company;

    }

    /**
     * @param CrmAddresses $visitAddress
     * @param bool $double_address
     * @return ModelForm
     */
    private function createAddressForm(CrmAddresses $visitAddress, bool $double_address = false): ModelForm {

      $form_visit = new ModelForm("visit");
      $form_visit->addClass("edit-form");
      $form_visit->buildElementsFromModel($visitAddress);

      $select = new Select("Land", "country", $visitAddress->country);
      $select->addOption(new Option("", "Selecteer land..."));
      $select->addOption(new Option("NL", "Nederland"));
      $select->addOption(new Option("BE", "België"));
      $select->addOption(new Option("DE", "Duitsland"));
      $form_visit->addElement($select);

      $form_visit->setElementsLabel([
        "street"    => "Straat",
        "nr"        => "Nummer",
        "extension" => "Extensie",
        "zipcode"   => "Postcode",
        "domestic"  => "Plaats",
        "country"   => "Land",
      ], true);
      $form_visit->setElementsRequired([
        "street",
        "nr",
        "zipcode",
        "domestic",
      ]);

      if ($visitAddress->type == CrmAddresses::TYPE_POST) {
        if ($double_address) {
          $form_visit->setElementsReadonly([
            "street"    => "Straat",
            "nr"        => "Nummer",
            "extension" => "Extensie",
            "zipcode"   => "Postcode",
            "domestic"  => "Plaats",
            "country"   => "Land",
          ]);
        }
      }

      return $form_visit;
    }

    /**
     * @param CrmAddresses $visitAddress
     * @param bool $double_address
     * @return ModelForm
     */
    private function createAddressPostForm(CrmAddresses $visitAddress, bool $double_address = false) {

      $form_visit = new ModelForm("post");
      $form_visit->addClass("edit-form");
      $form_visit->buildElementsFromModel($visitAddress);

      $select = new Select("Land", "country", $visitAddress->country);
      $select->addOption(new Option("", "Selecteer land..."));
      $select->addOption(new Option("NL", "Nederland"));
      $select->addOption(new Option("BE", "België"));
      $select->addOption(new Option("DE", "Duitsland"));
      $form_visit->addElement($select);

      $form_visit->setElementsLabel([
        "street"    => "Straat",
        "nr"        => "Nummer",
        "extension" => "Extensie",
        "zipcode"   => "Postcode",
        "domestic"  => "Plaats",
        "country"   => "Land",
      ], true);
      $form_visit->setElementsRequired([
        "street",
        "nr",
        "zipcode",
        "domestic",
      ]);

//      if ($double_address) {
//        $form_visit->setElementsReadonly([
//          "street",
//          "nr",
//          "extension",
//          "zipcode",
//          "domestic",
//          "country",
//        ]);
//      }

      return $form_visit;
    }

    private function createAddressesForm($address) {

      $form_visit = new ModelForm("adres");
      $form_visit->addClass("edit-form");
      $form_visit->buildElementsFromModel($address);

      $select = new Select("Land", "country", $address->country);
      $select->addOptionHelper("", "Selecteer land...");
      $select->addOption(new Option("NL", "Nederland"));
      $select->addOption(new Option("BE", "België"));
      $select->addOption(new Option("DE", "Duitsland"));
      $form_visit->addElement($select);

      $form_visit->setElementsLabel([
        "title"     => "Titel",
        "street"    => "Straat",
        "nr"        => "Nummer",
        "extension" => "Extensie",
        "zipcode"   => "Postcode",
        "domestic"  => "Plaats",
        "country"   => "Land",
        "mapExtra"  => "Nuttige punten",
        "extraInfo" => "Extra informatie",
      ], true);
      $form_visit->setElementsRequired([
        "street",
        "nr",
        "zipcode",
      ]);

      $form_visit->getElement("extraInfo")->addAtribute("rows", 8);

      return $form_visit;
    }

    /**
     * @param $company
     * @return ModelForm
     */
    private function createCompanyAddressForm($company) {
      $form_company = new ModelForm("company_address");
      $form_company->addClass("edit-form");
      $form_company->buildElementsFromModel($company);

      $form_company->setElementsLabel([
        "noDelivery"          => "Geen levering op dit adres",
        "phone"               => "Telefoonnummer",
        "fax"                 => "Fax",
        "email"               => "E-mail",
        "establishmentNumber" => "Vestigingsnummer",
        "receiveDMA"          => "Ontvangt nieuwsbrief",
        "blocked"             => "Geblokkeerd",
        "url"                 => "Website",
      ], true);
      $form_company->setElementsRequired([
        "name",
      ])->getElement("email")->setTextAfterElement(BtnHelper::getEmail($company->email));

      return $form_company;

    }

    /**
     * @param CrmPersons $person
     * @param SandboxUsers $sandboxuser
     * @return ModelForm
     */
    private function createPersonForm($person, $sandboxuser) {
      $form_person = new ModelForm("person");
      $form_person->addClass("edit-form");
      $form_person->buildElementsFromModel($person);

      $select = new Select("Geslacht", "gender", $person->gender);
      $select->addOption(new Option("", "Selecteer geslacht..."));
      $select->addOption(new Option("male", "Man"));
      $select->addOption(new Option("female", "Vrouw"));
      $form_person->addElement($select);

      $select_country = new Select("Land", "country", $person->country);
      $select_country->addOption(new Option("", "Selecteer land..."));
      $select_country->addOption(new Option("NL", "Nederland"));
      $select_country->addOption(new Option("BE", "België"));
      $select_country->addOption(new Option("DE", "Duitsland"));
      $form_person->addElement($select_country);

      $labelMap = [
        "gender"            => "Geslacht",
        "firstName"         => "Voornaam",
        "lastName"          => "Achternaam",
        "department"        => "Afdeling",
        "jobtitle"          => "Functie",
        "phone"             => "Telefoon",
        "mobile"            => "Mobiel",
        "email"             => "Email",
        "fax"               => "Fax",
        "flagForDirectMail" => "Direct E-mail pers.",
        "country"           => "Land",
        "notes"             => "Notities",
        "flagForExecutor"   => "Uitvoerder",
        "flagForDeletion"   => "Verwijderen",
      ];

      // If a sandbox user is linked to this person, we want to be able to change the last delivery address with a dropdown
      if ($sandboxuser && $sandboxuser->personId == $person->personId) {
        $select_last_delivery_address = new Select("Laatste leveringsadres", "lastDeliveryAddressId", $sandboxuser->lastDeliveryAddressId);
        $select_last_delivery_address->addOption(new Option("", "Selecteer adres..."));
        foreach (CrmAddresses::find_all_by(['companyId' => $person->companyId]) as $address) {
          $select_last_delivery_address->addOption(new Option($address->addressId, $address->street . ' ' . $address->nr . ' ' . $address->extension . ' ' . $address->zipcode . ' ' . $address->domestic));
        }
        $select_last_delivery_address->setLabel("Laatste leveringsadres");
        $form_person->addElement($select_last_delivery_address);
        $labelMap["lastDeliveryAddressId"] = "Laatste leveringsadres";
      }

      $form_person->setElementsLabel($labelMap, true);
      $form_person->setElementsRequired([
        "lastName",
      ]);

      return $form_person;
    }

    /**
     * @param CrmInvoiceparties $invoiceparty
     * @return ModelForm
     */
    private function createInvoicepartyForm($invoiceparty) {
      $form_invoiceparty = new ModelForm("invoiceparty");
      $form_invoiceparty->addClass("edit-form");
      $form_invoiceparty->buildElementsFromModel($invoiceparty);
      $form_invoiceparty->setElementsLabel([
        "attendant"         => "Ter attentie van",
        "email"             => "Factuur e-mailadres",
        "emailReminder"     => "Aanmaningen e-mailadres",
        "emailConfirmed"    => "Factuur e-mailadres bevestigd",
        "vatRegNo"          => "BTW-nummer",
        "invoicePartyNotes" => "Notities",
        "orders_separate"   => "Per order factureren",
      ], true);
      $form_invoiceparty->setElementsRequired([
        "attendant",
        "email",
      ]);

      return $form_invoiceparty;
    }

    private function createInvoiceparty_companyForm($company) {
      $form_invoiceparty_company = new ModelForm("invoiceparty_company");
      $form_invoiceparty_company->addClass("edit-form");
      $form_invoiceparty_company->buildElementsFromModel($company);
      $form_invoiceparty_company->setElementsLabel([
        "payInAdvance"         => "Vooruit betalen",
        "executorTicket"       => "Uitvoerders bon",
        "cargo_receipt_mail_1" => "Vrachtbon email 1",
        "cargo_receipt_mail_2" => "Vrachtbon email 2",
        "cargo_receipt_mail_3" => "Vrachtbon email 3",
      ], true);

      return $form_invoiceparty_company;
    }

    private function createInvoiceparty_addressForm($address) {
      $form_invoiceparty_address = new ModelForm("invoiceparty_company");
      $form_invoiceparty_address->addClass("edit-form");
      $form_invoiceparty_address->buildElementsFromModel($address);
      $form_invoiceparty_address->setElementsLabel([
        "street"    => "Straat",
        "nr"        => "Huisnummer",
        "extension" => "Toevoeging",
        "zipcode"   => "Postcode",
        "domestic"  => "Stad",
        "country"   => "Land",
      ], true);
//      $form_invoiceparty_address->setElementsReadonly([
//        "street",
//        "nr",
//        "extension",
//        "zipcode",
//        "domestic",
//        "country",
//      ]);

      return $form_invoiceparty_address;
    }

    private function createSettingsForm($company) {
      $form_settings = new ModelForm("settings");
      $form_settings->addClass("edit-form");
      $form_settings->buildElementsFromModel($company);

      $select = new Select("Klantgroep", "customerGroupId", $company->customerGroupId);
      $select->addOption(new Option("", "Selecteer klantgroep..."));
      foreach (CustomerGroups::find_all("ORDER BY name") as $cgroup) {
        $select->addOption(new Option($cgroup->groupId, $cgroup->name));
      }
      $form_settings->addElement($select);

      $select_paymentterm = new Select("Betalingstermijn", "paymentTerm", $company->paymentTerm);
      $select_paymentterm->addOption(new Option("", "Maak een keuze..."));
      $select_paymentterm->addOption(new Option("14", "14"));
      $select_paymentterm->addOption(new Option("21", "21"));
      $select_paymentterm->addOption(new Option("30", "30"));
      $select_paymentterm->addOption(new Option("45", "45"));
      $select_paymentterm->addOption(new Option("60", "60"));
      $select_paymentterm->addOption(new Option("75", "75"));
      $form_settings->addElement($select_paymentterm);

      $form_settings->setElementsLabel([
        "customerGroupId"         => "Klantgroep",
        "paymentTerm"             => "Betalingstermijn",
        "afhalen"                 => "Klant wil orders zelf afhalen",
        "noRack"                  => "Klant wil orders enkel in bak ontvangen",
        "noContainer"             => "Klant wil orders enkel in rek ontvangen",
        "noRackNoContainer"       => "Klant wil orders niet in rek of bak ontvangen",
        "pallet"                  => "Klant wil orders op een pallet",
        "blocked"                 => "Klant toegang tot B1MO ontzeggen",
        "containerDoNotReturn"    => "Container NIET retour",
        "stJoris"                 => "St. Joris",
        "terca"                   => "Terca-Wienerberger",
        "toNumber"                => "Elementen nummeren",
        "callToDelivery"          => "Bellen voor levering",
        "sendMailToClient"        => "Klant bellen",
        "callOrEmailNotes"        => "Klant bellen of mailen",
        "rackContainerReturn"     => "Bak-Rek retour nemen",
        "carrierCode"             => "Vervoerscode",
        "introDiscount"           => "Introductie korting",
        "notes"                   => "Klanten notities",
        "showWeekOrderProduction" => "Weekvolgorde op productiestaat",
      ], true);

      return $form_settings;
    }

    private function createOtherForm($company) {
      $form_other = new ModelForm("other");
      $form_other->addClass("edit-form");
      $form_other->buildElementsFromModel($company);

      $form_other->setElementsLabel([
        "dateLiquidated"  => "Liquidatiedatum",
        "flagForDeletion" => "Verwijderen",
        "lastUpdate"      => "Laatst bewerkt",
        "dateCreated"     => "Datum aangemaakt",
        "projectInfo"     => "Project info",
      ], true);

      $form_other->setElementsReadonly([
        "lastUpdate",
        "dateCreated",
      ]);

      return $form_other;
    }

    private function createDocumentForm($file) {
      $form_document = new ModelForm("files");
      $form_document->addClass("edit-form");
      $form_document->buildElementsFromModel($file);

      $form_document->setElementsLabel([
        "title"       => "Omschrijving",
        "uploadDate"  => "Datum",
        "uploadAlert" => "Alert",
        "notes"       => "Document notities",
      ], true);

      return $form_document;
    }

    private function createBankaccountForm($bankaccount) {
      $form_bankaccount = new ModelForm("bankaccount");
      $form_bankaccount->addClass("edit-form");
      $form_bankaccount->buildElementsFromModel($bankaccount);

      $form_bankaccount->setElementsLabel([
        "IBAN" => "IBAN",
      ], true);

      return $form_bankaccount;
    }

    public function executeUserslist() {
      $this->executeUserslistFilters();
    }

    public function executeUserslistFilters() {

      $dataTable = new DataTable('userslist');
      $dataTable->setRequestUrl(reconstructQueryAdd() . "action=userslistajax");
      $dataTable->addColumnHelper("name", "Bedrijfsnaam");
      $dataTable->addColumnHelper("domestic", "Plaats");
      $dataTable->addColumnHelper("person", "Persoon");
      $dataTable->addColumnHelper("categoryId", "Categorie");
      $dataTable->addColumnHelper("email", "E-mail");
      $dataTable->addColumnHelper("actions", "Acties");

      $dataTable->getColumn("actions")->setSortable(false);

      $dataTable->setDefaultSort("name", "desc");
      $dataTable->addSearchInput();

      $category_select = new Select('Categorie', 'category');
      $category_select->addOptionHelper('', __("Filter op categorie..."));
      foreach (CrmCategories::getCategories() as $code => $category) {
        $category_select->addOptionHelper($category->categoryId, $category->name);
      }
      $dataTable->getForm()->addElement($category_select);

      $is_deleted_check = new Checkbox('Toon personen', 'show_persons');
      $dataTable->getForm()->addElement($is_deleted_check);

      $is_deleted_check = new Checkbox('Toon verwijderde bedrijven', 'deleted_companies');
      $dataTable->getForm()->addElement($is_deleted_check);

      $dataTable->addSearchReset();

      $dataTable->getForm()->addElement((new A(IconHelper::getPlus() . " Nieuw bedrijf toevoegen", "label_add", reconstructQuery() . 'action=companyedit'))->addClass("gsd-btn gsd-btn-primary gsd-btn-icon"));

      $dataTable->handleRequest($_POST);
      $this->dataTable = $dataTable;
    }

    public function executeUserslistajax() {
      $this->executeUserslistFilters();

      $filter_query = "";
      $filter_query .= "LEFT JOIN " . CrmAddresses::getTablename() . " ON crm_addresses.addressId = crm_companies.visitAddressId ";
      if ($this->dataTable->hasFormElementValue("show_persons")) {
        $filter_query .= "LEFT JOIN " . CrmPersons::getTablename() . " ON crm_persons.companyId = crm_companies.companyId ";
      }
      $filter_query .= "WHERE 1 ";

      if ($this->dataTable->hasFormElementValue("search")) {
        $searchstr = DbHelper::escape($this->dataTable->getFormElementValue("search"));
        $filter_query .= " AND (crm_companies.name LIKE '%" . $searchstr . "%'";
        $filter_query .= " OR crm_addresses.domestic LIKE '%" . $searchstr . "%'";
        if ($this->dataTable->hasFormElementValue("show_persons")) {
          $filter_query .= " OR CONCAT(crm_persons.firstName, ' ', crm_persons.lastName) LIKE '%" . $searchstr . "%'";
          $filter_query .= " OR crm_persons.firstName LIKE '%" . $searchstr . "%'";
          $filter_query .= " OR crm_persons.lastName LIKE '%" . $searchstr . "%'";
          $filter_query .= " OR crm_persons.phone LIKE '%" . $searchstr . "%'";
          $filter_query .= " OR crm_persons.mobile LIKE '%" . $searchstr . "%'";
          $filter_query .= " OR crm_persons.email LIKE '%" . $searchstr . "%'";
        }
        $filter_query .= " ) ";
      }

      if ($this->dataTable->hasFormElementValue("category")) {
        $filter_query .= "AND crm_companies.categoryId = " . DbHelper::escape($this->dataTable->getFormElementValue("category")) . " ";
      }

      if (!$this->dataTable->hasFormElementValue("deleted_companies")) {
        $filter_query .= "AND crm_companies.flagForDeletion = 0 ";
      }

      if ($this->dataTable->hasFormElementValue("deleted_companies")) {
        $filter_query .= "AND crm_companies.flagForDeletion = 1 ";
      }

      /** TOTALS */
      $total_count = CrmCompanies::count_all_by([]);
      $total_count_filtered = CrmCompanies::count_all_by([], $filter_query);

      /** GET DATA */
      $query = "SELECT * FROM " . CrmCompanies::getTablename() . " ";
      $query .= $filter_query;
      $query .= $this->dataTable->getSortQuery();
      $query .= $this->dataTable->getPager()->getLimitQuery();

      $result = DBConn::db_link()->query($query);

      $table_data = [];
      while ($row = $result->fetch_array()) {
        $column_count = 0;

        $company = (new CrmCompanies())->hydrateNext($row, $column_count);
        $address = (new CrmAddresses())->hydrateNext($row, $column_count);
        $person = (new CrmPersons())->hydrateNext($row, $column_count);
        $category = CrmCategories::find_by(['categoryId' => $company->categoryId]);

        $actions_html = "";
        $actions_html .= " " . BtnHelper::getEdit(PageMap::getUrl("M_BEDRIJVENGIDS_EDIT") . '?companyid=' . $company->companyId);
        if ($person && $person->email) {
          $sandbox_user = SandboxUsers::find_by(['personId' => $person->personId]);
          if ($sandbox_user) {
            $actions_html .= " " . BtnHelper::getLogin($sandbox_user->getLoginLink(), "Inloggen op raamdorpel.nl als deze klant", "_blank");
          }
        }

        $person_fullname = $person->firstName . ' ' . $person->lastName;

        $table_data[] = [
          'DT_RowId'   => $person->personId,
          'name'       => '<a href="' . PageMap::getUrl("M_BEDRIJVENGIDS_EDIT") . '?companyid=' . $company->companyId . '" >' . $company->name . '</a>',
          'domestic'   => $address->domestic,
          'person'     => '<a href="' . PageMap::getUrl("M_BEDRIJVENGIDS_PERSONS") . '?action=personedit&companyid=' . $company->companyId . '&personid=' . $person->personId . '" >' . $person_fullname . '</a>',
          'categoryId' => $category->name,
          'email'      => $person->email,
          'actions'    => $actions_html,
        ];
      }

      /** RETURN DATA AS JSON */
      ResponseHelper::exitAsJson([
        'data'            => $table_data,
        'recordsTotal'    => $total_count,
        'recordsFiltered' => $total_count_filtered,
        'draw'            => (int)$_POST['draw'],
      ]);
    }

    public function executeUserOverview() {
      $company = CrmCompanies::find_by(['companyId' => $_GET['companyid']]);
      if (!$company) {
        ResponseHelper::redirectNotFound("Bedrijf niet gevonden.");
      }
      $sandboxuser = SandboxUsers::find_by(['companyId' => $company->companyId]);
      $persons = CrmPersons::find_all_by(['companyId' => $company->companyId]);
      $address = CrmAddresses::find_by(['addressId' => $company->visitAddressId]);

      $personsGrouped = [
        "others"    => [],
        "executors" => [],
        "old"       => [],
      ];
      foreach ($persons as $person) {
        if ($person->flagForDeletion == 1) {
          $personsGrouped["old"][] = $person;
        }
        elseif ($person->flagForExecutor == 1) {
          $personsGrouped["executors"][] = $person;
        }
        else {
          $personsGrouped["others"][] = $person;
        }
      }

      $this->personsGrouped = $personsGrouped;
      $this->sandboxuser = $sandboxuser;
      $this->company = $company;
      $this->address = $address;
    }

    public function executeFinancialedit() {
      $company = CrmCompanies::find_by(['companyId' => $_GET['companyid']]);
      if (!$company) {
        ResponseHelper::redirectNotFound("Bedrijf niet gevonden.");
      }
      $sandboxuser = SandboxUsers::find_by(['companyId' => $company->companyId]);
      $persons = CrmPersons::find_all_by(['companyId' => $company->companyId]);
      $invoiceparty = CrmInvoiceparties::find_by(['companyId' => $company->companyId]);
      $quotations = Quotations::find_all_by(['companyId' => $company->companyId]);
      $bankaccounts = CrmBankaccounts::find_all_by(['companyId' => $company->companyId]);

      //addresses
      $visit_address = CrmAddresses::find_by(['addressId' => $company->visitAddressId]);
      $postAddress = CrmAddresses::find_by(['addressId' => $company->postAddressId]);

      $sAverageAmountInvoice = 'Er zijn nog geen facturen aanwezig';
      if (count($quotations) > 0) {
        $invoiceDayCounter = 0;
        $invoiceCounter = 0;
        foreach ($quotations as $key => $quotation) {
          $invoice = Invoices::find_by(['invoiceId' => $quotation->invoiceId]);
          if (!$invoice || empty($invoice->paid)) continue;
          $date1 = new DateTime($invoice->dateInvoice);
          $date2 = new DateTime($invoice->paid);
          $interval = $date1->diff($date2);
          $invoiceDayCounter += (int)$interval->format('%a');
          $invoiceCounter++;
        }
        if ($invoiceCounter != 0) {
          $sAverageAmountInvoice = $invoiceDayCounter / $invoiceCounter;
          $sAverageAmountInvoice = round($sAverageAmountInvoice, 2) . ' dagen';
        }
        else {
          $sAverageAmountInvoice = 'Er zijn nog geen facturen aanwezig';
        }
      }


      $form_invoiceparty = $this->createInvoicepartyForm($invoiceparty);
      $form_invoiceparty_company = $this->createInvoiceparty_companyForm($company);
      $form_invoiceparty_address = $this->createInvoiceparty_addressForm($visit_address);

      if (isset($_POST['go']) || isset($_POST['go_list'])) {
        $form_invoiceparty->setElementsAndObjectValue($_POST, $invoiceparty);
        $form_invoiceparty_company->setElementsAndObjectValue($_POST, $company);
        $form_invoiceparty_address->setElementsAndObjectValue($_POST, $visit_address);

        if ($form_invoiceparty_address->isValid() && $form_invoiceparty->isValid() && $form_invoiceparty_company) {

          $invoiceparty->save();
          $company->save();
          $visit_address->save();

          MessageFlashCoordinator::addMessage("Factuurgegevens opgeslagen.");
          if (isset($_POST['go_list'])) {
            ResponseHelper::redirect(PageMap::getUrl('M_BEDRIJVENGIDS_FINANCIAL'));
          }
          ResponseHelper::redirect(reconstructQuery());
        }
      }

      if (isset($_POST['save_address'])) {
        if ($_POST['address_radio'] == 1) {
          $visit_address->street = trim($_POST['street_2']);
          $visit_address->nr = trim($_POST['number_2']);
          $visit_address->extension = trim($_POST['extension_2']);
          $visit_address->zipcode = trim($_POST['zip_2']);
          $visit_address->domestic = trim($_POST['city_2']);
          $visit_address->country = trim($_POST['country_2']);
          $visit_address->save();
          MessageFlashCoordinator::addMessage("Bezoekadresgegevens opgeslagen.");
        }
        elseif ($_POST['address_radio'] == 2) {
          if ($postAddress) {
            $postAddress->street = trim($_POST['street_2']);
            $postAddress->nr = trim($_POST['number_2']);
            $postAddress->extension = trim($_POST['extension_2']);
            $postAddress->zipcode = trim($_POST['zip_2']);
            $postAddress->domestic = trim($_POST['city_2']);
            $postAddress->country = trim($_POST['country_2']);
            $postAddress->save();
            MessageFlashCoordinator::addMessage("Postadresgegevens opgeslagen.");
          }
          else {
            MessageFlashCoordinator::addMessage("Postadres niet gevonden.");
          }
        }
        ResponseHelper::redirect(reconstructQuery());
      }

      $this->form_invoiceparty = $form_invoiceparty;
      $this->form_invoiceparty_company = $form_invoiceparty_company;
      $this->form_invoiceparty_address = $form_invoiceparty_address;
      $this->persons = $persons;
      $this->sandboxuser = $sandboxuser;
      $this->company = $company;
      $this->address = $visit_address;
      $this->quotation_days = $sAverageAmountInvoice;
      $this->bankaccounts = $bankaccounts;
      $this->visitAddress = $visit_address;
      $this->postAddress = $postAddress;
    }

    public function executeBankaccountedit() {
      $bankaccount = new CrmBankaccounts();
      if (isset($_GET['bankaccountid'])) {
        $bankaccount = CrmBankaccounts::find_by(['accountId' => $_GET['bankaccountid']]);
        if (!$bankaccount) {
          ResponseHelper::redirectNotFound("Betaalrekening niet gevonden.");
        }
      }
      $company = CrmCompanies::find_by(['companyId' => $_GET['companyid']]);
      $sandboxuser = SandboxUsers::find_by(['companyId' => $company->companyId]);
      $persons = CrmPersons::find_all_by(['companyId' => $company->companyId]);
      $visit_address = CrmAddresses::find_by(['addressId' => $company->visitAddressId]);
      $invoiceparty = CrmInvoiceparties::find_by(['companyId' => $company->companyId]);

      $form_bankaccount = $this->createBankaccountForm($bankaccount);

      if (isset($_POST['go']) || isset($_POST['go_list'])) {
        $form_bankaccount->setElementsAndObjectValue($_POST, $bankaccount);

        $iban_object = new IBAN();
//        dumpe($_POST['bankaccount']['IBAN']);
//        dumpe($iban_object->Country($_POST['bankaccount']['IBAN']));
        if ($form_bankaccount->isValid()) {
          // @todo IBAN nummer checken
          if (!isset($_POST['bankaccount']['IBAN']) || !$iban_object->Verify($_POST['bankaccount']['IBAN'])) {
            ResponseHelper::redirectAlertMessage('Ongeldig IBAN nummer', reconstructQuery());
          }
          $country = $iban_object->Country($_POST['bankaccount']['IBAN']);
          if ($iban_object->Country($_POST['bankaccount']['IBAN']) == '') {
            $country = substr($_POST['bankaccount']['IBAN'], 0, 2);
          }
          $bankaccount->country = $country;
          $bankaccount->IBAN = $_POST['bankaccount']['IBAN'];
          $bankaccount->companyId = $company->companyId;
          $bankaccount->save();

          MessageFlashCoordinator::addMessage("Rekening opgeslagen.");
          if (isset($_POST['go_list'])) {
            ResponseHelper::redirect(PageMap::getUrl('M_BEDRIJVENGIDS_FINANCIAL'));
          }
          ResponseHelper::redirect(reconstructQuery());
        }
      }

      $this->persons = $persons;
      $this->sandboxuser = $sandboxuser;
      $this->company = $company;
      $this->address = $visit_address;
      $this->form_bankaccount = $form_bankaccount;
      $this->bankaccount = $bankaccount;
    }

    public function executeBankaccountdelete() {
      $bankaccount = CrmBankaccounts::find_by(["accountId" => $_GET["bankaccountid"]]);
      if (!$bankaccount) {
        ResponseHelper::redirectNotFound("Betaalrekening niet gevonden.");
      }

      $bankaccount->destroy();
      ResponseHelper::redirectMessage("Betaakrekening verwijderd.", PageMap::getUrl('M_BEDRIJVENGIDS_FINANCIAL'));

    }

    public function executeAddressoverview() {
      $company = CrmCompanies::find_by(['companyId' => $_GET['companyid']]);
      $sandboxuser = SandboxUsers::find_by(['companyId' => $company->companyId]);
      $persons = CrmPersons::find_all_by(['companyId' => $company->companyId]);
      $address = CrmAddresses::find_by(['addressId' => $company->visitAddressId]);

      $this->persons = $persons;
      $this->sandboxuser = $sandboxuser;
      $this->company = $company;
      $this->address = $address;

      $this->buildAddressDatatable();
    }

    public function buildAddressDatatable(): void {
      $dataTable = new DataTable('addresslist');
      $dataTable->setSaveFilterToSession(false);

      $dataTable->setRequestUrl(reconstructQueryAdd() . "action=addresslistajax&companyid=" . $_GET['companyid']);
      $dataTable->addColumnHelper("adress", "Straat en nummer")->setSortable(false);
      $dataTable->addColumnHelper("zipcode", "Postcode")->setSortable(false);
      $dataTable->addColumnHelper("domestic", "Stad")->setSortable(false);
      $dataTable->addColumnHelper("type", "Soort")->setSortable(false);
      $dataTable->addColumnHelper("actions", "Acties")->setSortable(false);

      $select = new Select("Soort", 'type');
      foreach (CrmAddresses::TYPES as $code => $name) {
        $select->addOptionHelper($code, $name);
      }

      $select->setMuliple(true);
      $select->setValue([CrmAddresses::TYPE_VISIT, CrmAddresses::TYPE_DELIVERY]);
      $dataTable->getForm()->addElement($select);

      $dataTable->handleRequest($_POST);

      $this->dataTable = $dataTable;
    }

    public function executeAddresslistajax(): void {
      $this->buildAddressDatatable();

      $filter_query = "";
      if ($this->dataTable->hasFormElementValue("type") && !empty($this->dataTable->getFormElementValue("type")[0])) {
        $filter_query .= "AND " . DbHelper::getSqlIn("type", $this->dataTable->getFormElementValue("type"));
      }

      $companyId = $_GET['companyid'];
      $addresses = CrmAddresses::find_all_by(['companyId' => $companyId], $filter_query);

      $table_data = array_map(fn($address) => [
        'DT_RowId' => $address->addressId,
        'adress'   => $address->street . ' ' . $address->nr,
        'zipcode'  => $address->zipcode,
        'domestic' => $address->domestic,
        'type'     => CrmAddresses::TYPES[$address->type],
        'actions'  => BtnHelper::getEdit(reconstructQueryAdd(['pageId']) . 'action=addressedit&addressid=' . $address->addressId, __('Bewerk locatie')) . "" .
          BtnHelper::getRemove(reconstructQueryAdd(['pageId']) . 'action=addressdelete&addressid=' . $address->addressId, __('Verwijder locatie')),
      ], $addresses);

      ResponseHelper::exitAsJson([
        'data'            => $table_data,
        'recordsTotal'    => count($addresses),
        'recordsFiltered' => count($addresses),
        'draw'            => (int)$_POST['draw'],
      ]);
    }

    public function executeAddressedit() {
      $address = new CrmAddresses();
      if (isset($_GET['addressid'])) {
        $address = CrmAddresses::find_by(['addressId' => $_GET['addressid']]);
        if (!$address) {
          ResponseHelper::redirectNotFound("Locatie niet gevonden.");
        }
      }
      $company = CrmCompanies::find_by(['companyId' => $_GET['companyid']]);
      $sandboxuser = SandboxUsers::find_by(['companyId' => $company->companyId]);
      $persons = CrmPersons::find_all_by(['companyId' => $company->companyId]);
      $addresses = CrmAddresses::find_all_by(['companyId' => $company->companyId]);

      $form_address = $this->createAddressesForm($address);

      if (isset($_POST['go']) || isset($_POST['go_list'])) {
        $form_address->setElementsAndObjectValue($_POST, $address);

        if ($form_address->isValid()) {
          if (empty($address->type)) {
            $address->type = CrmAddresses::TYPE_DELIVERY;
          }
          $address->companyId = $_GET['companyid'];
          $address->latitude = $_POST['lat'];
          $address->longitude = $_POST['lng'];
          $address->save();

          if (empty($company->visitAddressId)) {
            $company->visitAddressId = $address->addressId;
          }

          MessageFlashCoordinator::addMessage("Adres opgeslagen.");
          if (isset($_POST['go_list'])) {
            ResponseHelper::redirect(PageMap::getUrl('M_BEDRIJVENGIDS_LOCATIONS'));
          }
          ResponseHelper::redirect(reconstructQuery());
        }
      }

      $this->address = $address;
      $this->form_address = $form_address;
      $this->sandboxuser = $sandboxuser;
      $this->company = $company;
      $this->persons = $persons;
      $this->addresses = $addresses;
      Context::addJavascript("https://maps.googleapis.com/maps/api/js?v=3.exp&libraries=places&key=" . LocationHelper::getGoogleMapsKey());
    }

    public function executeAddressdelete(): void {
      $address = CrmAddresses::find_by(["addressId" => $_GET["addressid"]]);
      if (!$address) {
        ResponseHelper::redirectNotFound("Adres niet gevonden.");
      }
      $quotations = QuotationsExtra::find_all_by(['addressDeliveryId' => $address->addressId]);
      if (!empty($quotations)) {
        ResponseHelper::redirectAlertMessage("Adres kan niet verwijderd worden, er zijn nog offertes aan dit adres gekoppeld.", PageMap::getUrl('M_BEDRIJVENGIDS_LOCATIONS'));
      }
      $sandboxUsers = SandboxUsers::find_all_by(['lastDeliveryAddressId' => $address->addressId]);
      $userFilter = DbHelper::getSqlIn('userId', array_map(fn($user) => $user->userId, $sandboxUsers));
      $invoices = Invoices::find_all("WHERE $userFilter");
      if (!empty($invoices)) {
        ResponseHelper::redirectAlertMessage("Adres kan niet verwijderd worden, er zijn nog facturen aan dit adres gekoppeld.", PageMap::getUrl('M_BEDRIJVENGIDS_LOCATIONS'));
      }

      foreach ($sandboxUsers as $sandboxUser) {
        $sandboxUser->lastDeliveryAddressId = null;
        $sandboxUser->save();
      }
      $address->destroy();
      ResponseHelper::redirectMessage("Adres verwijderd.", PageMap::getUrl('M_BEDRIJVENGIDS_LOCATIONS'));
    }

    public function executeFindlatlng() {
      $countries = Organisation::getCountries();

      $zipcode = '';
      $street = '';
      $nr = '';
      $city = '';
      $countrysub = '';

      if (isset($_GET['street']))
        $street = $_GET['street'];
      if (isset($_GET['nr']))
        $nr = $_GET['nr'];
      if (isset($_GET['zipcode']))
        $zipcode = $_GET['zipcode'];
      if (isset($_GET['city']))
        $city = $_GET['city'];
      if (isset($_GET['country']) && $_GET['country'] != '')
        $countrysub = $_GET['country'];

      $country = "Nederland";
      if ($countrysub != "") {
        if (isset($countries[$countrysub])) {
          $country = $countries[$countrysub];
        }
      }
      if ($country == 'Engeland') {
        $country = 'Verenigd Koninkrijk';
      }

      $latlng = LocationHelper::retrieveLatLngGoogle($zipcode, $street . ' ' . $nr, $city, $country);
      ResponseHelper::exitAsJson($latlng);

    }

    public function executeSettingsedit() {
      $company = CrmCompanies::find_by(['companyId' => $_GET['companyid']]);
      if (!$company) {
        ResponseHelper::redirectNotFound("Bedrijf niet gevonden.");
      }
      $sandboxuser = SandboxUsers::find_by(['companyId' => $company->companyId]);
      $persons = CrmPersons::find_all_by(['companyId' => $company->companyId]);
      $address = CrmAddresses::find_by(['addressId' => $company->visitAddressId]);
      $addresses = CrmAddresses::find_all_by(['companyId' => $company->companyId]);

      $form_settings = $this->createSettingsForm($company);

      if (isset($_POST['go']) || isset($_POST['go_list'])) {
        $form_settings->setElementsAndObjectValue($_POST, $company);

        if ($form_settings->isValid()) {
          $company->companyId = $_GET['companyid'];
          $company->save();

          MessageFlashCoordinator::addMessage("Instellingen opgeslagen.");
          if (isset($_POST['go_list'])) {
            ResponseHelper::redirect(PageMap::getUrl('M_BEDRIJVENGIDS_SETTINGS'));
          }
          ResponseHelper::redirect(reconstructQuery());
        }
      }

      $this->form_settings = $form_settings;
      $this->persons = $persons;
      $this->sandboxuser = $sandboxuser;
      $this->company = $company;
      $this->address = $address;
      $this->addresses = $addresses;
    }

    public function executeOtheredit() {
      $company = CrmCompanies::find_by(['companyId' => $_GET['companyid']]);
      if (!$company) {
        ResponseHelper::redirectNotFound("Bedrijf niet gevonden.");
      }
      $sandboxuser = SandboxUsers::find_by(['companyId' => $company->companyId]);
      $persons = CrmPersons::find_all_by(['companyId' => $company->companyId]);
      $address = CrmAddresses::find_by(['addressId' => $company->visitAddressId]);
      $addresses = CrmAddresses::find_all_by(['companyId' => $company->companyId]);

      $form_other = $this->createOtherForm($company);

      if (isset($_POST['go']) || isset($_POST['go_list'])) {
        $form_other->setElementsAndObjectValue($_POST, $company);

        if ($form_other->isValid()) {
          $company->save();

          MessageFlashCoordinator::addMessage("Instellingen opgeslagen.");
          if (isset($_POST['go_list'])) {
            ResponseHelper::redirect(PageMap::getUrl('M_BEDRIJVENGIDS_LOCATIONS'));
          }
          ResponseHelper::redirect(reconstructQuery());
        }
      }

      $this->form_other = $form_other;
      $this->persons = $persons;
      $this->sandboxuser = $sandboxuser;
      $this->company = $company;
      $this->address = $address;
      $this->addresses = $addresses;
    }

    public function executeDocumentslist() {
      $company = CrmCompanies::find_by(['companyId' => $_GET['companyid']]);
      if (!$company) {
        ResponseHelper::redirectNotFound("Bedrijf niet gevonden.");
      }
      $sandboxuser = SandboxUsers::find_by(['companyId' => $company->companyId]);
      $persons = CrmPersons::find_all_by(['companyId' => $company->companyId]);
      $address = CrmAddresses::find_by(['addressId' => $company->visitAddressId]);
      $addresses = CrmAddresses::find_all_by(['companyId' => $company->companyId]);
      $files = Files::find_all_by(['companyId' => $company->companyId], ' ORDER BY uploadDate DESC');

      $this->files = $files;
      $this->persons = $persons;
      $this->sandboxuser = $sandboxuser;
      $this->company = $company;
      $this->address = $address;
      $this->addresses = $addresses;
    }

    public function executeDocumentedit() {
      $file = Files::find_by(['fileId' => $_GET['fileid']]);

      $form_document = $this->createDocumentForm($file);

      if (isset($_POST['go']) || isset($_POST['go_list'])) {
        $form_document->setElementsAndObjectValue($_POST, $file);

        if ($form_document->isValid()) {
          if (isset($_POST['uploadAlert'])) {
            $file->showInDocuments = 1;
          }
          $file->save();

          MessageFlashCoordinator::addMessage("Document opgeslagen.");
          if (isset($_POST['go_list'])) {
            ResponseHelper::redirect(PageMap::getUrl('M_BEDRIJVENGIDS_DOCUMENTS'));
          }
          ResponseHelper::redirect(reconstructQuery());
        }
      }

      $this->form_document = $form_document;
      $this->file = $file;
    }

    public function executeDocumentdelete() {
      $file = Files::find_by(['fileId' => $_GET['fileid']]);
      if (!$file) {
        ResponseHelper::redirectNotFound("Document niet gevonden.");
      }

      $file->destroy();

      ResponseHelper::redirectMessage("Document verwijderd.", reconstructQuery());

    }

    public function executeViewpdf() {

      $full_path = substr(DIR_ROOT_HTTPDOCS, 0, -1);

      $pdf = Files::find_by(['fileId' => $_GET['fileid']]);
      $full_path .= $pdf->folder . $pdf->filename;


      if (!$pdf) {
        ResponseHelper::redirectAlertMessage("Deze PDF is niet beschikbaar.", PageMap::getUrl("M_BEDRIJVENGIDS_DOCUMENTS"));
      }

      if (!file_exists($full_path)) {
        ResponseHelper::redirectAlertMessage("De PDF is niet gevonden op de server?", PageMap::getUrl("M_BEDRIJVENGIDS_DOCUMENTS"));
      }

      FileHelper::getFileAndOuput($full_path);
      ResponseHelper::exit();
    }

    public function executeCall() {
      Context::addStylesheet(URL_INCLUDES . "vendor/ivaynberg/select2/dist/css/select2.min.css");
      Context::addJavascript(URL_INCLUDES . "vendor/ivaynberg/select2/dist/js/select2.min.js");

      if (isset($_POST['pickup'])) {
        $this->handleContainerPickup();
      }

      $companyName = escapeForDB($_GET['company'] ?? '');
      $domesticSearchValue = escapeForDB($_GET['domestic'] ?? '');
      if ($companyName) {
        $this->handleNameDomesticSearch($companyName, $domesticSearchValue);
      }

      $phoneSearchValue = escapeForDB(StringHelper::cleanPhone($_GET['phone'] ?? ''));
      if ($phoneSearchValue) {
        $this->handlePhoneSearch($phoneSearchValue);
      }
      $this->phone = $phoneSearchValue;
    }

    private function handleNameDomesticSearch($companyName, $domesticSearchValue): void {
      $companies = CrmCompanies::getTablename();
      $adresses = CrmAddresses::getTablename();
      $query = <<<SQL
        SELECT * FROM $companies
        LEFT JOIN $adresses ON $companies.visitAddressId = $adresses.addressId
        WHERE 1 
      SQL;
      if ($companyName) {
        $query .= " AND $companies.name LIKE '%$companyName%'";
      }
      if ($domesticSearchValue) {
        $query .= " AND $adresses.domestic LIKE '%$domesticSearchValue%'";
      }
      $query .= " LIMIT 20";

      $result = DBConn::db_link()->query($query);
      $companies = [];
      while ($row = $result->fetch_array()) {
        $offset = 0;
        $company = (new CrmCompanies())->hydrateNext($row, $offset);
        $address = (new CrmAddresses())->hydrateNext($row, $offset);
        $company->address = $address;
        $companies[] = $company;
      }
      $this->companiesFromNameDomesticSearch = $companies;
    }

    private function handlePhoneSearch($phone): void {
      $searchQuery = "WHERE phone = '$phone' OR fax = '$phone'";
      $mobileQuery = "OR mobile = '$phone'";

      $companies = [];

      // Fetch companies first
      $companyRecords = CrmCompanies::find_all("$searchQuery LIMIT 20");
      foreach ($companyRecords as $company) {
        $this->setCompanyValues($company);
        $companies[$company->companyId] = $company;
      }

      // Used for the direct login link
      $this->mainHost = SiteHost::getMainSiteHost(1)->getDomainSmart(true);
      $this->userhash = urlencode(EncryptionHelper::encrypt(("RDEUSER#824yy" . date("Ydm"))));

      $personRecords = CrmPersons::find_all("$searchQuery $mobileQuery LIMIT 20");
      $companies = $this->getCompanies($personRecords, $companies);

      $sandboxUsers = SandboxUsers::find_all("$searchQuery $mobileQuery LIMIT 20");
      $sandboxUsers = $this->getUnlinkedSandboxUsers($sandboxUsers, $personRecords);

      $companies = $this->getCompanies($sandboxUsers, $companies);

      // move unlinked users out of the array
      $this->unlinked = $companies[-1] ?? [];
      unset($companies[-1]);

      $this->companies = array_values($companies);
      $this->calledAt = $_GET['calledAt'] ?? '';
    }

    /**
     * Handles an AJAX request for searching places.
     *
     * This method fetches up to 10 places from the database that match the given search term.
     * The results are formatted for use with Select2.
     */
    public function executeDomesticSearchAjax(): void {
      $search = DbHelper::escape($_GET['search'] ?? '');

      $addressTable = CrmAddresses::getTablename();
      $query = "SELECT DISTINCT domestic FROM $addressTable WHERE domestic <> '' AND domestic LIKE '%$search%' ORDER BY domestic LIMIT 10";
      $result = DBConn::db_link()->query($query);
      $items = [];
      while ($row = $result->fetch_array()) {
        $items[] = [
          'id'   => $row['domestic'],
          'text' => $row['domestic'],
        ];
      }
      ResponseHelper::exitAsJson($items);
    }

    /**
     * Filters a list of sandbox users and returns only those
     * who are not linked to any existing person.
     *
     * @param array $users   List of sandbox users (each user must have a `personId` property).
     * @param array $persons List of persons (each person must have a `personId` property).
     *
     * @return array The sandbox users that do not have a matching person in the $persons list.
     */
    private function getUnlinkedSandboxUsers(array $users, array $persons): array {
      return array_filter($users, function ($sandboxUser) use ($persons) {
        $persons = array_filter($persons, fn ($person) => $person->personId == $sandboxUser->personId);
        return count($persons) === 0;
      });
    }

    private function handleContainerPickup(): void {
      $containerQuotation = ContainersQuotations::find_by(['containerQuotationId' => $_POST['containerQuotationId'], 'returnDate' => null]);
      if (!$containerQuotation) {
        ResponseHelper::redirectMessage("Container niet gevonden.", reconstructQuery());
      }

      // Toggle nextroute
      $containerQuotation->nextroute = ($containerQuotation->nextroute !== 'true') ? 'true' : 'false';

      $responseMessage = ($containerQuotation->nextroute === 'true')
        ? "Container is gemarkeerd om op te halen."
        : "Container is gemarkeerd om niet meer op te halen.";

      $containerQuotation->save();
      ResponseHelper::redirectMessage($responseMessage, reconstructQuery());
    }

    /**
     * @param array $personRecords
     * @param array $companies
     * @return array
     */
    private function getCompanies(array $personRecords, array $companies): array {
      foreach ($personRecords as $person) {
        if (isset($person->flagForDeletion) && $person->flagForDeletion) continue;

        $person->isBlocked = isset($person->blocked) && $person->blocked === 'true';
        $person->directLoginLink = "$this->mainHost/offerte?email=$person->email&userhash=$this->userhash";
        $person->company = CrmCompanies::find_by(['companyId' => $person->companyId]);
        if ($person->company) {
          if (!isset($companies[$person->company->companyId])) {
            $this->setCompanyValues($person->company);
            $companies[$person->company->companyId] = $person->company;
          }
          $companies[$person->company->companyId]->users[] = $person;
        } else {
          // reserve seperate key for unlinked (sandbox) users
          $companies[-1] ??= [];
          $companies[-1][] = $person;
        }
      }
      return $companies;
    }

    private function setCompanyValues($company) {
      $company->users = [];
      $company->address = CrmAddresses::find_by(['addressId' => $company->visitAddressId]);
      $company->category = CrmCategories::find_by(['categoryId' => $company->categoryId]);
      $company->quotations = $this->getQuotationsOfCompany($company);
      $company->openInvoices = $this->getInvoicesOfCompany($company);
      $company->paidInvoices = $this->getInvoicesOfCompany($company, true);
      $company->containers = $this->getContainersOfCompany($company);
    }

    private function getQuotationsOfCompany($company): array {
      $statusQuery = DbHelper::getSqlIn('statusId', [Status::STATUS_ORDER, Status::STATUS_CHECKED, Status::STATUS_PREPARED, Status::STATUS_IN_PRODUCTION, Status::STATUS_PRODUCED, Status::STATUS_PACKED, Status::STATUS_LOADED, Status::STATUS_DELIVERED]);
      $quotations = Quotations::find_all_by(['companyId' => $company->companyId], "AND $statusQuery");
      foreach ($quotations as $quotation) {
        $gps = GpsbuddyRde::find_by(['quotationId' => $quotation->quotationId]);
        $route = $gps ? GpsbuddyRoutes::find_by(['routeId' => $gps->routeId]) : null;
        $quotation->plannedDate = $route?->date;
      }
      return $quotations;
    }

    private function getInvoicesOfCompany($company, $paid = false): array {
      $invoicesTable = Invoices::getTablename();
      $sandboxUsersTable = SandboxUsers::getTablename();
      $crmCompaniesTable = CrmCompanies::getTablename();

      $paidQuery = $paid
        ? "AND $invoicesTable.paid IS NOT NULL"
        : "AND $invoicesTable.paid IS NULL";
      $limitClause = $paid ? "LIMIT 5" : "";

      $query = <<<SQL
        SELECT * FROM $invoicesTable
        LEFT JOIN $sandboxUsersTable ON $sandboxUsersTable.userId = $invoicesTable.userId
        LEFT JOIN $crmCompaniesTable ON $crmCompaniesTable.companyId = $sandboxUsersTable.companyId
        WHERE $crmCompaniesTable.companyId = $company->companyId $paidQuery
        ORDER BY $invoicesTable.dateInvoice DESC
        $limitClause
      SQL;
      $result = DBConn::db_link()->query($query);
      $invoices = [];
      while ($row = $result->fetch_row()) {
        $invoice = new Invoices();
        $invoice->hydrate($row);
        $invoices[] = $invoice;
      }
      return $invoices;
    }

    private function getContainersOfCompany($company) {
      $c = Containers::getTablename();
      $cq = ContainersQuotations::getTablename();
      $q = Quotations::getTablename();
      $ct = ContainerTag::getTablename();
      $s = SandboxUsers::getTablename();

      $query = <<<SQL
        SELECT * FROM $c
        LEFT JOIN $cq ON $c.containerId = $cq.containerId AND $cq.returnDate IS NULL
        LEFT JOIN $q ON $cq.quotationId = $q.quotationId
        LEFT JOIN $ct ON $c.containerId = $ct.containerId
        LEFT JOIN $s ON $s.userId = $q.userId
        WHERE $s.companyId = $company->companyId
        GROUP BY $c.containerId
      SQL;

      $result = DBConn::db_link()->query($query);
      $containers = [];
      while ($row = $result->fetch_row()) {
        $offset = 0;
        $container = (new Containers())->hydrateNext($row, $offset);
        $container->containerQuotation = (new ContainersQuotations())->hydrateNext($row, $offset);
        $container->quotation = (new Quotations())->hydrateNext($row, $offset);
        $container->containerTag = (new ContainerTag())->hydrateNext($row, $offset);

        $containers[] = $container;
      }
      return $containers;
    }
  }
