<?php

  namespace domain\elements\service;
  use domain\elements\service\helpers\ElementStoneAmount;
  use domain\elements\service\helpers\SplitElementParts;
  use Mitres;
  use OrderElementparts;
  use OrderElements;
  use Quotations;
  use QuotationsExtra;
  use Stones;
  use StoneSizes;

  class SplitCalculator
  {
    private Quotations $quotation;
    private QuotationsExtra $quotationExtra;
    private ?Stones $stone;
    private ?StoneSizes $stoneSize;

    public function __construct(
      Quotations $quotation,
      QuotationsExtra $quotationExtra
    ) {
      $this->quotation = $quotation;
      $this->quotationExtra = $quotationExtra;
    }

    public function run(): void {
      $elements = OrderElements::find_all_by(['quotationId' => $this->quotation->quotationId]);
      if (empty($elements)) return;

      $this->stone = Stones::find_by(['stoneId' => $this->quotation->stoneId]);
      if (!$this->stone) return;

      $this->stoneSize = StoneSizes::find_by(['sizeId' => $this->stone->sizeId]);

      foreach ($elements as $element) {
        // For each element, determine the type and call the appropriate calculation method
        if ($this->stone->isNatuursteen() || $this->stone->isBeton() || $this->stone->isIsosill()) {
          $this->runSplitCalculationConcrete($element);
        }
        else {
          $this->runSplitCalculationCeramic($element);
        }
      }
    }

    /**
     * Berekening voor de verdeling van beton en natuursteen elementen (ik verwacht ook isosill)
     */
    private function runSplitCalculationConcrete(OrderElements $element): void {
      // Only process if we have a valid element with reference name
      if (!$element->referenceName) return;

      // Determine mitre amount and side
      $side = $element->getMitre();

      // Get max measure element
      $maxMeasureElement = intval($this->quotationExtra->maxMeasureElement);

      // Calculate division values
      $iElementLength = intval($element->elementLength);
      $divisionValueElementLength = $iElementLength / $maxMeasureElement;
      $divisionValueElementLengthRoundUp = ceil($divisionValueElementLength);
      $amountOfSplitsForAdd = $divisionValueElementLengthRoundUp - 1;

      // Initialize OrderElementParts object
      $orderElementParts = OrderElementparts::find_by(['elementId' => $element->elementId, 'quotationId' => $this->quotation->quotationId]);
      if (!$orderElementParts) {
        $orderElementParts = new OrderElementparts();
      }

      $orderElementParts->elementId = $element->elementId;
      $orderElementParts->quotationId = $this->quotation->quotationId;

      if ($divisionValueElementLengthRoundUp > 0) {
        // Calculate additional stonework based on calculation type
        $addStonecalc = 0;
        if ($this->stone->isBeton()) {
          $addStonecalc = $amountOfSplitsForAdd * 10;
        }
        elseif ($this->stone->isNatuursteen() || $this->stone->isIsosill()) {
          $addStonecalc = $amountOfSplitsForAdd * 5;
        }

        $totalLengthMinusAdd = $iElementLength - $addStonecalc;
        $sizePerSplit = $totalLengthMinusAdd / $divisionValueElementLengthRoundUp;
        $lengthPerElement = floor($sizePerSplit);

        // Set element part values based on number of divisions and mitre config
        switch (intVal($divisionValueElementLengthRoundUp)) {
          case 0:
            // No divisions needed
            break;

          case 1:
            // Single element, no split needed
            $orderElementParts->aAmount1 = $divisionValueElementLengthRoundUp;
            $orderElementParts->aLength1 = $lengthPerElement;
            break;

          case 2:
            if ($side === 'none') {
              $orderElementParts->aAmount1 = $divisionValueElementLengthRoundUp;
              $orderElementParts->aLength1 = $lengthPerElement;
            } elseif ($side === 'left' || $side === 'right' || $side === 'both') {
              $orderElementParts->aAmount1 = 1;
              $orderElementParts->aLength1 = $lengthPerElement;
              $orderElementParts->bAmount1 = 1;
              $orderElementParts->bLength1 = $lengthPerElement;
            }
            break;

          case 3:
            if ($side === 'none') {
              $orderElementParts->aAmount1 = $divisionValueElementLengthRoundUp;
              $orderElementParts->aLength1 = $lengthPerElement;
            } elseif ($side === 'left' || $side === 'right') {
              $orderElementParts->aAmount1 = 1;
              $orderElementParts->aLength1 = $lengthPerElement;
              $orderElementParts->bAmount1 = 2;
              $orderElementParts->bLength1 = $lengthPerElement;
            } elseif ($side === 'both') {
              $orderElementParts->aAmount1 = 1;
              $orderElementParts->aLength1 = $lengthPerElement;
              $orderElementParts->bAmount1 = 1;
              $orderElementParts->bLength1 = $lengthPerElement;
              $orderElementParts->cAmount1 = 1;
              $orderElementParts->cLength1 = $lengthPerElement;
            }
            break;

          case 4:
            if ($side === 'none') {
              $orderElementParts->aAmount1 = $divisionValueElementLengthRoundUp;
              $orderElementParts->aLength1 = $lengthPerElement;
            } elseif ($side === 'left' || $side === 'right') {
              $orderElementParts->aAmount1 = 1;
              $orderElementParts->aLength1 = $lengthPerElement;
              $orderElementParts->bAmount1 = 3;
              $orderElementParts->bLength1 = $lengthPerElement;
            } elseif ($side === 'both') {
              $orderElementParts->aAmount1 = 1;
              $orderElementParts->aLength1 = $lengthPerElement;
              $orderElementParts->bAmount1 = 2;
              $orderElementParts->bLength1 = $lengthPerElement;
              $orderElementParts->cAmount1 = 1;
              $orderElementParts->cLength1 = $lengthPerElement;
            }
            break;

          default:
            // For 5+ divisions
            $divisionValueMainNumber = intVal($divisionValueElementLengthRoundUp);

            if ($side === 'none') {
              $orderElementParts->aAmount1 = $divisionValueElementLengthRoundUp;
              $orderElementParts->aLength1 = $lengthPerElement;
            } elseif ($side === 'left' || $side === 'right') {
              $orderElementParts->aAmount1 = 1;
              $orderElementParts->aLength1 = $lengthPerElement;
              $orderElementParts->bAmount1 = $divisionValueMainNumber - 1;
              $orderElementParts->bLength1 = $lengthPerElement;
            } elseif ($side === 'both') {
              $orderElementParts->aAmount1 = 1;
              $orderElementParts->aLength1 = $lengthPerElement;
              $orderElementParts->bAmount1 = $divisionValueMainNumber - 2;
              $orderElementParts->bLength1 = $lengthPerElement;
              $orderElementParts->cAmount1 = 1;
              $orderElementParts->cLength1 = $lengthPerElement;
            }
            break;
        }

        // Add or remove element parts based on calculation
        if ($element->elementLength > $maxMeasureElement) {
          $this->ensureEmptyValuesAreSet($orderElementParts);
          $orderElementParts->save();
        } elseif ($orderElementParts->orderElementPartId) {
          $orderElementParts->destroy();
        }
      }
    }

    private function runSplitCalculationCeramic(OrderElements $element): void {
      // Only process if we have a valid element with reference name
      if (!$element->referenceName) return;

      // Early return if element length is less than or equal to max measure element
      if ($element->elementLength <= $this->quotationExtra->maxMeasureElement) {
        $this->removeExistingElementParts($element);
        return;
      }

      $splitElementParts = $this->prepareSplitElementParts($element);
      $elementParts = $this->calculateElementParts($splitElementParts, $element);

      $this->saveOrRemoveElementParts($elementParts, $element);
    }

    private function prepareSplitElementParts(OrderElements $element): SplitElementParts {
      $splitElementParts = new SplitElementParts();

      // Mitre data ophalen
      $leftMitre = $element->leftMitreId ? Mitres::find_by(['mitreId' => $element->leftMitreId]) : null;
      $rightMitre = $element->rightMitreId ? Mitres::find_by(['mitreId' => $element->rightMitreId]) : null;

      // Stone counts
      $splitElementParts->mitreStoneCountLeft = $leftMitre?->stoneCount ?? 0;
      $splitElementParts->mitreStoneCountRight = $rightMitre?->stoneCount ?? 0;

      // Basis eigenschappen
      $splitElementParts->divisionMeasure = $element->divisionMeasure;
      $splitElementParts->stoneSizeWidth = $this->quotationExtra->stoneSizeWidth * 10; // times 10 to get mm
      $splitElementParts->maxMeasureElement = intval($this->quotationExtra->maxMeasureElement);
      $splitElementParts->heartClickSize = $element->heartClickSize;

      // Heart lengths
      $splitElementParts->heartLengthLeft = $leftMitre?->heartLength ?? 0;
      $splitElementParts->heartLengthRight = $rightMitre?->heartLength ?? 0;

      // Element eigenschappen
      $splitElementParts->inputLength = $element->inputLength;
      $splitElementParts->dbElementLength = $element->elementLength;
      $splitElementParts->rightMitreId = $element->rightMitreId;
      $splitElementParts->leftMitreId = $element->leftMitreId;
      $splitElementParts->shorter = $this->quotation->shorter;
      $splitElementParts->stoneId = $this->quotation->stoneId;

      // Mitre angles en lengths
      $this->setMitreProperties($splitElementParts, $leftMitre, $rightMitre);

      // Stone amount berekenen
      $stoneAmount = ElementStoneAmount::getStoneAmount($element, $this->quotationExtra);
      $mitreAmount = $this->calculateMitreAmount($splitElementParts, $element);

      $splitElementParts->stoneAmount = $stoneAmount + $mitreAmount;

      return $splitElementParts;
    }

    private function setMitreProperties(SplitElementParts $splitElementParts, ?object $leftMitre, ?object $rightMitre): void {
      if ($leftMitre && $rightMitre) {
        // Twee verstekken
        $splitElementParts->mitreLengthSizeLeft = $splitElementParts->heartLengthLeft;
        $splitElementParts->mitreAngleLeft = $leftMitre->angle;
        $splitElementParts->mitreLengthSizeRight = $splitElementParts->heartLengthRight;
        $splitElementParts->mitreAngleRight = $rightMitre->angle;
      }
      elseif ($leftMitre || $rightMitre) {
        // Een verstek
        if ($leftMitre) {
          $splitElementParts->mitreLengthSizeLeft = max($leftMitre->shortLength, $leftMitre->longLength);
          $splitElementParts->mitreAngleLeft = $leftMitre->angle;
        }
        if ($rightMitre) {
          $splitElementParts->mitreLengthSizeRight = max($rightMitre->shortLength, $rightMitre->longLength);
          $splitElementParts->mitreAngleRight = $rightMitre->angle;
        }
      }
    }

    private function calculateMitreAmount(SplitElementParts $splitElementParts, OrderElements $element): int {
      $mitreAmount = 0;

      if ($element->leftMitreId) {
        $oLeftMitre = $splitElementParts->setMitreElementPartsReturnObject($element->leftMitreId, $splitElementParts->mitreAngleLeft, $this->quotation->stoneId);
        $mitreAmount += $oLeftMitre ? $oLeftMitre->stoneCount : 0;
      }

      if ($element->rightMitreId) {
        $oRightMitre = $splitElementParts->setMitreElementPartsReturnObject($element->rightMitreId, $splitElementParts->mitreAngleRight, $this->quotation->stoneId);
        $mitreAmount += $oRightMitre ? $oRightMitre->stoneCount : 0;
      }

      return $mitreAmount;
    }

    private function calculateElementParts(SplitElementParts $splitElementParts, OrderElements $element): ?array {
      // Ensure stoneSize is available for ceramic calculations
      if (!$this->stoneSize) return null;

      // Start met 2 delen
      $elementParts = $splitElementParts->splitElementParts(2, $this->quotationExtra, $element, $this->stoneSize);

      // Probeer meer delen tot maximaal 26
      for ($i = 3; $i < 26 && $elementParts['allComparesAreTrue'] === 'false'; $i++) {
        $elementParts = $splitElementParts->splitElementParts($i, $this->quotationExtra, $element, $this->stoneSize);
      }

      return $elementParts;
    }

    private function saveOrRemoveElementParts(?array $elementParts, OrderElements $element): void {
      $orderElementParts = OrderElementparts::find_by(['elementId' => $element->elementId, 'quotationId' => $this->quotation->quotationId]);

      if ($elementParts) {
        if (!$orderElementParts) {
          $orderElementParts = new OrderElementparts();
        }

        $orderElementParts->from_array($elementParts);
        $orderElementParts->elementId = $element->elementId;
        $orderElementParts->quotationId = $this->quotation->quotationId;

        $this->ensureEmptyValuesAreSet($orderElementParts);
        $orderElementParts->save();
      }
      elseif ($orderElementParts) {
        $orderElementParts->destroy();
      }
    }

    private function ensureEmptyValuesAreSet($orderElementParts): void {
      if (empty($orderElementParts->aAmount1)) {
        $orderElementParts->aAmount1 = 0;
      }
      if (empty($orderElementParts->bAmount1)) {
        $orderElementParts->bAmount1 = 0;
      }
      if (empty($orderElementParts->cAmount1)) {
        $orderElementParts->cAmount1 = 0;
      }
      if (empty($orderElementParts->dAmount1)) {
        $orderElementParts->dAmount1 = 0;
      }
    }

    private function removeExistingElementParts(OrderElements $element): void {
      $orderElementParts = OrderElementparts::find_by(['elementId' => $element->elementId, 'quotationId' => $this->quotation->quotationId]);
      if (!$orderElementParts) return;

      $orderElementParts->destroy();
    }
  }