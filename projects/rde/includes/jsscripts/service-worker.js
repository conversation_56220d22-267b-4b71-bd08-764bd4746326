self.addEventListener("push", (event) => {

  const notif = event.data.json().notification;
  event.waitUntil(self.registration.showNotification(notif.title, {
    body: notif.body,
    icon: notif.image,
    data: {
      url: notif.click_action,
    }
  }));

  if (!notif.click_action || notif.click_action.length === 0) return;

  // Send a message to the main thread
  self.clients.matchAll({includeUncontrolled: true, type: "window"}).then((clients) => {
    let clientToPost = clients.find((client) => client.focused) || clients[0];
    if (clientToPost) {
      clientToPost.postMessage({
        type: 'RAAMDORPEL_VOIP_CALL',
        title: notif.title,
        body: notif.body,
        url: notif.click_action,
      });
    }
  });

});

self.addEventListener("notificationclick", (event) => {
  // Close the notification popout
  event.notification.close();

  if (!event.notification.data.url || event.notification.data.url.length === 0) return;

  event.waitUntil(
    // Get all the Window clients
    clients.matchAll({includeUncontrolled: true, type: "window"}).then((clientsArr) => {
      // If a Window tab matching the targeted URL already exists, focus that;
      const hadWindowToFocus = clientsArr.some((windowClient) =>
        windowClient.url === event.notification.data.url
          ? (windowClient.focus(), true)
          : false,
      );

      // Otherwise, open a new tab to the applicable URL and focus it.
      if (!hadWindowToFocus)
        clients
          .openWindow(event.notification.data.url)
          .then((windowClient) => (windowClient ? windowClient.focus() : null));
    }),
  );
});