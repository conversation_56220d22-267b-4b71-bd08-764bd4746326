Raamdorpel overzicht stenen selector app
--------------------------
Di<PERSON> is de SPA voor de Raamdorpel stenen selector app. (Dit is niet de offerte wizard)  
Met deze app zorg ervoor dat er duidelijk overzicht van steen soorten is, en dat de klant snel zijn steen kan selecteren.   

Installatie instructies
----------------------
* Draai `npm ci` in de /vueapps map
* Draai `npm ci` in de /vueapps/rde map

Algemene informatie
----------------------
De <PERSON>gende builds zijn beschik<PERSON>, te draaien de de map /vueapps/rde: 
npm run 
* `dev` - Voor actieve ontwikkeling: build als je bestanden wijzigt.
* `build-dev` - Eenmalige dev build.
* `build` - Eenmalige productie build. Draai deze alleen als je gaat releasen.

Te bekijken op URL:
* development: http://www.raamdorpel.nl.rde.localhost/natuursteen/soorten?action=stoneoverview
* productie: https://www.raamdorpel.nl/natuursteen/soorten?action=stoneoverview

PHP code: 
Deze app praat tegen de action siteRdeActions en functies executestoneoverview() en executestoneoverviewvalues()
