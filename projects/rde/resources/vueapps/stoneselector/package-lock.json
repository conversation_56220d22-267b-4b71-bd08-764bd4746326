{"name": "rde-stoneselector", "version": "0.0.1", "lockfileVersion": 2, "requires": true, "packages": {"": {"name": "rde-stoneselector", "version": "0.0.1", "dependencies": {"@vitejs/plugin-vue": "^4.0.0", "vite": "^4.0.0", "vue-sweetalert2": "^5.0.5"}}, "node_modules/@babel/parser": {"version": "7.22.16", "license": "MIT", "peer": true, "bin": {"parser": "bin/babel-parser.js"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@esbuild/linux-x64": {"version": "0.18.20", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=12"}}, "node_modules/@jridgewell/sourcemap-codec": {"version": "1.4.15", "license": "MIT", "peer": true}, "node_modules/@vitejs/plugin-vue": {"version": "4.3.4", "license": "MIT", "engines": {"node": "^14.18.0 || >=16.0.0"}, "peerDependencies": {"vite": "^4.0.0", "vue": "^3.2.25"}}, "node_modules/@vue/compiler-core": {"version": "3.3.4", "license": "MIT", "peer": true, "dependencies": {"@babel/parser": "^7.21.3", "@vue/shared": "3.3.4", "estree-walker": "^2.0.2", "source-map-js": "^1.0.2"}}, "node_modules/@vue/compiler-dom": {"version": "3.3.4", "license": "MIT", "peer": true, "dependencies": {"@vue/compiler-core": "3.3.4", "@vue/shared": "3.3.4"}}, "node_modules/@vue/compiler-sfc": {"version": "3.3.4", "license": "MIT", "peer": true, "dependencies": {"@babel/parser": "^7.20.15", "@vue/compiler-core": "3.3.4", "@vue/compiler-dom": "3.3.4", "@vue/compiler-ssr": "3.3.4", "@vue/reactivity-transform": "3.3.4", "@vue/shared": "3.3.4", "estree-walker": "^2.0.2", "magic-string": "^0.30.0", "postcss": "^8.1.10", "source-map-js": "^1.0.2"}}, "node_modules/@vue/compiler-ssr": {"version": "3.3.4", "license": "MIT", "peer": true, "dependencies": {"@vue/compiler-dom": "3.3.4", "@vue/shared": "3.3.4"}}, "node_modules/@vue/reactivity": {"version": "3.3.4", "license": "MIT", "peer": true, "dependencies": {"@vue/shared": "3.3.4"}}, "node_modules/@vue/reactivity-transform": {"version": "3.3.4", "license": "MIT", "peer": true, "dependencies": {"@babel/parser": "^7.20.15", "@vue/compiler-core": "3.3.4", "@vue/shared": "3.3.4", "estree-walker": "^2.0.2", "magic-string": "^0.30.0"}}, "node_modules/@vue/runtime-core": {"version": "3.3.4", "license": "MIT", "peer": true, "dependencies": {"@vue/reactivity": "3.3.4", "@vue/shared": "3.3.4"}}, "node_modules/@vue/runtime-dom": {"version": "3.3.4", "license": "MIT", "peer": true, "dependencies": {"@vue/runtime-core": "3.3.4", "@vue/shared": "3.3.4", "csstype": "^3.1.1"}}, "node_modules/@vue/server-renderer": {"version": "3.3.4", "license": "MIT", "peer": true, "dependencies": {"@vue/compiler-ssr": "3.3.4", "@vue/shared": "3.3.4"}, "peerDependencies": {"vue": "3.3.4"}}, "node_modules/@vue/shared": {"version": "3.3.4", "license": "MIT", "peer": true}, "node_modules/csstype": {"version": "3.1.2", "license": "MIT", "peer": true}, "node_modules/esbuild": {"version": "0.18.20", "hasInstallScript": true, "license": "MIT", "bin": {"esbuild": "bin/esbuild"}, "engines": {"node": ">=12"}, "optionalDependencies": {"@esbuild/android-arm": "0.18.20", "@esbuild/android-arm64": "0.18.20", "@esbuild/android-x64": "0.18.20", "@esbuild/darwin-arm64": "0.18.20", "@esbuild/darwin-x64": "0.18.20", "@esbuild/freebsd-arm64": "0.18.20", "@esbuild/freebsd-x64": "0.18.20", "@esbuild/linux-arm": "0.18.20", "@esbuild/linux-arm64": "0.18.20", "@esbuild/linux-ia32": "0.18.20", "@esbuild/linux-loong64": "0.18.20", "@esbuild/linux-mips64el": "0.18.20", "@esbuild/linux-ppc64": "0.18.20", "@esbuild/linux-riscv64": "0.18.20", "@esbuild/linux-s390x": "0.18.20", "@esbuild/linux-x64": "0.18.20", "@esbuild/netbsd-x64": "0.18.20", "@esbuild/openbsd-x64": "0.18.20", "@esbuild/sunos-x64": "0.18.20", "@esbuild/win32-arm64": "0.18.20", "@esbuild/win32-ia32": "0.18.20", "@esbuild/win32-x64": "0.18.20"}}, "node_modules/estree-walker": {"version": "2.0.2", "license": "MIT", "peer": true}, "node_modules/magic-string": {"version": "0.30.3", "license": "MIT", "peer": true, "dependencies": {"@jridgewell/sourcemap-codec": "^1.4.15"}, "engines": {"node": ">=12"}}, "node_modules/nanoid": {"version": "3.3.6", "funding": [{"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "bin": {"nanoid": "bin/nanoid.cjs"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}}, "node_modules/picocolors": {"version": "1.0.0", "license": "ISC"}, "node_modules/postcss": {"version": "8.4.29", "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/postcss"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"nanoid": "^3.3.6", "picocolors": "^1.0.0", "source-map-js": "^1.0.2"}, "engines": {"node": "^10 || ^12 || >=14"}}, "node_modules/rollup": {"version": "3.29.3", "license": "MIT", "bin": {"rollup": "dist/bin/rollup"}, "engines": {"node": ">=14.18.0", "npm": ">=8.0.0"}, "optionalDependencies": {"fsevents": "~2.3.2"}}, "node_modules/source-map-js": {"version": "1.0.2", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/sweetalert2": {"version": "11.7.30", "license": "MIT", "funding": {"type": "individual", "url": "https://github.com/sponsors/limonte"}}, "node_modules/vite": {"version": "4.4.9", "license": "MIT", "dependencies": {"esbuild": "^0.18.10", "postcss": "^8.4.27", "rollup": "^3.27.1"}, "bin": {"vite": "bin/vite.js"}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "funding": {"url": "https://github.com/vitejs/vite?sponsor=1"}, "optionalDependencies": {"fsevents": "~2.3.2"}, "peerDependencies": {"@types/node": ">= 14", "less": "*", "lightningcss": "^1.21.0", "sass": "*", "stylus": "*", "sugarss": "*", "terser": "^5.4.0"}, "peerDependenciesMeta": {"@types/node": {"optional": true}, "less": {"optional": true}, "lightningcss": {"optional": true}, "sass": {"optional": true}, "stylus": {"optional": true}, "sugarss": {"optional": true}, "terser": {"optional": true}}}, "node_modules/vue": {"version": "3.3.4", "license": "MIT", "peer": true, "dependencies": {"@vue/compiler-dom": "3.3.4", "@vue/compiler-sfc": "3.3.4", "@vue/runtime-dom": "3.3.4", "@vue/server-renderer": "3.3.4", "@vue/shared": "3.3.4"}}, "node_modules/vue-sweetalert2": {"version": "5.0.5", "license": "MIT", "dependencies": {"sweetalert2": "11.x"}, "peerDependencies": {"vue": "*"}}}, "dependencies": {"@babel/parser": {"version": "7.22.16", "peer": true}, "@esbuild/linux-x64": {"version": "0.18.20", "optional": true}, "@jridgewell/sourcemap-codec": {"version": "1.4.15", "peer": true}, "@vitejs/plugin-vue": {"version": "4.3.4", "requires": {}}, "@vue/compiler-core": {"version": "3.3.4", "peer": true, "requires": {"@babel/parser": "^7.21.3", "@vue/shared": "3.3.4", "estree-walker": "^2.0.2", "source-map-js": "^1.0.2"}}, "@vue/compiler-dom": {"version": "3.3.4", "peer": true, "requires": {"@vue/compiler-core": "3.3.4", "@vue/shared": "3.3.4"}}, "@vue/compiler-sfc": {"version": "3.3.4", "peer": true, "requires": {"@babel/parser": "^7.20.15", "@vue/compiler-core": "3.3.4", "@vue/compiler-dom": "3.3.4", "@vue/compiler-ssr": "3.3.4", "@vue/reactivity-transform": "3.3.4", "@vue/shared": "3.3.4", "estree-walker": "^2.0.2", "magic-string": "^0.30.0", "postcss": "^8.1.10", "source-map-js": "^1.0.2"}}, "@vue/compiler-ssr": {"version": "3.3.4", "peer": true, "requires": {"@vue/compiler-dom": "3.3.4", "@vue/shared": "3.3.4"}}, "@vue/reactivity": {"version": "3.3.4", "peer": true, "requires": {"@vue/shared": "3.3.4"}}, "@vue/reactivity-transform": {"version": "3.3.4", "peer": true, "requires": {"@babel/parser": "^7.20.15", "@vue/compiler-core": "3.3.4", "@vue/shared": "3.3.4", "estree-walker": "^2.0.2", "magic-string": "^0.30.0"}}, "@vue/runtime-core": {"version": "3.3.4", "peer": true, "requires": {"@vue/reactivity": "3.3.4", "@vue/shared": "3.3.4"}}, "@vue/runtime-dom": {"version": "3.3.4", "peer": true, "requires": {"@vue/runtime-core": "3.3.4", "@vue/shared": "3.3.4", "csstype": "^3.1.1"}}, "@vue/server-renderer": {"version": "3.3.4", "peer": true, "requires": {"@vue/compiler-ssr": "3.3.4", "@vue/shared": "3.3.4"}}, "@vue/shared": {"version": "3.3.4", "peer": true}, "csstype": {"version": "3.1.2", "peer": true}, "esbuild": {"version": "0.18.20", "requires": {"@esbuild/android-arm": "0.18.20", "@esbuild/android-arm64": "0.18.20", "@esbuild/android-x64": "0.18.20", "@esbuild/darwin-arm64": "0.18.20", "@esbuild/darwin-x64": "0.18.20", "@esbuild/freebsd-arm64": "0.18.20", "@esbuild/freebsd-x64": "0.18.20", "@esbuild/linux-arm": "0.18.20", "@esbuild/linux-arm64": "0.18.20", "@esbuild/linux-ia32": "0.18.20", "@esbuild/linux-loong64": "0.18.20", "@esbuild/linux-mips64el": "0.18.20", "@esbuild/linux-ppc64": "0.18.20", "@esbuild/linux-riscv64": "0.18.20", "@esbuild/linux-s390x": "0.18.20", "@esbuild/linux-x64": "0.18.20", "@esbuild/netbsd-x64": "0.18.20", "@esbuild/openbsd-x64": "0.18.20", "@esbuild/sunos-x64": "0.18.20", "@esbuild/win32-arm64": "0.18.20", "@esbuild/win32-ia32": "0.18.20", "@esbuild/win32-x64": "0.18.20"}}, "estree-walker": {"version": "2.0.2", "peer": true}, "magic-string": {"version": "0.30.3", "peer": true, "requires": {"@jridgewell/sourcemap-codec": "^1.4.15"}}, "nanoid": {"version": "3.3.6"}, "picocolors": {"version": "1.0.0"}, "postcss": {"version": "8.4.29", "requires": {"nanoid": "^3.3.6", "picocolors": "^1.0.0", "source-map-js": "^1.0.2"}}, "rollup": {"version": "3.29.3", "requires": {"fsevents": "~2.3.2"}}, "source-map-js": {"version": "1.0.2"}, "sweetalert2": {"version": "11.7.30"}, "vite": {"version": "4.4.9", "requires": {"esbuild": "^0.18.10", "fsevents": "~2.3.2", "postcss": "^8.4.27", "rollup": "^3.27.1"}}, "vue": {"version": "3.3.4", "peer": true, "requires": {"@vue/compiler-dom": "3.3.4", "@vue/compiler-sfc": "3.3.4", "@vue/runtime-dom": "3.3.4", "@vue/server-renderer": "3.3.4", "@vue/shared": "3.3.4"}}, "vue-sweetalert2": {"version": "5.0.5", "requires": {"sweetalert2": "11.x"}}}}