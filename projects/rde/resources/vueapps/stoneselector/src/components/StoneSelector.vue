<template>
  <div class="gsdeditor" v-cloak>
    <div v-if="loading" id="loader">
      <img src="../assets/loader.gif"/>
    </div>
    <div v-else>
      <div id="filter">
        <div class="select">
          <select id="brand" v-model="brandId">
            <option value="">Merk...</option>
            <option v-for="brand in filteredBrands" :key="brand.brandId" :value="brand.brandId">{{brand.name}}</option>
          </select>
        </div>
        <div class="select">
          <select id="color" v-model="colorGroupId">
            <option value="">Kleur...</option>
            <option v-for="color in filteredColors" :key="color.colorGroupId" :value="color.colorGroupId">{{color.name}}</option>
          </select>
        </div>
        <div class="select">
          <select id="variant" v-model="categoryId">
            <option value="">Variant...</option>
            <option v-for="cat in categories" :key="cat.id" :value="cat.id">{{cat.shortname}}</option>
          </select>
        </div>
        <div class="select">
          <select id="thickness" v-model="thicknessId">
            <option value="">Dikte...</option>
            <option value="thin">Dun</option>
            <option value="thick">Dik</option>
          </select>
        </div>
        <a href="#" @click.prevent="resetFilter">Reset</a>
      </div>
      <div v-if="noneFound">
        Geen producten gevonden bij deze filter.
      </div>
      <div v-for="brand in filteredList" :key="brand.brandId">
        <div v-for="color in brand.colors" :key="color.colorId">
          <div v-for="categorie in color.categories" :key="categorie.id">
            <div class="group-title">{{brand.name}} | {{color.name}} | {{categorie.shortname}}</div>
            <div v-for="group in categorie.groups">
              <div class="product-row">
                <div v-for="(size, index) in group.items" class="product" :class="{'not-available' : size == false }">
                  <a href="#" @click.prevent="clickOther(brand.brandId, color.colorId, categorie.id, group)" v-if="index=='Overige maten'" class="product-container product-other">
                    Overige<br/>
                    maten
                  </a>
                  <a href="#" @click.prevent="clickSize(size)" v-else class="product-container">
                    <div class="product-image">
                      <img :src="'https://www.raamdorpel.nl/images/thresholds/'+size.stone.image"  v-if="size.stone && size.stone.image!='' && size.stone.image!=null"/>
                    </div>
                    <div v-if="size.pricePerMeter" class="product-price-circle" :class="getPriceClass(size.pricePerMeter)" title="Prijs per meter">{{getPriceFormatted(size.pricePerMeter)}}</div>
                    <div class="product-title" v-if="size != false">{{size.name}} - {{groupName(group.name)}}</div>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {h} from 'vue'

const config = window.gsdStoneSelectorConfig; //get config from windows variable

let id = 1;
export default {
  name: "GsdEditor",
  components: {
  },
  data() {
    return {
      loading: true,
      colors: [],
      colorsFlatten: [],
      brands: [],
      categories: [],
      list: [],
      filteredList: [],
      brandId: '',
      colorGroupId: '',
      categoryId: '',
      thicknessId: '',
      filteredColors: [],
      filteredBrands: [],
      noneFound: false,
    };
  },
  created() {
    this.fetchData();
  },
  mounted() {
    //get filters from localstore
    if (localStorage.stoneFilter) {
      let filter = JSON.parse(localStorage.stoneFilter);
      this.brandId = filter.brandId;
      this.colorGroupId = filter.colorGroupId;
      this.categoryId = filter.categoryId;
      this.thicknessId = filter.thicknessId;
    }
  },
  computed: {
    filteredList() {
      // return this.list;

      this.noneFound = true;

      //kopiereen opmdat ik de hele json op zijn kop gooi
      this.filteredList = JSON.parse(JSON.stringify(this.list));

      let colorIds = [];
      if(this.colorGroupId!=="") {
        //filter op kleur
        colorIds = this.getColorIdsByColorGroup(this.colorGroupId);
      }

      this.filteredList = this.filteredList.filter(brand => {

        //filter op merk
        if(this.brandId!=="" && this.brandId!=brand.brandId) {
          return false;
        }

        brand.colors = brand.colors.filter(color => {

          if(this.colorGroupId!=="") {
            //filter op kleur
            if (!colorIds.includes(color.colorId)) {
              return false;
            }
          }

          //kleur mag getoond worden, filter op variant
          color.categories = color.categories.filter(category => {
            if(this.categoryId!=="" && this.categoryId!=category.id) {
              return false;
            }

            //variant mag getoond worden, filter op dikte
            if(this.thicknessId!=="") {
              if(this.thicknessId==="thin") {
                delete category.groups.thick;
              }
              if(this.thicknessId==="thick") {
                delete category.groups.thin;
              }
            }

            //er moet minstens 1 product worden gevonden.
            this.noneFound = false;
            return true;
          });

          return true;
        });

        if(brand.colors.length==0) return false;
        return true;
      });

      this.setFilters();

      return this.filteredList;
    },
  },
  methods: {
    getColorIdsByColorGroup(colorGroupId) {
      for (let colorKey in this.colorsFlatten) {
        if(this.colorsFlatten[colorKey].colorGroupId == colorGroupId) {
          return this.colorsFlatten[colorKey].ids;
        }
      }
      return [];
    },
    setFilters() {

      localStorage.setItem("stoneFilter", JSON.stringify({
        "brandId": this.brandId,
        "colorGroupId": this.colorGroupId,
        "categoryId": this.categoryId,
        "thicknessId": this.thicknessId,
      }));

      this.filteredColors = this.colorsFlatten;
      this.filteredBrands = this.brands;

      if(this.brandId!=="") {
        //merk is gezet, filter kleuren

        //ophalen mogelijke kleuren bij dit merk
        let colorIds = [];
        for (let brandKey in this.filteredList) {
          let brand = this.filteredList[brandKey];
          for (let colorKey in brand.colors) {
            let color = brand.colors[colorKey];
            colorIds.push(color.colorId);
          }
        }

        //filter kleuren select op deze merken
        this.filteredColors = this.colorsFlatten.filter(color => {
          for (let colorKey in color.ids) {
            let colorId = color.ids[colorKey];
            if (colorIds.includes(colorId)) {
              return true;
            }
          }
          return false;
        });
      }
      else if(this.colorGroupId!=="") {
        //kleur is gezet, filter merken
        let colorIds = this.getColorIdsByColorGroup(this.colorGroupId);

        //verzamel alle merken met deze kleur
        let brandIds = [];
        for (let brandKey in this.filteredList) {
          let brand = this.filteredList[brandKey];
          for (let colorKey in brand.colors) { //loop over kleuren van dit merk
            let color = brand.colors[colorKey];
            if(colorIds.includes(color.colorId)) {
              //deze kleur zit in dit merk
              brandIds.push(brand.brandId);
            }
          }
        }

        //filter merken select op deze merken
        this.filteredBrands = this.brands.filter(brand => {
          if (brandIds.includes(brand.brandId)) {
            return true;
          }
          return false;
        });
      }


    },
    fetchData() {
      let url = '';
      fetch(url+"?action=stoneoverviewvalues", {
        headers: {'Content-type': 'application/json'},
      }).then(res => res.json()).then((response) => {

        // if(ErrorHelper.handleError(this, response)) return;
        // console.log(response);
        this.brands = response.brands;
        this.colors = response.colors;
        this.colorsFlatten = response.colorsFlatten;
        this.categories = response.categories;
        this.list = response.list;
        this.loading = false;

      }).catch((error) => {
        // this.errors = [];
        // this.errors.push("Server fout opgetreden. Probeer het later nog eens, of neem contact op (" + error + ")");
      });
    },
    groupName(name) {
      if(name=="thin") return "DUN";
      if(name=="thick") return "DIK";
    },
    clickSize(size) {
      if(size) {
        let url = "/offerte?action=wizard&stoneId=" + size.stone.stoneId + "&sizeId="+size.sizeId;
        // console.log(url);
        location.href= url;
        return;
      }
      this.$swal({
        title: 'Niet beschikbaar',
        text: "Deze variant is niet beschikbaar",
        icon: 'error',
      });
    },
    clickOther(brandId, colorId, variant, group) {
      let sizeFound = false;
      for (let itemKey in group.items) {
        let item = group.items[itemKey];
        if(item!==false) {
          sizeFound = item;
          break;
        }
      }
      let url = "/offerte?action=wizard&brandId=" + brandId + "&colorId=" + colorId + "&variant=" + variant;
      if(sizeFound) {
        url += "&stoneId="+sizeFound.stone.stoneId;
      }
      // console.log(url);
      location.href= url;
    },
    resetFilter() {
      this.filteredList = JSON.parse(JSON.stringify(this.list));
      this.brandId = "";
      this.colorGroupId = "";
      this.categoryId = "";
      this.thicknessId = "";
      this.setFilters();
    },
    getPriceFormatted(pricePerMeter) {
      if(pricePerMeter === undefined) return "?";

      let decimals = 2;
      let str = String(pricePerMeter);
      str = str.replace(',','.');
      str = Round(str, decimals).toFixed(decimals).toString();
      if(isNaN(str)) return '';
      str = str.replace('.',',');
      return "€ "+str;
      // return "€ 10,-";
    },
    getPriceClass(pricePerMeter) {
      if(pricePerMeter>=100) return "product-price-100";
      return "";
    },

  }
};
</script>
<style>
.product-row {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
}
.product {
  width: 100%;
  border: 1px solid #e8e8e8;
  border-radius: 5px 5px 0 0;
  position: relative;
}
.product:hover {
  opacity: 0.8;
}
.product-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-direction: column;
  min-height: 192.5px;
  text-align: center;
}
.product-other {
  display: flex;
  justify-content: center;
  font-weight: 600;
  font-size: 1.2em;
}
.product.not-available {
  background: #ebebeb;
}
.product .product-image {
}
.product img {
  max-width: 100%;
}
.product .product-title {
  background-color: #ebebeb;
  width: 100%;
  padding: 10px;
  font-weight: 600;
}
.group-title {
  padding: 0 0 15px 0;
  font-weight: 600;
}
#filter {
  display: flex;
  margin: 15px 0;
  gap: 15px;
  align-items: center;
}

.product-price-circle {
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 37.5px;
  padding: 25px 5px;
  height: 75px;
  width: 75px;
  position: absolute;
  top: -12px;
  right: -12px;
  background-color: white;
  color: black;
  box-shadow: 0 1px 3px 0 rgba(189, 189, 189, 1);
  font-size: 16.5px;
  font-weight: normal;
}
.product-price-100 {
}

</style>