<template>
  <div id="map-container">
    <div id="map" class="google-maps" />
    
    <v-dialog v-model="showTruckNotFoundDialog" max-width="400">
      <v-card>
        <v-card-text>
          Truck werd niet gevonden, heb je GPS Buddy aan staan?
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn @click="showTruckNotFoundDialog = false">OK</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script setup>
import {ref, watch} from 'vue'
import axios from "axios";
import { useGoogleMaps } from "../composables/googleMaps";
import { customIcons } from "../helpers/IconHelper";
import {useStatusStore} from "../store/status";
import {useFilterStore} from "../store/filter";
import {useTruckStore} from "../store/truck";
import {useMarkerStore} from "../store/marker";

const emit = defineEmits(['selectedCompanyChange'])

const statusStore = useStatusStore()
const filterStore = useFilterStore()
const truckStore = useTruckStore()
const markerStore = useMarkerStore()

watch(() => statusStore.selectedStatuses, async () => {
  await initMarkers()
})
watch(() => filterStore.dateRoute4weeks, async () => {
  await initMarkers()
})
watch(() => filterStore.showContainers, async () => {
  await initMarkers()
})
watch(() => filterStore.showPoi, async () => {
  await initMarkers()
})
watch(() => filterStore.smallOrders, async () => {
  await initMarkers()
})
watch(() => filterStore.showBrandChinese, async () => {
  await initMarkers()
})
watch(() => filterStore.showBrandBelgium, async () => {
  await initMarkers()
})
watch(() => filterStore.showGpsBuddy, async () => {
  await initBuddyMarkers()
})

const { map, loader } = useGoogleMaps('map');
watch(loader, async (value) => {
  if (!value) return
  await initMarkers()

  if (!filterStore.showGpsBuddy) return
  await initBuddyMarkers()
})

const markers = ref([])
const initMarkers = async () => {
  clearMarkers()

  const { InfoWindow } = await loader.value.importLibrary("maps")
  const { AdvancedMarkerElement, PinElement } = await loader.value.importLibrary("marker")
  const { LatLng } = await loader.value.importLibrary("core")

  const { data } = await axios.get('?action=getmarkers', {
    params: {
      dateRoute4weeks: filterStore.dateRoute4weeks,
      showContainers: filterStore.showContainers ? true : null,
      showPoi: filterStore.showPoi ? true : null,
      showGpsBuddy: filterStore.showGpsBuddy ? true : null,
      smallOrders: filterStore.smallOrders ? true : null,
      showBrandChinese: filterStore.showBrandChinese ? true : null,
      showBrandBelgium: filterStore.showBrandBelgium ? true : null,
      status: statusStore.selectedStatuses
    }
  })
  for(const marker of data) {
    if(customIcons[marker.icon] === undefined) {
      console.log("Onbekend icoon: ", marker);
      continue;
    }
    const iconFile = customIcons[marker.icon].icon
    const iconUrl = import.meta.env.DEV
      ? `/src/assets/${iconFile}`
      : `/projects/rde/templates/map/assets/${iconFile}`
    const img = document.createElement('img')
    img.src = iconUrl

    const newMarker = new AdvancedMarkerElement({
      position: new LatLng(marker.lat, marker.lng),
      map: map.value,
      title: marker.title,
      content: img
    })
    markers.value.push(newMarker)

    newMarker.addListener("gmp-click", () => {
      markerStore.selectedMarker = marker
    })
  }
}
const clearMarkers = () => {
  markers.value.forEach(marker => {
    marker.map = null;
  });
  markers.value = [];
}

// const createMapDirections = async () => {
//   if (companiesWithAddress.value.length < 2) return
//
//   const { DirectionsService, DirectionsRenderer, LatLng } = await loader.value.importLibrary("routes")
//   const { TravelMode } = await loader.value.importLibrary("maps")
//
//   const directionsService = new DirectionsService()
//   const directionsRenderer = new DirectionsRenderer({ suppressMarkers: true })
//   directionsRenderer.setMap(map.value)
//
//   const directions_json = {
//     origin: new LatLng(
//       companiesWithAddress.value[0].address.latitude,
//       companiesWithAddress.value[0].address.longitude
//     ),
//     destination: new LatLng(
//       companiesWithAddress.value[companiesWithAddress.value.length - 1].address.latitude,
//       companiesWithAddress.value[companiesWithAddress.value.length - 1].address.longitude
//     ),
//     waypoints: companiesWithAddress.value.slice(1, -1).map(company => ({
//       location: new LatLng(company.address.latitude, company.address.longitude)
//     })),
//     travelMode: TravelMode.DRIVING
//   }
//
//   try {
//     const result = await directionsService.route(directions_json)
//     directionsRenderer.setDirections(result)
//   } catch (e) {
//     console.error("Could not display directions due to:", e)
//   }
// }

const buddyMarkers = ref({})
const clearBuddyMarkers = () => {
  Object.values(buddyMarkers.value).forEach(marker => {
    marker.map = null;
  });
  buddyMarkers.value = {};
  showTruckNotFoundDialog.value = false
}

const initBuddyMarkers = async () => {
  clearBuddyMarkers()
  
  if (!filterStore.showGpsBuddy) return

  const { InfoWindow } = await loader.value.importLibrary("maps")
  const { AdvancedMarkerElement } = await loader.value.importLibrary("marker")
  const { LatLng } = await loader.value.importLibrary("core")

  const { data } = await axios.get('/nl/external?action=trucklocations');
  for(const truckLocation of data) {
    const iconFile = customIcons['truck'].icon
    const iconUrl = import.meta.env.DEV
      ? `/src/assets/${iconFile}`
      : `/projects/rde/templates/map/assets/${iconFile}`
    const img = document.createElement('img')
    img.src = iconUrl

    const newMarker = new AdvancedMarkerElement({
      position: new LatLng(truckLocation.latitude, truckLocation.longitude),
      map: map.value,
      title: truckLocation.location,
      content: img
    })

    const infoWindow = new InfoWindow({
      content: `
        <div>
          <strong>${truckLocation.vehiclename}</strong><br>
          ${truckLocation.location}<br>
          <a href="https://frontoffice2.gps-buddy.com/nl-NL/Account/Login" target="_blank">GPS Buddy</a>
        </div>
      `
    })

    newMarker.addListener("gmp-click", () => {
      infoWindow.open(map.value, newMarker)
    })

    buddyMarkers.value[truckLocation.vehicleid] = newMarker
  }
}

const showTruckNotFoundDialog = ref(false)
const focusOnTruck = (truckId) => {
  if (!truckId) return
  if (!filterStore.showGpsBuddy) return showTruckNotFoundDialog.value = true

  const truck = truckStore.trucks.find(t => t.truckId === truckId)
  if (!truck) return showTruckNotFoundDialog.value = true

  const targetMarker = buddyMarkers.value[truck.gpsbuddyId]

  if (!targetMarker || !map.value) return showTruckNotFoundDialog.value = true

  map.value.setCenter(targetMarker.position)
  map.value.setZoom(16)
}
watch(() => truckStore.clickedTruck, (value) => {
  if (value.truck) {
    focusOnTruck(value.truck.truckId)
  }
})
</script>

<style>
#map-container {
  height: 100%;
  width: 100%;
}

.google-maps {
  width: 100%;
  height: 100%;
}

.marker-window {
  padding: 8px;
  max-width: 300px;
}
</style>

