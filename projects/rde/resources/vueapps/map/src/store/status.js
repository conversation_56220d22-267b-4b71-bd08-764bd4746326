import { defineStore } from "pinia"
import { ref } from "vue"
import axios from "axios"
import { useErrorStore } from "./error";
export const useStatusStore = defineStore('statusStore', () => {
  const errorStore = useErrorStore()
  const statuses = ref()
  const selectedStatuses = ref([])
  const allStatusesSelected = ref(false)
  async function getStatuses() {
    try {
      const { data } = await axios.get('?action=getstatuses')
      statuses.value = data
      errorStore.clearErrors()
    } catch (error) {
      errorStore.addError(`Server fout opgetreden. Probeer het later nog eens, of neem contact op (${error})`)
    }
  }
  const toggleAllStatuses = (checked) => {
    if (!statuses.value) return

    if (checked) {
      selectedStatuses.value = Object.entries(statuses.value).map(([key, value]) => key)
    } else {
      selectedStatuses.value = []
    }
    allStatusesSelected.value = checked
  }
  return {
    statuses,
    allStatusesSelected,
    getStatuses,
    selectedStatuses,
    toggleAllStatuses,
  }
})