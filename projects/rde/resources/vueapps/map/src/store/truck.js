import { defineStore } from "pinia"
import { ref } from "vue"
import axios from "axios"
import { useErrorStore } from "./error";
export const useTruckStore = defineStore('truckStore', () => {
  const errorStore = useErrorStore()
  const trucks = ref([])
  async function getTrucks() {
    try {
      const { data } = await axios.get('?action=getactivetrucks')
      trucks.value = data
      errorStore.clearErrors()
    } catch (error) {
      errorStore.addError(`Server fout opgetreden. Probeer het later nog eens, of neem contact op (${error})`)
    }
  }
  const clickedTruck = ref({ truck: null, timestamp: 0 })
  function truckClicked(truck) {
    clickedTruck.value = { truck, timestamp: Date.now() }
  }
  return {
    trucks,
    getTrucks,
    truckClicked,
    clickedTruck,
  }
})