<template>
  <v-app>
    <v-main id="main-container">
      <div class="filter-column">
        <Filter />
      </div>
      <Map class="map-column"/>
      <Route class="route-column" />
    </v-main>
  </v-app>
</template>

<script setup>
import Map from './components/Map.vue'
import Filter from './components/Filter.vue'
import Route from './components/Route.vue'
</script>

<style>
#main-container {
  display: flex;
  width: 100%;
  height: 100vh;
}

.filter-column {
  width: 300px;
  height: 100%;
  overflow-y: auto;
  flex-shrink: 0;
}

.map-column {
  flex-grow: 1;
  height: 100%;
  position: relative;
}

.route-column {
  width: 250px;
  height: 100%;
  overflow-y: auto;
  flex-shrink: 0;
}
</style>