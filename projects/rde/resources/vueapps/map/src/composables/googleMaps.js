import { shallowRef, onMounted } from 'vue';
import { Loader } from '@googlemaps/js-api-loader';
import axios from 'axios';

/**
 * A composable for handling Google Maps API loading and map initialization.
 *
 * @param {string} mapId - The ID of the map container element (e.g., "map").
 * @param {object} initialCenter - The initial center coordinates for the map.
 * @returns {object} An object containing the map & loader instance
 */
export function useGoogleMaps(mapId, initialCenter = { lat: 51.36268401837771, lng: 5.2673055283842745 }) {
  const map = shallowRef();
  const loader = shallowRef();

  const initLoader = async () => {
    try {
      const { data } = await axios.get('?action=getGoogleMapsApiKey');
      loader.value = new Loader({
        apiKey: data,
        libraries: ["places", "geometry", "marker", "routes"], // Added 'routes' here for future use
      });
    } catch (error) {
      console.error('Error fetching Google Maps API key:', error);
    }
  };

  const initMap = async () => {
    if (!loader.value) {
      console.error('Google Maps Loader is not initialized.');
      return;
    }
    try {
      const { Map } = await loader.value.importLibrary("maps");
      map.value = new Map(document.getElementById(mapId), {
        zoom: 8,
        center: initialCenter,
        mapId: "ec8ef85a183320e871fc0ec5",
      });
    } catch (error) {
      console.error('Error loading Google Maps:', error);
    }
  };

  onMounted(async () => {
    await initLoader();
    await initMap();
  });

  return {
    map,
    loader,
  };
}