{"name": "wizard", "version": "0.1.0", "private": true, "scripts": {"serve": "export NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service serve", "build": "export NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service build && npm run sentry:sourcemaps", "lint": "export NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service lint", "sentry:sourcemaps": "sentry-cli sourcemaps inject --org gsderror --project javascript-vue ../../../templates/frontend/dist-spa/ && sentry-cli sourcemaps upload --org gsderror --project rde-wizard ../../../templates/frontend/dist-spa/"}, "dependencies": {"@sentry/cli": "^2.39.1", "@sentry/vue": "^8.43.0", "core-js": "^3.6.5", "vue": "^3.0.0", "vue-sweetalert2": "^5.0.2", "vue3-popper": "^1.4.1"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/compiler-sfc": "^3.0.0", "babel-eslint": "^10.1.0", "eslint": "^6.7.2", "eslint-plugin-vue": "^7.0.0", "sass": "^1.26.5", "sass-loader": "^8.0.2"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}