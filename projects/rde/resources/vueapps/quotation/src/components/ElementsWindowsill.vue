<template>

  <div class="wizard_inleiding">
    Vul in onderstaand lijst uw elementmaten in. U kunt ook tabs enters gebruiken bij de invoer.
  </div>

  <div class="alert alert-danger" v-if="errors.length>0">
    <PERSON>r z<PERSON><PERSON><PERSON> opgetreden, controleer uw invoer:
    <ul>
      <li v-for="error in errors" :key="error">{{error}}</li>
    </ul>
  </div>

  <div id="step2-vensterbank" class="wizard" v-cloak v-if="!loading">

    <form method="post" :ref="setForm">

      <div class="scroller-x">
        <div class="vensterbank-element" v-for="(element, key) in elements" :key="key">
          <div class="vensterbank-head">
            <div>
              <div class="vensterbank-kenmerk">Kenmerk</div>
              <input autocomplete="off" type="text" v-model="element.referenceName" @focus="$event.target.select()" :name="'elements['+key+'][referenceName]'" :id="'elements['+key+'][referenceName]'" :ref="'referenceName_'+key" @keydown="navigateReferenceName(key)" @keyup="navigateReferenceNameUp(key)" @change="validateReferenceNameInput(element,key)"  placeholder="Kenmerk..." class="form-input" :class="{inputerror: element.hasReferenceNameError}" maxlength="12" />
            </div>
            <div>
              <a href="#" class="elementtable_fa" v-on:click="removeElement(key)" title="Verwijderen" :ref="'remove_'+key">
                <i class="fa fa-remove"></i>
              </a>
              <a href="#close" @click.prevent="toggle(element, key)"><i class="fa" :class="{'fa-chevron-up': element.isShown, 'fa-chevron-down': !element.isShown}" ></i></a>
            </div>
          </div>
          <div class="vensterbank-content" :class="{'vensterbank-content-hidden':!element.isShown}">
            <div class="vensterbank-form">
              <div class="form-row">
                <label class="col3 col-form-label">Bovenaanzicht</label>
                <div class="col9">
                  <WindowsillSelect
                      ref="colorSelect"
                      v-bind:key="key"
                      v-bind:element="element"
                      v-bind:windowsillsTemplates="windowsillsTemplates"
                      v-on:changeWindowsillSelection="changeSelection(element, $event)">
                  ></WindowsillSelect>
                  <input type="hidden" :name="'elements['+key+'][windowsill_id]'"  :value="element.windowsill.windowsill_id"/>
                </div>
              </div>

              <div v-if="element.windowsillTemplate && element.windowsillTemplate.id!=-1">
                <div class="form-row">
                  <label class="col3 col-form-label">Maten</label>
                  <div class="col9">
                    <div v-if="element.windowsillTemplate" class="windowsillTemplate">
                      <div v-if="element.windowsillTemplate.x1">
                        <label>X1: {{element.windowsillTemplate.x1}}</label>
                        <input
                            type="number"
                            step="1"
                            v-model="element.windowsill.x1"
                            :name="'elements['+key+'][x1]'"
                            class="form-input inputnumber"
                            @change="validateSize(element, 'x1')"
                            @blur="element.windowsill.x1 = formatValue(element.windowsillTemplate.x1, element.windowsill.x1)"
                        />
                        {{getMetric(element.windowsillTemplate.x1)}}

                        <span v-if="element.windowsillTemplate.x1 === 'Hoek'" class="btn-options">
                          <button type="button" class="btn-option" @click="element.windowsill.x1 = 45">45°</button>
                          <button type="button" class="btn-option" @click="element.windowsill.x1 = 60">60°</button>
                          <button type="button" class="btn-option" @click="element.windowsill.x1 = 67.5">67.5°</button>
                        </span>
                      </div>
                      <div v-if="element.windowsillTemplate.x2">
                        <label>X2: {{element.windowsillTemplate.x2}}</label>
                        <input
                            type="number"
                            step="1"
                            v-model="element.windowsill.x2"
                            :name="'elements['+key+'][x2]'"
                            class="form-input inputnumber"
                            @change="validateSize(element, 'x2')"
                            @blur="element.windowsill.x2 = formatValue(element.windowsillTemplate.x2, element.windowsill.x2)"
                        />
                        {{getMetric(element.windowsillTemplate.x2)}}

                        <span v-if="element.windowsillTemplate.x2 === 'Hoek'" class="btn-options">
                          <button type="button" class="btn-option" @click="element.windowsill.x2 = 45">45°</button>
                          <button type="button" class="btn-option" @click="element.windowsill.x2 = 60">60°</button>
                          <button type="button" class="btn-option" @click="element.windowsill.x2 = 67.5">67.5°</button>
                        </span>
                      </div>
                      <div v-if="element.windowsillTemplate.x3">
                        <label>X3: {{element.windowsillTemplate.x3}}</label>
                        <input
                            type="number"
                            step="1"
                            v-model="element.windowsill.x3"
                            :name="'elements['+key+'][x3]'"
                            class="form-input inputnumber"
                            @change="validateSize(element, 'x3')"
                            @blur="element.windowsill.x3 = formatValue(element.windowsillTemplate.x3, element.windowsill.x3)"
                        />
                        {{getMetric(element.windowsillTemplate.x3)}}

                        <span v-if="element.windowsillTemplate.x3 === 'Hoek'" class="btn-options">
                          <button type="button" class="btn-option" @click="element.windowsill.x3 = 45">45°</button>
                          <button type="button" class="btn-option" @click="element.windowsill.x3 = 60">60°</button>
                          <button type="button" class="btn-option" @click="element.windowsill.x3 = 67.5">67.5°</button>
                        </span>
                      </div>
                      <div v-if="element.windowsillTemplate.x4">
                        <label>X4: {{element.windowsillTemplate.x4}}</label>
                        <input
                            type="number"
                            step="1"
                            v-model="element.windowsill.x4"
                            :name="'elements['+key+'][x4]'"
                            class="form-input inputnumber"
                            @change="validateSize(element, 'x4')"
                            @blur="element.windowsill.x4 = formatValue(element.windowsillTemplate.x4, element.windowsill.x4)"
                        />
                        {{getMetric(element.windowsillTemplate.x4)}}

                        <span v-if="element.windowsillTemplate.x4 === 'Hoek'" class="btn-options">
                          <button type="button" class="btn-option" @click="element.windowsill.x4 = 45">45°</button>
                          <button type="button" class="btn-option" @click="element.windowsill.x4 = 60">60°</button>
                          <button type="button" class="btn-option" @click="element.windowsill.x4 = 67.5">67.5°</button>
                        </span>
                      </div>
                      <div v-if="element.windowsillTemplate.x5">
                        <label>X5: {{element.windowsillTemplate.x5}}</label>
                        <input
                            type="number"
                            step="1"
                            v-model="element.windowsill.x5"
                            :name="'elements['+key+'][x5]'"
                            class="form-input inputnumber"
                            @change="validateSize(element, 'x5')"
                            @blur="element.windowsill.x5 = formatValue(element.windowsillTemplate.x5, element.windowsill.x5)"
                        />
                        {{getMetric(element.windowsillTemplate.x5)}}

                        <span v-if="element.windowsillTemplate.x5 === 'Hoek'" class="btn-options">
                          <button type="button" class="btn-option" @click="element.windowsill.x5 = 45">45°</button>
                          <button type="button" class="btn-option" @click="element.windowsill.x5 = 60">60°</button>
                          <button type="button" class="btn-option" @click="element.windowsill.x5 = 67.5">67.5°</button>
                        </span>
                      </div>
                      <div v-if="element.windowsillTemplate.x6">
                        <label>X6: {{element.windowsillTemplate.x6}}</label>
                        <input
                            type="number"
                            step="1"
                            v-model="element.windowsill.x6"
                            :name="'elements['+key+'][x6]'"
                            class="form-input inputnumber"
                            @change="validateSize(element, 'x6')"
                            @blur="element.windowsill.x6 = formatValue(element.windowsillTemplate.x6, element.windowsill.x6)"
                        />
                        {{getMetric(element.windowsillTemplate.x6)}}

                        <span v-if="element.windowsillTemplate.x6 === 'Hoek'" class="btn-options">
                          <button type="button" class="btn-option" @click="element.windowsill.x6 = 45">45°</button>
                          <button type="button" class="btn-option" @click="element.windowsill.x6 = 60">60°</button>
                          <button type="button" class="btn-option" @click="element.windowsill.x6 = 67.5">67.5°</button>
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="form-row">
                  <label class="col3 col-form-label">Overzicht</label>
                  <div class="col9 vensterbank-image">
                    <img :src="'//www.raamdorpel.nl/images/thresholds/vensterbanken/'+windowsillsTemplates[element.windowsill.windowsill_id].imagefilename"   v-if="windowsillsTemplates[element.windowsill.windowsill_id].imagefilename!=null && windowsillsTemplates[element.windowsill.windowsill_id].imagefilename!=''"/>
                  </div>
                </div>

                <div class="form-row">
                  <label class="col3 col-form-label">Aantal</label>
                  <div class="col9">
                    <input autocomplete="off" type="number" v-model="element.amount" @focus="$event.target.select()" :name="'elements['+key+'][amount]'"  :ref="'amount_'+key" @change="validateAmountInput(element)" min="1" placeholder="Aantal..." class="form-input inputnumber" maxlength="4"/>
                  </div>
                </div>

                <div class="form-row">
                  <label class="col3 col-form-label">Opmerking</label>
                  <div class="col9">
                    <textarea v-model="element.windowsill.remark_cust" :name="'elements['+key+'][remark_cust]'"  :ref="'remark_cust_'+key"  placeholder="Opmerking..." class="form-input remark_custblaock"></textarea>
                  </div>
                </div>

              </div>

            </div>
          </div>
        </div>

      </div>

      <br/><br/>

      <button type="button" name="add" id="add" class="btn" @click="addElement()"><i class="fa fa-plus"></i> Element toevoegen</button>

      <br/><br/>

      <button @click.prevent="clickPrev()"  type="button" name="prev" id="prev" class="btn" style="float: left;"><i class="fa fa-chevron-left"></i> Vorige stap</button>
      <button @click.prevent="clickNext()" type="button" name="next" id="next" class="btn" style="float: right;" v-bind:disabled="!isValid()">Doorgaan <i class="fa fa-chevron-right"></i></button>

    </form>
  </div>
</template>

<script>

import MathHelper from './../helpers/MathHelper.js';
import WindowsillSelect from "./WindowsillSelect";
import ErrorHelper from "../helpers/ErrorHelper";

export default {
  name: 'ElementsWindowsill',
  components: {
    WindowsillSelect,
  },
  data() {
    return {
      loading: true,
      errors: [],
      form: null,

      quotation : {},
      quotation_extra : {},
      elements : {},
      windowsillsTemplates: {},

      element_new : {},
      alphabetArray : ["A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"],

      windowsillSelectShow: [],
    }
  },

  created() {
    this.fetchData();
  },

  mounted() {

  },

  methods: {
    fetchData() {
      fetch('?action=wizard&step=2&json=1', {
        headers: {'Content-type': 'application/json'},
      })
          .then(res => res.json())
          .then((response) => {

            if (ErrorHelper.handleError(this, response)) return;

            this.quotation = response.data.quotation;
            this.quotation_extra = response.data.quotation_extra;
            this.elements = response.data.elements;
            this.element_new = response.data.element_new;
            this.windowsillsTemplates = response.data.windowsillsTemplates;
            this.errors = response.data.errors;

            for (var i = 0; i < this.elements.length; i++) {
              this.changeSelection(this.elements[i], this.elements[i].windowsill.windowsill_id);
            }

            this.elements[0].isShown = true;

            this.loading = false;

          })
          .catch((error) => {
            this.errors = [];
            this.errors.push("Server fout opgetreden. Probeer het later nog eens, of neem contact op (" + error + ")");
          });
    },
    postData(direction) {

      const formData = new FormData(this.form); // reference to form element
      formData.append(direction, 1);

      fetch('?action=wizard&step=2&json=1', {
        method: "POST",
        headers: {
          // 'Content-type': 'application/json',
          "Accept": "application/json",   // expected data sent back
        },
        body: formData
      })
          .then(res => res.json())
          .then((response) => {

            if (ErrorHelper.handleError(this, response)) return;

            this.errors = response.data.errors;
            if (this.errors.length === 0 && response.redirect) {
              location.href = response.redirect;
            }
          })
          .catch((error) => {
            this.errors = [];
            this.errors.push("Server fout opgetreden. Probeer het later nog eens, of neem contact op (" + error + ")");
          });
    },
    clickPrev() {
      if(this.checkForm()) {
        this.postData("prev");
      }
    },
    clickNext() {
      if(this.checkForm()) {
        this.postData("next");
      }
    },
    toggle: function (element) {
      element.isShown = !element.isShown;
    },
    changeSelection: function (element, windowsillId) { //change bovenaanzicht
      this.windowsillSelectShow = [];
      element.windowsill.windowsill_id = windowsillId;
      element.windowsillTemplate = this.windowsillsTemplates[windowsillId];

      // if(element.windowsill.x1=="" || element.windowsill.x1==null) {
        // this.setDefaultElementValues(element);
      // }
    },
    // setDefaultElementValues: function(element) { //set default values
      // element.windowsill.x1 = 1000;
      // element.windowsill.x2 = 200;
      // if(element.windowsillTemplate.x3!='') {
      //   if(element.windowsillTemplate.x3=='Hoek') {
      //     element.windowsill.x3 = 135;
      //   }
      //   else {
      //     element.windowsill.x3 = 200;
      //   }
      // }
      // if(element.windowsillTemplate.x4!='') {
      //   if(element.windowsillTemplate.x4=='Hoek') {
      //     element.windowsill.x4 = 135;
      //   }
      //   else {
      //     element.windowsill.x4 = 200;
      //   }
      // }
      // if(element.windowsillTemplate.x5!='') {
      //   if(element.windowsillTemplate.x5=='Hoek') {
      //     element.windowsill.x5 = 135;
      //   }
      //   else {
      //     element.windowsill.x5 = 200;
      //   }
      // }
      // if(element.windowsillTemplate.x6!='') {
      //   if(element.windowsillTemplate.x6=='Hoek') {
      //     element.windowsill.x6 = 135;
      //   }
      //   else {
      //     element.windowsill.x6 = 200;
      //   }
      // }
    // },
    getMetric: function (name) {
      if(name==="Hoek") {
        return '°';
      }
      return 'mm';
    },

    //validation
    validateAmountInput: function (element) {
      this.validateAmount(element);
    },
    validateAmount: function (element) {
      if(!MathHelper.isNumeric(element.amount) || element.amount != parseInt(element.amount, 10) || element.amount<0  || element.amount>9999) {
        element.amount = 1;
      }
      return true;
    },
    validateSize: function (element, prop) {
      //var sizeName = element.windowsillTemplate[prop];
      var min = 1;
      var max = 10000;
      if(element.windowsillTemplate[prop]==="Hoek") {
        min = 30;
        max = 150;
      }
      if(parseInt(element.windowsill[prop])<min) {
        element.windowsill[prop] = min;
      }
      else if(parseInt(element.windowsill[prop])>max) {
        element.windowsill[prop] = max;
      }
    },
    validateReferenceNameInput: function (element, key) {
      var valid = true;
      if(element.referenceName=="") {
        valid = false;
        this.$swal("Foutmelding", "Kenmerk mag niet leeg zijn.", "error");
        element.hasReferenceNameError = true;
      }
      if(valid && MathHelper.isNumeric(element.referenceName.substring(0,1))) {
        valid = false;
        this.$swal("Foutmelding", "Het eerste karakter van uw kenmerk mag geen getal zijn.", "error");
        element.hasReferenceNameError = true;
      }
      if(valid) { //check unique name
        for (var i = 0; i < this.elements.length; i++) {
          var el = this.elements[i];
          if(i!=key && el.referenceName.toLowerCase()==element.referenceName.toLowerCase()) {
            this.$swal("Foutmelding", "Kenmerk moet uniek zijn.", "error");
            element.hasReferenceNameError = true;
            valid = false;
          }
        }
      }
      if(valid) {
        element.hasReferenceNameError = false;
      }
      return valid;
    },

    validateWindowsillSelect: function (element) {
      if(element.windowsillTemplate==null || element.windowsillTemplate.id==-1) {
        this.$swal("Foutmelding", "Selecteer uw bovenaanzicht van element "+element.referenceName, "error");
        return false;
      }
      return true;
    },

    validateLengths: function (element) {
      if(element.windowsillTemplate.x1!="" && (element.windowsill.x1===null || element.windowsill.x1==="" || element.windowsill.x1===0)) return false;
      if(element.windowsillTemplate.x2!="" && (element.windowsill.x2===null || element.windowsill.x2==="" || element.windowsill.x2===0)) return false;
      if(element.windowsillTemplate.x3!="" && (element.windowsill.x3===null || element.windowsill.x3==="" || element.windowsill.x3===0)) return false;
      if(element.windowsillTemplate.x4!="" && (element.windowsill.x4===null || element.windowsill.x4==="" || element.windowsill.x4===0)) return false;
      if(element.windowsillTemplate.x5!="" && (element.windowsill.x5===null || element.windowsill.x5==="" || element.windowsill.x5===0)) return false;
      if(element.windowsillTemplate.x6!="" && (element.windowsill.x6===null || element.windowsill.x6==="" || element.windowsill.x6===0)) return false;
      return true;
    },

    //navigation
    navigateReferenceNameUp: function (key) {
      var re = /[^A-Z0-9.\-,]/gi;
      this.elements[key].referenceName = this.elements[key].referenceName.replace(re, '');
    },
    navigateReferenceName: function (key) {

      if(event.code==="ArrowUp") {
        if(this.elements[key-1]) {
          this.$refs['referenceName_'+(key-1)].focus();
        }
      }
      else if(event.code==="ArrowDown") {
        if(this.elements[key+1]) {
          this.$refs['referenceName_'+(key+1)].focus();
        }
      }
      else if(event.code==="Tab" && event.shiftKey) { //shift + tab
        if (this.elements[key - 1]) {
          this.$refs['mitrebox_'+(key-1)].focus(); //geef de voorgaande focus, zodat browser zelf naar juiste gaat....trucje
        }
      }
    },

    removeElement: function (key) {
      event.preventDefault();
      this.elements.splice(key, 1);
    },

    addElement: function (key) {
      event.preventDefault();
      this.element_new.referenceName = this.alphabetArray[this.elements.length];
      if(this.elements.length>=26) {
        this.element_new.referenceName = this.alphabetArray[Math.floor(this.elements.length/26)-1]+this.alphabetArray[this.elements.length%26];
      }

      if(key !== undefined) {
        this.elements.splice(key+1, 0, JSON.parse(JSON.stringify(this.element_new)));
      }
      else {
        this.elements.push(JSON.parse(JSON.stringify(this.element_new)));
      }

      // this.$nextTick(function() { //after dom set focus
      //   this.$refs['referenceName_'+(this.elements.length-1)].focus();
      // });

      this.changeSelection(this.elements[this.elements.length-1], this.windowsillsTemplates[-1].id);

      this.elements[this.elements.length-1].isShown = true;

    },

    isValid() {
      for (var i = 0; i < this.elements.length; i++) {
        var el = this.elements[i];

        if(el.amount===0 && el.inputLength===0) return false;
        if(el.windowsillTemplate==null || el.windowsillTemplate.id==-1) return false;
      }
      return true;
    },

    checkForm: function () {
      var valid = true;
      var totalamount = 0;
      for (var i = 0; i < this.elements.length; i++) {
        var el = this.elements[i];

        if(el.amount===0 && el.inputLength===0) {
          valid = false;
          continue;
        }

        totalamount += el.amount;
        if(!this.validateWindowsillSelect(el,i)) {
          valid = false;
        }
        if(!this.validateReferenceNameInput(el,i)) {
          valid = false;
        }
        if(!this.validateLengths(el,i)) {
          valid = false;
        }
      }

      if(totalamount===0) {
        this.$swal("Foutmelding", "U dient minimaal 1 element in te voeren.", "error");
        valid = false;
      }
      else if(!valid) {
        this.$swal("Foutmelding", "U dient alle maten in te voeren.", "error");
      }

      return valid;
    },
    setForm(el) {
      this.form = el;
    },
    formatValue(name, value) {
      const num = parseFloat(value);
      if (name === "Hoek") {
        // Toestaan van 67,5 met komma
        if (num === 67.5) return 67.5;

        return Math.round(num);
      }

      // Lengtemaat: afronden op hele mm
      return Math.round(num);
    }
  },
}


</script>

<style>
.vensterbank-element {
  padding-bottom: 5px;
}
.vensterbank-head {
  display: flex;
  justify-content: space-between;
  width: 100%;
  background-color: #ce000c;
  color: white;
  padding: 5px;
}
.vensterbank-head > div {
  align-items: center;
  display: flex;
}
.vensterbank-head a {
  color: white;
  display: block;
  padding: 10px;
}
.vensterbank-kenmerk {
  padding: 5px 15px;
  font-weight: bold;
}
.vensterbank-content {
  width: 100%;
}
.vensterbank-content-hidden {
  display: none;
}
.windowsill-select-btn {
  border: 1px solid #d2d2d2;
  padding: 10px 15px;
  width: 100%;
  border-radius: 3px;
  line-height: 25px;
  color: #333;
  display: flex;
  /*align-content: center;*/
}
.windowsill-select-btn .fa {
  margin-top: 5px;
}
.windowsill-select-name {
  flex-grow: 1;
}
.windowsill-select-btn span {
  margin-left: 10px;
}
.windowsill-select-btn .imagesmallfile {
  width: 45px;
  display: inline-block;
  margin-right: 15px;
}
.windowsill-select-btn .imagesmallfile img {
  vertical-align: middle;
}
.windowsill-select {
  position: absolute;
  background-color: white;
  box-shadow: 0 1px 5px #ddd;
  border: solid 1px #ccc;
  list-style: none;
  margin: 0;
  padding:0;
  z-index: 10;
}
.windowsill-select a {
  display: block;
  padding: 5px 15px;
  color: #333;
}
.windowsill-select a:hover {
  background-color: #EBEBEB;
  color: #ce000c;
}
.windowsill-select-active, .windowsill-select-active a {
  background-color: #EBEBEB;
  color: #ce000c;
}
.windowsill-select .imagesmallfile {
  width: 45px;
  min-height: 20px;
  margin-right: 15px;
  float: left;
}
.windowsill-select .imagesmallfile img {
  vertical-align: middle;
}
.vensterbank-image {
  max-width: 100%;
}
.windowsillTemplate input {
  width: 85px;
}
.windowsillTemplate div {
  padding: 2px;
}
.windowsillTemplate label {
  width: 140px;
  display: inline-block;
}

.btn-options {
  display: inline-flex;
  flex-wrap: nowrap;
  gap: .2em;
  margin-left: 2em;
}
.btn-option {
  background-color: #f0f0f0;
  border: 1px solid #ce000c;
  border-radius: 3px;
  padding: 5px;
  margin-right: 5px;
  cursor: pointer;
}
</style>
