<template>

  <div id="step2-popup-bg" v-if="showPopupBG"></div>

  <div class="wizard_inleiding">
    Vul in onderstaand lijst uw elementmaten in. U kunt ook tabs gebruiken bij de invoer.
  </div>

  <div class="alert alert-danger" v-if="errors.length>0">
    Er zijn foutmel<PERSON>en opgetreden, controleer uw invoer:
    <ul>
      <li v-for="error in errors" :key="error">{{error}}</li>
    </ul>
  </div>

  <div id="step2" class="wizard" v-cloak v-if="!loading">

    <form method="post" :ref="setForm">

      <div class="form-row">
        <label class="col3 col-form-label">Elementen</label>
        <div class="col1">
        </div>

        <label class="col7 col-form-label">Voer uw elementmaten in.</label>
        <div class="col1">
        </div>
        <div class="col12 scroller-x">
          <table id="elementtable">
            <tr class="header-row">
              <td>
                Kenmerk
              </td>
              <td style="width: 73px;">
                Aantal
              </td>
              <td style="width: 95px;">
                Lengte
              </td>
              <td style="width: 103px;">
                Breedte (A)
              </td>
              <td style="width: 103px;">
                Hoogte (B)
              </td>
              <td style="width: 95px;">
                {{this.verstekhoektitle}}
                <GsdPopper>
                  <a class="question-mark-inline fa fa-info-circle"></a>
                  <template #content>
                    <div v-if="this.stone.type==='spekband' && quotation.brandId==1">
                      Geef hier aan of een hoek wilt links, rechts of aan beide kanten.
                    </div>
                    <div v-else>
                      <p>U kunt hier aangeven aan welke kant een verstek komt.<br>
                        (Hier vind u een handige reken voorbeeld "pdf")</p>
                      <ul>
                        <li>Hartklik gemeten
                          <ul>
                            <li><a href="/downloads/verstekken-invulformulier-uitwendige-hoek.pdf" target="_blank">uitwendige hoek.pdf</a></li>
                            <li><a href="/downloads/verstekken-invulformulier-inwendige-hoek.pdf" target="_blank">inwendige hoek.pdf</a></li>
                            <li><a href="/downloads/verstekken-invulformulier-uitwendige-hoek-van-675-135-graden.pdf" target="_blank">uitwendige hoek van 67,5 (135) graden.pdf</a></li>
                            <li><a href="/downloads/verstekken-invulformulier-uitwendige-hoek-van-60-120-graden.pdf" target="_blank">uitwendige hoek van 60 (120) graden.pdf</a></li>
                          </ul>
                        </li>
                        <li>Tot aan de punt gemeten
                          <ul>
                            <li><a href="/downloads/verstekken/verstekken-informatie-uitwendige-hoek-tot-aan-de-punt-gemeten.pdf" target="_blank">uitwendige hoek tot aan de punt gemeten.pdf</a></li>
                            <li><a href="/downloads/verstekken/verstekken-informatie-inwendige-hoek-tot-aan-de-punt-gemeten.pdf" target="_blank">inwendige hoek tot aan de punt gemeten.pdf</a></li>
                          </ul>
                        </li>
                      </ul>
                    </div>
                  </template>
                </GsdPopper>
              </td>
            </tr>

            <tr class="elementrow" v-for="(element, key) in elements" :key="key">
              <td>
                <input type="hidden" v-model="element.mitre" :name="'elements['+key+'][mitre]'" />
                <input type="hidden" v-model="element.oldkey" :name="'elements['+key+'][oldkey]'" />
                <input autocomplete="off" type="text" v-model="element.referenceName" @focus="$event.target.select()" :name="'elements['+key+'][referenceName]'" :id="'elements['+key+'][referenceName]'" @keydown="navigateReferenceName(key)" @keyup="navigateReferenceNameUp(key)" @change="validateReferenceNameInput(element,key)"  placeholder="Kenmerk..." class="form-input" :class="{inputerror: element.hasReferenceNameError}" maxlength="12" :ref="'referenceName_'+key"/>
              </td>
              <td>
                <input autocomplete="off" type="number" min="0" v-model="element.amount" @focus="$event.target.select()" :name="'elements['+key+'][amount]'"  :ref="'amount_'+key" @keydown="navigateAmount(key)" @change="validateAmountInput(element)" placeholder="Aantal..." class="form-input inputnumber" maxlength="4"/>
              </td>
              <td>
                <input autocomplete="off" type="number" min="0" v-model="element.inputLength" @focus="$event.target.select()"  :name="'elements['+key+'][inputLength]'"  :ref="'inputLength_'+key"  @keydown="navigateInputLength(key)" @change="validateInputLengthInput(element, key, true)" placeholder="Lengte..."  class="form-input inputnumber" :class="{inputerror: element.hasInputLengthError}" maxlength="5"/>
              </td>
              <td>
                <input autocomplete="off" type="number" min="0" v-model="element.width" @focus="$event.target.select()"  :name="'elements['+key+'][width]'"  :ref="'inputWidth_'+key"  @keydown="navigateInputWidth(key)" @change="validateInputWidth(element, key, true)" placeholder="Breedte..."  class="form-input inputnumber" :class="{inputerror: element.hasInputWidthError}" maxlength="5"/>
              </td>
              <td>
                <input autocomplete="off" type="number" min="0" v-model="element.height" @focus="$event.target.select()"  :name="'elements['+key+'][height]'"  :ref="'inputHeight_'+key"  @keydown="navigateInputHeight(key)" @change="validateInputHeight(element, key, true)" placeholder="Hoogte..."  class="form-input inputnumber" :class="{inputerror: element.hasInputHeightError}" maxlength="5"/>
              </td>
              <td>
                <input type="button" :value="mitreText(element)" @click="openMitrebox(element)" class="form-input form-btn" :ref="'mitrebox_'+key"  />
              </td>
              <td>
                <a href="#" class="elementtable_fa" v-on:click="remove(key)" title="Verwijderen" :ref="'remove_'+key">
                  <i class="fa fa-remove"></i>
                </a>
                <a href="#" class="elementtable_fa" @click="addElement(key)" title="Toevoegen" :ref="'add_'+key">
                  <i class="fa fa-plus"></i>
                </a>
              </td>
            </tr>


            <tr class="header-row">
              <td>Totalen </td>
              <td>
                <input type="text" v-model="amount_total" disabled class="form-input inputnumber inputreadonly_cust"/>
              </td>
              <td colspan="5" style="text-align: right">
                <button type="button" name="add" id="add" class="btn" @click="addElement()"><i class="fa fa-plus"></i> Toevoegen</button>
              </td>
            </tr>
          </table>
        </div>
      </div>

      <div>
        <img src="/projects/rde/templates/frontend/images/balkje.png"  style="border: 1px solid #d6d5d5;"/>
      </div>


      <br/><br/>


      <button @click.prevent="clickPrev()" type="button" name="prev" id="prev" class="btn" style="float: left;"><i class="fa fa-chevron-left"></i> Vorige stap</button>
      <button @click.prevent="clickNext()" type="button" name="next" id="next" class="btn" :class="{disabled: !all_valid}" style="float: right;">Doorgaan <i class="fa fa-chevron-right"></i></button>

    </form>

    <div :class="mitreBoxClass()" id="mitrebox" v-if="mitreboxShow">
      <div class="questionbox_title">{{this.verstekhoektitle}} - Element <span id="mitreElement">{{mitreboxElement.referenceName}}</span></div>
      <div class="questionbox_content">
        <p>	Kies aan welke uiteinden van het element u een (verstek)hoek wilt.<br/>U ziet hier het bovenaanzicht van de balk (zichtzijde).</p>

        <div class="verstekhoekImgAfstand">
          <input type="radio" v-model="mitreboxElement.mitre" value="none" id="mitrebox_0" @change="selectMitre()">
          <label for="mitrebox_0">
            <div class="mitre_example mitre_example_none">
              <div id="rd_mitre_none" :title="'Geen '+this.verstekhoektitle">
                <div class="mitre_line"></div>
              </div>
            </div>
            <div class="mitre_label">Geen</div>
          </label>
        </div>
        <div class="verstekhoekImgAfstand">
          <input type="radio" v-model="mitreboxElement.mitre" value="left" id="mitrebox_1" @change="selectMitre()">
          <label for="mitrebox_1">
            <div class="mitre_example mitre_example_left">
              <div title="Verstekhoek aan linker elementeinde">
                <div class="mitre_line"></div>
                <div class="mitre_schuin_left"></div>
              </div>
            </div>
            <div class="mitre_of">OF</div>
            <div class="mitre_example mitre_example_left_top">
              <div :title="this.verstekhoektitle+' aan linker elementeinde'">
                <div class="mitre_line"></div>
                <div class="mitre_schuin_left_top"></div>
              </div>
            </div>
            <div class="mitre_label">Links</div>
          </label>
        </div>
        <div class="verstekhoekImgAfstand">
          <input type="radio" v-model="mitreboxElement.mitre" value="right" id="mitrebox_2" @change="selectMitre()">
          <label for="mitrebox_2">
            <div class="mitre_example mitre_example_right">
              <div :title="this.verstekhoektitle+' aan linker elementeinde'">
                <div class="mitre_line"></div>
                <div class="mitre_schuin_right"></div>
              </div>
            </div>
            <div class="mitre_of">OF</div>
            <div class="mitre_example mitre_example_right_top">
              <div :title="this.verstekhoektitle+' aan rechter elementeinde'">
                <div class="mitre_line"></div>
                <div class="mitre_schuin_right_top"></div>
              </div>
            </div>
            <div class="mitre_label">Rechts</div>
          </label>
        </div>
        <div class="verstekhoekImgAfstand">
          <input type="radio" v-model="mitreboxElement.mitre" value="both" id="mitrebox_3" @change="selectMitre()">
          <label for="mitrebox_3">
            <div class="mitre_example mitre_example_leftright">
              <div :title="this.verstekhoektitle+'en aan weerszijden'">
                <div class="mitre_line"></div>
                <div class="mitre_schuin_left"></div>
                <div class="mitre_schuin_right"></div>
              </div>
            </div>
            <div class="mitre_of">OF</div>
            <div class="mitre_example mitre_example_leftright_top">
              <div :title="this.verstekhoektitle+'en aan weerszijden'">
                <div class="mitre_line"></div>
                <div class="mitre_schuin_right_top"></div>
                <div class="mitre_schuin_left_top"></div>
              </div>
            </div>
            <div class="mitre_label">Beide</div>
          </label>
        </div>
        <p></p>
        <p class="center">
          <input type="button" name="btnMitre" value="Sluiten" @click="selectMitre()" class="btn">
        </p>
      </div>
    </div>

  </div>
</template>

<script>

import MathHelper from './../helpers/MathHelper.js';
import GsdPopper from './../components/GsdPopper.vue';
import ErrorHelper from "../helpers/ErrorHelper";

export default {
  name: 'Elements',
  components: {
    GsdPopper,
  },
  data() {
    return {
      loading: true,
      is_valid: {},
      all_valid: false,
      is_active: {},
      errors: [],
      form: null,

      quotation : {},
      quotation_extra : {},
      elements : {},
      stone : {},
      stone_size : {},
      verstekhoektitle : "Vestekhoek",

      element_new : {},
      mitreboxShow : false,
      mitreboxElement : {},

      amount_total: 0,
      alphabetArray : ["A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"],

      showPopupBG : false,
    }
  },

  created() {
    this.fetchData();
  },

  mounted() {
  },

  watch: {
  },
  computed: {
    possible_shorter: function () {
      var filtered_array = [];
      for(var i=0;i<=10;i+=2) {
        var txt = i+" mm";
        if(i==0) {
          txt += " - geadviseerd renovatie";
        }
        else if(i==10) {
          txt += " - geadviseerd nieuwbouw";
        }
        filtered_array.push({value: i, text: txt});
      }
      return filtered_array;
    }
  },
  methods: {
    fetchData() {
      fetch('?action=wizard&step=2&json=1', {
        headers: {'Content-type': 'application/json'},
      })
          .then(res => res.json())
          .then((response) => {

            if (ErrorHelper.handleError(this, response)) return;

            this.quotation = response.data.quotation;
            this.quotation_extra = response.data.quotation_extra;
            this.stone = response.data.stone;
            this.stone_size = response.data.stone_size;
            this.errors = response.data.errors;
            this.elements = response.data.elements;
            this.element_new = response.data.element_new;

            this.mitreboxElement = response.data.element_new;

            this.calculateTotals();

            this.loading = false;

          })
          .catch((error) => {
            this.errors = [];
            this.errors.push("Server fout opgetreden. Probeer het later nog eens, of neem contact op (" + error + ")");
          });
    },
    postData(direction) {

      const formData = new FormData(this.form); // reference to form element
      formData.append(direction, 1);

      fetch('?action=wizard&step=2&json=1', {
        method: "POST",
        headers: {
          // 'Content-type': 'application/json',
          "Accept": "application/json",   // expected data sent back
        },
        body: formData
      })
          .then(res => res.json())
          .then((response) => {

            if (ErrorHelper.handleError(this, response)) return;

            this.errors = response.data.errors;
            if ("redirect" in response) {
              location.href = response.redirect;
            }
          })
          .catch((error) => {
            this.errors = [];
            this.errors.push("Server fout opgetreden. Probeer het later nog eens, of neem contact op (" + error + ")");
          });
    },
    clickPrev() {
      if(this.checkForm()) {
        this.postData("prev");
      }
    },
    clickNext() {
      if(this.checkForm()) {
        this.postData("next");
      }
    },
    mitreText: function (element) {
      if (element.mitre == "both") {
        return 'Beide'
      } else if (element.mitre == "left") {
        return 'Links'
      } else if (element.mitre == "right") {
        return 'Rechts'
      }
      return 'Nee';
    },
    remove: function (key) {
      this.elements.splice(key, 1);
      this.calculateTotals();
    },
    addElement: function (key) {
      event.preventDefault();
      this.element_new.referenceName = this.alphabetArray[this.elements.length];
      if (this.elements.length >= 26) {
        this.element_new.referenceName = this.alphabetArray[Math.floor(this.elements.length / 26) - 1] + this.alphabetArray[this.elements.length % 26];
      }
      if (key !== undefined) {
        this.elements.splice(key + 1, 0, JSON.parse(JSON.stringify(this.element_new)));
      } else {
        this.elements.push(JSON.parse(JSON.stringify(this.element_new)));
      }
      this.$nextTick(function () { //after dom set focus
        this.$refs['referenceName_' + (this.elements.length - 1)][0].focus();
      });
      this.calculateTotals();
    },
    calculateElementLength: function (element) {
      this.calculateElementLengthDefault(element);
    },
    calculateElementLengthDefault: function (element) {
      element.elementLength = 0;
      element.inputLength = parseInt(element.inputLength);
      element.elementLength = element.inputLength;
      if (element.elementLength < 0) {
        element.elementLength = 0;
      }
    },
    calculateTotals() {
      let all_valid = this.elements.length > 0;
      this.amount_total = 0;
      let nr_of_valid_elements = 0;
      for (var i = 0; i < this.elements.length; i++) {
        this.calculateElementLength(this.elements[i]);
        var amount = parseInt(this.elements[i].amount);
        this.amount_total += amount;
        if (all_valid) {
          if(this.elements[i].amount==0 && this.elements[i].inputLength==0) {
            //als hij 0 is noemen we m valid, oftewel negeren die handel
          }
          else if (!this.validateAmount(this.elements[i]) || !this.validateInputLength(this.elements[i], i, false)) {
            all_valid = false;
          }
          else {
            nr_of_valid_elements++;
          }
        }
      }
      if(nr_of_valid_elements===0) {
        all_valid = false;
      }

      this.all_valid = all_valid;
    },

    //popups
    openMitrebox: function (element) {
      this.mitreboxShow = true;
      this.showPopupBG = true;
      this.mitreboxElement = element;
    },
    selectMitre() {
      this.mitreboxShow = false;
      this.showPopupBG = false;
      this.calculateTotals();
    },

    //navigations
    navigateReferenceNameUp: function (key) {
      var re = /[^A-Z0-9.\-,]/gi;
      this.elements[key].referenceName = this.elements[key].referenceName.replace(re, '');
    },
    navigateReferenceName: function (key) {

      if (event.code == "ArrowUp") {
        if (this.elements[key - 1]) {
          this.$refs['referenceName_' + (key - 1)][0].focus();
        }
      } else if (event.code == "ArrowDown") {
        if (this.elements[key + 1]) {
          this.$refs['referenceName_' + (key + 1)][0].focus();
        }
      } else if (event.code == "Tab" && event.shiftKey) { //shift + tab
        if (this.elements[key - 1]) {
          this.$refs['mitrebox_' + (key - 1)][0].focus(); //geef de voorgaande focus, zodat browser zelf naar juiste gaat....trucje
        }
      }
    },
    navigateAmount: function (key) {

      if (event.code == "ArrowUp") {
        if (this.elements[key - 1]) {
          this.$refs['amount_' + (key - 1)][0].focus();
        }
      } else if (event.code == "ArrowDown") {
        if (this.elements[key + 1]) {
          this.$refs['amount_' + (key + 1)][0].focus();
        }
      }
    },
    navigateInputLength: function (key) {
      if (event.code == "ArrowUp") {
        if (this.elements[key - 1]) {
          this.$refs['inputLength_' + (key - 1)][0].focus();
        }
      } else if (event.code == "ArrowDown") {
        if (this.elements[key + 1]) {
          this.$refs['inputLength_' + (key + 1)][0].focus();
        }
      }
    },
    navigateInputWidth: function (key) {
      if (event.code == "ArrowUp") {
        if (this.elements[key - 1]) {
          this.$refs['inputWidth_' + (key - 1)][0].focus();
        }
      } else if (event.code == "ArrowDown") {
        if (this.elements[key + 1]) {
          this.$refs['inputWidth_' + (key + 1)][0].focus();
        }
      }
    },
    navigateInputHeight: function (key) {
      if (event.code == "ArrowUp") {
        if (this.elements[key - 1]) {
          this.$refs['inputHeight_' + (key - 1)][0].focus();
        }
      } else if (event.code == "ArrowDown") {
        if (this.elements[key + 1]) {
          this.$refs['inputHeight_' + (key + 1)][0].focus();
        }
      } else if (event.code == "Tab") {
        if (!event.shiftKey) {
          if (this.elements[key + 1]) {
            this.$refs['add_' + key][0].focus(); //geef de voorgaande focus, zodat browser zelf naar juiste gaat....trucje
            // event.preventDefault();
          } else {
            //nieuwe rij maken
            this.addElement();
          }
        } else { //shift+tab
          this.$refs['inputHeight_' + key][0].focus(); //geef de voorgaande focus, zodat browser zelf naar juiste gaat....trucje
        }
      }
    },


    //validation
    validateReferenceNameInput: function (element, key) {
      var valid = true;
      if (element.referenceName == "") {
        valid = false;
        this.$swal("Foutmelding", "Kenmerk mag niet leeg zijn.", "error");
        element.hasReferenceNameError = true;
      }
      if (valid && MathHelper.isNumeric(element.referenceName.substring(0, 1))) {
        valid = false;
        this.$swal("Foutmelding", "Het eerste karakter van uw kenmerk mag geen getal zijn.", "error");
        element.hasReferenceNameError = true;
      }
      if (valid) { //check unique name
        for (var i = 0; i < this.elements.length; i++) {
          var el = this.elements[i];
          if (i != key && el.referenceName.toLowerCase() == element.referenceName.toLowerCase()) {
            this.$swal("Foutmelding", "Kenmerk moet uniek zijn.", "error");
            element.hasReferenceNameError = true;
            valid = false;
          }
        }
      }
      if (valid) {
        element.hasReferenceNameError = false;
      }
      return valid;
    },
    validateAmountInput: function (element) {
      this.validateAmount(element);
      this.calculateTotals();
    },
    validateAmount: function (element) {
      if (!MathHelper.isNumeric(element.amount) || element.amount != parseInt(element.amount, 10) || element.amount < 0 || element.amount > 9999) {
        element.amount = 1;
      }
      return true;
    },
    validateInputLengthInput: function (element, key) {
      this.validateInputLength(element, key);
      this.calculateTotals();
    },
    validateInputLength: function (element, key, show) {
      var valid = true;
      if (!MathHelper.isNumeric(element.inputLength) || element.inputLength < 0) {
        element.inputLength = 0;
      } else if (element.inputLength != parseInt(element.inputLength, 10)) {
        element.inputLength = Math.floor(element.inputLength);
      }
      if (element.inputLength < 100) {
        valid = false;
        if (show) {
          this.$swal("Foutmelding", "Kozijnmaat in mm moet minimaal 100 mm zijn.", "error");
          element.hasInputLengthError = true;
        }
      }
      if (valid) {
        element.hasInputLengthError = false;
      }
      return valid;
    },
    validateInputWidth: function (element, key, show) {
      var valid = true;
      if (!MathHelper.isNumeric(element.width) || element.width < 0) {
        element.width = 0;
      } else if (element.width != parseInt(element.width, 10)) {
        element.width = Math.floor(element.width);
      }
      if (element.width < 20) {
        valid = false;
        if (show) {
          this.$swal("Foutmelding", "Breedte in mm moet minimaal 20 mm zijn.", "error");
          element.hasInputWidthError = true;
        }
      }
      else if (element.width > 100 && element.height > 200) {
        valid = false;
        if (show) {
          this.$swal("Foutmelding", "Breedte mag maximaal 100 mm zijn, wanneer de hoogte hoger is dan 200mm.", "error");
          element.hasInputWidthError = true;
        }
      }
      else if (element.width > 400) {
        valid = false;
        if (show) {
          this.$swal("Foutmelding", "Breedte mag maximaal 400 mm zijn.", "error");
          element.hasInputWidthError = true;
        }
      }
      if (valid) {
        element.hasInputWidthError = false;
      }
      return valid;
    },
    validateInputHeight: function (element, key, show) {
      var valid = true;
      if (!MathHelper.isNumeric(element.height) || element.height < 0) {
        element.height = 0;
      } else if (element.height != parseInt(element.height, 10)) {
        element.height = Math.floor(element.height);
      }
      if (element.height < 20) {
        valid = false;
        if (show) {
          this.$swal("Foutmelding", "Hoogte moet minimaal 20 mm zijn.", "error");
          element.hasInputHeightError = true;
        }
      }
      else if (element.height > 200 && element.width > 200) {
        valid = false;
        if (show) {
          this.$swal("Foutmelding", "Hoogte mag maximaal 200 mm zijn, wanneer de breedte hoger is dan 200mm.", "error");
          element.hasInputHeightError = true;
        }
      }
      else if (element.height > 400) {
        valid = false;
        if (show) {
          this.$swal("Foutmelding", "Hoogte mag maximaal 400 mm zijn.", "error");
          element.hasInputHeightError = true;
        }
      }
      if (valid) {
        element.hasInputHeightError = false;
      }
      return valid;
    },

    checkForm() {

      var valid = true;
      var totalamount = 0;
      for (var i = 0; i < this.elements.length; i++) {
        var el = this.elements[i];

        if (el.amount == 0 && el.inputLength == 0) continue;

        totalamount += el.amount;
        if (!this.validateInputLength(el, i, true)) {
          valid = false;
        }
        if (!this.validateReferenceNameInput(el, i, true)) {
          valid = false;
        }
      }
      if (totalamount == 0) {
        this.$swal("Elementen", "U dient minimaal 1 element in te voeren.", "error");
        valid = false;
      }
      return this.all_valid && valid;
    },
    setForm(el) {
      this.form = el;
    },
    mitreBoxClass() {
      let mbclass = 'questionbox ' + this.stone.type + ' brand' + this.quotation.brandId;
      if (this.stone.material === "natuursteen") {
        mbclass += this.isEzelsrug() ? 'ezelsrug' : 'no-ezelsrug';
      }
      return mbclass;
    },
    flagEndstoneBoxClass() {
      let mbclass = 'questionbox ' + this.stone.type + ' ';
      if (this.stone.material === "natuursteen") {
        mbclass += this.isEzelsrug() ? 'ezelsrug' : 'no-ezelsrug';
      }
      return mbclass;
    },
    isEzelsrug() {
      return this.stone.category_id == 18; //ezelsrug
    }

  }

}


</script>

<style>

#step2-popup-bg {
  overflow: auto;
  overflow-y: scroll;
  position: fixed;
  bottom: 0;
  right: 0;
  width: auto;
  height: auto;
  top: 0;
  left: 0;
  z-index: 900;
  background: #0e0e0ebd;
}
input.inputnumber[type=number] {
  padding-right: 0;
}

</style>
