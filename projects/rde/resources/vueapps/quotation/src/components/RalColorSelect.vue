<template>
  <div class="ral-selector">
    <div class="dropdown">
      <div class="dropdown-header" @click="toggleDropdown">
        <div>
          <div class="color-swatch" :style="{ backgroundColor: selectedColor?.hex || '#fff' }"></div>
          {{ selectedColor ? `${selectedColor.code} - ${selectedColor.name}` : '<PERSON>es een RAL kleur' }}
        </div>
        <span class="fa fa-chevron-down"></span>
      </div>

      <div v-if="isOpen" class="dropdown-body">
        <input
            type="text"
            v-model="query"
            class="search-input"
            placeholder="Zoek op naam of code..."
        />
        <ul class="options">
          <li
              v-for="color in filteredColors"
              :key="color.code"
              @click="selectColor(color)"
              class="option"
          >
            <div class="color-swatch" :style="{ backgroundColor: color.hex }"></div>
            {{ color.code }} - {{ color.name }}
          </li>
        </ul>
      </div>
    </div>
    <input type="hidden" name="ralColor" :value="selectedColor?.code" />
  </div>
</template>

<script setup>
import { onMounted, onUnmounted ,ref, computed, defineProps, defineEmits } from 'vue'
import { ralColors } from '../helpers/RalColorHelper'

function handleClickOutside(event) {
  const dropdown = document.querySelector('.dropdown-body')
  if (dropdown && !dropdown.contains(event.target)) {
    isOpen.value = false
  }
}
onMounted(() => {
  document.addEventListener('click', handleClickOutside)

  // Check if modelValue is set and find the corresponding color
  if (!props.modelValue.ralColor) return
  const color = ralColors.find(c => c.code === props.modelValue.ralColor)
  if (color) selectedColor.value = color
})
onUnmounted(() => document.removeEventListener('click', handleClickOutside))

const props = defineProps({
  modelValue: Object,
  isActive: Boolean,
})
const emit = defineEmits(['ralColorChanged'])

const isOpen = ref(false)
const selectedColor = ref(props.modelValue.ralColor || null)
const query = ref('')

const toggleDropdown = () => {
  if (!props.isActive) return
  isOpen.value = !isOpen.value
}

const selectColor = (color) => {
  selectedColor.value = color
  emit('ralColorChanged', color)
  isOpen.value = false
  query.value = ''
}

const filteredColors = computed(() => {
  if (!query.value) return ralColors
  return ralColors.filter(c =>
      `${c.code} ${c.name}`.toLowerCase().includes(query.value.toLowerCase())
  )
})
</script>

<style scoped>
.dropdown {
  position: relative;
  background-color: #fff;
}

.dropdown-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border: 1px solid #ccc;
  padding: 10px 15px;
  border-radius: 4px;
  cursor: pointer;
}

.color-swatch {
  width: 16px;
  height: 16px;
  border: 1px solid #aaa;
  margin-right: 8px;
  display: inline-block;
}

.dropdown-body {
  position: absolute;
  width: 100%;
  z-index: 100;
  background: white;
  border: 1px solid #ccc;
  border-top: none;
  max-height: 200px;
  overflow: auto;
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.search-input {
  width: 100%;
  box-sizing: border-box;
  padding: 6px 8px;
  outline: none;
}

.options {
  list-style: none;
  margin: 0;
  padding: 0;
}

.option {
  padding: 10px 15px;
  display: flex;
  align-items: center;
  cursor: pointer;
}

.option:hover {
  background-color: #f0f0f0;
}

.fa-chevron-down {
  color: #888;
}

.disabled .dropdown {
  background-color: #dcdedf;
  color: #666;
  font-size: 15px;
}
</style>