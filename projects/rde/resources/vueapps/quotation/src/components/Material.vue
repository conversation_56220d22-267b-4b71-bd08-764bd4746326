<template>

  <div class="wizard_inleiding">
    <button @click.prevent="clickNext()" type="button" name="next" id="next_t" class="btn" style="float: right;font-size: 18px;" v-bind:disabled="!all_valid"  @click="validate()">Doorgaan <i class="fa fa-chevron-right"></i></button>
    In slechts een paar stappen stelt u een vrijblijvende offerte samen.<br/>
    <br/>
    <span  v-html="getTitle()"></span>
  </div>

  <div id="step0" class="wizard" v-cloak v-if="!loading">

    <div class="alert alert-danger" v-if="errors.length>0">
      <PERSON>r zijn foutmel<PERSON> opgetreden, controleer uw invoer:
      <ul>
        <li v-for="error in errors" :key="error">{{error}}</li>
      </ul>
    </div>

    <form method="post" :ref="setForm">

      <div class="row">
        <div class="col12">
          <div style="font-weight: bold;padding: 5px;">
            Uw materiaal
          </div>
        </div>
      </div>

      <div class="row" id="material_material">
        <div v-for="category in getCategories()" :key="category.id" class="col12-xs" :class="{'col3':getCategories().length===4, 'col4':getCategories().length===3}">
          <div class="material material_material" :class="{'material_disabled':disabledMaterials[category.id]}" @click="clickMaterial(category)">
            <img :src="'/uploads/rde/stonecategoryimages/'+category.imagefilename"/>
            <div class="material_title">{{category.shortname}}</div>
          </div>
        </div>
      </div>

      <div class="row" v-if="show_material_type">

        <div class="col12">
          <div style="font-weight: bold;padding: 5px;">
            Uw productgroep
          </div>
        </div>

        <div class="variants">
          <div v-for="category_variant in getCategories(this.categoryMaterial)" :key="category_variant.id" class="col12-xs"  :class="{'col4':(getCategories(this.categoryMaterial).length==3 || getCategories(this.categoryMaterial).length>=4),'col3':(getCategories(this.categoryMaterial).length==4),'material_disabled':disabledTypes[category_variant.id]}">
            <div class="material material_type"  @click="clickType(category_variant)">
              <img :src="'/uploads/rde/stonecategoryimages/'+category_variant.imagefilename"/>
              <div class="material_title">{{category_variant.shortname}}</div>
            </div>
          </div>
        </div>

      </div>

      <div class="row" v-if="show_material_model">

        <div class="col12">
          <div style="font-weight: bold;padding: 5px;">
            Uw model
          </div>
        </div>

        <div class="model">
          <div v-for="category_model in getCategories(this.categoryType)" :key="category_model.id" class="col12-xs"  :class="{'col4':(getCategories(this.categoryType).length==3),'col3':(getCategories(this.categoryType).length==4),'material_disabled':disabledModels[category_model.id]}">
            <div class="material material_type"  @click="clickModel(category_model)">
              <img :src="'/uploads/rde/stonecategoryimages/'+category_model.imagefilename"/>
              <div class="material_title">{{category_model.shortname}}</div>
            </div>
          </div>
        </div>

      </div>

      <br/><br/>

      <button @click.prevent="clickNext()" type="button" name="next" id="next" class="btn" style="float: right;" v-bind:disabled="!all_valid"  @click="validate()">Doorgaan <i class="fa fa-chevron-right"></i></button>

    </form>
  </div>

</template>

<script>

import ErrorHelper from "../helpers/ErrorHelper";

export default {
  name: "Material",
  data() {
    return {
      loading: true,
      form: null,
      all_valid: false,
      errors: [],

      quotation: {},
      categories: {},
      isAdmin: false,

      categoryMaterial: "",
      categoryType: "",
      categoryModel: "",
      fullName : "",

      disabledMaterial: {},
      disabledTypes : {},
      disabledModels : {},

      show_material_type: false,
      show_material_model: false,

    }
  },

  mounted() {
  },

  created() {
    this.fetchData();
  },

  watch: {
  },
  computed: {
  },
  methods: {
    fetchData() {
      fetch('?action=wizard&step=0&json=1', {
        headers: {'Content-type': 'application/json'},
      })
          .then(res => res.json())
          .then((response) => {

            if (ErrorHelper.handleError(this, response)) return;

            this.quotation = response.data.quotation;
            this.categories = response.data.categories;
            this.isAdmin = response.data.isAdmin;

            this.initDisabledMaterials();
            if (this.quotation.stoneCategoryId !== "" && this.quotation.stoneCategoryId !== null) {
              this.init(this.quotation.stoneCategoryId);
            }

            this.loading = false;

          })
          .catch((error) => {
            this.errors = [];
            this.errors.push("Server fout opgetreden. Probeer het later nog eens, of neem contact op (" + error + ")");
          });
    },
    postData() {

      fetch('?action=wizard&step=0&json=1', {
        method: "POST",
        headers: {
          'Content-type': 'application/json',
          "Accept": "application/json",   // expected data sent back
        },
        body: JSON.stringify({
          stoneCategoryId: this.quotation.stoneCategoryId,
        })
      })
          .then(res => res.json())
          .then((response) => {

            if (ErrorHelper.handleError(this, response)) return;

            this.errors = response.data.errors;
            if ("redirect" in response) {
              location.href = response.redirect;
            }
            else {
              this.domestic_readonly = false;
              this.street_readonly = false;
              this.quotation.street_city_manual = true;
            }
          })
          .catch((error) => {
            this.errors = [];
            this.errors.push("Server fout opgetreden. Probeer het later nog eens, of neem contact op (" + error + ")");
          });
    },
    clickNext() {
      this.postData();
    },
    //validation
    validate: function () {
      this.all_valid = true;
    },
    setForm(el) {
      this.form = el;
    },
    initDisabledMaterials() {
      this.disabledMaterials = [];
      if(!this.isAdmin) {
        this.disabledMaterials[3] = true; //beton
        this.disabledMaterials[14] = true; //isosill
      }
    },
    clickMaterial(category) {
      if(!this.isAdmin && (category.id==3 || category.id==14)) {
        return;
      }
      this.categoryMaterial = category;
      this.categoryType = "";
      this.categoryModel = "";
      this.disabledMaterials = [];
      let categories = this.getCategories();
      for (let key in categories) {
        let lcat = this.categories[key];
        if(lcat.id!==category.id) {
          this.disabledMaterials[lcat.id] = true;
        }
      }
      this.show_material_type = true;
      this.disabledTypes = [];
      this.disabledModels = [];
      this.all_valid = false;
    },
    clickType(categoryType) {
      this.quotation.stoneCategoryId = categoryType.id;
      this.categoryType = categoryType;
      this.disabledTypes = [];
      let categories1 = this.getCategories(this.categoryMaterial);
      for (let key in categories1) {
        let type = categories1[key];
        if(type.id!==this.categoryType.id) {
          this.disabledTypes[type.id] = true;
        }
      }

      this.disabledModels = [];

      this.show_material_model = this.getCategories(categoryType).length>0;
      this.all_valid = !this.show_material_model;
    },
    clickModel(categoryModel) {
      this.quotation.stoneCategoryId = categoryModel.id;
      this.categoryModel = categoryModel;
      this.disabledModels = [];
      let categories2 = this.getCategories(this.categoryType);
      for (let key in categories2) {
        let type = categories2[key];
        if(type.id!==this.categoryModel.id) {
          this.disabledModels[type.id] = true;
        }
      }

      this.all_valid = true;
    },
    getCategories(category) {
      if(category===undefined) {
        return this.categories;
      }
      for (let key in this.categories) {
        let cat1 = this.categories[key];
        if (cat1.children !== undefined) {
          if (cat1.id === category.id) {
            return cat1.children;
          }
          for (let key1 in cat1.children) {
            let cat2 = cat1.children[key1];
            if (cat2.children !== undefined) {
              if (cat2.id === category.id) {
                return cat2.children;
              }
              for (let key2 in cat2.children) {
                let cat3 = cat2.children[key2];
                if (cat3.id === category.id) {
                  return cat3.children;
                }
              }
            }
          }
        }
      }
      return [];
    },
    init(categoryId) {
      let catsflat = [];
      for (let key in this.categories) {
        let cat1 = this.categories[key];
        cat1.level = 0;
        catsflat[cat1.id] = cat1;
        if (cat1.children !== undefined) {
          for (let key1 in cat1.children) {
            let cat2 = cat1.children[key1];
            cat2.level = 1;
            catsflat[cat2.id] = cat2;
            if (cat2.children !== undefined) {
              for (let key2 in cat2.children) {
                let cat3 = cat2.children[key2];
                cat3.level = 2;
                catsflat[cat3.id] = cat3;
              }
            }
          }
        }
      }

      let currentCat;
      for (let catid in catsflat) {
        let cat = catsflat[catid];
        if(cat.id == categoryId) {
          currentCat = cat;
          break;
        }
      }

      if(currentCat.level==2) {
        let parent = catsflat[currentCat.parent_id];
        this.clickMaterial(catsflat[parent.parent_id]);
        this.clickType(parent);
        this.clickModel(currentCat);
      }
      else {
        this.clickMaterial(catsflat[currentCat.parent_id]);
        this.clickType(currentCat);
      }

    },
    getTitle() {
      if(this.categoryModel!=="") {
        return "<b>Uw selectie: </b><b style='color: #ce000c'>"+this.categoryModel.name+"</b>";
      }
      if(this.categoryType!=="") {
        return "<b>Uw selectie: </b><b style='color: #ce000c'>"+this.categoryType.name+"</b>";
      }
      if(this.categoryMaterial!=="") {
        return '<b>Selecteer een productgroep</b>';
      }
      return '<b>Selecteer uw materiaal en productgroep</b>';
    }

  }

}

</script>

<style>
</style>

