<template>
  <div :ref="setWindowsillSelectEl" >
    <a href="" @click.prevent="showSelection" class="windowsill-select-btn" :id="'windowsill-'+key"  :ref="'windowsill_'+key">
      <div class="imagesmallfile">
        <img :src="'//www.raamdorpel.nl/images/thresholds/vensterbanken/'+windowsillsTemplates[element.windowsill.windowsill_id].imagesmallfilename"  v-if="windowsillsTemplates[element.windowsill.windowsill_id].imagesmallfilename!=null && windowsillsTemplates[element.windowsill.windowsill_id].imagesmallfilename!=''"/>
      </div>
      <div class="windowsill-select-name">
        {{ windowsillsTemplates[element.windowsill.windowsill_id].name }}
      </div>
      <span class="fa fa-chevron-down"></span>
    </a>
    <div v-if="open">
      <ul class="windowsill-select">
        <li v-for="windowsill in windowsillsTemplates" :class="{'windowsill-select-active': element.windowsill.windowsill_id==windowsill.id}" :key="windowsill.id">
          <a href="#"  @click="clickSelection(element, windowsill)">
            <div class="imagesmallfile">
              <img :src="'//www.raamdorpel.nl/images/thresholds/vensterbanken/'+windowsill.imagesmallfilename"  v-if="windowsill.imagesmallfilename!=null && windowsill.imagesmallfilename!=''" />
            </div>
            {{windowsill.name}}
          </a>
        </li>
      </ul>
    </div>
  </div>

</template>
<script type="text/javascript">

  export default {
    props: ['key', 'element','windowsillsTemplates', 'color', 'is_active_color_id'],
    data() {
      return {
        open : false,
        windowsillSelectEl: null
      }
    },
    mounted() {
      document.addEventListener('mouseup', this.clickOutside);
    },
    methods :  {

      showSelection: function () { //show bovenaanzicht dropdown
        this.open = true;
      },
      hideSelection: function () { //show bovenaanzicht dropdown
        this.open = false;
      },

      clickSelection: function (element, windowsill) {
        event.preventDefault(); //prevent scroll to top
        this.$emit("changeWindowsillSelection", windowsill.id);
        this.hideSelection();
      },
      clickOutside: function (e) {
        var el = this.windowsillSelectEl;
        if (!(el === e.target || el.contains(e.target))) {
          this.hideSelection();
        }
      },
      setWindowsillSelectEl(el) {
        if (el) {
          this.windowsillSelectEl = el;
        }
      }
    }
  }


</script>

<style>

</style>
