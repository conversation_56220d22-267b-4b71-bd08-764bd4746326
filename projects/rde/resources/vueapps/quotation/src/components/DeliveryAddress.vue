<template>

  <div class="wizard_inleiding">
    Vul in onderstaand lijst uw elementmaten in. U kunt ook tabs enters gebruiken bij de invoer.
  </div>

  <div id="step5" class="wizard" v-cloak v-if="!loading">

    <div class="alert alert-danger" v-if="errors.length>0">
      <PERSON>r <PERSON>i<PERSON> fout<PERSON> opgetreden, controleer uw invoer:
      <ul>
        <li v-for="error in errors" :key="error">{{error}}</li>
      </ul>
    </div>

    <form method="post" :ref="setForm">
      <div v-if="isAdmin">
        <div class="form-row">
          <label class="col3 col-form-label">Prijslijst</label>
          <div class="col1">
            <div class="input-validation-icon">
              <img src="/projects/rde/templates/frontend/images/check_green.svg" alt="" width="20" />
            </div>
          </div>
          <div class="col7">
            <div class="select" >
              <select v-model="quotation_extra.quotationAltPriceYear" name="quotationAltPriceYear" >
                <option v-for="priceyear in priceyears" :value="priceyear.start" :key="priceyear.start">
                  {{priceyear.year}}
                </option>
              </select>
            </div>
          </div>
          <div class="col1">
            <GsdPopper content="Selecteer de te gebruiken prijslijst.">
              <a class="question-mark-inline fa fa-info-circle"></a>
            </GsdPopper>
          </div>
        </div>
        <div class="form-row">
          <label class="col3 col-form-label">E-mail niet verzenden</label>
          <div class="col1">
            <div class="input-validation-icon">
              <img src="/projects/rde/templates/frontend/images/check_green.svg" alt="" width="20" />
            </div>
          </div>
          <div class="col7">
            <label style="line-height: 2.5;">
              <input type="checkbox" value="1" name="NoEmail" id="NoEmail" class="form-checkbox" v-model="quotation.NoEmail" :true-value="1"/> niet verzenden per e-mail
            </label>
          </div>
          <div class="col1">
            <GsdPopper content="Wanneer dit vinkje is gezet gaat er geen e-mail naar de klant, wel naar Bart.">
              <a class="question-mark-inline fa fa-info-circle"></a>
            </GsdPopper>
          </div>
        </div>
      </div>

      <div class="form-row">
        <label class="col3 col-form-label">Afleveradres</label>
        <div class="col1">
          <div v-if="is_valid_address" class="input-validation-icon">
            <img src="/projects/rde/templates/frontend/images/check_green.svg" alt="" width="20" />
          </div>
        </div>
        <div class="col7">
          <div class="select" >
            <select name="addressDeliveryId" v-model="quotation_extra.addressDeliveryId" @change="changeAddressDeliveryId()">
              <option value="">Selecteer verzendmethode...</option>
              <option v-for="address in addresses" :value="address.addressId" :key="address.addressId">
                {{ getAddressFormatted(address) }}
              </option>
            </select>
          </div>
        </div>
        <div class="col1">
          <GsdPopper content="Selecteer uw afleveradres, of maak een nieuwe afleveradres aan. U kunt de raamdorpels ook komen ophalen, hiermee bespaart u verzendkosten.">
            <a class="question-mark-inline fa fa-info-circle"></a>
          </GsdPopper>
        </div>
      </div>

      <div v-if="quotation_extra.addressDeliveryId=='NEW'" id="wizard_delivery">
        <div class="">
          <label class="col3 col-form-label" style="font-weight: bold; padding-bottom: 10px;">Nieuw afleveradres</label>
          <div class="col9">
          </div>
        </div>

        <div class="form-row">
          <label class="col3 col-form-label">Postcode + nummer <span class="form-arterisk">*</span></label>
          <div class="col1">
            <div v-if="is_valid_nr" class="input-validation-icon">
              <img src="/projects/rde/templates/frontend/images/check_green.svg" alt="" width="20" />
            </div>
          </div>
          <div class="col3">
            <input type="text" class="form-input" v-model="quotation.zipcode" :ref="'zipcode'"  @change="validateZipcodeAndNr(true)" name="zipcode" id="zipcode" placeholder="Postcode..." required maxlength="6"/>
          </div>
          <div class="col2">
            <input type="number" class="form-input" name="nr" v-model="quotation.nr" :ref="'nr'" @change="validateZipcodeAndNr(true)" placeholder="Nummer..." required="required" maxlength="5"/>
          </div>
          <div class="col2">
            <input type="text" class="form-input" name="ext" v-model="quotation.ext"  placeholder="Toevoeging..." maxlength="10"/>
          </div>
        </div>

        <div class="form-row">
          <label class="col3 col-form-label">Plaats + straat <span class="form-arterisk">*</span></label>
          <div class="col1">
            <div v-if="is_valid_zipcode && is_valid_domestic" class="input-validation-icon">
              <img src="/projects/rde/templates/frontend/images/check_green.svg" alt="" width="20" />
            </div>
          </div>
          <div class="col3">
            <input type="text" class="form-input" :class="{inputerror: input_errors.street}" v-model="quotation.street" :ref="'street'" name="street" id="street" placeholder="Straat..." required @change="validateStreet(true)" :readonly="street_readonly"/>
          </div>
          <div class="col4">
            <input type="text" class="form-input" :class="{inputerror: input_errors.domestic}" v-model="quotation.domestic" :ref="'domestic'" @change="validateDomestic(true)" name="domestic" id="domestic" placeholder="Plaats..." required  :readonly="domestic_readonly" />
            <input type="hidden" v-model="quotation.street_city_manual" name="street_city_manual" id="street_city_manual"/>
          </div>
        </div>

        <div class="form-row">
          <label class="col3 col-form-label">Land <span class="form-arterisk">*</span></label>
          <div class="col1">
            <div class="input-validation-icon">
              <img src="/projects/rde/templates/frontend/images/check_green.svg" alt="" width="20" />
            </div>
          </div>
          <div class="col7">
            <div class="select" >
              <select name="country" id="country" v-model="quotation.country" :ref="'country'" required  @change="validateCountry(true)" >
                <option value="BE">België</option>
                <option value="DE">Duitsland</option>
                <option value="NL">Nederland</option>
              </select>
            </div>
            <div v-if="quotation.country!='NL'" style="color: #CE000C; padding-top: 5px;">Let op: levering buiten Nederland kan extra bezorgkosten met zich meebrengen.</div>
          </div>
        </div>

      </div>


      <div class="form-row">
        <label class="col3 col-form-label">Bijzonderheden levering</label>
        <div class="col1">
          <div class="input-validation-icon">

          </div>
        </div>
        <div class="col8">
          <label style="line-height: 1.5;display: flex;margin: 10px  0 10px 0;">
            <div>
              <input type="checkbox" value="1" v-model="delivery_reach" name="delivery_reach" id="delivery_reach" class="form-checkbox"/>
            </div>
            <div>
              Een vrachtwagen met oplegger kan de locatie goed bereiken<br/>
              Geen lengte / breedte / gewicht beperking
            </div>
          </label>

        </div>
        <div id="delivery_notes_container" v-if="!delivery_reach">
          <div class="col4">
          </div>
          <div class="col7">
            <textarea class="form-input" :class="{inputerror: input_errors.deliveryNotes}" name="deliveryNotes" id="deliveryNotes" placeholder="Toelichting bijzonderheden levering" v-model="quotation.deliveryNotes"></textarea>
          </div>
        </div>
      </div>

      <div class="form-row" v-if="showSms">
        <label class="col3 col-form-label">SMS</label>
        <div class="col1">
          <div class="input-validation-icon">

          </div>
        </div>
        <div class="col8">
          <label style="line-height: 1.5;display: flex;margin: 10px  0 10px 0;">
            <div>
              <input type="checkbox" value="1" v-model="quotation_extra.sms" name="sms" id="sms" class="form-checkbox" :true-value="1"/>
            </div>
            <div>
              Verstuur een SMS/Whatsapp als de chauffeur onderweg is naar het afleveradres
            </div>
          </label>
          <label style="line-height: 1.5;display: flex;margin: 10px  0 10px 0;">
            <div>
              <input type="checkbox" value="1" v-model="quotation_extra.sms_delivered" name="sms_delivered" id="sms_delivered" class="form-checkbox" :true-value="1"/>
            </div>
            <div>
              Verstuur een SMS/Whatsapp als uw bestelling is afgeleverd op het afleveradres
            </div>
          </label>
          <div v-if="quotation_extra.sms==1 || quotation_extra.sms_delivered==1">
            <div style="display: block;">
              <input type="text" class="form-input" name="smsnumber" v-model="quotation_extra.smsnumber" size="25" maxlength="15" placeholder="Mobiel nummer..." style="width: 250px;" />
            </div>
            <div style="display: block;margin: 10px 0;">
              Wilt u altijd een SMS ontvangen? Dit kunt u instellen bij <a href="/mijn-instellingen">Mijn instellingen</a>
            </div>
          </div>
        </div>
      </div>

      <br/><br/>

      <button @click.prevent="clickPrev()" type="button" name="prev" id="prev" class="btn" style="float: left;" formnovalidate><i class="fa fa-chevron-left"></i> Vorige stap</button>
      <button @click.prevent="clickNext()" type="button" name="next" id="next" class="btn" style="float: right;" v-bind:disabled="!all_valid"  @click="validate(true,  $event)">Doorgaan <i class="fa fa-chevron-right"></i></button>

    </form>
  </div>

</template>

<script>

import GsdPopper from './../components/GsdPopper.vue';
import ErrorHelper from "../helpers/ErrorHelper";

export default {
  name: 'DeliveryAddress',
  components: {
    GsdPopper,
  },
  data() {
    return {
      loading: true,
      form: null,
      is_valid: {},
      all_valid: false,
      is_active: {},
      errors: [],
      input_errors: {},
      isAdmin: false,
      priceyears: {},

      quotation: {},
      quotation_extra: {},
      addresses: {},

      delivery_reach: false,

      is_valid_address: false,
      is_valid_street: false,
      is_valid_nr: false,
      is_valid_zipcode: false,
      is_valid_domestic: false,

      street_readonly : true,
      domestic_readonly : true,
      showSms: false,
    }
  },

  mounted() {
  },

  created() {
    this.fetchData();
  },

  watch: {
  },
  computed: {
  },
  methods: {
    fetchData() {
      fetch('?action=wizard&step=5&json=1', {
        headers: {'Content-type': 'application/json'},
      })
          .then(res => res.json())
          .then((response) => {

            if (ErrorHelper.handleError(this, response)) return;

            this.quotation = response.data.quotation;
            this.quotation_extra = response.data.quotation_extra;
            this.elements = response.data.elements;
            this.addresses = response.data.addresses;
            this.priceyears = response.data.priceyears;
            this.isAdmin = response.data.isAdmin;
            this.delivery_reach = response.data.delivery_reach;
            this.showSms = response.data.showSms;

            this.validate(false);

            this.loading = false;

          })
          .catch((error) => {
            this.errors = [];
            this.errors.push("Server fout opgetreden. Probeer het later nog eens, of neem contact op (" + error + ")");
          });
    },
    postData(direction) {

      const formData = new FormData(this.form); // reference to form element
      formData.append(direction, 1);

      fetch('?action=wizard&step=5&json=1', {
        method: "POST",
        headers: {
          // 'Content-type': 'application/json',
          "Accept": "application/json",   // expected data sent back
        },
        body: formData
      })
          .then(res => res.json())
          .then((response) => {

            if (ErrorHelper.handleError(this, response)) return;

            this.errors = response.data.errors;
            if ("redirect" in response) {
              location.href = response.redirect;
            }
            this.domestic_readonly = false;
            this.street_readonly = false;
            this.quotation.street_city_manual = true;

          })
          .catch((error) => {
            this.errors = [];
            this.errors.push("Server fout opgetreden. Probeer het later nog eens, of neem contact op (" + error + ")");
          });
    },
    clickPrev() {
      this.postData("prev");
    },
    clickNext() {
      this.postData("next");
    },
    //validation
    validate: function (show, event) {

      this.validateZipcodeAndNr(false);

      if(this.quotation_extra.addressDeliveryId==="NEW" && (!this.is_valid_zipcode || !this.is_valid_nr)) {
        this.all_valid = false;
        return;
      }

      if(!this.delivery_reach && this.quotation.deliveryNotes==="") {
        if(show) this.input_errors.deliveryNotes = true;
      }
      else {
        this.input_errors.deliveryNotes = false;
      }

      this.all_valid = true;
      if(!this.all_valid && event) event.preventDefault();
    },
    validateZipcodeAndNr: function (show) {
      this.is_valid_address = true;
      if(this.quotation_extra.addressDeliveryId==="NEW") {

        if(!this.quotation.zipcode || this.quotation.zipcode=="") {
          this.is_valid_zipcode = false;
          if(show) this.input_errors.zipcode = true;
        }
        else {
          this.is_valid_zipcode = true;
          if(show) this.input_errors.zipcode = false;
        }

        if(!this.quotation.nr || this.quotation.nr=="") {
          this.is_valid_nr = false;
          if(show) this.input_errors.nr = true;
        }
        else {
          this.is_valid_nr = true;
          if(show) this.input_errors.nr = false;
        }

        if(this.is_valid_zipcode && this.is_valid_nr) {
          if(this.quotation.country!=="NL") { //only use postcode api in netherlands
            this.domestic_readonly = false;
            this.street_readonly = false;
            let allvalid = true;
            if(!this.validateStreet(show)) allvalid = false;
            if(!this.validateDomestic(show)) allvalid = false;
            this.all_valid = allvalid;
            return;
          }
          if(this.quotation.street_city_manual) {
            //handmatig straat een plaats invullen
            this.domestic_readonly = false;
            this.street_readonly = false;
          }
          else {
            this.domestic_readonly = true;
            this.street_readonly = true;
            //beide valid, haal adres op
            fetch("?action=postcodeapi&zipcode=" + this.quotation.zipcode + "&nr=" + this.quotation.nr)
                .then(res => res.json())
                .then(res => {
                  if (res.error) {
                    this.errors.push(res.error);
                  }
                  else {
                    if (res.success) {
                      if (res.success === "open") {
                        //no credits at postcode api, customer can enter the info himself
                        this.domestic_readonly = false;
                        this.street_readonly = false;
                        this.quotation.street_city_manual = true;
                      }
                      else {
                        this.quotation.street = res.data.street;
                        this.quotation.domestic = res.data.city;
                      }
                    }
                    else if (!this.quotation.street_city_manual) {
                      this.quotation.street = "";
                      this.quotation.domestic = "";
                    }
                    // let allvalid = true;
                    // if(!this.validateStreet(show)) allvalid = false;
                    // if(!this.validateDomestic(show)) allvalid = false;

                    this.all_valid = true;
                  }
                })
                .catch(error => {
                  this.errors = [];
                  this.errors.push("Server fout opgetreden. Probeer het later nog eens, of neem contact op (" + error + ")");
                });
          }
        }
      }
      else if(this.quotation_extra.addressDeliveryId=="") {
        this.is_valid_address = false;
      }
      else {
        this.is_valid_address = true;
      }
    },
    validateStreet: function (show) {
      var valid = true;
      if(!this.quotation.street || this.quotation.street==="") {
        valid = false;
        if(show) this.input_errors.street = true;
      }
      else {
        if(show) this.input_errors.street = false;
      }
      this.is_valid_street = valid;
      if(show) {
        this.validate(false);
      }
      return valid;
    },
    validateDomestic: function (show) {
      var valid = true;
      if(!this.quotation.domestic || this.quotation.domestic==="") {
        valid = false;
        if(show) this.input_errors.domestic = true;
      }
      else {
        if(show) this.input_errors.domestic = false;
      }
      this.is_valid_domestic = valid;
      if(show) {
        this.validate(false);
      }
      return valid;
    },
    validateCountry: function (show) {
      if(show) {
        this.validate(false);
      }
      return true;
    },
    //other
    getAddressFormatted: function (address) {
      var str = "";
      if(address.addressId==20357) {
        str = "Ophalen - Raambrug 9, 5531 AG Bladel, Nederland";
      }
      else if(address.addressId==="NEW") {
        str = "Nieuw afleveradres";
      }
      else {
        str = "Afleveren - ";
        if(address.type==="visit") {
          str += "Bezoekadres - ";
        }
        if(address.title!="" && address.title!=null) {
          str += address.title+" - ";
        }
        str += address.street+" "+ address.nr;
        if(address.extension!="" && address.extension!=null) {
          str += " "+address.extension;
        }
        str += ", "+address.zipcode+" "+address.domestic+", " +address.country;
      }
      return str;
    },
    setForm(el) {
      this.form = el;
    },
    changeAddressDeliveryId() {
      if(this.quotation_extra.addressDeliveryId==="NEW") {
        //word zichtbaar gezet, leegmaken velden.
        this.quotation.street = '';
        this.quotation.nr = '';
        this.quotation.zipcode = '';
        this.quotation.domestic = '';
        this.quotation.ext = '';
      }
      else {
        this.validate(false);
      }
    }

  }

}

</script>

<style>
</style>

