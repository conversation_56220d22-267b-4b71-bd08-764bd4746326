<template>
  <div :ref="setMainEl" >
    <a href="" @click.prevent="show()" :id="name" class="vue-gsd-select-btn" :class="{'vue-gsd-open':open}">
      <div class="imagesmallfile" v-if="selectedOption!=null && selectedOption.image && selectedOption.value!==''" :class="{ noimage: (selectedOption.image==null || selectedOption.image==='') }">
        <img :src="selectedOption.image"  v-if="selectedOption!=null && selectedOption.image!==''" :alt="selectedOption.name"/>
      </div>
      <div class="vue-gsd-select-name" v-if="selectedOption==null || selectedOption.value===''">
        {{no_selection_text}}
      </div>
      <div class="vue-gsd-select-name" v-else>
        {{ selectedOption.name }}
      </div>
      <span class="fa fa-chevron-down"></span>
    </a>
    <div class="vue-gsd-select" v-if="open">
      <ul>
        <li v-for="option in options" :class="{'vue-gsd-select-active': option.value===value}" :key="option.value">
          <a href="#"  @click="clickSelection(option)">
            <div class="imagesmallfile" v-if="option.image">
              <img :src="option.image"  v-if="option.image!=null && option.image!==''" />
            </div>
            {{ option.name }}
          </a>
        </li>
      </ul>
    </div>
    <input type="hidden" :name="name" :id="name"  :value="selectedOptionValue"/>
  </div>
</template>
<script type="text/javascript">

/**
 * name: name of select
 * options:
 * [
 *   {
 *    name: name of selection
 *    value: value of selection
 *    image: image of selection
 *  }
 *  ,...
 * ]
 * value: selected option alue
 * no_selection_text: text when nothing selected
 * disabled: disable select
 */
export default {
  props: ['name', 'options', 'value', 'no_selection_text', 'disabled'],
  data() {
    return {
      open: false,
      mainEl: null,
      selectedOption : null,
      selectedOptionValue : '',
    }
  },
  watch: {
    options() {
      this.updateSelection();
    },
    value() {
      this.updateSelection();
    },
  },
  mounted() {
    this.updateSelection();
    document.addEventListener('mouseup', this.clickOutside);
  },
  methods :  {
    updateSelection() {
      this.selectedOption = null;
      this.selectedOptionValue = "";
      for (var key in this.options) {
        let option = this.options[key];
        if(option.value===this.value) {
          this.selectedOption = option;
          this.selectedOptionValue = option.value;
          break;
        }
      }
    },
    show() {
      if(this.disabled) return false;
      this.open = true;
    },
    hide() {
      this.open = false;
    },
    clickSelection(option) {
      event.preventDefault(); //prevent scroll to top
      this.selectedOption = option;
      this.selectedOptionValue = option.value;
      this.$emit("changeValue", this.selectedOptionValue, 1);
      this.hide();
    },
    clickOutside(e) {
      var el = this.mainEl;
      if (!(el === e.target || el.contains(e.target))) {
        this.hide();
      }
    },
    setMainEl(el) {
      if (el) {
        this.mainEl = el;
      }
    }
  }
}
</script>

<style scoped>

.vue-gsd-select-btn {
  border: 1px solid #d2d2d2;
  padding: 10px 15px;
  width: 100%;
  border-radius: 3px;
  line-height: 25px;
  color: #333;
  display: flex;
  /*align-content: center;*/
  background-color: white;
}

.vue-gsd-select-btn .fa {
  margin-top: 5px;
}

.vue-gsd-select-name {
  flex-grow: 1;
}

.vue-gsd-select-btn span {
  margin-left: 10px;
}

.vue-gsd-select-btn .imagesmallfile {
  width: 35px;
  display: inline-block;
  margin-right: 15px;
}

.vue-gsd-select-btn .imagesmallfile.noimage {
  background: #f7f7f7;
}

.vue-gsd-select {
  position: absolute;
  background-color: white;
  box-shadow: 0 1px 5px #ddd;
  border: solid 1px #ccc;
  z-index: 10;
  min-width: 432px;
  max-width: 100%;
}

.vue-gsd-select-btn .fa.fa-chevron-down {
  color: #888;
  transition: transform .15s cubic-bezier(1, -.115, .975, .855);
  transition-timing-function: cubic-bezier(1, -.115, .975, .855);
}
.vue-gsd-select-btn.vue-gsd-open .fa.fa-chevron-down {
  transform: rotate(180deg);
}

.vue-gsd-select ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

.vue-gsd-select a {
  display: flex;
  align-items: center;
  padding: 5px 15px;
  color: #333;
}

.vue-gsd-select a:hover {
  background-color: #EBEBEB;
  color: #ce000c;
}

.vue-gsd-select-active, .vue-gsd-select-active a {
  background-color: #EBEBEB;
  color: #ce000c;
}

.vue-gsd-select .imagesmallfile {
  width: 50px;
  min-height: 30px;
  margin-right: 15px;
}

.disabled .vue-gsd-select-btn {
  background-color: #dcdedf;
  color: #666;
  font-size: 15px;
}
</style>
