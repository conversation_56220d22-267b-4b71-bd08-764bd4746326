<!-- <PERSON><PERSON><PERSON><PERSON> Muurafdekker Plat Breedte Dikte Selector -->
<template>
  <div>

    <input type="hidden" :value="this.sizeId" name="sizeId"/>

    <div class="form-row" :class="{ disabled: disabled }">
      <label class="col3 col-form-label">Breedte</label>
      <div class="col1">
        <div v-if="width!=null && !disabled" class="input-validation-icon">
          <img src="/projects/rde/templates/frontend/images/check_green.svg" alt="" width="20" />
        </div>
      </div>
      <div class="col7">
        <gsd-select
            name="width"
            no_selection_text="Kies een breedte..."
            v-bind:options="width_values"
            v-bind:value="width"
            v-bind:disabled="disabled"
            @changeValue="setWidth"
        ></gsd-select>
      </div>
      <div class="col1">
        <GsdPopper>
          <a class="question-mark-inline fa fa-info-circle"></a>
          <template #content>
            Selecteer uw breedte.
          </template>
        </GsdPopper>
      </div>
    </div>
    <div class="form-row" :class="{ disabled: disabled }">
      <label class="col3 col-form-label">Dikte</label>
      <div class="col1">
        <div v-if="thickness!=null && !disabled" class="input-validation-icon">
          <img src="/projects/rde/templates/frontend/images/check_green.svg" alt="" width="20" />
        </div>
      </div>
      <div class="col7">
        <gsd-select
            name="thickness"
            no_selection_text="Kies een dikte..."
            v-bind:options="thickness_values"
            v-bind:value="thickness"
            v-bind:disabled="disabled"
            @changeValue="setThickness"
        ></gsd-select>
      </div>
      <div class="col1">
        <GsdPopper>
          <a class="question-mark-inline fa fa-info-circle"></a>
          <template #content>
            Selecteer uw dikte.
          </template>
        </GsdPopper>
      </div>
    </div>
  </div>
</template>

<script>

// Breedte Diepte selector voor Natuursteen > Muurafdekkers > Plat

import GsdPopper from "@/components/GsdPopper.vue";
import GsdSelect from "@/components/GsdSelect.vue";

export default {
  name: 'NaturalStoneWallCopingFlat',
  components: {GsdSelect, GsdPopper},
  props: ['sizes', 'sizeId', 'possible_sizes', 'disabled'],
  data() {
    return {
      width: null,
      width_values: [],
      thickness: null,
      thickness_values: [],
      possibleSizeIds : [],
      activeSize : null,
    }
  },
  watch: {
    sizes() {
      this.updateSelection();
    },
    possible_sizes() {
      this.updateSelection();
    },
  },
  mounted() {
    this.updateSelection();
  },
  methods: {
    updateSelection() {

      this.width_values = [];
      this.thickness_values = [];
      this.usedWidths = [];
      this.usedThickness= [];


      //get possible size id, from allowed sizes of this brand and color
      this.possibleSizeIds = [];
      for (let sizeGroupKey in this.possible_sizes) {
        for (let sizeKey in this.possible_sizes[sizeGroupKey]) {
          this.possibleSizeIds.push(this.possible_sizes[sizeGroupKey][sizeKey].value);
        }
      }

      //set current active size object, if allowed
      this.activeSize = null;
      if(this.sizeId!=null) {
        for (let sizeKey in this.sizes) {
          let size = this.sizes[sizeKey];
          if(size.sizeId==this.sizeId) {
            if(this.possibleSizeIds.indexOf(size.sizeId)!==-1) { //mag deze maat wel gekozen worden?
              this.activeSize = size;
            }
            break;
          }
        }
      }

      //build array width / thickness
      for (let sizeKey in this.sizes) {
        let size = this.sizes[sizeKey];

        if(this.possibleSizeIds.indexOf(size.sizeId)===-1) {
          //dit formaat is niet mogelijk, overslaan
          continue;
        }

        if(this.usedWidths.indexOf(size.length)===-1) {
          this.usedWidths.push(size.length);
          let option = {
            name: (size.length * 10) + " mm",
            value: size.length,
          };
          this.width_values.push(option);
        }

        if(this.usedThickness.indexOf(size.height)===-1) {
          this.usedThickness.push(size.height);
          let option = {
            name: (size.height * 10) + " mm",
            value: size.height,
          };
          this.thickness_values.push(option);
        }

        if(this.activeSize!=null) {
          this.width = this.activeSize.length;
          this.thickness = this.activeSize.height;
        }
        else {
          this.width = null;
          this.thickness = null;
        }


      }

    },
    setWidth(width) {
      this.width = width;
      this.changeValue();
    },
    setThickness(thickness) {
      this.thickness = thickness;
      this.changeValue();
    },
    changeValue() {
      let sizeId = null;
      //valid witdh and thicness, emit to parent
      for (let sizeKey in this.sizes) {
        let size = this.sizes[sizeKey];
        if(this.possibleSizeIds.indexOf(size.sizeId)===-1) continue; //is niet in lijst
        if(size.height==this.thickness && size.length==this.width) {
          sizeId = size.sizeId;
          break;
        }
      }
      //console.log(sizeId);
      this.$emit("changeValue", sizeId, 1);
    }
  }
}
</script>

<style scoped>
</style>
