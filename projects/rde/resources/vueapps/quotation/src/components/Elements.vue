<template>

  <div id="step2-popup-bg" v-if="showPopupBG"></div>

  <div class="wizard_inleiding">
    Vul in onderstaand lijst uw elementmaten in. U kunt ook tabs gebruiken bij de invoer.
  </div>

  <div class="alert alert-danger" v-if="errors.length>0">
    Er zijn foutmeldingen opgetreden, controleer uw invoer:
    <ul>
      <li v-for="error in errors" :key="error">{{error}}</li>
    </ul>
  </div>

  <div id="step2" class="wizard" v-cloak v-if="!loading">

    <form method="post" :ref="setForm">

      <div class="form-row"  v-if="showSpeling()">
        <label class="col3 col-form-label">Speling</label>
        <div class="col1">
          <div v-if="is_valid.shorter" class="input-validation-icon">
            <img src="/projects/rde/templates/frontend/images/check_green.svg" alt="" width="20" />
          </div>
        </div>
        <div class="col7">
          <div class="select">
            <select name="shorter" class="shorter" v-model="quotation.shorter" :class="{inputerror: hasShorterError}">
              <option value="">
                Kies de gewenste speling...
              </option>
              <option v-for="shorter in possible_shorter" :value="shorter.value" :key="shorter.value">
                {{ shorter.text }}
              </option>
            </select>
          </div>
        </div>
        <div class="col1">
          <GsdPopper>
            <a class="question-mark-inline fa fa-info-circle"></a>
            <template #content>
              Normaal heeft u bij nieuwbouw hier een waarde van 10 mm. Dit betekent dat u 5 mm voeg aan beide zijden van
              het element tegen de neggekant overhoud. Bij renovatie worden de kozijnen vaak kleiner als de bestaande
              muuropening gemaakt, daarom wordt hier vaak een kleinere waarde genomen. Wanneer u 0 mm kiest heeft u het
              element precies even breed als de kozijnbreedte.
            </template>
          </GsdPopper>
        </div>
      </div>


      <div class="form-row"  v-if="showMuurdikte()">
        <label class="col3 col-form-label">Muurdikte (mm)</label>
        <div class="col1">
          <div v-if="is_valid.wall_thickness" class="input-validation-icon">
            <img src="/projects/rde/templates/frontend/images/check_green.svg" alt="" width="20" />
          </div>
        </div>
        <div class="col7">
          <input type="number" v-model="quotation_extra.wall_thickness"  name="wall_thickness" class="form-input inputnumber" :class="{inputerror: hasWallthicknessError}"/>
        </div>
        <div class="col1">
          <GsdPopper>
            <a class="question-mark-inline fa fa-info-circle"></a>
            <template #content>
              Voer uw muurdikte in. Is uw muurdikte niet mogelijk? Kies dan op de vorige pagina een andere muurafdekker welke past bij uw muurdikte, of neem even contact op.
            </template>
          </GsdPopper>
        </div>
      </div>

      <div class="form-row">
        <label class="col3 col-form-label">Elementen</label>
        <div class="col1">
        </div>

        <label class="col7 col-form-label">Voer uw elementmaten in.</label>
        <div class="col1">
        </div>
        <div class="col12 scroller-x">
          <table id="elementtable">
            <tr class="header-row">
              <td>
                Kenmerk
              </td>
              <td style="width: 100px;">
                Aantal
              </td>
              <td style="width: 120px;">
                {{ maatTextTitle }}
              </td>
              <td style="width: 120px;">
                {{this.verstekhoektitle}}
                <GsdPopper>
                  <a class="question-mark-inline fa fa-info-circle"></a>
                  <template #content>
                    <div v-if="this.stone.type==='spekband' && quotation.brandId==1">
                      Geef hier aan of een hoek wilt links, rechts of aan beide kanten.
                    </div>
                    <div v-else>
                      <p>U kunt hier aangeven aan welke kant een verstek komt.<br>
                        (Hier vind u een handige reken voorbeeld "pdf")</p>
                      <ul>
                        <li>Hartklik gemeten
                          <ul>
                            <li><a href="/downloads/verstekken-invulformulier-uitwendige-hoek.pdf" target="_blank">uitwendige hoek.pdf</a></li>
                            <li><a href="/downloads/verstekken-invulformulier-inwendige-hoek.pdf" target="_blank">inwendige hoek.pdf</a></li>
                            <li><a href="/downloads/verstekken-invulformulier-uitwendige-hoek-van-675-135-graden.pdf" target="_blank">uitwendige hoek van 67,5 (135) graden.pdf</a></li>
                            <li><a href="/downloads/verstekken-invulformulier-uitwendige-hoek-van-60-120-graden.pdf" target="_blank">uitwendige hoek van 60 (120) graden.pdf</a></li>
                          </ul>
                        </li>
                        <li>Tot aan de punt gemeten
                          <ul>
                            <li><a href="/downloads/verstekken/verstekken-informatie-uitwendige-hoek-tot-aan-de-punt-gemeten.pdf" target="_blank">uitwendige hoek tot aan de punt gemeten.pdf</a></li>
                            <li><a href="/downloads/verstekken/verstekken-informatie-inwendige-hoek-tot-aan-de-punt-gemeten.pdf" target="_blank">inwendige hoek tot aan de punt gemeten.pdf</a></li>
                          </ul>
                        </li>
                      </ul>
                    </div>
                  </template>
                </GsdPopper>
              </td>
              <td style="width: 120px;" v-if="showVlagkozijn">
                Vlagkozijn<br/>
                <GsdPopper>
                  <a class="question-mark-inline fa fa-info-circle"></a>
                  <template #content>
                    Een vlagkozijn, melkmeisje of schouderkozijn is een bouwkundige benaming voor een kozijntype waarbij een deurkozijn aan weerszijden geflankeerd wordt door een raamopening of zijlicht.<br/>
                    De jukvorm van het kozijn doet denken aan een melkmeisje dat twee emmers draagt.<br/>
                    Met deze toepassing wordt het element tegen het kozijn geplaatst hierdoor wordt de helft van de speling erbij geteld.
                  </template>
                </GsdPopper>
              </td>
              <td style="width: 120px;" v-if="showKieseindsteen">
                Eindsteen<br/>
                <GsdPopper content="Kies een eindsteen.">
                  <a class="question-mark-inline fa fa-info-circle"></a>
                </GsdPopper>
              </td>
              <td style="min-width: 140px;">
                Element lengte <br/> (mm)
                <GsdPopper content="De element lengte word uitgerekend a.d.v. speling, verstekhoek e.d. De daadwerkelijk element lengte kunnen we pas uitrekenen als ook eventuele verstekhoeken bekend zijn, welke in de volgende stap gevraagd worden.">
                  <a class="question-mark-inline fa fa-info-circle"></a>
                </GsdPopper>
              </td>
            </tr>

            <tr class="elementrow" v-for="(element, key) in elements" :key="key">
              <td>
                <input type="hidden" v-model="element.mitre" :name="'elements['+key+'][mitre]'" />
                <input type="hidden" v-model="element.flagWindow" :name="'elements['+key+'][flagWindow]'" />
                <input type="hidden" v-model="element.oldkey" :name="'elements['+key+'][oldkey]'" />
                <input autocomplete="off" type="text" v-model="element.referenceName" @focus="$event.target.select()" :name="'elements['+key+'][referenceName]'" :id="'elements['+key+'][referenceName]'" @keydown="navigateReferenceName(key)" @keyup="navigateReferenceNameUp(key)" @change="validateReferenceNameInput(element,key)"  placeholder="Kenmerk..." class="form-input" :class="{inputerror: element.hasReferenceNameError}" maxlength="12" :ref="'referenceName_'+key"/>
              </td>
              <td>
                <input autocomplete="off" type="text" v-model="element.amount" @focus="$event.target.select()" :name="'elements['+key+'][amount]'"  :ref="'amount_'+key" @keydown="navigateAmount(key)" @change="validateAmountInput(element)" placeholder="Aantal..." class="form-input inputnumber" maxlength="4"/>
              </td>
              <td>
                <input autocomplete="off" type="text" v-model="element.inputLength" @focus="$event.target.select()"  :name="'elements['+key+'][inputLength]'"  :ref="'inputLength_'+key"  @keydown="navigateInputLength(key)" @change="validateInputLengthInput(element, key, true)" placeholder="Maat..."  class="form-input inputnumber" :class="{inputerror: element.hasInputLengthError}" maxlength="5"/>
              </td>
              <td>
                <input type="button" :value="mitreText(element)" @click="openMitrebox(element)" class="form-input form-btn" :ref="'mitrebox_'+key"  />
              </td>
              <td v-if="showVlagkozijn">
                <input type="button" :value="flagWindowText(element)" @click="openflagWindowbox(element)" class="form-input form-btn" />
              </td>
              <td v-if="showKieseindsteen">
                <input type="button" :value="endstoneText(element)"  @click="openEndstoneBox(element)" class="form-input form-btn" />
                <input type="hidden" v-model="element.leftEndstone" :name="'elements['+key+'][leftEndstone]'" />
                <input type="hidden" v-model="element.rightEndstone" :name="'elements['+key+'][rightEndstone]'" />
              </td>
              <td>
                <input type="text" v-model="element.elementLengthText" readonly :name="'elements['+key+'][elementLength]'" class="form-input inputnumber wizard_elementLength inputreadonly_cust"/>
                <a href="#" class="elementtable_fa" @click.prevent="remove(key)" title="Verwijderen" :ref="'remove_'+key">
                  <i class="fa fa-remove"></i>
                </a>
                <a href="#" class="elementtable_fa" @click.prevent="addElement(key)" title="Toevoegen" :ref="'add_'+key">
                  <i class="fa fa-plus"></i>
                </a>
              </td>
            </tr>


            <tr class="header-row">
              <td>Totalen </td>
              <td>
                <input type="text" v-model="amount_total" disabled class="form-input inputnumber inputreadonly_cust"/>
              </td>
              <td></td>
              <td></td>
              <td v-if="showVlagkozijn"></td>
              <td v-if="showKieseindsteen"></td>
              <td>
                <input type="text" v-model="elementLength_total" disabled class="form-input inputnumber wizard_elementLength inputreadonly_cust"/>
              </td>
            </tr>
          </table>
        </div>
      </div>


      <br/><br/>

      <button type="button" name="add" id="add" class="btn" @click="addElement()"><i class="fa fa-plus"></i> Element toevoegen</button>

      <br/><br/>

      <button @click.prevent="clickPrev()" type="button" name="prev" id="prev" class="btn" style="float: left;"><i class="fa fa-chevron-left"></i> Vorige stap</button>
      <button @click.prevent="clickNext()" type="button" name="next" id="next" class="btn" :class="{disabled: !all_valid}" style="float: right;">Doorgaan <i class="fa fa-chevron-right"></i></button>

    </form>

    <div :class="mitreBoxClass()" id="mitrebox" v-if="mitreboxShow">
      <div class="questionbox_title">{{this.verstekhoektitle}} - Element <span id="mitreElement">{{mitreboxElement.referenceName}}</span></div>
      <div class="questionbox_content">
        <p>	Kies aan welke uiteinden van het element u een (verstek)hoek wilt.</p>

        <div class="verstekhoekImgAfstand">
          <input type="radio" v-model="mitreboxElement.mitre" value="none" id="mitrebox_0" @change="selectMitre()">
          <label for="mitrebox_0">
            <div class="mitre_example mitre_example_none">
              <div id="rd_mitre_none" :title="'Geen '+this.verstekhoektitle">
                <div class="mitre_line"></div>
              </div>
            </div>
            <div class="mitre_label">Geen</div>
          </label>
        </div>
        <div class="verstekhoekImgAfstand">
          <input type="radio" v-model="mitreboxElement.mitre" value="left" id="mitrebox_1" @change="selectMitre()" v-bind:disabled="mitreboxElement.flagWindow==='double'">
          <label for="mitrebox_1">
            <div class="mitre_example mitre_example_left">
              <div title="Verstekhoek aan linker elementeinde">
                <div class="mitre_line"></div>
                <div class="mitre_schuin_left"></div>
              </div>
            </div>
            <div class="mitre_of">OF</div>
            <div class="mitre_example mitre_example_left_top">
              <div :title="this.verstekhoektitle+' aan linker elementeinde'">
                <div class="mitre_line"></div>
                <div class="mitre_schuin_left_top"></div>
              </div>
            </div>
            <div class="mitre_label">Links</div>
          </label>
        </div>
        <div class="verstekhoekImgAfstand">
          <input type="radio" v-model="mitreboxElement.mitre" value="right" id="mitrebox_2" @change="selectMitre()" v-bind:disabled="mitreboxElement.flagWindow==='double'">
          <label for="mitrebox_2">
            <div class="mitre_example mitre_example_right">
              <div :title="this.verstekhoektitle+' aan linker elementeinde'">
                <div class="mitre_line"></div>
                <div class="mitre_schuin_right"></div>
              </div>
            </div>
            <div class="mitre_of">OF</div>
            <div class="mitre_example mitre_example_right_top">
              <div :title="this.verstekhoektitle+' aan rechter elementeinde'">
                <div class="mitre_line"></div>
                <div class="mitre_schuin_right_top"></div>
              </div>
            </div>
            <div class="mitre_label">Rechts</div>
          </label>
        </div>
        <div class="verstekhoekImgAfstand">
          <input type="radio" v-model="mitreboxElement.mitre" value="both" id="mitrebox_3" @change="selectMitre()" v-bind:disabled="mitreboxDisableDouble">
          <label for="mitrebox_3">
            <div class="mitre_example mitre_example_leftright">
              <div :title="this.verstekhoektitle+'en aan weerszijden'">
                <div class="mitre_line"></div>
                <div class="mitre_schuin_left"></div>
                <div class="mitre_schuin_right"></div>
              </div>
            </div>
            <div class="mitre_of">OF</div>
            <div class="mitre_example mitre_example_leftright_top">
              <div :title="this.verstekhoektitle+'en aan weerszijden'">
                <div class="mitre_line"></div>
                <div class="mitre_schuin_right_top"></div>
                <div class="mitre_schuin_left_top"></div>
              </div>
            </div>
            <div class="mitre_label">Beide</div>
          </label>
        </div>
        <p></p>
        <p class="center">
          <input type="button" name="btnMitre" value="Sluiten" @click="selectMitre()" class="btn">
        </p>
      </div>
    </div>

    <div class="questionbox" id="flagWindowbox" v-if="flagWindowboxShow">
      <div class="questionbox_title">Vlagkozijn - Element <span id="flagWindowElement">{{flagWindowboxElement.referenceName}}</span></div>
      <div class="questionbox_content">
        <p>Kies of het element aan geen, een of twee kanten aan een deurkozijn grenst.</p>
        <div class="vlagkozijnImgAfstand">
          <input type="radio" v-model="flagWindowboxElement.flagWindow" value="false" id="flagWindowbox_0" @change="selectflagWindow()">
          <label for="flagWindowbox_0">Geen</label>
        </div>
        <div class="vlagkozijnImgAfstand">
          <input type="radio" v-model="flagWindowboxElement.flagWindow" value="single" id="flagWindowbox_1" @change="selectflagWindow()" v-bind:disabled="flagWindowboxElement.mitre=='both'">
          <label for="flagWindowbox_1"><img src="/images/mitres/flagframe_single.gif" alt="Enkel vlagkozijn" class="exampleimg"></label>
        </div>
        <div class="vlagkozijnImgAfstand">
          <input type="radio" v-model="flagWindowboxElement.flagWindow" value="double" id="flagWindowbox_2" @change="selectflagWindow()" v-bind:disabled="flagWindowboxElement.mitre!='none'">
          <label for="flagWindowbox_2"><img src="/images/mitres/flagframe_double.gif" alt="Dubbel vlagkozijn" class="exampleimg"></label>
        </div>
        <p class="center">
          <input type="button" name="btnflagWindow" value="Sluiten" @click="selectflagWindow()" class="btn">
        </p>
      </div>
    </div>

    <div :class="flagEndstoneBoxClass()" id="flagEndstoneBox" v-if="flagEndstoneShow">
      <div class="questionbox_title">Eindsteen - Element <span id="flagEndstoneElement">{{flagEndstoneElement.referenceName}}</span></div>
      <div class="questionbox_content">
        <p>Kies uw eindsteen</p>
        <div class="endStoneRow">
          <input type="radio" v-model="flagEndstoneValue" value="none" id="flageEndstone_0" @change="selectEndstone()">
          <label for="flageEndstone_0">
            <div class="endstone_example">
              <div title="Deurkozijn aan linker zijde">
                <div class="endstone_line"></div>
              </div>
            </div>
            <div class="mitre_label">Geen</div>
          </label>
        </div>
        <div class="endStoneRow">
          <input type="radio" v-model="flagEndstoneValue" value="left" id="flageEndstone_1" @change="selectEndstone()" v-bind:disabled="flagEndstoneElement.mitre=='left' || flagEndstoneElement.mitre=='both'">
          <label for="flageEndstone_1">
            <div class="endstone_example">
              <div title="Deurkozijn aan linker zijde">
                <div class="endstone_line"></div>
                <div class="endstone_left"></div>
                <div class="line_left"></div>
              </div>
            </div>
            <div class="mitre_label">Links</div>
          </label>
        </div>
        <div class="endStoneRow">
          <input type="radio" v-model="flagEndstoneValue" value="right" id="flageEndstone_2" @change="selectEndstone()" v-bind:disabled="flagEndstoneElement.mitre=='right' || flagEndstoneElement.mitre=='both'">
          <label for="flageEndstone_2">
            <div class="endstone_example">
              <div title="Deurkozijn aan rechter zijde">
                <div class="endstone_line"></div>
                <div class="endstone_right"></div>
                <div class="line_right"></div>
              </div>
            </div>
            <div class="mitre_label">Rechts</div>
          </label>
        </div>
        <div class="endStoneRow">
          <input type="radio" v-model="flagEndstoneValue" value="both" id="flageEndstone_3" @change="selectEndstone()" v-bind:disabled="flagEndstoneElement.mitre=='left' || flagEndstoneElement.mitre=='right' || flagEndstoneElement.mitre=='both'">
          <label for="flageEndstone_3">
            <div class="endstone_example">
              <div title="Deurkozijn aan beide zijde">
                <div class="endstone_line"></div>
                <div class="endstone_left"></div>
                <div class="line_left"></div>
                <div class="endstone_right"></div>
                <div class="line_right"></div>
              </div>
            </div>
            <div class="mitre_label">Beide</div>
          </label>
        </div>
        <p class="center">
          <br/>
          <input type="button" name="btnflagWindow" value="Sluiten" @click="selectEndstone()" class="btn">
        </p>
      </div>
    </div>

  </div>
</template>

<script>

import MathHelper from './../helpers/MathHelper.js';
import GsdPopper from './../components/GsdPopper.vue';
import ErrorHelper from "../helpers/ErrorHelper";

export default {
  name: 'Elements',
  components: {
    GsdPopper,
  },
  data() {
    return {
      loading: true,
      is_valid: {},
      all_valid: false,
      is_active: {},
      errors: [],
      form: null,
      hasShorterError: false,
      hasWallthicknessError : false,

      quotation : {},
      quotation_extra : {},
      elements : {},
      stone : {},
      stone_size : {},
      showVlagkozijn : false,
      showKieseindsteen : false,
      verstekhoektitle : "",

      element_new : {},
      mitreboxShow : false,
      mitreboxElement : {},
      mitreboxDisableDouble: false,
      flagWindowboxShow : false,
      flagWindowboxElement : {},
      flagEndstoneShow : false,
      flagEndstoneValue : 'none',
      flagEndstoneElement : {},

      amount_total: 0,
      elementLength_total: '',
      alphabetArray : ["A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"],

      showPopupBG : false,
    }
  },

  created() {
    this.fetchData();
  },

  mounted() {
  },

  watch: {
    'quotation.shorter': function() {
      this.shorterChanged();
    },
    'quotation_extra.wall_thickness': function() {
      this.wallthicknessChanged();
    }
  },
  computed: {
    maatTextTitle() {
      return this.stone.type==="raamdorpel"?"Kozijnmaat (mm)":"Muurmaat (mm)";
    },
    possible_shorter: function () {
      var filtered_array = [];
      for(var i=0;i<=10;i+=2) {
        var txt = i+" mm";
        if(i==0) {
          txt += " - geadviseerd renovatie";
        }
        else if(i==10) {
          txt += " - geadviseerd nieuwbouw";
        }
        filtered_array.push({value: i, text: txt});
      }
      return filtered_array;
    }
  },
  methods: {
    fetchData() {
      fetch('?action=wizard&step=2&json=1', {
        headers: {'Content-type': 'application/json'},
      })
          .then(res => res.json())
          .then((response) => {

            if (ErrorHelper.handleError(this, response)) return;

            this.quotation = response.data.quotation;
            this.quotation_extra = response.data.quotation_extra;
            this.stone = response.data.stone;
            this.stone_size = response.data.stone_size;
            this.errors = response.data.errors;
            this.elements = response.data.elements;
            this.element_new = response.data.element_new;
            this.showVlagkozijn = response.data.showVlagkozijn;
            this.showKieseindsteen = response.data.showKieseindsteen;
            this.verstekhoektitle = response.data.verstekhoektitle;

            this.mitreboxElement = response.data.element_new;
            this.flagWindowboxElement = response.data.element_new;
            this.flagEndstoneElement = response.data.element_new;

            if (this.quotation_extra.wall_thickness === null) {
              this.quotation_extra.wall_thickness = "";
            }

            this.shorterChanged();
            this.wallthicknessChanged();
            this.calculateTotals();

            this.loading = false;

          })
          .catch((error) => {
            this.errors = [];
            this.errors.push("Server fout opgetreden. Probeer het later nog eens, of neem contact op (" + error + ")");
          });
    },
    postData(direction) {

      const formData = new FormData(this.form); // reference to form element
      formData.append(direction, 1);

      fetch('?action=wizard&step=2&json=1', {
        method: "POST",
        headers: {
          // 'Content-type': 'application/json',
          "Accept": "application/json",   // expected data sent back
        },
        body: formData
      })
          .then(res => res.json())
          .then((response) => {

            if (ErrorHelper.handleError(this, response)) return;

            this.errors = response.data.errors;
            if ("redirect" in response) {
              location.href = response.redirect;
            }
          })
          .catch((error) => {
            this.errors = [];
            this.errors.push("Server fout opgetreden. Probeer het later nog eens, of neem contact op (" + error + ")");
          });
    },
    clickPrev() {
      if(this.checkForm()) {
        this.postData("prev");
      }
    },
    clickNext() {
      if(this.checkForm()) {
        this.postData("next");
      }
    },
    showSpeling() {
      if (this.stone.type === "spekband") {
        return false;
      }
      return true;
    },
    showMuurdikte() {
      return this.stone.type === "muurafdekker";
    },
    shorterChanged() {
      this.is_valid['shorter'] = false;
      if (this.quotation.shorter === null || this.quotation.shorter === "") {
        this.is_valid['shorter'] = false;
      } else {
        this.is_valid['shorter'] = true;
      }
      this.calculateTotals();
    },
    getWallthicknessMin() {
      return (this.stone_size.length * 10) - 130;
    },
    getWallthicknessMax() {
      return (this.stone_size.length * 10) - 30;
    },
    wallthicknessChanged() {

      if (this.quotation_extra.wall_thickness === null || this.quotation_extra.wall_thickness === "" || this.quotation_extra.wall_thickness==0) {
        this.is_valid['wall_thickness'] = false;
        this.hasWallthicknessError = false;
      }
      else if(this.quotation_extra.wall_thickness<this.getWallthicknessMin() || this.quotation_extra.wall_thickness>this.getWallthicknessMax()) {
        this.is_valid['wall_thickness'] = false;
        this.hasWallthicknessError = true;
      }
      else {
        this.is_valid['wall_thickness'] = true;
        this.hasWallthicknessError = false;
      }
      this.calculateTotals();
    },
    mitreText: function (element) {
      if (element.mitre == "both") {
        return 'Beide'
      } else if (element.mitre == "left") {
        return 'Links'
      } else if (element.mitre == "right") {
        return 'Rechts'
      }
      return 'Nee';
    },
    flagWindowText: function (element) {
      if (element.flagWindow == "single") {
        return 'Enkel'
      } else if (element.flagWindow == "double") {
        return 'Dubbel'
      }
      return 'Nee';
    },
    endstoneText: function (element) {
      if (element.leftEndstone == "1" && element.rightEndstone == "1") {
        return 'Beide'
      } else if (element.leftEndstone == "1") {
        return 'Links'
      } else if (element.rightEndstone == "1") {
        return 'Rechts'
      }
      return 'Nee';
    },
    remove: function (key) {
      this.elements.splice(key, 1);
      this.calculateTotals();
    },
    addElement: function (key) {
      event.preventDefault();
      this.element_new.referenceName = this.alphabetArray[this.elements.length];
      if (this.elements.length >= 26) {
        this.element_new.referenceName = this.alphabetArray[Math.floor(this.elements.length / 26) - 1] + this.alphabetArray[this.elements.length % 26];
      }
      if (key !== undefined) {
        this.elements.splice(key + 1, 0, JSON.parse(JSON.stringify(this.element_new)));
      } else {
        this.elements.push(JSON.parse(JSON.stringify(this.element_new)));
      }
      this.$nextTick(function () { //after dom set focus
        this.$refs['referenceName_' + (this.elements.length - 1)][0].focus();
      });
      this.calculateTotals();
    },
    calculateElementLength: function (element) {
      if (this.stone.type === "muurafdekker") {
        this.calculateElementLengthMuurafdekker(element);
      } else {
        this.calculateElementLengthDefault(element);
      }
    },
    calculateElementLengthDefault: function (element) {
      element.elementLength = 0;
      element.inputLength = parseInt(element.inputLength);
      if (element.mitre == "none" && element.inputLength >= 100) { //alleen bij geen mitre
        //bij double dan marge niet eraf halen, bij single 1x, bij standaard in zijn geheel
        if (element.flagWindow == "single") {
          element.elementLength = element.inputLength - this.quotation.shorter / 2;
        } else if (element.flagWindow == "double") {
          element.elementLength = element.inputLength;
        } else {
          element.elementLength = element.inputLength - this.quotation.shorter;
        }
      }
      if (element.elementLength < 0) {
        element.elementLength = 0;
      }
      if (element.elementLength != 0) {
        element.elementLengthText = element.elementLength;
      } else if (element.mitre == "none") {
        element.elementLengthText = 0;
      } else {
        element.elementLengthText = '';
      }
    },
    calculateElementLengthMuurafdekker: function (element) {
      element.elementLength = 0;
      element.elementLengthText = '';
      if (this.quotation_extra.wall_thickness === "" || this.quotation.shorter === "") {
        element.elementLengthText = '';
        return;
      }
      element.inputLength = parseInt(element.inputLength);

      if (element.mitre == "none" && element.inputLength >= 100) { //alleen bij geen mitre

        var muurdikte = parseInt(this.quotation_extra.wall_thickness);
        var oversteek = Math.round(this.stone_size.length * 10 - muurdikte) / 2;

        //bij double dan marge niet eraf halen, bij single 1x, bij standaard in zijn geheel

        if (element.leftEndstone == "1" && element.rightEndstone == "1") {
          element.elementLength = element.inputLength + oversteek + oversteek - this.quotation.shorter / 2;
        } else if (element.leftEndstone == "1" || element.rightEndstone == "1") {
          element.elementLength = element.inputLength + oversteek - this.quotation.shorter / 2;
        } else {
          element.elementLength = element.inputLength - this.quotation.shorter;
        }
        element.elementLength = Math.round(element.elementLength);
      }
      if (element.elementLength < 0) {
        element.elementLength = 0;
      }
      if (element.elementLength != 0) {
        element.elementLengthText = element.elementLength;
      } else if (element.mitre == "none") {
        element.elementLengthText = 0;
      } else {
        element.elementLengthText = '';
      }

    },
    calculateTotals() {
      let all_valid = this.elements.length > 0;
      let totals = 0;
      let valid_totals = true;
      this.amount_total = 0;
      let nr_of_valid_elements = 0;
      for (var i = 0; i < this.elements.length; i++) {
        this.calculateElementLength(this.elements[i]);
        var amount = parseInt(this.elements[i].amount);
        this.amount_total += amount;
        if (MathHelper.isNumeric(this.elements[i].elementLength) && this.elements[i].mitre == "none") {
          totals += amount * parseInt(this.elements[i].elementLength);
        } else {
          valid_totals = false;
        }
        if (all_valid) {
          if(this.elements[i].amount==0 && this.elements[i].inputLength==0) {
            //als hij 0 is noemen we m valid, oftewel negeren die handel
          }
          else if (!this.validateAmount(this.elements[i]) || !this.validateInputLength(this.elements[i], i, false)) {
            all_valid = false;
          }
          else {
            nr_of_valid_elements++;
          }
        }
      }
      if(nr_of_valid_elements===0) {
        all_valid = false;
      }

      this.all_valid = all_valid;
      if (valid_totals) {
        this.elementLength_total = totals;
      } else {
        this.elementLength_total = "";
      }
    },

    //popups
    openMitrebox: function (element) {
      this.mitreboxShow = true;
      this.showPopupBG = true;
      this.mitreboxElement = element;
      this.mitreboxDisableDouble = !(this.mitreboxElement.flagWindow == false || this.mitreboxElement.flagWindow == 'false');
    },
    selectMitre() {
      this.mitreboxShow = false;
      this.showPopupBG = false;
      this.calculateTotals();
    },
    openflagWindowbox: function (element) {
      this.flagWindowboxShow = true;
      this.showPopupBG = true;
      this.flagWindowboxElement = element;
    },
    selectflagWindow() {
      this.flagWindowboxShow = false;
      this.showPopupBG = false;
      this.calculateTotals();
    },
    openEndstoneBox: function (element) {
      this.flagEndstoneShow = true;
      this.showPopupBG = true;
      this.flagEndstoneElement = element;
      if (element.leftEndstone == "1" && element.rightEndstone == "1") {
        this.flagEndstoneValue = "both";
      } else if (element.leftEndstone == "1") {
        this.flagEndstoneValue = "left";
      } else if (element.rightEndstone == "1") {
        this.flagEndstoneValue = "right";
      } else {
        this.flagEndstoneValue = "none";
      }
    },
    selectEndstone() {
      //aanpassen eindsteen
      if (this.flagEndstoneValue === "both") {
        this.flagEndstoneElement.leftEndstone = "1";
        this.flagEndstoneElement.rightEndstone = "1";
      } else if (this.flagEndstoneValue === "left") {
        this.flagEndstoneElement.leftEndstone = "1";
        this.flagEndstoneElement.rightEndstone = "0";
      } else if (this.flagEndstoneValue === "right") {
        this.flagEndstoneElement.leftEndstone = "0";
        this.flagEndstoneElement.rightEndstone = "1";
      } else {
        this.flagEndstoneElement.leftEndstone = "0";
        this.flagEndstoneElement.rightEndstone = "0";
      }

      this.flagEndstoneShow = false;
      this.showPopupBG = false;
      this.calculateTotals();
    },

    //navigations
    navigateReferenceNameUp: function (key) {
      var re = /[^A-Z0-9.\-,]/gi;
      this.elements[key].referenceName = this.elements[key].referenceName.replace(re, '');
    },
    navigateReferenceName: function (key) {

      if (event.code == "ArrowUp") {
        if (this.elements[key - 1]) {
          this.$refs['referenceName_' + (key - 1)][0].focus();
        }
      } else if (event.code == "ArrowDown") {
        if (this.elements[key + 1]) {
          this.$refs['referenceName_' + (key + 1)][0].focus();
        }
      } else if (event.code == "Tab" && event.shiftKey) { //shift + tab
        if (this.elements[key - 1]) {
          this.$refs['mitrebox_' + (key - 1)][0].focus(); //geef de voorgaande focus, zodat browser zelf naar juiste gaat....trucje
        }
      }
    },
    navigateAmount: function (key) {

      if (event.code == "ArrowUp") {
        if (this.elements[key - 1]) {
          this.$refs['amount_' + (key - 1)][0].focus();
        }
      } else if (event.code == "ArrowDown") {
        if (this.elements[key + 1]) {
          this.$refs['amount_' + (key + 1)][0].focus();
        }
      }
    },
    navigateInputLength: function (key) {
      if (event.code == "ArrowUp") {
        if (this.elements[key - 1]) {
          this.$refs['inputLength_' + (key - 1)][0].focus();
        }
      } else if (event.code == "ArrowDown") {
        if (this.elements[key + 1]) {
          this.$refs['inputLength_' + (key + 1)][0].focus();
        }
      } else if (event.code == "Tab") {
        if (!event.shiftKey) {
          if (this.elements[key + 1]) {
            this.$refs['add_' + key][0].focus(); //geef de voorgaande focus, zodat browser zelf naar juiste gaat....trucje
            // event.preventDefault();
          } else {
            //nieuwe rij maken
            this.addElement();
          }
        } else { //shift+tab
          this.$refs['inputLength_' + key][0].focus(); //geef de voorgaande focus, zodat browser zelf naar juiste gaat....trucje
        }
      }
    },


    //validation
    validateReferenceNameInput: function (element, key) {
      var valid = true;
      if (element.referenceName == "") {
        valid = false;
        this.$swal("Foutmelding", "Kenmerk mag niet leeg zijn.", "error");
        element.hasReferenceNameError = true;
      }
      if (valid && MathHelper.isNumeric(element.referenceName.substring(0, 1))) {
        valid = false;
        this.$swal("Foutmelding", "Het eerste karakter van uw kenmerk mag geen getal zijn.", "error");
        element.hasReferenceNameError = true;
      }
      if (valid) { //check unique name
        for (var i = 0; i < this.elements.length; i++) {
          var el = this.elements[i];
          if (i != key && el.referenceName.toLowerCase() == element.referenceName.toLowerCase()) {
            this.$swal("Foutmelding", "Kenmerk moet uniek zijn.", "error");
            element.hasReferenceNameError = true;
            valid = false;
          }
        }
      }
      if (valid) {
        element.hasReferenceNameError = false;
      }
      return valid;
    },
    validateAmountInput: function (element) {
      this.validateAmount(element);
      this.calculateTotals();
    },
    validateAmount: function (element) {
      if (!MathHelper.isNumeric(element.amount) || element.amount != parseInt(element.amount, 10) || element.amount < 0 || element.amount > 9999) {
        element.amount = 1;
      }
      return true;
    },
    validateInputLengthInput: function (element, key) {
      this.validateInputLength(element, key);
      this.calculateTotals();
    },
    validateInputLength: function (element, key, show) {
      var valid = true;
      if (!MathHelper.isNumeric(element.inputLength) || element.inputLength < 0) {
        element.inputLength = 0;
      } else if (element.inputLength != parseInt(element.inputLength, 10)) {
        element.inputLength = Math.floor(element.inputLength);
      }
      if (element.inputLength < 100) {
        valid = false;
        if (show) {
          this.$swal("Foutmelding", "Kozijnmaat in mm moet minimaal 100 mm zijn.", "error");
          element.hasInputLengthError = true;
        }
      }
      if (valid) {
        element.hasInputLengthError = false;
      }
      return valid;

    },
    checkForm() {

      if (this.showSpeling() && (this.quotation.shorter === null || this.quotation.shorter === "")) {
        this.$swal("Speling", "Selecteer eerst de gewenste speling.", "error");
        this.hasShorterError = true;
        return false;
      }
      this.hasShorterError = false;
      this.hasWallthicknessError = false;

      if (this.showMuurdikte() && !this.is_valid['wall_thickness']) {
        this.$swal("Muurdikte", "Voer een geldige muurdikte in.<Br/>Minimale waarde: "+this.getWallthicknessMin()+" mm<Br/>Maximale waarde: "+this.getWallthicknessMax()+" mm", "error");
        this.hasWallthicknessError = true;
        return false;
      }

      var valid = true;
      var totalamount = 0;
      for (var i = 0; i < this.elements.length; i++) {
        var el = this.elements[i];

        if (el.amount == 0 && el.inputLength == 0) continue;

        totalamount += el.amount;
        if (!this.validateInputLength(el, i, true)) {
          valid = false;
        }
        if (!this.validateReferenceNameInput(el, i, true)) {
          valid = false;
        }
      }
      if (totalamount == 0) {
        this.$swal("Elementen", "U dient minimaal 1 element in te voeren.", "error");
        valid = false;
      }
      return this.all_valid && valid;
    },
    setForm(el) {
      this.form = el;
    },
    mitreBoxClass() {
      let mbclass = 'questionbox ' + this.stone.type + ' brand' + this.quotation.brandId;
      if (this.stone.material === "natuursteen") {
        mbclass += " "+(this.isEzelsrug() ? 'ezelsrug' : 'no-ezelsrug');
      }
      return mbclass;
    },
    flagEndstoneBoxClass() {
      let mbclass = 'questionbox ' + this.stone.type + ' ';
      if (this.stone.material === "natuursteen") {
        mbclass += this.isEzelsrug() ? 'ezelsrug' : 'no-ezelsrug';
      }
      return mbclass;
    },
    isEzelsrug() {
      return this.stone.category_id == 18; //ezelsrug
    }

  }

}


</script>

<style>

#step2-popup-bg {
  overflow: auto;
  overflow-y: scroll;
  position: fixed;
  bottom: 0;
  right: 0;
  width: auto;
  height: auto;
  top: 0;
  left: 0;
  z-index: 900;
  background: #0e0e0ebd;
}

</style>
