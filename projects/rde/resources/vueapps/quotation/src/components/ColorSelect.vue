<template>
  <div :ref="setColorEl" >
    <a href="" @click.prevent="showSelection()" :id="'color'" class="color-select-btn" :class="{'color-open':colorOpen}">
      <div class="imagesmallfile" v-if="quotation.colorId!=''" :class="{ noimage: !getImage(color) }" :style="isColorhex(color)?'background-color: '+color.hexcolor:''">
        <img :src="getImage(color)"  v-if="getImage(color)"/>
      </div>
      <div class="color-select-name" v-if="quotation.colorId==''">
        Kies een kleur...
      </div>
      <div class="color-select-name" v-else>
        {{ color.name }} {{ color.short!="" && color.short!=null?" - "+color.short:"" }}
      </div>
      <span class="fa fa-chevron-down"></span>
    </a>
<!--    <div>{{quotation}}</div>-->
    <div class="color-select" v-if="colorOpen">
      <div v-for="(group, name) in possible_colors" :key="name">
<!--        //@TODO: niet laten zien bij natuursteen en beton-->
        <div v-if="!natuursteenBetonIds.includes(quotation.stoneCategoryId)" class="groupname">{{name}}</div>
        <ul>
          <li v-for="color in group" :class="{'color-select-active': quotation.colorId==color.colorId}" :key="color">
            <a href="#"  @click="clickSelection(color)">
              <div class="imagesmallfile" :style="isColorhex(color)?'background-color: '+color.hexcolor:''">
                <img :src="getImage(color)"  v-if="getImage(color)" />
              </div>
              {{ color.name }} {{ color.short!="" && color.short!=null?" - "+color.short:"" }}
            </a>
          </li>
        </ul>
      </div>
    </div>
    <input type="hidden" name="colorId"  :value="color.colorId"/>
  </div>
</template>
<script type="text/javascript">

  export default {
    props: ['quotation', 'possible_colors', 'color', 'is_active_color_id'], //let op props zijn altijd lowercase!
    data() {
      return {
        colorOpen: false,
        colorEl: null,
        natuursteenBetonIds: ['7','8','9','10','11','12','13','14','15','16','17','18','19','20','21','22']
      }
    },
    mounted() {
      document.addEventListener('mouseup', this.clickOutside);
    },
    methods :  {
      showSelection() { //show bovenaanzicht dropdown
        if(!this.is_active_color_id) return false;
        this.colorOpen = true;
      },
      hideSelection() { //show bovenaanzicht dropdown
        this.colorOpen = false;
      },
      clickSelection(color) {
        event.preventDefault(); //prevent scroll to top
        this.$emit("changeColorId", color.colorId);
        this.hideSelection();
      },
      clickOutside(e) {
        var el = this.colorEl;
        if (!(el === e.target || el.contains(e.target))) {
          this.hideSelection();
        }
      },
      setColorEl(el) {
        if (el) {
          this.colorEl = el;
        }
      },
      getImage(color) {
        if(color.filename!==null && color.filename!=="") {
          return "/uploads/rde/stonecolors/"+color.filename;
        }
        if(color.hexcolor!==null && color.hexcolor!=="") {
          return false;
        }
        if(color.image!==null && color.image!=="") {
          return color.image;
        }
        return false;
      },
      isColorhex(color) {
        if(color.filename!==null && color.filename!=="") { //bestand is leidend
          return false;
        }
        return color.hexcolor!==null && color.hexcolor!==""
      }
    }
  }
</script>

<style>

  .color-select-btn {
    border: 1px solid #d2d2d2;
    padding: 10px 15px;
    width: 100%;
    border-radius: 3px;
    line-height: 25px;
    color: #333;
    display: flex;
    /*align-content: center;*/
    background-color: white;
  }

  .color-select-btn .fa {
    margin-top: 5px;
  }

  .color-select .groupname {
    padding: 10px 15px;
    font-weight: bold;
    background: #ce000c;
    color: white;
  }

  .color-select-name {
    flex-grow: 1;
  }

  .color-select-btn span {
    margin-left: 10px;
  }

  .color-select-btn .imagesmallfile {
    width: 35px;
    display: inline-block;
    margin-right: 15px;
  }

  .color-select-btn .imagesmallfile.noimage {
    background: #f7f7f7;
  }

  .color-select-btn .imagesmallfile.hexcolor {
    background: #f7f7f7;
  }

  .color-select {
    position: absolute;
    background-color: white;
    box-shadow: 0 1px 5px #ddd;
    border: solid 1px #ccc;
    z-index: 10;
    min-width: 432px;
    max-width: 100%;
  }

  .color-select-btn .fa.fa-chevron-down {
    color: #888;
    transition: transform .15s cubic-bezier(1, -.115, .975, .855);
    transition-timing-function: cubic-bezier(1, -.115, .975, .855);
  }
  .color-select-btn.color-open .fa.fa-chevron-down {
    transform: rotate(180deg);
  }

  .color-select ul {
    list-style: none;
    margin: 0;
    padding: 0;
  }

  .color-select a {
    display: flex;
    align-items: center;
    padding: 5px 15px;
    color: #333;
  }

  .color-select a:hover {
    background-color: #EBEBEB;
    color: #ce000c;
  }

  .color-select-active, .color-select-active a {
    background-color: #EBEBEB;
    color: #ce000c;
  }

  .color-select .imagesmallfile {
    width: 50px;
    min-height: 30px;
    margin-right: 15px;
  }

  .disabled .color-select-btn {
    background-color: #dcdedf;
    color: #666;
    font-size: 15px;
  }
</style>
