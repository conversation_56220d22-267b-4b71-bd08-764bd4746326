@media screen and (max-width: 425px) {
  .text-center a {
    font-size: 14px;
    width: 100%;
    margin: 10px 0;
  }

  .product-table td {
    display: inline-block;
    width: 100%;
    float: left;
  }

  td.mob-hide {
    display: none;
  }
}

@media (max-width: 895px) {
  .form-row .col-4 {
    width: 40%;
  }

  .form-row .col-4-3 {
    width: 60%;
  }
}

@media (max-width: 670px) {

  .form-row {
    margin: 0;
  }

  .form-row .col-1,
  .form-row .col-2,
  .form-row .col-3,
  .form-row .col-4,
  .form-row .col-4-3 {
    float: none;
    width: 100%;
  }

  .footer .widget_content ul li {
    padding: 5px 0;
  }

  .footer #tags li {
    padding: 5px 0;
  }

}


@media (max-width: 1100px) {

  .hidden-s {
    display: none;
  }

  .slider .slide .slide-title {

    line-height: 50px;
  }

  .visible-s {
    display: block;
  }

  .wrapper {
    overflow-x: hidden;
  }

  .wrap,
  .wrap-lg {
    padding-left: 20px;
    padding-right: 20px;
  }

  .c-hamburger {
    display: block;
    position: relative;
    overflow: hidden;
    padding: 0;
    width: 24px !important;
    height: 36px;
    font-size: 0;
    text-indent: -9999px;
    appearance: none;
    box-shadow: none;
    border-radius: unset;
    cursor: pointer;
    background: none;
    border: 0;
    margin: 17px 0 0 20px;
    /* transition: background 0.3s; */
  }

  .c-hamburger span {
    display: block;
    position: absolute;
    top: 16px;
    left: 0;
    right: 0;
    height: 2px;
    background: #fff;
  }

  .c-hamburger span::before,
  .c-hamburger span::after {
    position: absolute;
    display: block;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: #fff;
    content: "";
  }

  .btn-stroke:hover.c-hamburger span,
  .btn-stroke:hover.c-hamburger span::before,
  .btn-stroke:hover.c-hamburger span::after {
    background-color: #A72072
  }

  .btn-stroke:hover.c-hamburger--htx.is-active span {
    background: none;
  }

  .c-hamburger span::before {
    top: -7px;
  }

  .c-hamburger span::after {
    bottom: -7px;
  }


  .c-hamburger--htx span {
    transition: background 0s 0.3s;
  }

  .c-hamburger--htx span::before,
  .c-hamburger--htx span::after {
    transition-duration: 0.3s, 0.3s;
    transition-delay: 0.3s, 0s;
  }

  .c-hamburger--htx span::before {
    transition-property: top, transform;
  }

  .c-hamburger--htx span::after {
    transition-property: bottom, transform;
  }

  /* active state, i.e. menu open */
  .c-hamburger--htx.is-active {

  }

  .c-hamburger--htx.is-active span {
    background: none;
  }

  .c-hamburger--htx.is-active span::before {
    top: 0;
    transform: rotate(45deg);
  }

  .c-hamburger--htx.is-active span::after {
    bottom: 0;
    transform: rotate(-45deg);
  }

  .c-hamburger--htx.is-active span::before,
  .c-hamburger--htx.is-active span::after {
    transition-delay: 0s, 0.3s;
  }


  #mobilemenu {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  #mobilemenu li {
    line-height: 40px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.15);
  }

  #mobilemenu li a {
    color: #fff;
    font-size: 18px;
    display: block;
  }

  #mobilemenu li.haschild > a:after {
    content: '\f105';
    font-family: FontAwesome;
    font-weight: normal;
    font-style: normal;
    font-size: 17px;
    margin: 0 10px 0 0;
    float: right;
  }

  #mobilemenu li a:hover {
    /*color:#999;*/
  }

  #mobilemenu li.current > a {
    color: #ce000c;
  }

  #mobilemenu .child {
    display: none;
    list-style: none;
    margin: 0;
    padding: 0 0 0 20px
  }

  #mobilemenu .haschild.active .child {
    display: block;

  }

  #mobilemenu .haschild {
    left: -81%;
    overflow-x: hidden;
    overflow-y: auto;
    visibility: visible;
    -webkit-overflow-scrolling: touch;
    -moz-transition: left 0.3s ease;
    -webkit-transition: left 0.3s ease;
    transition: left 0.3s ease;
    -webkit-transition-timing-function: cubic-bezier(0.420, 0.000, 0.580, 1.000);
    -moz-transition-timing-function: cubic-bezier(0.420, 0.000, 0.580, 1.000);
    -o-transition-timing-function: cubic-bezier(0.420, 0.000, 0.580, 1.000);
    transition-timing-function: cubic-bezier(0.420, 0.000, 0.580, 1.000); /* ease-in-out */
  }

  #mobilemenu .haschild.active {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    padding: 0;
    margin: 0;
    background-color: #111;
    border-bottom: 0;
    padding-top: 11px;
    z-index: 2;
  }

  #mobilemenu .haschild.active > a {
    margin-left: 20px;
    color: #96c322;
    border-bottom: 1px solid rgba(255, 255, 255, 0.15);
    /* background-color: white; */
    font-weight: bold;
  }

  #mobilemenu .haschild.active > a:before {
    display: none;
  }

  #mobilemenu .first-child .mobile-exit,
  #mobilemenu .haschild.active .mobile-close {
    position: absolute;
    z-index: 2;
    float: right;
    font-size: 30px;
    background: #ce000c;
    top: 0;
    right: -1px;
    height: 51px;
    width: 50px;
    line-height: 50px;
    text-align: center;
    color: #fff;
  }

  #mobilemenu .mobile-exit .c-hamburger {
    margin-left: 14px;
    margin-top: 10px;
  }

  #mobilemenu .mobile-exit .c-hamburger span::before,
  #mobilemenu .mobile-exit .c-hamburger span::after {
    background-color: #ffffff;
  }

  #mobilemenu .haschild.active .mobile-close i {
    pointer-events: None;
  }

  #mobilemenu .child li a {
    text-transform: none; /* color:#d2d2d2 */
  }

  .container-wrapper {
    -webkit-transition: -webkit-transform 0.3s ease;
    -moz-transition: -moz-transform 0.3s ease;
    -o-transition: -o-transform 0.3s ease;
    transition: transform 0.3s ease;
    -webkit-transition-timing-function: cubic-bezier(0.420, 0.000, 0.580, 1.000);
    -moz-transition-timing-function: cubic-bezier(0.420, 0.000, 0.580, 1.000);
    -o-transition-timing-function: cubic-bezier(0.420, 0.000, 0.580, 1.000);
    transition-timing-function: cubic-bezier(0.420, 0.000, 0.580, 1.000); /* ease-in-out */

  }

  .mobile-menu-container {
    background-color: #111;
    padding: 10px 0 20px 20px;
    position: fixed;
    top: 0;
    left: -71%;
    bottom: 0;
    z-index: 101;
    display: block !important;
    width: 71% !important;
    overflow-x: hidden;
    overflow-y: auto;
    visibility: visible;
    -webkit-overflow-scrolling: touch;
    -moz-transition: left 0.3s ease;
    -webkit-transition: left 0.3s ease;
    transition: left 0.3s ease;
    -webkit-transition-timing-function: cubic-bezier(0.420, 0.000, 0.580, 1.000);
    -moz-transition-timing-function: cubic-bezier(0.420, 0.000, 0.580, 1.000);
    -o-transition-timing-function: cubic-bezier(0.420, 0.000, 0.580, 1.000);
    transition-timing-function: cubic-bezier(0.420, 0.000, 0.580, 1.000); /* ease-in-out */
  }

  .off-screen {
    left: 0;
  }

  .off-screen + * {
    position: relative;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 5;
    width: 100%;
    overflow: hidden;
    -webkit-transform: translate3d(70%, 0, 0);
    transform: translate3d(70%, 0, 0);
    -webkit-transition: -webkit-transform .3s ease;
    -moz-transition: -moz-transform .3s ease;
    -o-transition: -o-transform .3s ease;
    transition: transform .3s ease;
    -webkit-backface-visibility: hidden;
    -moz-backface-visibility: hidden;
    backface-visibility: hidden;
    -webkit-transition-delay: 0.1s; /* Safari */
    transition-delay: 0.1s;
  }

  /* USPS */
  .usps {
    position: relative;
    padding-left: 55px;
  }

  .usps .usp-icon {
    position: absolute;
    left: 0;
  }

}

@media only screen
and (min-width: 670px)
and (max-width: 1100px) {

  /* GENERAL */
  .visible-s {
    display: block !important;
  }

  .logo {
    width: 30%;
    padding-right: 15px;
  }

  .contact {
    width: 70%;
  }

  .hidden-s {
    display: none;
  }

  .gallery .gallery-item {
    height: 350px;
  }

  .mobile-menu-container {
    left: -36%;
    width: 35% !important;
  }

  .off-screen {
    left: 0;
  }

  .off-screen + * {
    -webkit-transform: translate3d(35%, 0, 0);
    transform: translate3d(35%, 0, 0);
  }

  /* SLIDER */
  .slider {
    min-height: 150px;
  }

  .home .slider {
    min-height: 350px;
  }

  .slider .slide .slide-title {
    width: 100%
  }

  /* FOOTER */
  .footer .widget {
    width: 33.33%;
    padding: 0 0 30px 0 !important;
  }

  .footer .widget.first-child {
    width: 100% !Important
  }

  /* COLUMNS */
  .col12-s {
    width: 100%
  }

  .col11-s {
    width: 91.66666667%
  }

  .col10-s {
    width: 83.33333333%
  }

  .col9-s {
    width: 75%
  }

  .col8-s {
    width: 66.66666667%
  }

  .col7-s {
    width: 58.33333333%
  }

  .col6-s {
    width: 50%
  }

  .col5-s {
    width: 41.66666667%
  }

  .col4-s {
    width: 33.33333333%
  }

  .col3-s {
    width: 25%
  }

  .col2-s {
    width: 16.66666667%
  }

  .col1-s {
    width: 8.33333333%
  }

}

@media only screen
and (min-device-width: 768px)
and (max-device-width: 1024px)
and (orientation: portrait) {

}

@media (max-width: 670px) {

  .read-more span {

    width: 25px;
  }

  .read-more:after {
    width: 25px;
  }

  .hidden-xs {
    display: none;
  }

  .visible-xs {
    display: block;
  }

  /* HEADER */
  .logo {
    width: 60%;
  }

  .header .contact .action {
    padding-left: 0;
    margin-left: 15px;

  }

  .badge-wrapper {
    display: none;
  }

  .header .contact .icon-sm {
    position: relative;
  }

  .no-padding-responsive {
    padding-left: 20px;
  }

  .btn-login {
    font-size: 15px;
  }

  /* OFFER */
  .offer .btn {
    width: 100%;
    margin-top: 20px;
  }

  #img_cont {
    display: block !important;
    flex-direction: row !important;
    width: 100% !important;
    margin-left: 0 !important;
  }

  #img_cont img {
    width: 100%;
    height: 100%;
    vertical-align: initial;
  }

  /* SLIDER */
  .slider {
    min-height: 150px;
  }

  .slider .slide .slide-title {
    font-size: 28px;
    line-height: 30px;
    width: 100%;
  }

  .form-row .col-4 {
    padding-top: 5px;
    line-height: 1;
  }

  /* FOOTER */
  .footer .widget {
    width: 100% !Important;
    float: none;
    padding: 0 0 30px 0 !Important
  }

  .footer-bottom {
    text-align: center;
  }

  .footer-bottom a {
    padding-top: 10px;
    display: block;
  }

  /* OFFER */
  .offer {
    padding-left: 0;
  }

  .offer:before {
    position: relative;
    margin-bottom: 20px;
    display: block;
  }

  /* COLUMNS */
  .col12-xs {
    width: 100%
  }

  .col11-xs {
    width: 91.66666667%
  }

  .col10-xs {
    width: 83.33333333%
  }

  .col9-xs {
    width: 75%
  }

  .col8-xs {
    width: 66.66666667%
  }

  .col7-xs {
    width: 58.33333333%
  }

  .col6-xs {
    width: 50%
  }

  .col5-xs {
    width: 41.66666667%
  }

  .col4-xs {
    width: 33.33333333%
  }

  .col3-xs {
    width: 25%
  }

  .col2-xs {
    width: 16.66666667%
  }

  .col1-xs {
    width: 8.33333333%
  }


}

@media (max-width: 1600px) {
  .slider_usp li {
    float: left;
    padding-left: 10px;
  }
}

@media (max-width: 1300px) {
  .slider .slide .slide-title {
    font-size: 30px;
    line-height: 40px
  }
}

@media (min-width: 992px) {
  #city {
    width: calc(50% - 20px);
  }
}

@media (max-width: 992px) {
  .mobile-menu.hidden {
    display: block;
  }
}

@media (max-width: 670px) {
  label.col-form-label {
    display: block;
    width: 100%;
    font-weight: bold;
    padding-top: 10px !important;
    padding-bottom: 5px;
  }

  .wizard label.col-form-label {
    padding-top: 0 !important;
    padding-bottom: 5px;
  }

  label.mobileshow {
    display: block;
  }

  .slider_usp {
    display: none;
  }

  .home .slider .slide .slide-title {
    font-size: 20px;
    line-height: 25px;
    width: 100%;
    padding-bottom: 15px;
  }

  .slide-description .btn {
    margin-right: 5px;
  }

  .slider .slide .slide-content {
    padding: 5% 15px 0 0;
  }

  .sidebar {
    visibility: hidden;
    height: 0;
    opacity: 0;
    transition: visibility 0s, opacity 0.3s ease;
    transition-timing-function: cubic-bezier(0.420, 0.000, 0.580, 1.000);
  }

  .submenutoggle {
    display: block;
  }

  #startnewquotation {
    float: none;
    margin-top: 10px;
  }

  .steps {
    display: block;
  }

  .steps div {
    display: inline-block;
    margin-right: -4px;
  }

  .product-addtocart-div {
    display: block;
  }

  .product-col {
    min-height: auto;
  }

  .product-col .prcaption h4 {
    height: auto;
  }

  .product-col > div {
    text-align: center;
  }

  .rdecontact {
    display: block;
    padding-bottom: 30px;
  }
}


@media (max-width: 1100px) {
  #mobilemenu li:not(.active) {
    border-bottom-width: 0;
  }

  #mobilemenu li.active li, .off-screen #mobilemenu li {
    border-bottom-width: 1px;
  }

  #basket .btn {
    margin-right: 5px;
    margin-bottom: 5px;
    float: left !important;
  }

  #mobilemenu {
    display: inline-block;
    margin: 17px 0 0 0
  }

  .off-screen #mobilemenu {
    display: block;
  }

  #mobilemenu > li {
    display: inline-block;
    vertical-align: top;
  }

  .off-screen #mobilemenu li {
    display: block;
  }

  #mobilemenu li.haschild > a:after {
    content: '';
    margin: 0;
  }

  .off-screen #mobilemenu li.haschild > a:after {
    content: '\f105';
    margin: 0 10px 0 0;
  }
}

@media (max-width: 895px) {
  .header .contact {
    display: none;
  }
}
