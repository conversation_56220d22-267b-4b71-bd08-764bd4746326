.quotation_icon {
  width: 16px;
  height: 16px;
  border-radius: 8px;
  box-shadow: 1px 1px 4px #dddbdb;
  border: 1px solid #dddbdb;
  color: white;
  font-size: 12px !important;
  padding: 1px;
  position: relative;
}

.quotation_icon.fa.fa-bolt {
  padding: 1px 5px;
  color: black;
}

.quotation_icon_10, .quotation_icon_80 {
  background-color: white;
}

.quotation_icon_20, .quotation_icon_21 {
  background-color: #65CF0F;
}

.quotation_icon_30 {
  background-color: #DEC300;
}

.quotation_icon_31, .quotation_icon_35, .quotation_icon_38 {
  background-color: #de6d00;
}

.quotation_icon_40 {
  background-color: #CF0F1C;
}

.quotation_icon_50 {
  background-color: #9D0091;
}

.quotation_icon_53 {
  background-color: #69DBCF;
}

.quotation_icon_55 {
  background-color: #4E00DE;
}

.quotation_icon_60, .quotation_icon_61, .quotation_icon_62, .quotation_icon_63 {
  background-color: black;
}

.quotation_icon_70 {
  background-color: #D6D9BA;
}

.quotation_icon_80::after {
  content: "✖";
  color: red;
  font-weight: bold;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}