html, body, div, span, applet, object, iframe,
h1, h2, h3, h4, h5, h6, p, blockquote, pre,
a, abbr, acronym, address, big, cite, code,
del, dfn, em, font, img, ins, kbd, q, s, samp,
small, strike, strong, sub, sup, tt, var,
b, u, i, center,
dl, dt, dd,
fieldset, form, label, legend,
table, caption, tbody, tfoot, thead, tr, th, td {
  background: transparent;
  border: 0;
  margin: 0;
  padding: 0;
  vertical-align: baseline;
}

html {
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  font-family: sans-serif;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
}

*,
*:before,
*:after {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

*:focus {
  outline: 0 !important;
}

select,
input[type="text"],
input[type="email"],
input[type="password"],
input[type="submit"],
textarea {
  -webkit-appearance: none;
  -webkit-border-radius: 0;
}

body {
  line-height: 1;
}

p {
  margin: 0 0 18px 0;
}

h1, h2, h3, h4, h5, h6 {
  clear: both;
  font-weight: normal;
}

blockquote {
  quotes: none;
}

blockquote:before, blockquote:after {
  content: none;
}

del {
  text-decoration: line-through;
}

/* tables still need 'cellspacing="0"' in the markup */
table {
  border-collapse: collapse;
  border-spacing: 0;
}

img {
  max-width: 100%;
  height: auto;
  vertical-align: top;
}

a img {
  border: none;
}

a {
  text-decoration: none;
  color: #ce000c
}

a.white {
  color: #fff;
}

/*template sheet*/
body {
  font-family: 'Roboto', sans-serif;
  -webkit-font-smoothing: antialiased;
  font-size: 16px;
  line-height: 22px;
  font-weight: 300;
  margin: 0;
  color: #333;
  background: #fff;
  height: 100%;
  overflow-y: scroll
}

.wrap-sm {
  max-width: 800px;
  padding: 0 30px;
  margin: 0 auto;
}

.wrap {
  max-width: 1100px;
  padding: 0 30px;
  margin: 0 auto;
}

.wrap-lg {
  max-width: 1200px;
  padding: 0 30px;
  margin: 0 auto;
}

.container {
  position: relative;
}

/* HEADER */
.header-top {
  padding-top: 20px;
  padding-bottom: 20px;
}

.header-bottom {
  height: 70px;
}

.header .contact {
  padding-top: 15px;
}

.header .contact .text {
  font-size: 18px;
  color: #777777;
  font-weight: 500;
}

.header .action {
  display: inline-block;
}

/* MENU */
.nav > ul {
  margin: 0;
  padding: 0;
  display: flex;
  justify-content: space-between;
}

.nav > ul li {
  list-style: none;
  height: 70px;
  display: block;
}

.nav > ul li a {
  line-height: 70px;
  padding: 0 5px;
  color: #fff;
  font-weight: 400;
  font-size: 18px;
  display: block;
  transition: all 0.3s ease;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
}

.nav > ul li a:hover {
  color: rgba(255, 255, 255, 0.8)
}

.nav > ul li.first-child a {
  padding-left: 0
}

.nav > ul li.first-child a:before {
  font-family: 'simple-line-icons';
  content: "\e069";
  margin-left: 10px
}

.nav > ul li.first-child a span {
  display: none;
}

.nav > ul li.haschild {
  position: relative;
}

.nav > ul li.haschild a {
  -webkit-transition: none;
  -moz-transition: none;
  -o-transition: none;
  transition: none;
}

.nav > ul li.haschild > a:after {
  font-family: 'FontAwesome';
  content: '\f107';
  margin-left: 5px;
}

.nav > ul li.haschild:hover > a {
  color: #ebebeb;
  position: relative;
  z-index: 999;
}

.nav > ul li .child {
  position: absolute;
  top: 100%;
  left: -15px;
  width: 280px;
  padding: 20px 0;
  margin: 0;
  z-index: 100;
  display: none;
  background: #fff;
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.3);
}

.nav > ul li .child li {
  float: none;
  height: 36px;
}

.nav > ul li .child li a {
  font-size: 16px;
  line-height: 36px;
  color: #151515;
  padding: 0 20px;
}

.nav > ul li .child li a:hover {
  color: #999
}

.nav > ul li .child li.current a {
  color: #ce000c
}

/* BANNER */
.banner {
  position: relative;
}

.banner .badge-container {
  position: absolute;
  z-index: 70;
  right: 0;
  bottom: 10%;
}

.badge-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.badge-wrapper .wrap-lg {
  position: relative;
  height: 100%;
}

.banner .badge {
  width: 240px;
  height: 240px;
  border-radius: 100%;
  -moz-border-radius: 100%;
  padding: 94px 0 0 50px;
}

.banner .badge .text {
  font-size: 24px;
  font-weight: bold;
  padding-top: 10px;
}

.banner .badge-sm {
  position: absolute;
  right: -5px;
  top: -5px;
  width: 85px;
  height: 85px;
  line-height: 85px;
  color: #ce000c;
  font-size: 24px;
  text-align: center;
  border-radius: 100%;
  -moz-border-radius: 100%;
}

/* FOOTER */
.footer .widget {
  width: 20%;
  float: left;
}

.footer .widget.first-child {
  width: 40%;
  padding-right: 50px
}

.footer .widget_header h3 {
  font-size: 20px;
  font-weight: bold;
  padding-bottom: 15px;
}

.footer .widget_content ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer .widget_content ul li {
}

.footer-top a {
  color: #444;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  transition: all 0.3s ease;
}

.footer-top a:hover {
  color: #ce000c
}

.footer-bottom ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-bottom ul li {
  display: inline-block;
  padding-left: 20px;
}

/* SCROLLTOTOP */
.scrollToTop {
  width: 50px;
  height: 50px;
  line-height: 50px;
  text-align: center;
  background: rgba(206, 0, 12, 0.8);
  color: #fff;
  text-decoration: none;
  position: fixed;
  z-index: 75;
  bottom: 20px;
  right: 20px;
  display: none;
  cursor: pointer;
  border-radius: 2px;
  -moz-border-radius: 2px;
  -webkit-border-radius: 2px;
  -webkit-transition: all 0.2s ease-out 0s;
  -moz-transition: all 0.2s ease-out 0s;
  -o-transition: all 0.2s ease-out 0s;
  transition: all 0.2s ease-out 0s;
}

.scrollToTop:hover {
  background: rgba(206, 0, 12, 1);
}

/* BREADCRUMB */
.breadcrumb-bar,
.breadcrumb-bar a {
  color: #222;
  font-size: 14px;
}

.breadcrumb-bar ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.breadcrumb-bar ul li {
  display: inline-block;
}

.breadcrumb-bar ul li:after {
  content: '\/';
  padding: 0 3px;
}

.breadcrumb-bar ul li:last-child:after {
  display: none;
}

/* POSTS */
.post {
  position: relative;
  border-bottom: 1px solid #ddd;
  margin: 0 0 20px 0;
  padding: 0 0 50px 0;
}

.post .entry-date {
  color: #999;
  display: inline-block;
  padding: 5px 0 15px 0
}

/* TABLE */
.table {
  width: 100%;
  margin: 0 0 20px 0
}

.table th {
  padding: 5px;
  text-align: left;
}

.table td {
  padding: 5px;
  border-top: 1px solid #ddd
}

/* SPACEERS */
.bg-spacer-t {
  position: absolute;
  bottom: 50%;
  top: 0;
  left: 0;
  right: 0;
  z-index: -1;
}

.bg-spacer-b {
  position: absolute;
  top: 50%;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: -1;
}

/* USPS */
.usp-icon i {
  font-size: 40px;
  color: #666464
}

/* HEADING */
.heading .title {
  font-size: 24px;
  font-weight: 900;
}

.heading-sm .title {
  font-size: 20px;
  font-weight: 900;
}

/* OFFER */
.offer {
  position: relative;
  padding-left: 60px;
}

.offer:before {
  position: absolute;
  left: 0;
  font-family: 'simple-line-icons';
  content: "\e049";
  font-size: 46px;
  color: #fff;
  line-height: 100%;
}

/* BORDERS */
.b-t {
  border-top: 1px solid #b8b8b8
}

.btn {
  font-family: Arial, Helvetica, sans-serif;
  min-height: 42px;
  line-height: 42px;
  padding: 0 10px;
  background-color: #ce000c;
  font-size: 18px;
  font-weight: 500;
  color: #fff;
  display: inline-block;
  cursor: pointer;
  border: 0;
  border-radius: 2px;
  -moz-border-radius: 2px;
  -webkit-border-radius: 2px;
  text-transform: uppercase;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}

.btn i {
  margin-right: 10px;
}

.btn:hover {
  box-shadow: 2px 2px 4px #dddbdb;
}

#next_top:hover i, #prev_top:hover i {
  transition: color 0.3s ease;
  color: #6b6b6b;
}

.btn-lt {
  background: none;
  border: 1px solid #fff;
}

.btn-lt:hover {
  background: #fff;
  color: #ce000c
}

.btn-lg {
  width: 100%;
}

.l-h-xl {
  line-height: 40px
}

/* FORMS */
.input,
.select select {
  height: 40px;
  line-height: 40px;
  background: #fff;
  border-radius: 2px;
  -moz-border-radius: 2px;
  -webkit-border-radius: 2px;
  width: 100%;
  border: 1px solid #d3d3d3;
  padding: 0 5px;
  color: #5d5d5d;
  font-family: 'Roboto', sans-serif;
  font-size: 15px;
}

textarea.input {
  resize: vertical;
  min-height: 100px;
}

.select {
  position: relative;
}

.select:after {
  content: '\f0d7';
  font-family: FontAwesome;
  font-weight: normal;
  font-style: normal;
  position: absolute;
  right: 10px;
  top: 0;
  line-height: 40px;
  pointer-events: none;
}

.select select {
  padding: 0 10px;
}

input[type="checkbox"],
input[type="radio"] {
  background: #fff;
  border: 1px solid #e3dfd8;
  color: #555;
  clear: none;
  cursor: pointer;
  display: inline-block;
  line-height: 0;
  height: 16px;
  margin: -2px 10px 0 0;
  outline: 0;
  padding: 0 !important;
  text-align: center;
  vertical-align: middle;
  width: 16px;
  min-width: 16px;
  -webkit-appearance: none;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-transition: .05s border-color ease-in-out;
  transition: .05s border-color ease-in-out;
}

input[type="radio"] {
  border-radius: 50px;
}

input[type="checkbox"]:checked:before {
  content: '\f00c';
  font-family: FontAwesome;
  font-style: normal;
  font-weight: normal;
  color: #ce000c;
  font-size: 20px;
  display: inline-block;
  vertical-align: middle;
  width: 16px;
  speak: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  margin: 5px 0 0 0;
}

input[type="radio"]:checked:before {
  content: '\f111';
  font-family: FontAwesome;
  text-indent: -9999px;
  -webkit-border-radius: 50px;
  border-radius: 50px;
  width: 6px;
  height: 6px;
  margin: 3px;
  line-height: 16px;
  color: #ce000c;
  font-size: 11px;
  text-align: center;
}

/* READ MORE */
.read-more {
  background: none;
  border: 0;
  text-transform: uppercase;
  height: 40px;
  line-height: 40px;
  position: absolute;
  z-index: 14;
  padding: 0 0 0 10px;
  right: 0;
  bottom: 0;
  margin: 0;
  cursor: pointer;
  -webkit-transition: color 0.2s ease-out 0s;
  -moz-transition: color 0.2s ease-out 0s;
  -o-transition: color 0.2s ease-out 0s;
  transition: color 0.2s ease-out 0s;
}

.read-more-lt {
  color: #fff
}

.read-more.pull-left {
  left: 0;
  right: auto;
  padding: 0 10px 0 0
}

.read-more:before {
  content: "";
  clear: both;
}

.read-more:hover {
  color: #fff;
}

.read-more:after {
  content: "";
  background-color: #ce000c;
  width: 40px;
  height: 40px;
  display: block;
  position: absolute;
  top: 0;
  right: 0;
  z-index: -1;
  -webkit-transition: width 0.2s ease-in 0s;
  -moz-transition: width 0.2s ease-in 0s;
  -o-transition: width 0.2s ease-in 0s;
  transition: width 0.2s ease-in 0s;
}

.read-more.pull-left:after {
  left: 0;
  right: auto;
}

.read-more span {
  float: right;
  width: 40px;
  height: 40px;
  color: #fff;
  margin-left: 10px;
  background-color: #ce000c;
  text-align: center
}

.read-more.pull-left span {
  float: left;
  margin: 0 10px 0 0
}

.read-more:hover:after {
  width: 100%
}

/* MORE INFO */
.more-info:before {
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
  font-family: 'FontAwesome';
  content: '\f105';
  margin-right: 5px;
}

.more-info:hover:before {
  margin-right: 10px;
}

/* SERVICE */
.service-content {
  position: relative;
}

/* SIDEBAR */
.sidebar .widget {
  margin: 0 0 30px 0;
}

.sidebar .widget_header h3 {
  font-size: 20px;
  font-weight: 900;
  padding-bottom: 10px;
}

.sidebar .widget_content ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.sidebar .widget_content ul li a {
  display: block;
  position: relative;
  line-height: 28px;
  padding-left: 15px;
  color: #333;
}

.sidebar .widget_content ul li li a {
  padding-left: 30px;
}

.sidebar .widget_content ul li a:hover {
  color: #ce000c
}

.sidebar .widget_content ul li.current a {
  color: #ce000c
}

.sidebar .widget_content ul li a:before {
  font-family: 'FontAwesome';
  content: '\f105';
  position: absolute;
  left: 0;
}

.sidebar .widget_content ul li li a:before {
  font-family: 'FontAwesome';
  content: '\f105';
  position: absolute;
  left: 15px;
}

table.download {
  width: 100%;
}

table.download th,
table.download td {
  padding: 5px;
  text-align: left;
}

table.download th {
  padding: 7px 5px;
  background: #ebebeb;
}

/* VISIBILITY */
.hidden {
  display: none;
}


.quote-icon {
  background-image: url(../images/quote.svg);
  background-size: cover;
  background-position: center;
  width: 62px;
  height: 55px;
}

/* BG COLORS */
.bg-primary {
  background-color: #ce000c;
}

.bg-dk {
  background-color: #111
}

.bg-lt {
  background-color: #ebebeb;
}

.bg-lter {
  background-color: #fff;
}

.bg-image {
  background-size: cover;
  background-position: center center;
  padding-bottom: 120%;
  position: relative;
}

.bg-image .inner {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
}

.bg-shadow {
  box-shadow: 0 0 2px rgba(0, 0, 0, 0.2);
}

/* BORDER RADIUSES */
.r {
  border-radius: 2px 2px 2px 2px;
}

.r-2x {
  border-radius: 4px;
}

.r-3x {
  border-radius: 6px;
}

.r-l {
  border-radius: 2px 0 0 2px;
}

.r-r {
  border-radius: 0 2px 2px 0;
}

.r-t {
  border-radius: 2px 2px 0 0;
}

.r-b {
  border-radius: 0 0 2px 2px;
}

/* paddingS */
.p-xxs {
  padding: 2px 4px;
}

.p-xs {
  padding: 5px;
}

.p-sm {
  padding: 10px;
}

.p {
  padding: 15px;
}

.p-md {
  padding: 20px;
}

.p-lg {
  padding: 30px;
}

.p-xl {
  padding: 50px;
}

.p-n {
  padding: 0 !important;
}

.p-l-none {
  padding-left: 0 !important;
}

.p-l-xs {
  padding-left: 5px;
}

.p-l-sm {
  padding-left: 10px;
}

.p-l {
  padding-left: 15px;
}

.p-l-md {
  padding-left: 20px;
}

.p-l-lg {
  padding-left: 30px;
}

.p-l-xl {
  padding-left: 40px;
}

.p-l-xxl {
  padding-left: 50px;
}

.p-l-n-xxs {
  padding-left: -1px;
}

.p-l-n-xs {
  padding-left: -5px;
}

.p-l-n-sm {
  padding-left: -10px;
}

.p-l-n {
  padding-left: -15px;
}

.p-l-n-md {
  padding-left: -20px;
}

.p-l-n-lg {
  padding-left: -30px;
}

.p-l-n-xl {
  padding-left: -40px;
}

.p-l-n-xxl {
  padding-left: -50px;
}

.p-t-none {
  padding-top: 0 !important;
}

.p-t-xxs {
  padding-top: 1px;
}

.p-t-xs {
  padding-top: 5px;
}

.p-t-sm {
  padding-top: 10px;
}

.p-t {
  padding-top: 15px;
}

.p-t-md {
  padding-top: 20px;
}

.p-t-lg {
  padding-top: 30px;
}

.p-t-xl {
  padding-top: 40px;
}

.p-t-xxl {
  padding-top: 50px;
}

.p-t-n-xxs {
  padding-top: -1px;
}

.p-t-n-xs {
  padding-top: -5px;
}

.p-t-n-sm {
  padding-top: -10px;
}

.p-t-n {
  padding-top: -15px;
}

.p-t-n-md {
  padding-top: -20px;
}

.p-t-n-lg {
  padding-top: -30px;
}

.p-t-n-xl {
  padding-top: -40px;
}

.p-t-n-xxl {
  padding-top: -50px;
}

.p-r-none {
  padding-right: 0 !important;
}

.p-r-xxs {
  padding-right: 1px;
}

.p-r-xs {
  padding-right: 5px;
}

.p-r-sm {
  padding-right: 10px;
}

.p-r {
  padding-right: 15px;
}

.p-r-md {
  padding-right: 20px;
}

.p-r-lg {
  padding-right: 30px;
}

.p-r-xl {
  padding-right: 40px;
}

.p-r-xxl {
  padding-right: 50px;
}

.p-r-n-xxs {
  padding-right: -1px;
}

.p-r-n-xs {
  padding-right: -5px;
}

.p-r-n-sm {
  padding-right: -10px;
}

.p-r-n {
  padding-right: -15px;
}

.p-r-n-md {
  padding-right: -20px;
}

.p-r-n-lg {
  padding-right: -30px;
}

.p-r-n-xl {
  padding-right: -40px;
}

.p-r-n-xxl {
  padding-right: -50px;
}

.p-b-none {
  padding-bottom: 0 !important;
}

.p-b-xxs {
  padding-bottom: 1px;
}

.p-b-xs {
  padding-bottom: 5px;
}

.p-b-sm {
  padding-bottom: 10px;
}

.p-b {
  padding-bottom: 15px;
}

.p-b-md {
  padding-bottom: 20px;
}

.p-b-lg {
  padding-bottom: 30px;
}

.p-b-xl {
  padding-bottom: 40px;
}

.p-b-xxl {
  padding-bottom: 50px;
}

.p-b-n-xxs {
  padding-bottom: -1px;
}

.p-b-n-xs {
  padding-bottom: -5px;
}

.p-b-n-sm {
  padding-bottom: -10px;
}

.p-b-n {
  padding-bottom: -15px;
}

.p-b-n-md {
  padding-bottom: -20px;
}

.p-b-n-lg {
  padding-bottom: -30px;
}

.p-b-n-xl {
  padding-bottom: -40px;
}

.p-b-n-xxl {
  padding-bottom: -50px;
}


/* MARGINS */
.m-xxs {
  margin: 2px 4px;
}

.m-xs {
  margin: 5px;
}

.m-sm {
  margin: 10px;
}

.m {
  margin: 15px;
}

.m-md {
  margin: 20px;
}

.m-lg {
  margin: 30px;
}

.m-xl {
  margin: 50px;
}

.m-n {
  margin: 0 !important;
}

.m-l-none {
  margin-left: 0 !important;
}

.m-l-xs {
  margin-left: 5px;
}

.m-l-sm {
  margin-left: 10px;
}

.m-l {
  margin-left: 15px;
}

.m-l-md {
  margin-left: 20px;
}

.m-l-lg {
  margin-left: 30px;
}

.m-l-xl {
  margin-left: 40px;
}

.m-l-xxl {
  margin-left: 50px;
}

.m-l-n-xxs {
  margin-left: -1px;
}

.m-l-n-xs {
  margin-left: -5px;
}

.m-l-n-sm {
  margin-left: -10px;
}

.m-l-n {
  margin-left: -15px;
}

.m-l-n-md {
  margin-left: -20px;
}

.m-l-n-lg {
  margin-left: -30px;
}

.m-l-n-xl {
  margin-left: -40px;
}

.m-l-n-xxl {
  margin-left: -50px;
}

.m-t-none {
  margin-top: 0 !important;
}

.m-t-xxs {
  margin-top: 1px;
}

.m-t-xs {
  margin-top: 5px;
}

.m-t-sm {
  margin-top: 10px;
}

.m-t {
  margin-top: 15px;
}

.m-t-md {
  margin-top: 20px;
}

.m-t-lg {
  margin-top: 30px;
}

.m-t-xl {
  margin-top: 40px;
}

.m-t-xxl {
  margin-top: 50px;
}

.m-t-n-xxs {
  margin-top: -1px;
}

.m-t-n-xs {
  margin-top: -5px;
}

.m-t-n-sm {
  margin-top: -10px;
}

.m-t-n {
  margin-top: -15px;
}

.m-t-n-md {
  margin-top: -20px;
}

.m-t-n-lg {
  margin-top: -30px;
}

.m-t-n-xl {
  margin-top: -40px;
}

.m-t-n-xxl {
  margin-top: -50px;
}

.m-r-none {
  margin-right: 0 !important;
}

.m-r-xxs {
  margin-right: 1px;
}

.m-r-xs {
  margin-right: 5px;
}

.m-r-sm {
  margin-right: 10px;
}

.m-r {
  margin-right: 15px;
}

.m-r-md {
  margin-right: 20px;
}

.m-r-lg {
  margin-right: 30px;
}

.m-r-xl {
  margin-right: 40px;
}

.m-r-xxl {
  margin-right: 50px;
}

.m-r-n-xxs {
  margin-right: -1px;
}

.m-r-n-xs {
  margin-right: -5px;
}

.m-r-n-sm {
  margin-right: -10px;
}

.m-r-n {
  margin-right: -15px;
}

.m-r-n-md {
  margin-right: -20px;
}

.m-r-n-lg {
  margin-right: -30px;
}

.m-r-n-xl {
  margin-right: -40px;
}

.m-r-n-xxl {
  margin-right: -50px;
}

.m-b-none {
  margin-bottom: 0 !important;
}

.m-b-xxs {
  margin-bottom: 1px;
}

.m-b-xs {
  margin-bottom: 5px;
}

.m-b-sm {
  margin-bottom: 10px;
}

.m-b {
  margin-bottom: 15px;
}

.m-b-md {
  margin-bottom: 20px;
}

.m-b-lg {
  margin-bottom: 30px;
}

.m-b-xl {
  margin-bottom: 40px;
}

.m-b-xxl {
  margin-bottom: 50px;
}

.m-b-n-xxs {
  margin-bottom: -1px;
}

.m-b-n-xs {
  margin-bottom: -5px;
}

.m-b-n-sm {
  margin-bottom: -10px;
}

.m-b-n {
  margin-bottom: -15px;
}

.m-b-n-md {
  margin-bottom: -20px;
}

.m-b-n-lg {
  margin-bottom: -30px;
}

.m-b-n-xl {
  margin-bottom: -40px;
}

.m-b-n-xxl {
  margin-bottom: -50px;
}

/* TEXT */
.text-italic {
  font-style: italic;
}

.text-center {
  text-align: center;
}

.text-lter {
  color: #fff;
}


/* COLS */
.row {
  margin: 0 -10px !important
}

.row:after {
  clear: both;
}

.row:before,
.row:after {
  display: table;
  content: "";
  line-height: 0;
}

.col1, .col2, .col3, .col4, .col5, .col6, .col7, .col8, .col9, .col10, .col11, .col12,
.col1-s, .col2-s, .col3-s, .col4-s, .col5-s, .col6-s, .col7-s, .col8-s, .col9-s, .col10-s, .col11-s, .col12-s,
.col1-xs, .col2-xs, .col3-xxs, .col4-s, .col5-xs, .col6-xs, .col7-xs, .col8-xs, .col9-xs, .col10-xs, .col11-xs, .col12x-s {
  float: left;
  min-height: 1px;
  padding-left: 10px;
  padding-right: 10px;
  box-sizing: border-box;
}

.col12 {
  width: 100%
}

.col11 {
  width: 91.66666667%
}

.col10 {
  width: 83.33333333%
}

.col9 {
  width: 75%
}

.col8 {
  width: 66.66666667%
}

.col7 {
  width: 58.33333333%
}

.col6 {
  width: 50%
}

.col5 {
  width: 41.66666667%
}

.col4 {
  width: 33.33333333%
}

.col3 {
  width: 25%
}

.col2 {
  width: 16.66666667%
}

.col1 {
  width: 8.33333333%
}


.pull-left {
  float: left;
}

.pull-right {
  float: right;
}

/*defaults iMod*/
.alignnone {
  margin: 5px 20px 20px 0;
}

.aligncenter, div.aligncenter {
  display: block;
  margin: 5px auto 5px auto;
}

.alignright {
  float: right;
  margin: 5px 0 20px 20px;
}

.alignleft {
  float: left;
  margin: 5px 20px 20px 0;
}

.aligncenter {
  display: block;
  margin: 5px auto 5px auto;
}

a img.alignright {
  float: right;
  margin: 5px 0 20px 20px;
}

a img.alignnone {
  margin: 5px 20px 20px 0;
}

a img.alignleft {
  float: left;
  margin: 5px 20px 20px 0;
}

a img.aligncenter {
  display: block;
  margin-left: auto;
  margin-right: auto
}

.caption {
  background: #fff;
  border: 1px solid #f0f0f0;
  max-width: 96%; /* Image does not overflow the content area */
  padding: 5px 3px 10px;
  text-align: center;
}

.caption.alignnone {
  margin: 5px 20px 20px 0;
}

.caption.alignleft {
  margin: 5px 20px 20px 0;
}

.caption.alignright {
  margin: 5px 0 20px 20px;
}

.caption img {
  border: 0 none;
  height: auto;
  margin: 0;
  max-width: 98.5%;
  padding: 0;
  width: auto;
}

.caption p.caption-text {
  font-size: 11px;
  line-height: 17px;
  margin: 0;
  padding: 0 4px 5px;
}

.widget, #content {
  position: relative
}

.edit_page_link, .edit_menu_link, .edit_widget_link {
  position: absolute;
  right: 10px;
  top: 10px
}

.clear {
  clear: both;
}

.clearfix:after {
  visibility: hidden;
  display: block;
  font-size: 0;
  content: " ";
  clear: both;
  height: 0;
}

.clearfix {
  display: inline-block;
}

* html .clearfix {
  height: 1%;
}

.clearfix {
  display: block;
}

[data-href] {
  cursor: pointer;
}

.gallery-item .text {
  min-height: 120px;
}

.mceContentBody {
}

.social-icons img {
  width: 40px;
  float: left;
  display: inline-block;
  margin-right: 10px;
  border-radius: 6px;
}

.text-center {
  text-align: center;
}

table.product-table {
  width: 100%;
  margin: 10px 0;
}

.product-table td {
  width: 50%;
}

.product-table img {
  width: 75%;
  height: auto;
}

table.product-table h2 {
  font-size: 18px;
}

ul.check {
  list-style: none;
  margin: 10px 0;
  padding-left: 0 !important;
}

ul.check li:before {
  font-family: 'FontAwesome';
  margin-right: 5px;
  content: '\f00c';
  color: #d93744;
}

img.alignright.img-margin {
  margin: 5px 0 0px 20px;
}

.faq-item {
  border-bottom: 1px solid #b8b8b8;

}

.faq-question {
  position: relative;
  padding: 15px 15px 15px 30px;
  text-transform: uppercase;
}

.faq-question:before {
  font-family: 'FontAwesome';
  content: '\f067';
  position: absolute;
  left: 0;
}

.faq-question.collapsed:before {
  font-family: 'FontAwesome';
  content: '\f068';
}

.faq-question:hover {
  cursor: pointer
}

.faq-answer {
  padding: 0 15px 15px 30px;
  display: none;
  color: #777
}