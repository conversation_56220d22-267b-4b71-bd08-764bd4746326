.wizard .form-row {
  background-color: #EBEBEB;
  padding: 0.6em 0;
}

.wizard .header-row {
  font-weight: bold;
}

.wizard table td {
  padding: 0 10px 10px 10px;

}

.wizard .col-form-label {
  line-height: 2.5em;
}

input[readonly].inputreadonly_cust, input[disabled].inputreadonly_cust {
  color: #666;
  border: 1px solid #ccc;
  background: #F0F0F0;
}

#elementtable {
  width: 100%;
}

.form-row.disabled {
  background-color: #BEC4C7 !important;
}

#laststep {
  font-weight: bold;
  display: inline-block;
  float: right;
  padding: 15px;
  color: #B43929;
}

.steps {
  margin-top: 15px;
  margin-bottom: 15px;
  width: 100%;
  display: flex;
}

.steps div:first-child {
  border-radius: 5px 0 0 5px;
}

.steps div:last-child {
  border-radius: 0 5px 5px 0;
}

.steps div {
  text-align: center;
  padding: 7px;
  flex-grow: 1;
}

div.step_on {
  background-color: #333;
  color: white;
}

div.step_on a {
  color: white;
  text-decoration: none;
}

div.step_off {
  background-color: #EAEBEC;
  color: black;
}

.input-row {
  background-color: #EBEBEB;
  padding: 10px;
}

.input-validation-icon {
  padding-top: 13px;
  text-align: right;
  min-width: 20px;
}

.input-error-message {
  margin: 0;
  margin-top: 8px;
  color: #f65e3f;
}

.input-warning-message {
  margin: 0;
  margin-top: 8px;
  color: #057fcc;
}

.notification {
  color: #057fcc;
}

[v-cloak] > * {
  display: none
}

[v-cloak]::before {
  content: " ";
  display: block;
  width: 16px;
  height: 16px;
  background-image: url(data:image/gif;base64,R0lGODlhEAAQAPIAAP///wAAAMLCwkJCQgAAAGJiYoKCgpKSkiH/C05FVFNDQVBFMi4wAwEAAAAh/hpDcmVhdGVkIHdpdGggYWpheGxvYWQuaW5mbwAh+QQJCgAAACwAAAAAEAAQAAADMwi63P4wyklrE2MIOggZnAdOmGYJRbExwroUmcG2LmDEwnHQLVsYOd2mBzkYDAdKa+dIAAAh+QQJCgAAACwAAAAAEAAQAAADNAi63P5OjCEgG4QMu7DmikRxQlFUYDEZIGBMRVsaqHwctXXf7WEYB4Ag1xjihkMZsiUkKhIAIfkECQoAAAAsAAAAABAAEAAAAzYIujIjK8pByJDMlFYvBoVjHA70GU7xSUJhmKtwHPAKzLO9HMaoKwJZ7Rf8AYPDDzKpZBqfvwQAIfkECQoAAAAsAAAAABAAEAAAAzMIumIlK8oyhpHsnFZfhYumCYUhDAQxRIdhHBGqRoKw0R8DYlJd8z0fMDgsGo/IpHI5TAAAIfkECQoAAAAsAAAAABAAEAAAAzIIunInK0rnZBTwGPNMgQwmdsNgXGJUlIWEuR5oWUIpz8pAEAMe6TwfwyYsGo/IpFKSAAAh+QQJCgAAACwAAAAAEAAQAAADMwi6IMKQORfjdOe82p4wGccc4CEuQradylesojEMBgsUc2G7sDX3lQGBMLAJibufbSlKAAAh+QQJCgAAACwAAAAAEAAQAAADMgi63P7wCRHZnFVdmgHu2nFwlWCI3WGc3TSWhUFGxTAUkGCbtgENBMJAEJsxgMLWzpEAACH5BAkKAAAALAAAAAAQABAAAAMyCLrc/jDKSatlQtScKdceCAjDII7HcQ4EMTCpyrCuUBjCYRgHVtqlAiB1YhiCnlsRkAAAOwAAAAAAAAAAAA==);
}

.stoneimage {
  border: 1px solid #d6d5d5;
  border-radius: 3px;
}

.wizard_inleiding {
  padding: 5px 5px 15px 5px;
}

input.inputnumber {
  text-align: right;
}

.wizard .form-row.elementrow {
  padding: 0;
}

.select_custom_depth, .select_custom_thickness, .select_custom_height, .select_custom_width_click, .select_custom_height_click {
  width: 120px;
  display: inline-block;
}

.select_custom_wrapper {
  display: flex;
}

.select_custom {
  display: flex;
  flex-direction: column;
}

.select_custom > div {
  padding-bottom: 5px;
}

.select_custom > div > label {
  width: 25px;
  display: inline-block;
}

.custom_img img {
  width: 268px;
  margin-left: 15px;
  border: 1px solid #d6d5d5;
}

#step3.wizard .elementrow .form-row, #step3.wizard .form-row {
  background: white;
}

#step3.wizard .elementrow .elementrow-header {
  background: #EAEBEC;
}

.elementtable_fa {
  padding: 0 0 0 5px;
  display: inline-block;
}

.wizard_elementLength {
  width: calc(100% - 45px);
}

.wizard_inputmm {
  width: calc(100% - 35px);
  min-width: 60px;
}

.wizard a.imagegallery {
  display: block;
  padding: 3px;
}

.wizard a.imagegallery img {
  max-width: 80px;
  border: 1px solid white;
  border-radius: 3px;
}

.wizard a.imagegallery:hover img {
  border-color: #ccc;
  transition: 0.3s;
}

.questionbox {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  margin: auto;
  z-index: 1000;
  height: 412px;
  width: 600px;
  background-color: #FFFFFF;
  border: 1px solid #000000;
  border-radius: 5px;
  /*display: none;*/
}

#flagWindowbox {
  height: 500px !important;
  width: 400px;
}

.questionbox_title {
  background-color: #ce000c;
  color: white;
  padding: 15px 15px;
  font-weight: bold;
  border-radius: 4px 4px 0 0;
}

.questionbox_content {
  padding: 10px 15px;
}

.questionbox img.exampleimg {
  border: none;
  vertical-align: middle;
  padding: 10px 4px;
}

.center {
  text-align: center;
}

.form-btn {
  border: 1px solid #ccc;
  display: inline-block;
  cursor: pointer;
  text-transform: uppercase;
  transition: all 0.3s ease;
}

.form-btn:hover {
  border: 1px solid #666;
  color: black;
}

.form-input[readonly], .form-input[disabled], .select select[disabled] {
  background-color: #eaeaea;
  color: #666;
}

.mitre_div_parent {
  width: 380px;
}

.mitre_div {
  padding: 10px 0;
  background: white;
}

.mitre_middle {
  width: 200px;
  height: 60px;
}

.speling {
  border: 1px solid #ccc;
  color: #ccc;
  margin: 9px 0;
  padding: 1px 5px;
  display: inline-block;
  width: 54px;
}

.vlagkozijnlbl {
  color: #ccc;
  font-size: 16px;
  padding: 0 5px;
}

#wizard_delivery {
  margin: 0 0 0.6em 0;
}

#wizard_delivery .form-row {
  margin: 0 0 0.2em 0;
}

textarea.form-input {
  padding: 15px;
}

.wizard .col-form-label .wizard_stonealert {
  line-height: 1.5em;
  display: inline-block;
  color: #ce000c;
}

.material {
  border: 1px solid #e8e8e8;
  border-radius: 5px 5px 0 0;
  cursor: pointer;
  margin-bottom: 15px;
}

.material:hover {
  box-shadow: 2px 2px 4px #dddbdb;
}

#material_type {
  display: none;
}

.material_selected {
  box-shadow: 2px 2px 4px #dddbdb;
}

.material_disabled {
  opacity: 0.4;
}

.material img {
  border-radius: 5px 5px 0 0;
}

.material_title {
  font-size: 21px;
  font-weight: 900;
  text-align: center;
  padding: 15px;
}

.verstekhoekImgAfstand {
  padding-bottom: 10px;
}

.mitre_example, .endstone_example {
  overflow: hidden;
  display: inline-block;
  vertical-align: middle;
}

.mitre_example > div, .endstone_example > div {
  width: 200px;
  height: 40px;
  border: 1px solid grey;
  position: relative;
}

.mitre_line {
  border-top: 1px solid grey;
  margin-top: 5px;
}

.mitre_of {
  display: inline-block;
  padding: 10px 4px;
  font-size: 18px;
}

.spekband.brand1 .mitre_of, .spekband.brand1 .mitre_example_left_top, .spekband.brand1 .mitre_example_right_top, .spekband.brand1 .mitre_example_leftright_top {
  display: none;
}

.mitre_schuin_left {
  transform: skew(-45deg, 0deg);
  position: absolute;
  border-right: 1px solid grey;
  height: 40px;
  width: 40px;
  top: -1px;
  left: -20px;
  background: white;
  z-index: 1;
}

.mitre_schuin_left_top {
  transform: skew(45deg, 0deg);
  position: absolute;
  border-right: 1px solid grey;
  height: 40px;
  width: 40px;
  top: -1px;
  left: -21px;
  background: white;
  z-index: 1;
}

.mitre_schuin_right {
  transform: skew(0deg, 45deg);
  position: absolute;
  border-bottom: 1px solid grey;
  height: 40px;
  width: 40px;
  top: -21px;
  right: -1px;
  background: white;
  z-index: 1;
}

.mitre_schuin_right_top {
  transform: skew(-45deg, 0deg);
  position: absolute;
  border-left: 1px solid grey;
  height: 40px;
  width: 40px;
  top: 0;
  right: -21px;
  background: white;
  z-index: 1;
}

.spekband .mitre_line {
  margin-top: 23px;
}

.muurafdekker .mitre_line {
  margin-top: 18px;
}

.muurafdekker.no-ezelsrug .mitre_line {
  border: 0;
}

.balkjes .mitre_line {
  border: 0;
}

.spekband.brand1 .mitre_example_left .mitre_schuin_left,
.spekband.brand1 .mitre_example_leftright .mitre_schuin_left {
  transform: none;
  background: white;
  top: 0;
  left: 0;
  height: 24px;
  width: 15px;
  border-right: 1px solid grey;
}

.spekband.brand1 .mitre_example_right .mitre_schuin_right,
.spekband.brand1 .mitre_example_leftright .mitre_schuin_right {
  transform: none;
  background: white;
  top: 0;
  right: 0;
  height: 24px;
  width: 15px;
  border-left: 1px solid grey;
  border-bottom: 0;
}

.spekband.brand1 .mitre_example.mitre_example_left > div {
  border-radius: 0 0 0 15px;
}

.spekband.brand1 .mitre_example.mitre_example_right > div {
  border-radius: 0 0 15px 0;
}

.spekband.brand1 .mitre_example.mitre_example_leftright > div {
  border-radius: 0 0 15px 15px;
}

.mitre_label {
  width: 70px;
  display: inline-block;
  text-align: left;
  padding-left: 10px;
}

.spekband.brand1 .verstekhoekImgAfstand {
  text-align: center;
  padding-right: 30px;
}

.endstone_line {
  border-top: 1px solid grey;
  margin-top: 23px;
}

.endStoneRow {
  padding-bottom: 10px;
}

.endstone_example .endstone_left {
  position: absolute;
  transform: none;
  background: white;
  top: 0;
  left: 0;
  height: 24px;
  width: 5px;
  border-right: 1px solid grey;
  border-bottom: 0;
}

.endstone_example .endstone_right {
  position: absolute;
  transform: none;
  background: white;
  top: 0;
  right: 0;
  height: 24px;
  width: 5px;
  border-left: 1px solid grey;
  border-bottom: 0;
}

.endstone_example .line_left, .endstone_example .line_right {
  display: none;
}

.muurafdekker .endstone_line {
  margin-top: 19px;
}
.muurafdekker.no-ezelsrug .endstone_line {
  border: 0;
}
.muurafdekker.no-ezelsrug .endstone_example .endstone_right,
.muurafdekker.no-ezelsrug .endstone_example .endstone_left {
  border: 0;
}

.endstone_example .endstone_left {
  left: -15px;
  width: 30px;
  background: white;
  border-top: 1px solid grey;
  top: 4px;
  transform: rotate(45deg);
  height: 30px;
}

.muurafdekker .endstone_example .endstone_right {
  right: -15px;
  width: 30px;
  background: white;
  border-top: 1px solid grey;
  top: 4px;
  transform: rotate(-45deg);
  height: 30px;
}

.muurafdekker .endstone_example .line_left {
  display: block;
  top: 0;
  left: 0;
  border-left: 1px solid grey;
  height: 40px;
  position: absolute;
}

.muurafdekker .endstone_example .line_right {
  display: block;
  top: 0;
  right: 0;
  border-left: 1px solid grey;
  height: 40px;
  position: absolute;
}

.wizard .confirmoptions .col-form-label {
  line-height: 1.5em;
}

.wizard .producsextrahead td {
  font-weight: bold;
  background-color: #EBEBEB;
  padding: 5px 15px;
  margin-bottom: 5px;
}

@media (max-width: 992px) {
  .steps div:first-child {
    border-radius: 0;
  }

  .steps div:last-child {
    border-radius: 0;
  }
}