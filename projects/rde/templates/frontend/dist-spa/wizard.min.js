
!function(){try{var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},n=(new e.Error).stack;n&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[n]="4be673d6-e390-5285-a2ff-dd33181994c9")}catch(e){}}();
(function(e){var t={};function n(i){if(t[i])return t[i].exports;var o=t[i]={i:i,l:!1,exports:{}};return e[i].call(o.exports,o,o.exports,n),o.l=!0,o.exports}n.m=e,n.c=t,n.d=function(e,t,i){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:i})},n.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"===typeof e&&e&&e.__esModule)return e;var i=Object.create(null);if(n.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var o in e)n.d(i,o,function(t){return e[t]}.bind(null,o));return i},n.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="../../../templates/frontend/dist-spa/",n(n.s=0)})({0:function(e,t,n){e.exports=n("56d7")},"04f8":function(e,t,n){var i=n("2d00"),o=n("d039");e.exports=!!Object.getOwnPropertySymbols&&!o((function(){var e=Symbol();return!String(e)||!(Object(e)instanceof Symbol)||!Symbol.sham&&i&&i<41}))},"06cf":function(e,t,n){var i=n("83ab"),o=n("c65b"),s=n("d1e7"),r=n("5c6c"),a=n("fc6a"),l=n("a04b"),c=n("1a2d"),u=n("0cfb"),d=Object.getOwnPropertyDescriptor;t.f=i?d:function(e,t){if(e=a(e),t=l(t),u)try{return d(e,t)}catch(n){}if(c(e,t))return r(!o(s.f,e,t),e[t])}},"07fa":function(e,t,n){var i=n("50c4");e.exports=function(e){return i(e.length)}},"083a":function(e,t,n){"use strict";var i=n("0d51"),o=TypeError;e.exports=function(e,t){if(!delete e[t])throw o("Cannot delete property "+i(t)+" of "+i(e))}},"0920":function(e,t,n){},"0cfb":function(e,t,n){var i=n("83ab"),o=n("d039"),s=n("cc12");e.exports=!i&&!o((function(){return 7!=Object.defineProperty(s("div"),"a",{get:function(){return 7}}).a}))},"0d51":function(e,t){var n=String;e.exports=function(e){try{return n(e)}catch(t){return"Object"}}},"11ec":function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));const i="8.43.0"},"13d2":function(e,t,n){var i=n("e330"),o=n("d039"),s=n("1626"),r=n("1a2d"),a=n("83ab"),l=n("5e77").CONFIGURABLE,c=n("8925"),u=n("69f3"),d=u.enforce,h=u.get,p=String,m=Object.defineProperty,f=i("".slice),g=i("".replace),v=i([].join),b=a&&!o((function(){return 8!==m((function(){}),"length",{value:8}).length})),_=String(String).split("String"),y=e.exports=function(e,t,n){"Symbol("===f(p(t),0,7)&&(t="["+g(p(t),/^Symbol\(([^)]*)\)/,"$1")+"]"),n&&n.getter&&(t="get "+t),n&&n.setter&&(t="set "+t),(!r(e,"name")||l&&e.name!==t)&&(a?m(e,"name",{value:t,configurable:!0}):e.name=t),b&&n&&r(n,"arity")&&e.length!==n.arity&&m(e,"length",{value:n.arity});try{n&&r(n,"constructor")&&n.constructor?a&&m(e,"prototype",{writable:!1}):e.prototype&&(e.prototype=void 0)}catch(o){}var i=d(e);return r(i,"source")||(i.source=v(_,"string"==typeof t?t:"")),e};Function.prototype.toString=y((function(){return s(this)&&h(this).source||c(this)}),"toString")},"14d9":function(e,t,n){"use strict";var i=n("23e7"),o=n("7b0b"),s=n("07fa"),r=n("3a34"),a=n("3511"),l=n("d039"),c=l((function(){return 4294967297!==[].push.call({length:4294967296},1)})),u=function(){try{Object.defineProperty([],"length",{writable:!1}).push()}catch(e){return e instanceof TypeError}},d=c||!u();i({target:"Array",proto:!0,arity:1,forced:d},{push:function(e){var t=o(this),n=s(t),i=arguments.length;a(n+i);for(var l=0;l<i;l++)t[n]=arguments[l],n++;return r(t,n),n}})},1517:function(e,t,n){},1626:function(e,t,n){var i=n("8ea1"),o=i.all;e.exports=i.IS_HTMLDDA?function(e){return"function"==typeof e||e===o}:function(e){return"function"==typeof e}},"1a2d":function(e,t,n){var i=n("e330"),o=n("7b0b"),s=i({}.hasOwnProperty);e.exports=Object.hasOwn||function(e,t){return s(o(e),t)}},"1b5c":function(e,t,n){"use strict";n("fcd2")},"1d80":function(e,t,n){var i=n("7234"),o=TypeError;e.exports=function(e){if(i(e))throw o("Can't call method on "+e);return e}},"1edf":function(e,t,n){"use strict";n.d(t,"a",(function(){return u})),n.d(t,"b",(function(){return c})),n.d(t,"c",(function(){return a}));var i=n("710b"),o=n("d96b");const s=o["a"],r=80;function a(e,t={}){if(!e)return"<unknown>";try{let n=e;const i=5,o=[];let s=0,a=0;const c=" > ",u=c.length;let d;const h=Array.isArray(t)?t:t.keyAttrs,p=!Array.isArray(t)&&t.maxStringLength||r;while(n&&s++<i){if(d=l(n,h),"html"===d||s>1&&a+o.length*u+d.length>=p)break;o.push(d),a+=d.length,n=n.parentNode}return o.reverse().join(c)}catch(n){return"<unknown>"}}function l(e,t){const n=e,o=[];if(!n||!n.tagName)return"";if(s.HTMLElement&&n instanceof HTMLElement&&n.dataset){if(n.dataset["sentryComponent"])return n.dataset["sentryComponent"];if(n.dataset["sentryElement"])return n.dataset["sentryElement"]}o.push(n.tagName.toLowerCase());const r=t&&t.length?t.filter(e=>n.getAttribute(e)).map(e=>[e,n.getAttribute(e)]):null;if(r&&r.length)r.forEach(e=>{o.push(`[${e[0]}="${e[1]}"]`)});else{n.id&&o.push("#"+n.id);const e=n.className;if(e&&Object(i["l"])(e)){const t=e.split(/\s+/);for(const e of t)o.push("."+e)}}const a=["aria-label","type","name","title","alt"];for(const i of a){const e=n.getAttribute(i);e&&o.push(`[${i}="${e}"]`)}return o.join("")}function c(){try{return s.document.location.href}catch(e){return""}}function u(e){if(!s.HTMLElement)return null;let t=e;const n=5;for(let i=0;i<n;i++){if(!t)return null;if(t instanceof HTMLElement){if(t.dataset["sentryComponent"])return t.dataset["sentryComponent"];if(t.dataset["sentryElement"])return t.dataset["sentryElement"]}t=t.parentNode}return null}},"23cb":function(e,t,n){var i=n("5926"),o=Math.max,s=Math.min;e.exports=function(e,t){var n=i(e);return n<0?o(n+t,0):s(n,t)}},"23e7":function(e,t,n){var i=n("da84"),o=n("06cf").f,s=n("9112"),r=n("cb2d"),a=n("6374"),l=n("e893"),c=n("94ca");e.exports=function(e,t){var n,u,d,h,p,m,f=e.target,g=e.global,v=e.stat;if(u=g?i:v?i[f]||a(f,{}):(i[f]||{}).prototype,u)for(d in t){if(p=t[d],e.dontCallGetSet?(m=o(u,d),h=m&&m.value):h=u[d],n=c(g?d:f+(v?".":"#")+d,e.forced),!n&&void 0!==h){if(typeof p==typeof h)continue;l(p,h)}(e.sham||h&&h.sham)&&s(p,"sham",!0),r(u,d,p,e)}}},"241c":function(e,t,n){var i=n("ca84"),o=n("7839"),s=o.concat("length","prototype");t.f=Object.getOwnPropertyNames||function(e){return i(e,s)}},"2c81":function(e,t,n){"use strict";n.d(t,"a",(function(){return o})),n.d(t,"b",(function(){return a})),n.d(t,"c",(function(){return p})),n.d(t,"d",(function(){return h})),n.d(t,"e",(function(){return l}));const i=50,o="?",s=/\(error: (.*)\)/,r=/captureMessage|captureException/;function a(...e){const t=e.sort((e,t)=>e[0]-t[0]).map(e=>e[1]);return(e,n=0,o=0)=>{const r=[],a=e.split("\n");for(let l=n;l<a.length;l++){const e=a[l];if(e.length>1024)continue;const n=s.test(e)?e.replace(s,"$1"):e;if(!n.match(/\S*Error: /)){for(const e of t){const t=e(n);if(t){r.push(t);break}}if(r.length>=i+o)break}}return c(r.slice(o))}}function l(e){return Array.isArray(e)?a(...e):e}function c(e){if(!e.length)return[];const t=Array.from(e);return/sentryWrapped/.test(u(t).function||"")&&t.pop(),t.reverse(),r.test(u(t).function||"")&&(t.pop(),r.test(u(t).function||"")&&t.pop()),t.slice(0,i).map(e=>({...e,filename:e.filename||u(t).filename,function:e.function||o}))}function u(e){return e[e.length-1]||{}}const d="<anonymous>";function h(e){try{return e&&"function"===typeof e&&e.name||d}catch(t){return d}}function p(e){const t=e.exception;if(t){const e=[];try{return t.values.forEach(t=>{t.stacktrace.frames&&e.push(...t.stacktrace.frames)}),e}catch(n){return}}}},"2d00":function(e,t,n){var i,o,s=n("da84"),r=n("342f"),a=s.process,l=s.Deno,c=a&&a.versions||l&&l.version,u=c&&c.v8;u&&(i=u.split("."),o=i[0]>0&&i[0]<4?1:+(i[0]+i[1])),!o&&r&&(i=r.match(/Edge\/(\d+)/),(!i||i[1]>=74)&&(i=r.match(/Chrome\/(\d+)/),i&&(o=+i[1]))),e.exports=o},"342f":function(e,t){e.exports="undefined"!=typeof navigator&&String(navigator.userAgent)||""},3511:function(e,t){var n=TypeError,i=9007199254740991;e.exports=function(e){if(e>i)throw n("Maximum allowed index exceeded");return e}},"3a18":function(e,t,n){"use strict";function i(){const e="function"===typeof WeakSet,t=e?new WeakSet:[];function n(n){if(e)return!!t.has(n)||(t.add(n),!1);for(let e=0;e<t.length;e++){const i=t[e];if(i===n)return!0}return t.push(n),!1}function i(n){if(e)t.delete(n);else for(let e=0;e<t.length;e++)if(t[e]===n){t.splice(e,1);break}}return[n,i]}n.d(t,"a",(function(){return i}))},"3a34":function(e,t,n){"use strict";var i=n("83ab"),o=n("e8b5"),s=TypeError,r=Object.getOwnPropertyDescriptor,a=i&&!function(){if(void 0!==this)return!0;try{Object.defineProperty([],"length",{writable:!1}).length=1}catch(e){return e instanceof TypeError}}();e.exports=a?function(e,t){if(o(e)&&!r(e,"length").writable)throw s("Cannot set read only .length");return e.length=t}:function(e,t){return e.length=t}},"3a9b":function(e,t,n){var i=n("e330");e.exports=i({}.isPrototypeOf)},"3c65":function(e,t,n){"use strict";var i=n("23e7"),o=n("7b0b"),s=n("07fa"),r=n("3a34"),a=n("083a"),l=n("3511"),c=1!==[].unshift(0),u=function(){try{Object.defineProperty([],"length",{writable:!1}).unshift()}catch(e){return e instanceof TypeError}},d=c||!u();i({target:"Array",proto:!0,arity:1,forced:d},{unshift:function(e){var t=o(this),n=s(t),i=arguments.length;if(i){l(n+i);var c=n;while(c--){var u=c+i;c in t?t[u]=t[c]:a(t,u)}for(var d=0;d<i;d++)t[d]=arguments[d]}return r(t,n+i)}})},"40d5":function(e,t,n){var i=n("d039");e.exports=!i((function(){var e=function(){}.bind();return"function"!=typeof e||e.hasOwnProperty("prototype")}))},4413:function(e,t,n){},"44ad":function(e,t,n){var i=n("e330"),o=n("d039"),s=n("c6b6"),r=Object,a=i("".split);e.exports=o((function(){return!r("z").propertyIsEnumerable(0)}))?function(e){return"String"==s(e)?a(e,""):r(e)}:r},"485a":function(e,t,n){var i=n("c65b"),o=n("1626"),s=n("861d"),r=TypeError;e.exports=function(e,t){var n,a;if("string"===t&&o(n=e.toString)&&!s(a=i(n,e)))return a;if(o(n=e.valueOf)&&!s(a=i(n,e)))return a;if("string"!==t&&o(n=e.toString)&&!s(a=i(n,e)))return a;throw r("Can't convert object to primitive value")}},"4d64":function(e,t,n){var i=n("fc6a"),o=n("23cb"),s=n("07fa"),r=function(e){return function(t,n,r){var a,l=i(t),c=s(l),u=o(r,c);if(e&&n!=n){while(c>u)if(a=l[u++],a!=a)return!0}else for(;c>u;u++)if((e||u in l)&&l[u]===n)return e||u||0;return!e&&-1}};e.exports={includes:r(!0),indexOf:r(!1)}},"50c4":function(e,t,n){var i=n("5926"),o=Math.min;e.exports=function(e){return e>0?o(i(e),9007199254740991):0}},5692:function(e,t,n){var i=n("c430"),o=n("c6cd");(e.exports=function(e,t){return o[e]||(o[e]=void 0!==t?t:{})})("versions",[]).push({version:"3.30.1",mode:i?"pure":"global",copyright:"© 2014-2023 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.30.1/LICENSE",source:"https://github.com/zloirock/core-js"})},"56d7":function(e,t,n){"use strict";n.r(t);var i=n("9ff4");let o;class s{constructor(e=!1){this.detached=e,this._active=!0,this.effects=[],this.cleanups=[],this.parent=o,!e&&o&&(this.index=(o.scopes||(o.scopes=[])).push(this)-1)}get active(){return this._active}run(e){if(this._active){const t=o;try{return o=this,e()}finally{o=t}}else 0}on(){o=this}off(){o=this.parent}stop(e){if(this._active){let t,n;for(t=0,n=this.effects.length;t<n;t++)this.effects[t].stop();for(t=0,n=this.cleanups.length;t<n;t++)this.cleanups[t]();if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].stop(!0);if(!this.detached&&this.parent&&!e){const e=this.parent.scopes.pop();e&&e!==this&&(this.parent.scopes[this.index]=e,e.index=this.index)}this.parent=void 0,this._active=!1}}}function r(e,t=o){t&&t.active&&t.effects.push(e)}function a(){return o}const l=e=>{const t=new Set(e);return t.w=0,t.n=0,t},c=e=>(e.w&f)>0,u=e=>(e.n&f)>0,d=({deps:e})=>{if(e.length)for(let t=0;t<e.length;t++)e[t].w|=f},h=e=>{const{deps:t}=e;if(t.length){let n=0;for(let i=0;i<t.length;i++){const o=t[i];c(o)&&!u(o)?o.delete(e):t[n++]=o,o.w&=~f,o.n&=~f}t.length=n}},p=new WeakMap;let m=0,f=1;const g=30;let v;const b=Symbol(""),_=Symbol("");class y{constructor(e,t=null,n){this.fn=e,this.scheduler=t,this.active=!0,this.deps=[],this.parent=void 0,r(this,n)}run(){if(!this.active)return this.fn();let e=v,t=k;while(e){if(e===this)return;e=e.parent}try{return this.parent=v,v=this,k=!0,f=1<<++m,m<=g?d(this):w(this),this.fn()}finally{m<=g&&h(this),f=1<<--m,v=this.parent,k=t,this.parent=void 0,this.deferStop&&this.stop()}}stop(){v===this?this.deferStop=!0:this.active&&(w(this),this.onStop&&this.onStop(),this.active=!1)}}function w(e){const{deps:t}=e;if(t.length){for(let n=0;n<t.length;n++)t[n].delete(e);t.length=0}}let k=!0;const x=[];function j(){x.push(k),k=!1}function O(){const e=x.pop();k=void 0===e||e}function E(e,t,n){if(k&&v){let t=p.get(e);t||p.set(e,t=new Map);let i=t.get(n);i||t.set(n,i=l());const o=void 0;C(i,o)}}function C(e,t){let n=!1;m<=g?u(e)||(e.n|=f,n=!c(e)):n=!e.has(v),n&&(e.add(v),v.deps.push(e))}function S(e,t,n,o,s,r){const a=p.get(e);if(!a)return;let c=[];if("clear"===t)c=[...a.values()];else if("length"===n&&Object(i["o"])(e)){const e=Number(o);a.forEach((t,n)=>{("length"===n||n>=e)&&c.push(t)})}else switch(void 0!==n&&c.push(a.get(n)),t){case"add":Object(i["o"])(e)?Object(i["t"])(n)&&c.push(a.get("length")):(c.push(a.get(b)),Object(i["u"])(e)&&c.push(a.get(_)));break;case"delete":Object(i["o"])(e)||(c.push(a.get(b)),Object(i["u"])(e)&&c.push(a.get(_)));break;case"set":Object(i["u"])(e)&&c.push(a.get(b));break}if(1===c.length)c[0]&&I(c[0]);else{const e=[];for(const t of c)t&&e.push(...t);I(l(e))}}function I(e,t){const n=Object(i["o"])(e)?e:[...e];for(const i of n)i.computed&&P(i,t);for(const i of n)i.computed||P(i,t)}function P(e,t){(e!==v||e.allowRecurse)&&(e.scheduler?e.scheduler():e.run())}function T(e,t){var n;return null===(n=p.get(e))||void 0===n?void 0:n.get(t)}const A=Object(i["K"])("__proto__,__v_isRef,__isVue"),L=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>"arguments"!==e&&"caller"!==e).map(e=>Symbol[e]).filter(i["G"])),D=V(),z=V(!1,!0),q=V(!0),N=M();function M(){const e={};return["includes","indexOf","lastIndexOf"].forEach(t=>{e[t]=function(...e){const n=Te(this);for(let t=0,o=this.length;t<o;t++)E(n,"get",t+"");const i=n[t](...e);return-1===i||!1===i?n[t](...e.map(Te)):i}}),["push","pop","shift","unshift","splice"].forEach(t=>{e[t]=function(...e){j();const n=Te(this)[t].apply(this,e);return O(),n}}),e}function B(e){const t=Te(this);return E(t,"has",e),t.hasOwnProperty(e)}function V(e=!1,t=!1){return function(n,o,s){if("__v_isReactive"===o)return!e;if("__v_isReadonly"===o)return e;if("__v_isShallow"===o)return t;if("__v_raw"===o&&s===(e?t?ye:_e:t?be:ve).get(n))return n;const r=Object(i["o"])(n);if(!e){if(r&&Object(i["k"])(N,o))return Reflect.get(N,o,s);if("hasOwnProperty"===o)return B}const a=Reflect.get(n,o,s);return(Object(i["G"])(o)?L.has(o):A(o))?a:(e||E(n,"get",o),t?a:Ne(a)?r&&Object(i["t"])(o)?a:a.value:Object(i["w"])(a)?e?Oe(a):xe(a):a)}}const F=U(),R=U(!0);function U(e=!1){return function(t,n,o,s){let r=t[n];if(Se(r)&&Ne(r)&&!Ne(o))return!1;if(!e&&(Ie(o)||Se(o)||(r=Te(r),o=Te(o)),!Object(i["o"])(t)&&Ne(r)&&!Ne(o)))return r.value=o,!0;const a=Object(i["o"])(t)&&Object(i["t"])(n)?Number(n)<t.length:Object(i["k"])(t,n),l=Reflect.set(t,n,o,s);return t===Te(s)&&(a?Object(i["j"])(o,r)&&S(t,"set",n,o,r):S(t,"add",n,o)),l}}function $(e,t){const n=Object(i["k"])(e,t),o=e[t],s=Reflect.deleteProperty(e,t);return s&&n&&S(e,"delete",t,void 0,o),s}function W(e,t){const n=Reflect.has(e,t);return Object(i["G"])(t)&&L.has(t)||E(e,"has",t),n}function H(e){return E(e,"iterate",Object(i["o"])(e)?"length":b),Reflect.ownKeys(e)}const K={get:D,set:F,deleteProperty:$,has:W,ownKeys:H},G={get:q,set(e,t){return!0},deleteProperty(e,t){return!0}},J=Object(i["h"])({},K,{get:z,set:R}),Y=e=>e,Z=e=>Reflect.getPrototypeOf(e);function X(e,t,n=!1,i=!1){e=e["__v_raw"];const o=Te(e),s=Te(t);n||(t!==s&&E(o,"get",t),E(o,"get",s));const{has:r}=Z(o),a=i?Y:n?De:Le;return r.call(o,t)?a(e.get(t)):r.call(o,s)?a(e.get(s)):void(e!==o&&e.get(t))}function Q(e,t=!1){const n=this["__v_raw"],i=Te(n),o=Te(e);return t||(e!==o&&E(i,"has",e),E(i,"has",o)),e===o?n.has(e):n.has(e)||n.has(o)}function ee(e,t=!1){return e=e["__v_raw"],!t&&E(Te(e),"iterate",b),Reflect.get(e,"size",e)}function te(e){e=Te(e);const t=Te(this),n=Z(t),i=n.has.call(t,e);return i||(t.add(e),S(t,"add",e,e)),this}function ne(e,t){t=Te(t);const n=Te(this),{has:o,get:s}=Z(n);let r=o.call(n,e);r||(e=Te(e),r=o.call(n,e));const a=s.call(n,e);return n.set(e,t),r?Object(i["j"])(t,a)&&S(n,"set",e,t,a):S(n,"add",e,t),this}function ie(e){const t=Te(this),{has:n,get:i}=Z(t);let o=n.call(t,e);o||(e=Te(e),o=n.call(t,e));const s=i?i.call(t,e):void 0,r=t.delete(e);return o&&S(t,"delete",e,void 0,s),r}function oe(){const e=Te(this),t=0!==e.size,n=void 0,i=e.clear();return t&&S(e,"clear",void 0,void 0,n),i}function se(e,t){return function(n,i){const o=this,s=o["__v_raw"],r=Te(s),a=t?Y:e?De:Le;return!e&&E(r,"iterate",b),s.forEach((e,t)=>n.call(i,a(e),a(t),o))}}function re(e,t,n){return function(...o){const s=this["__v_raw"],r=Te(s),a=Object(i["u"])(r),l="entries"===e||e===Symbol.iterator&&a,c="keys"===e&&a,u=s[e](...o),d=n?Y:t?De:Le;return!t&&E(r,"iterate",c?_:b),{next(){const{value:e,done:t}=u.next();return t?{value:e,done:t}:{value:l?[d(e[0]),d(e[1])]:d(e),done:t}},[Symbol.iterator](){return this}}}}function ae(e){return function(...t){return"delete"!==e&&this}}function le(){const e={get(e){return X(this,e)},get size(){return ee(this)},has:Q,add:te,set:ne,delete:ie,clear:oe,forEach:se(!1,!1)},t={get(e){return X(this,e,!1,!0)},get size(){return ee(this)},has:Q,add:te,set:ne,delete:ie,clear:oe,forEach:se(!1,!0)},n={get(e){return X(this,e,!0)},get size(){return ee(this,!0)},has(e){return Q.call(this,e,!0)},add:ae("add"),set:ae("set"),delete:ae("delete"),clear:ae("clear"),forEach:se(!0,!1)},i={get(e){return X(this,e,!0,!0)},get size(){return ee(this,!0)},has(e){return Q.call(this,e,!0)},add:ae("add"),set:ae("set"),delete:ae("delete"),clear:ae("clear"),forEach:se(!0,!0)},o=["keys","values","entries",Symbol.iterator];return o.forEach(o=>{e[o]=re(o,!1,!1),n[o]=re(o,!0,!1),t[o]=re(o,!1,!0),i[o]=re(o,!0,!0)}),[e,n,t,i]}const[ce,ue,de,he]=le();function pe(e,t){const n=t?e?he:de:e?ue:ce;return(t,o,s)=>"__v_isReactive"===o?!e:"__v_isReadonly"===o?e:"__v_raw"===o?t:Reflect.get(Object(i["k"])(n,o)&&o in t?n:t,o,s)}const me={get:pe(!1,!1)},fe={get:pe(!1,!0)},ge={get:pe(!0,!1)};const ve=new WeakMap,be=new WeakMap,_e=new WeakMap,ye=new WeakMap;function we(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function ke(e){return e["__v_skip"]||!Object.isExtensible(e)?0:we(Object(i["S"])(e))}function xe(e){return Se(e)?e:Ee(e,!1,K,me,ve)}function je(e){return Ee(e,!1,J,fe,be)}function Oe(e){return Ee(e,!0,G,ge,_e)}function Ee(e,t,n,o,s){if(!Object(i["w"])(e))return e;if(e["__v_raw"]&&(!t||!e["__v_isReactive"]))return e;const r=s.get(e);if(r)return r;const a=ke(e);if(0===a)return e;const l=new Proxy(e,2===a?o:n);return s.set(e,l),l}function Ce(e){return Se(e)?Ce(e["__v_raw"]):!(!e||!e["__v_isReactive"])}function Se(e){return!(!e||!e["__v_isReadonly"])}function Ie(e){return!(!e||!e["__v_isShallow"])}function Pe(e){return Ce(e)||Se(e)}function Te(e){const t=e&&e["__v_raw"];return t?Te(t):e}function Ae(e){return Object(i["g"])(e,"__v_skip",!0),e}const Le=e=>Object(i["w"])(e)?xe(e):e,De=e=>Object(i["w"])(e)?Oe(e):e;function ze(e){k&&v&&(e=Te(e),C(e.dep||(e.dep=l())))}function qe(e,t){e=Te(e);const n=e.dep;n&&I(n)}function Ne(e){return!(!e||!0!==e.__v_isRef)}function Me(e){return Be(e,!1)}function Be(e,t){return Ne(e)?e:new Ve(e,t)}class Ve{constructor(e,t){this.__v_isShallow=t,this.dep=void 0,this.__v_isRef=!0,this._rawValue=t?e:Te(e),this._value=t?e:Le(e)}get value(){return ze(this),this._value}set value(e){const t=this.__v_isShallow||Ie(e)||Se(e);e=t?e:Te(e),Object(i["j"])(e,this._rawValue)&&(this._rawValue=e,this._value=t?e:Le(e),qe(this,e))}}function Fe(e){return Ne(e)?e.value:e}const Re={get:(e,t,n)=>Fe(Reflect.get(e,t,n)),set:(e,t,n,i)=>{const o=e[t];return Ne(o)&&!Ne(n)?(o.value=n,!0):Reflect.set(e,t,n,i)}};function Ue(e){return Ce(e)?e:new Proxy(e,Re)}function $e(e){const t=Object(i["o"])(e)?new Array(e.length):{};for(const n in e)t[n]=He(e,n);return t}class We{constructor(e,t,n){this._object=e,this._key=t,this._defaultValue=n,this.__v_isRef=!0}get value(){const e=this._object[this._key];return void 0===e?this._defaultValue:e}set value(e){this._object[this._key]=e}get dep(){return T(Te(this._object),this._key)}}function He(e,t,n){const i=e[t];return Ne(i)?i:new We(e,t,n)}var Ke;class Ge{constructor(e,t,n,i){this._setter=t,this.dep=void 0,this.__v_isRef=!0,this[Ke]=!1,this._dirty=!0,this.effect=new y(e,()=>{this._dirty||(this._dirty=!0,qe(this))}),this.effect.computed=this,this.effect.active=this._cacheable=!i,this["__v_isReadonly"]=n}get value(){const e=Te(this);return ze(e),!e._dirty&&e._cacheable||(e._dirty=!1,e._value=e.effect.run()),e._value}set value(e){this._setter(e)}}function Je(e,t,n=!1){let o,s;const r=Object(i["q"])(e);r?(o=e,s=i["d"]):(o=e.get,s=e.set);const a=new Ge(o,s,r||!s,n);return a}Ke="__v_isReadonly";function Ye(e,t,n,i){let o;try{o=i?e(...i):e()}catch(s){Xe(s,t,n)}return o}function Ze(e,t,n,o){if(Object(i["q"])(e)){const s=Ye(e,t,n,o);return s&&Object(i["z"])(s)&&s.catch(e=>{Xe(e,t,n)}),s}const s=[];for(let i=0;i<e.length;i++)s.push(Ze(e[i],t,n,o));return s}function Xe(e,t,n,i=!0){const o=t?t.vnode:null;if(t){let i=t.parent;const o=t.proxy,s=n;while(i){const t=i.ec;if(t)for(let n=0;n<t.length;n++)if(!1===t[n](e,o,s))return;i=i.parent}const r=t.appContext.config.errorHandler;if(r)return void Ye(r,null,10,[e,o,s])}Qe(e,n,o,i)}function Qe(e,t,n,i=!0){console.error(e)}let et=!1,tt=!1;const nt=[];let it=0;const ot=[];let st=null,rt=0;const at=Promise.resolve();let lt=null;function ct(e){const t=lt||at;return e?t.then(this?e.bind(this):e):t}function ut(e){let t=it+1,n=nt.length;while(t<n){const i=t+n>>>1,o=vt(nt[i]);o<e?t=i+1:n=i}return t}function dt(e){nt.length&&nt.includes(e,et&&e.allowRecurse?it+1:it)||(null==e.id?nt.push(e):nt.splice(ut(e.id),0,e),ht())}function ht(){et||tt||(tt=!0,lt=at.then(_t))}function pt(e){const t=nt.indexOf(e);t>it&&nt.splice(t,1)}function mt(e){Object(i["o"])(e)?ot.push(...e):st&&st.includes(e,e.allowRecurse?rt+1:rt)||ot.push(e),ht()}function ft(e,t=(et?it+1:0)){for(0;t<nt.length;t++){const e=nt[t];e&&e.pre&&(nt.splice(t,1),t--,e())}}function gt(e){if(ot.length){const e=[...new Set(ot)];if(ot.length=0,st)return void st.push(...e);for(st=e,st.sort((e,t)=>vt(e)-vt(t)),rt=0;rt<st.length;rt++)st[rt]();st=null,rt=0}}const vt=e=>null==e.id?1/0:e.id,bt=(e,t)=>{const n=vt(e)-vt(t);if(0===n){if(e.pre&&!t.pre)return-1;if(t.pre&&!e.pre)return 1}return n};function _t(e){tt=!1,et=!0,nt.sort(bt);i["d"];try{for(it=0;it<nt.length;it++){const e=nt[it];e&&!1!==e.active&&Ye(e,null,14)}}finally{it=0,nt.length=0,gt(e),et=!1,lt=null,(nt.length||ot.length)&&_t(e)}}new Set;new Map;function yt(e,t,...n){if(e.isUnmounted)return;const o=e.vnode.props||i["b"];let s=n;const r=t.startsWith("update:"),a=r&&t.slice(7);if(a&&a in o){const e=("modelValue"===a?"model":a)+"Modifiers",{number:t,trim:r}=o[e]||i["b"];r&&(s=n.map(e=>Object(i["F"])(e)?e.trim():e)),t&&(s=n.map(i["J"]))}let l;let c=o[l=Object(i["Q"])(t)]||o[l=Object(i["Q"])(Object(i["e"])(t))];!c&&r&&(c=o[l=Object(i["Q"])(Object(i["l"])(t))]),c&&Ze(c,e,6,s);const u=o[l+"Once"];if(u){if(e.emitted){if(e.emitted[l])return}else e.emitted={};e.emitted[l]=!0,Ze(u,e,6,s)}}function wt(e,t,n=!1){const o=t.emitsCache,s=o.get(e);if(void 0!==s)return s;const r=e.emits;let a={},l=!1;if(!Object(i["q"])(e)){const o=e=>{const n=wt(e,t,!0);n&&(l=!0,Object(i["h"])(a,n))};!n&&t.mixins.length&&t.mixins.forEach(o),e.extends&&o(e.extends),e.mixins&&e.mixins.forEach(o)}return r||l?(Object(i["o"])(r)?r.forEach(e=>a[e]=null):Object(i["h"])(a,r),Object(i["w"])(e)&&o.set(e,a),a):(Object(i["w"])(e)&&o.set(e,null),null)}function kt(e,t){return!(!e||!Object(i["x"])(t))&&(t=t.slice(2).replace(/Once$/,""),Object(i["k"])(e,t[0].toLowerCase()+t.slice(1))||Object(i["k"])(e,Object(i["l"])(t))||Object(i["k"])(e,t))}let xt=null,jt=null;function Ot(e){const t=xt;return xt=e,jt=e&&e.type.__scopeId||null,t}function Et(e){jt=e}function Ct(){jt=null}function St(e,t=xt,n){if(!t)return e;if(e._n)return e;const i=(...n)=>{i._d&&Ni(-1);const o=Ot(t);let s;try{s=e(...n)}finally{Ot(o),i._d&&Ni(1)}return s};return i._n=!0,i._c=!0,i._d=!0,i}function It(e){const{type:t,vnode:n,proxy:o,withProxy:s,props:r,propsOptions:[a],slots:l,attrs:c,emit:u,render:d,renderCache:h,data:p,setupState:m,ctx:f,inheritAttrs:g}=e;let v,b;const _=Ot(e);try{if(4&n.shapeFlag){const e=s||o;v=eo(d.call(e,e,h,r,m,p,f)),b=c}else{const e=t;0,v=eo(e.length>1?e(r,{attrs:c,slots:l,emit:u}):e(r,null)),b=t.props?c:Pt(c)}}catch(w){Ai.length=0,Xe(w,e,1),v=Ki(Pi)}let y=v;if(b&&!1!==g){const e=Object.keys(b),{shapeFlag:t}=y;e.length&&7&t&&(a&&e.some(i["v"])&&(b=Tt(b,a)),y=Yi(y,b))}return n.dirs&&(y=Yi(y),y.dirs=y.dirs?y.dirs.concat(n.dirs):n.dirs),n.transition&&(y.transition=n.transition),v=y,Ot(_),v}const Pt=e=>{let t;for(const n in e)("class"===n||"style"===n||Object(i["x"])(n))&&((t||(t={}))[n]=e[n]);return t},Tt=(e,t)=>{const n={};for(const o in e)Object(i["v"])(o)&&o.slice(9)in t||(n[o]=e[o]);return n};function At(e,t,n){const{props:i,children:o,component:s}=e,{props:r,children:a,patchFlag:l}=t,c=s.emitsOptions;if(t.dirs||t.transition)return!0;if(!(n&&l>=0))return!(!o&&!a||a&&a.$stable)||i!==r&&(i?!r||Lt(i,r,c):!!r);if(1024&l)return!0;if(16&l)return i?Lt(i,r,c):!!r;if(8&l){const e=t.dynamicProps;for(let t=0;t<e.length;t++){const n=e[t];if(r[n]!==i[n]&&!kt(c,n))return!0}}return!1}function Lt(e,t,n){const i=Object.keys(t);if(i.length!==Object.keys(e).length)return!0;for(let o=0;o<i.length;o++){const s=i[o];if(t[s]!==e[s]&&!kt(n,s))return!0}return!1}function Dt({vnode:e,parent:t},n){while(t&&t.subTree===e)(e=t.vnode).el=n,t=t.parent}const zt=e=>e.__isSuspense;function qt(e,t){t&&t.pendingBranch?Object(i["o"])(e)?t.effects.push(...e):t.effects.push(e):mt(e)}function Nt(e,t){if(lo){let n=lo.provides;const i=lo.parent&&lo.parent.provides;i===n&&(n=lo.provides=Object.create(i)),n[e]=t}else 0}function Mt(e,t,n=!1){const o=lo||xt;if(o){const s=null==o.parent?o.vnode.appContext&&o.vnode.appContext.provides:o.parent.provides;if(s&&e in s)return s[e];if(arguments.length>1)return n&&Object(i["q"])(t)?t.call(o.proxy):t}else 0}function Bt(e,t){return Ut(e,null,t)}function Vt(e,t){return Ut(e,null,{flush:"post"})}const Ft={};function Rt(e,t,n){return Ut(e,t,n)}function Ut(e,t,{immediate:n,deep:o,flush:s,onTrack:r,onTrigger:l}=i["b"]){const c=a()===(null===lo||void 0===lo?void 0:lo.scope)?lo:null;let u,d,h=!1,p=!1;if(Ne(e)?(u=()=>e.value,h=Ie(e)):Ce(e)?(u=()=>e,o=!0):Object(i["o"])(e)?(p=!0,h=e.some(e=>Ce(e)||Ie(e)),u=()=>e.map(e=>Ne(e)?e.value:Ce(e)?Ht(e):Object(i["q"])(e)?Ye(e,c,2):void 0)):u=Object(i["q"])(e)?t?()=>Ye(e,c,2):()=>{if(!c||!c.isUnmounted)return d&&d(),Ze(e,c,3,[f])}:i["d"],t&&o){const e=u;u=()=>Ht(e())}let m,f=e=>{d=_.onStop=()=>{Ye(e,c,4)}};if(go){if(f=i["d"],t?n&&Ze(t,c,3,[u(),p?[]:void 0,f]):u(),"sync"!==s)return i["d"];{const e=To();m=e.__watcherHandles||(e.__watcherHandles=[])}}let g=p?new Array(e.length).fill(Ft):Ft;const v=()=>{if(_.active)if(t){const e=_.run();(o||h||(p?e.some((e,t)=>Object(i["j"])(e,g[t])):Object(i["j"])(e,g)))&&(d&&d(),Ze(t,c,3,[e,g===Ft?void 0:p&&g[0]===Ft?[]:g,f]),g=e)}else _.run()};let b;v.allowRecurse=!!t,"sync"===s?b=v:"post"===s?b=()=>wi(v,c&&c.suspense):(v.pre=!0,c&&(v.id=c.uid),b=()=>dt(v));const _=new y(u,b);t?n?v():g=_.run():"post"===s?wi(_.run.bind(_),c&&c.suspense):_.run();const w=()=>{_.stop(),c&&c.scope&&Object(i["O"])(c.scope.effects,_)};return m&&m.push(w),w}function $t(e,t,n){const o=this.proxy,s=Object(i["F"])(e)?e.includes(".")?Wt(o,e):()=>o[e]:e.bind(o,o);let r;Object(i["q"])(t)?r=t:(r=t.handler,n=t);const a=lo;uo(this);const l=Ut(s,r.bind(o),n);return a?uo(a):ho(),l}function Wt(e,t){const n=t.split(".");return()=>{let t=e;for(let e=0;e<n.length&&t;e++)t=t[n[e]];return t}}function Ht(e,t){if(!Object(i["w"])(e)||e["__v_skip"])return e;if(t=t||new Set,t.has(e))return e;if(t.add(e),Ne(e))Ht(e.value,t);else if(Object(i["o"])(e))for(let n=0;n<e.length;n++)Ht(e[n],t);else if(Object(i["D"])(e)||Object(i["u"])(e))e.forEach(e=>{Ht(e,t)});else if(Object(i["y"])(e))for(const n in e)Ht(e[n],t);return e}function Kt(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return vn(()=>{e.isMounted=!0}),yn(()=>{e.isUnmounting=!0}),e}const Gt=[Function,Array],Jt={name:"BaseTransition",props:{mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:Gt,onEnter:Gt,onAfterEnter:Gt,onEnterCancelled:Gt,onBeforeLeave:Gt,onLeave:Gt,onAfterLeave:Gt,onLeaveCancelled:Gt,onBeforeAppear:Gt,onAppear:Gt,onAfterAppear:Gt,onAppearCancelled:Gt},setup(e,{slots:t}){const n=co(),i=Kt();let o;return()=>{const s=t.default&&nn(t.default(),!0);if(!s||!s.length)return;let r=s[0];if(s.length>1){let e=!1;for(const t of s)if(t.type!==Pi){0,r=t,e=!0;break}}const a=Te(e),{mode:l}=a;if(i.isLeaving)return Qt(r);const c=en(r);if(!c)return Qt(r);const u=Xt(c,a,i,n);tn(c,u);const d=n.subTree,h=d&&en(d);let p=!1;const{getTransitionKey:m}=c.type;if(m){const e=m();void 0===o?o=e:e!==o&&(o=e,p=!0)}if(h&&h.type!==Pi&&(!Ri(c,h)||p)){const e=Xt(h,a,i,n);if(tn(h,e),"out-in"===l)return i.isLeaving=!0,e.afterLeave=()=>{i.isLeaving=!1,!1!==n.update.active&&n.update()},Qt(r);"in-out"===l&&c.type!==Pi&&(e.delayLeave=(e,t,n)=>{const o=Zt(i,h);o[String(h.key)]=h,e._leaveCb=()=>{t(),e._leaveCb=void 0,delete u.delayedLeave},u.delayedLeave=n})}return r}}},Yt=Jt;function Zt(e,t){const{leavingVNodes:n}=e;let i=n.get(t.type);return i||(i=Object.create(null),n.set(t.type,i)),i}function Xt(e,t,n,o){const{appear:s,mode:r,persisted:a=!1,onBeforeEnter:l,onEnter:c,onAfterEnter:u,onEnterCancelled:d,onBeforeLeave:h,onLeave:p,onAfterLeave:m,onLeaveCancelled:f,onBeforeAppear:g,onAppear:v,onAfterAppear:b,onAppearCancelled:_}=t,y=String(e.key),w=Zt(n,e),k=(e,t)=>{e&&Ze(e,o,9,t)},x=(e,t)=>{const n=t[1];k(e,t),Object(i["o"])(e)?e.every(e=>e.length<=1)&&n():e.length<=1&&n()},j={mode:r,persisted:a,beforeEnter(t){let i=l;if(!n.isMounted){if(!s)return;i=g||l}t._leaveCb&&t._leaveCb(!0);const o=w[y];o&&Ri(e,o)&&o.el._leaveCb&&o.el._leaveCb(),k(i,[t])},enter(e){let t=c,i=u,o=d;if(!n.isMounted){if(!s)return;t=v||c,i=b||u,o=_||d}let r=!1;const a=e._enterCb=t=>{r||(r=!0,k(t?o:i,[e]),j.delayedLeave&&j.delayedLeave(),e._enterCb=void 0)};t?x(t,[e,a]):a()},leave(t,i){const o=String(e.key);if(t._enterCb&&t._enterCb(!0),n.isUnmounting)return i();k(h,[t]);let s=!1;const r=t._leaveCb=n=>{s||(s=!0,i(),k(n?f:m,[t]),t._leaveCb=void 0,w[o]===e&&delete w[o])};w[o]=e,p?x(p,[t,r]):r()},clone(e){return Xt(e,t,n,o)}};return j}function Qt(e){if(rn(e))return e=Yi(e),e.children=null,e}function en(e){return rn(e)?e.children?e.children[0]:void 0:e}function tn(e,t){6&e.shapeFlag&&e.component?tn(e.component.subTree,t):128&e.shapeFlag?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function nn(e,t=!1,n){let i=[],o=0;for(let s=0;s<e.length;s++){let r=e[s];const a=null==n?r.key:String(n)+String(null!=r.key?r.key:s);r.type===Si?(128&r.patchFlag&&o++,i=i.concat(nn(r.children,t,a))):(t||r.type!==Pi)&&i.push(null!=a?Yi(r,{key:a}):r)}if(o>1)for(let s=0;s<i.length;s++)i[s].patchFlag=-2;return i}function on(e){return Object(i["q"])(e)?{setup:e,name:e.name}:e}const sn=e=>!!e.type.__asyncLoader;const rn=e=>e.type.__isKeepAlive;RegExp,RegExp;function an(e,t){return Object(i["o"])(e)?e.some(e=>an(e,t)):Object(i["F"])(e)?e.split(",").includes(t):!!Object(i["A"])(e)&&e.test(t)}function ln(e,t){un(e,"a",t)}function cn(e,t){un(e,"da",t)}function un(e,t,n=lo){const i=e.__wdc||(e.__wdc=()=>{let t=n;while(t){if(t.isDeactivated)return;t=t.parent}return e()});if(mn(t,i,n),n){let e=n.parent;while(e&&e.parent)rn(e.parent.vnode)&&dn(i,t,n,e),e=e.parent}}function dn(e,t,n,o){const s=mn(t,e,o,!0);wn(()=>{Object(i["O"])(o[t],s)},n)}function hn(e){e.shapeFlag&=-257,e.shapeFlag&=-513}function pn(e){return 128&e.shapeFlag?e.ssContent:e}function mn(e,t,n=lo,i=!1){if(n){const o=n[e]||(n[e]=[]),s=t.__weh||(t.__weh=(...i)=>{if(n.isUnmounted)return;j(),uo(n);const o=Ze(t,n,e,i);return ho(),O(),o});return i?o.unshift(s):o.push(s),s}}const fn=e=>(t,n=lo)=>(!go||"sp"===e)&&mn(e,(...e)=>t(...e),n),gn=fn("bm"),vn=fn("m"),bn=fn("bu"),_n=fn("u"),yn=fn("bum"),wn=fn("um"),kn=fn("sp"),xn=fn("rtg"),jn=fn("rtc");function On(e,t=lo){mn("ec",e,t)}function En(e,t){const n=xt;if(null===n)return e;const o=xo(n)||n.proxy,s=e.dirs||(e.dirs=[]);for(let r=0;r<t.length;r++){let[e,n,a,l=i["b"]]=t[r];e&&(Object(i["q"])(e)&&(e={mounted:e,updated:e}),e.deep&&Ht(n),s.push({dir:e,instance:o,value:n,oldValue:void 0,arg:a,modifiers:l}))}return e}function Cn(e,t,n,i){const o=e.dirs,s=t&&t.dirs;for(let r=0;r<o.length;r++){const a=o[r];s&&(a.oldValue=s[r].value);let l=a.dir[i];l&&(j(),Ze(l,n,8,[e.el,a,e,t]),O())}}const Sn="components";function In(e,t){return Tn(Sn,e,!0,t)||e}const Pn=Symbol();function Tn(e,t,n=!0,o=!1){const s=xt||lo;if(s){const n=s.type;if(e===Sn){const e=jo(n,!1);if(e&&(e===t||e===Object(i["e"])(t)||e===Object(i["f"])(Object(i["e"])(t))))return n}const r=An(s[e]||n[e],t)||An(s.appContext[e],t);return!r&&o?n:r}}function An(e,t){return e&&(e[t]||e[Object(i["e"])(t)]||e[Object(i["f"])(Object(i["e"])(t))])}function Ln(e,t,n,o){let s;const r=n&&n[o];if(Object(i["o"])(e)||Object(i["F"])(e)){s=new Array(e.length);for(let n=0,i=e.length;n<i;n++)s[n]=t(e[n],n,void 0,r&&r[n])}else if("number"===typeof e){0,s=new Array(e);for(let n=0;n<e;n++)s[n]=t(n+1,n,void 0,r&&r[n])}else if(Object(i["w"])(e))if(e[Symbol.iterator])s=Array.from(e,(e,n)=>t(e,n,void 0,r&&r[n]));else{const n=Object.keys(e);s=new Array(n.length);for(let i=0,o=n.length;i<o;i++){const o=n[i];s[i]=t(e[o],o,i,r&&r[i])}}else s=[];return n&&(n[o]=s),s}function Dn(e,t,n={},i,o){if(xt.isCE||xt.parent&&sn(xt.parent)&&xt.parent.isCE)return"default"!==t&&(n.name=t),Ki("slot",n,i&&i());let s=e[t];s&&s._c&&(s._d=!1),Di();const r=s&&zn(s(n)),a=Vi(Si,{key:n.key||r&&r.key||"_"+t},r||(i?i():[]),r&&1===e._?64:-2);return!o&&a.scopeId&&(a.slotScopeIds=[a.scopeId+"-s"]),s&&s._c&&(s._d=!0),a}function zn(e){return e.some(e=>!Fi(e)||e.type!==Pi&&!(e.type===Si&&!zn(e.children)))?e:null}const qn=e=>e?po(e)?xo(e)||e.proxy:qn(e.parent):null,Nn=Object(i["h"])(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>qn(e.parent),$root:e=>qn(e.root),$emit:e=>e.emit,$options:e=>Wn(e),$forceUpdate:e=>e.f||(e.f=()=>dt(e.update)),$nextTick:e=>e.n||(e.n=ct.bind(e.proxy)),$watch:e=>$t.bind(e)}),Mn=(e,t)=>e!==i["b"]&&!e.__isScriptSetup&&Object(i["k"])(e,t),Bn={get({_:e},t){const{ctx:n,setupState:o,data:s,props:r,accessCache:a,type:l,appContext:c}=e;let u;if("$"!==t[0]){const l=a[t];if(void 0!==l)switch(l){case 1:return o[t];case 2:return s[t];case 4:return n[t];case 3:return r[t]}else{if(Mn(o,t))return a[t]=1,o[t];if(s!==i["b"]&&Object(i["k"])(s,t))return a[t]=2,s[t];if((u=e.propsOptions[0])&&Object(i["k"])(u,t))return a[t]=3,r[t];if(n!==i["b"]&&Object(i["k"])(n,t))return a[t]=4,n[t];Vn&&(a[t]=0)}}const d=Nn[t];let h,p;return d?("$attrs"===t&&E(e,"get",t),d(e)):(h=l.__cssModules)&&(h=h[t])?h:n!==i["b"]&&Object(i["k"])(n,t)?(a[t]=4,n[t]):(p=c.config.globalProperties,Object(i["k"])(p,t)?p[t]:void 0)},set({_:e},t,n){const{data:o,setupState:s,ctx:r}=e;return Mn(s,t)?(s[t]=n,!0):o!==i["b"]&&Object(i["k"])(o,t)?(o[t]=n,!0):!Object(i["k"])(e.props,t)&&(("$"!==t[0]||!(t.slice(1)in e))&&(r[t]=n,!0))},has({_:{data:e,setupState:t,accessCache:n,ctx:o,appContext:s,propsOptions:r}},a){let l;return!!n[a]||e!==i["b"]&&Object(i["k"])(e,a)||Mn(t,a)||(l=r[0])&&Object(i["k"])(l,a)||Object(i["k"])(o,a)||Object(i["k"])(Nn,a)||Object(i["k"])(s.config.globalProperties,a)},defineProperty(e,t,n){return null!=n.get?e._.accessCache[t]=0:Object(i["k"])(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};let Vn=!0;function Fn(e){const t=Wn(e),n=e.proxy,o=e.ctx;Vn=!1,t.beforeCreate&&Un(t.beforeCreate,e,"bc");const{data:s,computed:r,methods:a,watch:l,provide:c,inject:u,created:d,beforeMount:h,mounted:p,beforeUpdate:m,updated:f,activated:g,deactivated:v,beforeDestroy:b,beforeUnmount:_,destroyed:y,unmounted:w,render:k,renderTracked:x,renderTriggered:j,errorCaptured:O,serverPrefetch:E,expose:C,inheritAttrs:S,components:I,directives:P,filters:T}=t,A=null;if(u&&Rn(u,o,A,e.appContext.config.unwrapInjectedRef),a)for(const D in a){const e=a[D];Object(i["q"])(e)&&(o[D]=e.bind(n))}if(s){0;const t=s.call(n,n);0,Object(i["w"])(t)&&(e.data=xe(t))}if(Vn=!0,r)for(const D in r){const e=r[D],t=Object(i["q"])(e)?e.bind(n,n):Object(i["q"])(e.get)?e.get.bind(n,n):i["d"];0;const s=!Object(i["q"])(e)&&Object(i["q"])(e.set)?e.set.bind(n):i["d"],a=Eo({get:t,set:s});Object.defineProperty(o,D,{enumerable:!0,configurable:!0,get:()=>a.value,set:e=>a.value=e})}if(l)for(const i in l)$n(l[i],o,n,i);if(c){const e=Object(i["q"])(c)?c.call(n):c;Reflect.ownKeys(e).forEach(t=>{Nt(t,e[t])})}function L(e,t){Object(i["o"])(t)?t.forEach(t=>e(t.bind(n))):t&&e(t.bind(n))}if(d&&Un(d,e,"c"),L(gn,h),L(vn,p),L(bn,m),L(_n,f),L(ln,g),L(cn,v),L(On,O),L(jn,x),L(xn,j),L(yn,_),L(wn,w),L(kn,E),Object(i["o"])(C))if(C.length){const t=e.exposed||(e.exposed={});C.forEach(e=>{Object.defineProperty(t,e,{get:()=>n[e],set:t=>n[e]=t})})}else e.exposed||(e.exposed={});k&&e.render===i["d"]&&(e.render=k),null!=S&&(e.inheritAttrs=S),I&&(e.components=I),P&&(e.directives=P)}function Rn(e,t,n=i["d"],o=!1){Object(i["o"])(e)&&(e=Yn(e));for(const s in e){const n=e[s];let r;r=Object(i["w"])(n)?"default"in n?Mt(n.from||s,n.default,!0):Mt(n.from||s):Mt(n),Ne(r)&&o?Object.defineProperty(t,s,{enumerable:!0,configurable:!0,get:()=>r.value,set:e=>r.value=e}):t[s]=r}}function Un(e,t,n){Ze(Object(i["o"])(e)?e.map(e=>e.bind(t.proxy)):e.bind(t.proxy),t,n)}function $n(e,t,n,o){const s=o.includes(".")?Wt(n,o):()=>n[o];if(Object(i["F"])(e)){const n=t[e];Object(i["q"])(n)&&Rt(s,n)}else if(Object(i["q"])(e))Rt(s,e.bind(n));else if(Object(i["w"])(e))if(Object(i["o"])(e))e.forEach(e=>$n(e,t,n,o));else{const o=Object(i["q"])(e.handler)?e.handler.bind(n):t[e.handler];Object(i["q"])(o)&&Rt(s,o,e)}else 0}function Wn(e){const t=e.type,{mixins:n,extends:o}=t,{mixins:s,optionsCache:r,config:{optionMergeStrategies:a}}=e.appContext,l=r.get(t);let c;return l?c=l:s.length||n||o?(c={},s.length&&s.forEach(e=>Hn(c,e,a,!0)),Hn(c,t,a)):c=t,Object(i["w"])(t)&&r.set(t,c),c}function Hn(e,t,n,i=!1){const{mixins:o,extends:s}=t;s&&Hn(e,s,n,!0),o&&o.forEach(t=>Hn(e,t,n,!0));for(const r in t)if(i&&"expose"===r);else{const i=Kn[r]||n&&n[r];e[r]=i?i(e[r],t[r]):t[r]}return e}const Kn={data:Gn,props:Xn,emits:Xn,methods:Xn,computed:Xn,beforeCreate:Zn,created:Zn,beforeMount:Zn,mounted:Zn,beforeUpdate:Zn,updated:Zn,beforeDestroy:Zn,beforeUnmount:Zn,destroyed:Zn,unmounted:Zn,activated:Zn,deactivated:Zn,errorCaptured:Zn,serverPrefetch:Zn,components:Xn,directives:Xn,watch:Qn,provide:Gn,inject:Jn};function Gn(e,t){return t?e?function(){return Object(i["h"])(Object(i["q"])(e)?e.call(this,this):e,Object(i["q"])(t)?t.call(this,this):t)}:t:e}function Jn(e,t){return Xn(Yn(e),Yn(t))}function Yn(e){if(Object(i["o"])(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function Zn(e,t){return e?[...new Set([].concat(e,t))]:t}function Xn(e,t){return e?Object(i["h"])(Object(i["h"])(Object.create(null),e),t):t}function Qn(e,t){if(!e)return t;if(!t)return e;const n=Object(i["h"])(Object.create(null),e);for(const i in t)n[i]=Zn(e[i],t[i]);return n}function ei(e,t,n,o=!1){const s={},r={};Object(i["g"])(r,Ui,1),e.propsDefaults=Object.create(null),ni(e,t,s,r);for(const i in e.propsOptions[0])i in s||(s[i]=void 0);n?e.props=o?s:je(s):e.type.props?e.props=s:e.props=r,e.attrs=r}function ti(e,t,n,o){const{props:s,attrs:r,vnode:{patchFlag:a}}=e,l=Te(s),[c]=e.propsOptions;let u=!1;if(!(o||a>0)||16&a){let o;ni(e,t,s,r)&&(u=!0);for(const r in l)t&&(Object(i["k"])(t,r)||(o=Object(i["l"])(r))!==r&&Object(i["k"])(t,o))||(c?!n||void 0===n[r]&&void 0===n[o]||(s[r]=ii(c,l,r,void 0,e,!0)):delete s[r]);if(r!==l)for(const e in r)t&&Object(i["k"])(t,e)||(delete r[e],u=!0)}else if(8&a){const n=e.vnode.dynamicProps;for(let o=0;o<n.length;o++){let a=n[o];if(kt(e.emitsOptions,a))continue;const d=t[a];if(c)if(Object(i["k"])(r,a))d!==r[a]&&(r[a]=d,u=!0);else{const t=Object(i["e"])(a);s[t]=ii(c,l,t,d,e,!1)}else d!==r[a]&&(r[a]=d,u=!0)}}u&&S(e,"set","$attrs")}function ni(e,t,n,o){const[s,r]=e.propsOptions;let a,l=!1;if(t)for(let c in t){if(Object(i["B"])(c))continue;const u=t[c];let d;s&&Object(i["k"])(s,d=Object(i["e"])(c))?r&&r.includes(d)?(a||(a={}))[d]=u:n[d]=u:kt(e.emitsOptions,c)||c in o&&u===o[c]||(o[c]=u,l=!0)}if(r){const t=Te(n),o=a||i["b"];for(let a=0;a<r.length;a++){const l=r[a];n[l]=ii(s,t,l,o[l],e,!Object(i["k"])(o,l))}}return l}function ii(e,t,n,o,s,r){const a=e[n];if(null!=a){const e=Object(i["k"])(a,"default");if(e&&void 0===o){const e=a.default;if(a.type!==Function&&Object(i["q"])(e)){const{propsDefaults:i}=s;n in i?o=i[n]:(uo(s),o=i[n]=e.call(null,t),ho())}else o=e}a[0]&&(r&&!e?o=!1:!a[1]||""!==o&&o!==Object(i["l"])(n)||(o=!0))}return o}function oi(e,t,n=!1){const o=t.propsCache,s=o.get(e);if(s)return s;const r=e.props,a={},l=[];let c=!1;if(!Object(i["q"])(e)){const o=e=>{c=!0;const[n,o]=oi(e,t,!0);Object(i["h"])(a,n),o&&l.push(...o)};!n&&t.mixins.length&&t.mixins.forEach(o),e.extends&&o(e.extends),e.mixins&&e.mixins.forEach(o)}if(!r&&!c)return Object(i["w"])(e)&&o.set(e,i["a"]),i["a"];if(Object(i["o"])(r))for(let d=0;d<r.length;d++){0;const e=Object(i["e"])(r[d]);si(e)&&(a[e]=i["b"])}else if(r){0;for(const e in r){const t=Object(i["e"])(e);if(si(t)){const n=r[e],o=a[t]=Object(i["o"])(n)||Object(i["q"])(n)?{type:n}:Object.assign({},n);if(o){const e=li(Boolean,o.type),n=li(String,o.type);o[0]=e>-1,o[1]=n<0||e<n,(e>-1||Object(i["k"])(o,"default"))&&l.push(t)}}}}const u=[a,l];return Object(i["w"])(e)&&o.set(e,u),u}function si(e){return"$"!==e[0]}function ri(e){const t=e&&e.toString().match(/^\s*(function|class) (\w+)/);return t?t[2]:null===e?"null":""}function ai(e,t){return ri(e)===ri(t)}function li(e,t){return Object(i["o"])(t)?t.findIndex(t=>ai(t,e)):Object(i["q"])(t)&&ai(t,e)?0:-1}const ci=e=>"_"===e[0]||"$stable"===e,ui=e=>Object(i["o"])(e)?e.map(eo):[eo(e)],di=(e,t,n)=>{if(t._n)return t;const i=St((...e)=>ui(t(...e)),n);return i._c=!1,i},hi=(e,t,n)=>{const o=e._ctx;for(const s in e){if(ci(s))continue;const n=e[s];if(Object(i["q"])(n))t[s]=di(s,n,o);else if(null!=n){0;const e=ui(n);t[s]=()=>e}}},pi=(e,t)=>{const n=ui(t);e.slots.default=()=>n},mi=(e,t)=>{if(32&e.vnode.shapeFlag){const n=t._;n?(e.slots=Te(t),Object(i["g"])(t,"_",n)):hi(t,e.slots={})}else e.slots={},t&&pi(e,t);Object(i["g"])(e.slots,Ui,1)},fi=(e,t,n)=>{const{vnode:o,slots:s}=e;let r=!0,a=i["b"];if(32&o.shapeFlag){const e=t._;e?n&&1===e?r=!1:(Object(i["h"])(s,t),n||1!==e||delete s._):(r=!t.$stable,hi(t,s)),a=t}else t&&(pi(e,t),a={default:1});if(r)for(const i in s)ci(i)||i in a||delete s[i]};function gi(){return{app:null,config:{isNativeTag:i["c"],performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let vi=0;function bi(e,t){return function(n,o=null){Object(i["q"])(n)||(n=Object.assign({},n)),null==o||Object(i["w"])(o)||(o=null);const s=gi(),r=new Set;let a=!1;const l=s.app={_uid:vi++,_component:n,_props:o,_container:null,_context:s,_instance:null,version:Ao,get config(){return s.config},set config(e){0},use(e,...t){return r.has(e)||(e&&Object(i["q"])(e.install)?(r.add(e),e.install(l,...t)):Object(i["q"])(e)&&(r.add(e),e(l,...t))),l},mixin(e){return s.mixins.includes(e)||s.mixins.push(e),l},component(e,t){return t?(s.components[e]=t,l):s.components[e]},directive(e,t){return t?(s.directives[e]=t,l):s.directives[e]},mount(i,r,c){if(!a){0;const u=Ki(n,o);return u.appContext=s,r&&t?t(u,i):e(u,i,c),a=!0,l._container=i,i.__vue_app__=l,xo(u.component)||u.component.proxy}},unmount(){a&&(e(null,l._container),delete l._container.__vue_app__)},provide(e,t){return s.provides[e]=t,l}};return l}}function _i(e,t,n,o,s=!1){if(Object(i["o"])(e))return void e.forEach((e,r)=>_i(e,t&&(Object(i["o"])(t)?t[r]:t),n,o,s));if(sn(o)&&!s)return;const r=4&o.shapeFlag?xo(o.component)||o.component.proxy:o.el,a=s?null:r,{i:l,r:c}=e;const u=t&&t.r,d=l.refs===i["b"]?l.refs={}:l.refs,h=l.setupState;if(null!=u&&u!==c&&(Object(i["F"])(u)?(d[u]=null,Object(i["k"])(h,u)&&(h[u]=null)):Ne(u)&&(u.value=null)),Object(i["q"])(c))Ye(c,l,12,[a,d]);else{const t=Object(i["F"])(c),o=Ne(c);if(t||o){const l=()=>{if(e.f){const n=t?Object(i["k"])(h,c)?h[c]:d[c]:c.value;s?Object(i["o"])(n)&&Object(i["O"])(n,r):Object(i["o"])(n)?n.includes(r)||n.push(r):t?(d[c]=[r],Object(i["k"])(h,c)&&(h[c]=d[c])):(c.value=[r],e.k&&(d[e.k]=c.value))}else t?(d[c]=a,Object(i["k"])(h,c)&&(h[c]=a)):o&&(c.value=a,e.k&&(d[e.k]=a))};a?(l.id=-1,wi(l,n)):l()}else 0}}function yi(){}const wi=qt;function ki(e){return xi(e)}function xi(e,t){yi();const n=Object(i["i"])();n.__VUE__=!0;const{insert:o,remove:s,patchProp:r,createElement:a,createText:l,createComment:c,setText:u,setElementText:d,parentNode:h,nextSibling:p,setScopeId:m=i["d"],insertStaticContent:f}=e,g=(e,t,n,i=null,o=null,s=null,r=!1,a=null,l=!!t.dynamicChildren)=>{if(e===t)return;e&&!Ri(e,t)&&(i=K(e),R(e,o,s,!0),e=null),-2===t.patchFlag&&(l=!1,t.dynamicChildren=null);const{type:c,ref:u,shapeFlag:d}=t;switch(c){case Ii:v(e,t,n,i);break;case Pi:b(e,t,n,i);break;case Ti:null==e&&_(t,n,i,r);break;case Si:A(e,t,n,i,o,s,r,a,l);break;default:1&d?x(e,t,n,i,o,s,r,a,l):6&d?L(e,t,n,i,o,s,r,a,l):(64&d||128&d)&&c.process(e,t,n,i,o,s,r,a,l,J)}null!=u&&o&&_i(u,e&&e.ref,s,t||e,!t)},v=(e,t,n,i)=>{if(null==e)o(t.el=l(t.children),n,i);else{const n=t.el=e.el;t.children!==e.children&&u(n,t.children)}},b=(e,t,n,i)=>{null==e?o(t.el=c(t.children||""),n,i):t.el=e.el},_=(e,t,n,i)=>{[e.el,e.anchor]=f(e.children,t,n,i,e.el,e.anchor)},w=({el:e,anchor:t},n,i)=>{let s;while(e&&e!==t)s=p(e),o(e,n,i),e=s;o(t,n,i)},k=({el:e,anchor:t})=>{let n;while(e&&e!==t)n=p(e),s(e),e=n;s(t)},x=(e,t,n,i,o,s,r,a,l)=>{r=r||"svg"===t.type,null==e?E(t,n,i,o,s,r,a,l):I(e,t,o,s,r,a,l)},E=(e,t,n,s,l,c,u,h)=>{let p,m;const{type:f,props:g,shapeFlag:v,transition:b,dirs:_}=e;if(p=e.el=a(e.type,c,g&&g.is,g),8&v?d(p,e.children):16&v&&S(e.children,p,null,s,l,c&&"foreignObject"!==f,u,h),_&&Cn(e,null,s,"created"),C(p,e,e.scopeId,u,s),g){for(const t in g)"value"===t||Object(i["B"])(t)||r(p,t,null,g[t],c,e.children,s,l,H);"value"in g&&r(p,"value",null,g.value),(m=g.onVnodeBeforeMount)&&oo(m,s,e)}_&&Cn(e,null,s,"beforeMount");const y=(!l||l&&!l.pendingBranch)&&b&&!b.persisted;y&&b.beforeEnter(p),o(p,t,n),((m=g&&g.onVnodeMounted)||y||_)&&wi(()=>{m&&oo(m,s,e),y&&b.enter(p),_&&Cn(e,null,s,"mounted")},l)},C=(e,t,n,i,o)=>{if(n&&m(e,n),i)for(let s=0;s<i.length;s++)m(e,i[s]);if(o){let n=o.subTree;if(t===n){const t=o.vnode;C(e,t,t.scopeId,t.slotScopeIds,o.parent)}}},S=(e,t,n,i,o,s,r,a,l=0)=>{for(let c=l;c<e.length;c++){const l=e[c]=a?to(e[c]):eo(e[c]);g(null,l,t,n,i,o,s,r,a)}},I=(e,t,n,o,s,a,l)=>{const c=t.el=e.el;let{patchFlag:u,dynamicChildren:h,dirs:p}=t;u|=16&e.patchFlag;const m=e.props||i["b"],f=t.props||i["b"];let g;n&&ji(n,!1),(g=f.onVnodeBeforeUpdate)&&oo(g,n,t,e),p&&Cn(t,e,n,"beforeUpdate"),n&&ji(n,!0);const v=s&&"foreignObject"!==t.type;if(h?P(e.dynamicChildren,h,c,n,o,v,a):l||M(e,t,c,null,n,o,v,a,!1),u>0){if(16&u)T(c,t,m,f,n,o,s);else if(2&u&&m.class!==f.class&&r(c,"class",null,f.class,s),4&u&&r(c,"style",m.style,f.style,s),8&u){const i=t.dynamicProps;for(let t=0;t<i.length;t++){const a=i[t],l=m[a],u=f[a];u===l&&"value"!==a||r(c,a,l,u,s,e.children,n,o,H)}}1&u&&e.children!==t.children&&d(c,t.children)}else l||null!=h||T(c,t,m,f,n,o,s);((g=f.onVnodeUpdated)||p)&&wi(()=>{g&&oo(g,n,t,e),p&&Cn(t,e,n,"updated")},o)},P=(e,t,n,i,o,s,r)=>{for(let a=0;a<t.length;a++){const l=e[a],c=t[a],u=l.el&&(l.type===Si||!Ri(l,c)||70&l.shapeFlag)?h(l.el):n;g(l,c,u,null,i,o,s,r,!0)}},T=(e,t,n,o,s,a,l)=>{if(n!==o){if(n!==i["b"])for(const c in n)Object(i["B"])(c)||c in o||r(e,c,n[c],null,l,t.children,s,a,H);for(const c in o){if(Object(i["B"])(c))continue;const u=o[c],d=n[c];u!==d&&"value"!==c&&r(e,c,d,u,l,t.children,s,a,H)}"value"in o&&r(e,"value",n.value,o.value)}},A=(e,t,n,i,s,r,a,c,u)=>{const d=t.el=e?e.el:l(""),h=t.anchor=e?e.anchor:l("");let{patchFlag:p,dynamicChildren:m,slotScopeIds:f}=t;f&&(c=c?c.concat(f):f),null==e?(o(d,n,i),o(h,n,i),S(t.children,n,h,s,r,a,c,u)):p>0&&64&p&&m&&e.dynamicChildren?(P(e.dynamicChildren,m,n,s,r,a,c),(null!=t.key||s&&t===s.subTree)&&Oi(e,t,!0)):M(e,t,n,h,s,r,a,c,u)},L=(e,t,n,i,o,s,r,a,l)=>{t.slotScopeIds=a,null==e?512&t.shapeFlag?o.ctx.activate(t,n,i,r,l):D(t,n,i,o,s,r,l):z(e,t,l)},D=(e,t,n,i,o,s,r)=>{const a=e.component=ao(e,i,o);if(rn(e)&&(a.ctx.renderer=J),vo(a),a.asyncDep){if(o&&o.registerDep(a,q),!e.el){const e=a.subTree=Ki(Pi);b(null,e,t,n)}}else q(a,e,t,n,o,s,r)},z=(e,t,n)=>{const i=t.component=e.component;if(At(e,t,n)){if(i.asyncDep&&!i.asyncResolved)return void N(i,t,n);i.next=t,pt(i.update),i.update()}else t.el=e.el,i.vnode=t},q=(e,t,n,o,s,r,a)=>{const l=()=>{if(e.isMounted){let t,{next:n,bu:o,u:l,parent:c,vnode:u}=e,d=n;0,ji(e,!1),n?(n.el=u.el,N(e,n,a)):n=u,o&&Object(i["n"])(o),(t=n.props&&n.props.onVnodeBeforeUpdate)&&oo(t,c,n,u),ji(e,!0);const p=It(e);0;const m=e.subTree;e.subTree=p,g(m,p,h(m.el),K(m),e,s,r),n.el=p.el,null===d&&Dt(e,p.el),l&&wi(l,s),(t=n.props&&n.props.onVnodeUpdated)&&wi(()=>oo(t,c,n,u),s)}else{let a;const{el:l,props:c}=t,{bm:u,m:d,parent:h}=e,p=sn(t);if(ji(e,!1),u&&Object(i["n"])(u),!p&&(a=c&&c.onVnodeBeforeMount)&&oo(a,h,t),ji(e,!0),l&&Z){const n=()=>{e.subTree=It(e),Z(l,e.subTree,e,s,null)};p?t.type.__asyncLoader().then(()=>!e.isUnmounted&&n()):n()}else{0;const i=e.subTree=It(e);0,g(null,i,n,o,e,s,r),t.el=i.el}if(d&&wi(d,s),!p&&(a=c&&c.onVnodeMounted)){const e=t;wi(()=>oo(a,h,e),s)}(256&t.shapeFlag||h&&sn(h.vnode)&&256&h.vnode.shapeFlag)&&e.a&&wi(e.a,s),e.isMounted=!0,t=n=o=null}},c=e.effect=new y(l,()=>dt(u),e.scope),u=e.update=()=>c.run();u.id=e.uid,ji(e,!0),u()},N=(e,t,n)=>{t.component=e;const i=e.vnode.props;e.vnode=t,e.next=null,ti(e,t.props,i,n),fi(e,t.children,n),j(),ft(),O()},M=(e,t,n,i,o,s,r,a,l=!1)=>{const c=e&&e.children,u=e?e.shapeFlag:0,h=t.children,{patchFlag:p,shapeFlag:m}=t;if(p>0){if(128&p)return void V(c,h,n,i,o,s,r,a,l);if(256&p)return void B(c,h,n,i,o,s,r,a,l)}8&m?(16&u&&H(c,o,s),h!==c&&d(n,h)):16&u?16&m?V(c,h,n,i,o,s,r,a,l):H(c,o,s,!0):(8&u&&d(n,""),16&m&&S(h,n,i,o,s,r,a,l))},B=(e,t,n,o,s,r,a,l,c)=>{e=e||i["a"],t=t||i["a"];const u=e.length,d=t.length,h=Math.min(u,d);let p;for(p=0;p<h;p++){const i=t[p]=c?to(t[p]):eo(t[p]);g(e[p],i,n,null,s,r,a,l,c)}u>d?H(e,s,r,!0,!1,h):S(t,n,o,s,r,a,l,c,h)},V=(e,t,n,o,s,r,a,l,c)=>{let u=0;const d=t.length;let h=e.length-1,p=d-1;while(u<=h&&u<=p){const i=e[u],o=t[u]=c?to(t[u]):eo(t[u]);if(!Ri(i,o))break;g(i,o,n,null,s,r,a,l,c),u++}while(u<=h&&u<=p){const i=e[h],o=t[p]=c?to(t[p]):eo(t[p]);if(!Ri(i,o))break;g(i,o,n,null,s,r,a,l,c),h--,p--}if(u>h){if(u<=p){const e=p+1,i=e<d?t[e].el:o;while(u<=p)g(null,t[u]=c?to(t[u]):eo(t[u]),n,i,s,r,a,l,c),u++}}else if(u>p)while(u<=h)R(e[u],s,r,!0),u++;else{const m=u,f=u,v=new Map;for(u=f;u<=p;u++){const e=t[u]=c?to(t[u]):eo(t[u]);null!=e.key&&v.set(e.key,u)}let b,_=0;const y=p-f+1;let w=!1,k=0;const x=new Array(y);for(u=0;u<y;u++)x[u]=0;for(u=m;u<=h;u++){const i=e[u];if(_>=y){R(i,s,r,!0);continue}let o;if(null!=i.key)o=v.get(i.key);else for(b=f;b<=p;b++)if(0===x[b-f]&&Ri(i,t[b])){o=b;break}void 0===o?R(i,s,r,!0):(x[o-f]=u+1,o>=k?k=o:w=!0,g(i,t[o],n,null,s,r,a,l,c),_++)}const j=w?Ei(x):i["a"];for(b=j.length-1,u=y-1;u>=0;u--){const e=f+u,i=t[e],h=e+1<d?t[e+1].el:o;0===x[u]?g(null,i,n,h,s,r,a,l,c):w&&(b<0||u!==j[b]?F(i,n,h,2):b--)}}},F=(e,t,n,i,s=null)=>{const{el:r,type:a,transition:l,children:c,shapeFlag:u}=e;if(6&u)return void F(e.component.subTree,t,n,i);if(128&u)return void e.suspense.move(t,n,i);if(64&u)return void a.move(e,t,n,J);if(a===Si){o(r,t,n);for(let e=0;e<c.length;e++)F(c[e],t,n,i);return void o(e.anchor,t,n)}if(a===Ti)return void w(e,t,n);const d=2!==i&&1&u&&l;if(d)if(0===i)l.beforeEnter(r),o(r,t,n),wi(()=>l.enter(r),s);else{const{leave:e,delayLeave:i,afterLeave:s}=l,a=()=>o(r,t,n),c=()=>{e(r,()=>{a(),s&&s()})};i?i(r,a,c):c()}else o(r,t,n)},R=(e,t,n,i=!1,o=!1)=>{const{type:s,props:r,ref:a,children:l,dynamicChildren:c,shapeFlag:u,patchFlag:d,dirs:h}=e;if(null!=a&&_i(a,null,n,e,!0),256&u)return void t.ctx.deactivate(e);const p=1&u&&h,m=!sn(e);let f;if(m&&(f=r&&r.onVnodeBeforeUnmount)&&oo(f,t,e),6&u)W(e.component,n,i);else{if(128&u)return void e.suspense.unmount(n,i);p&&Cn(e,null,t,"beforeUnmount"),64&u?e.type.remove(e,t,n,o,J,i):c&&(s!==Si||d>0&&64&d)?H(c,t,n,!1,!0):(s===Si&&384&d||!o&&16&u)&&H(l,t,n),i&&U(e)}(m&&(f=r&&r.onVnodeUnmounted)||p)&&wi(()=>{f&&oo(f,t,e),p&&Cn(e,null,t,"unmounted")},n)},U=e=>{const{type:t,el:n,anchor:i,transition:o}=e;if(t===Si)return void $(n,i);if(t===Ti)return void k(e);const r=()=>{s(n),o&&!o.persisted&&o.afterLeave&&o.afterLeave()};if(1&e.shapeFlag&&o&&!o.persisted){const{leave:t,delayLeave:i}=o,s=()=>t(n,r);i?i(e.el,r,s):s()}else r()},$=(e,t)=>{let n;while(e!==t)n=p(e),s(e),e=n;s(t)},W=(e,t,n)=>{const{bum:o,scope:s,update:r,subTree:a,um:l}=e;o&&Object(i["n"])(o),s.stop(),r&&(r.active=!1,R(a,e,t,n)),l&&wi(l,t),wi(()=>{e.isUnmounted=!0},t),t&&t.pendingBranch&&!t.isUnmounted&&e.asyncDep&&!e.asyncResolved&&e.suspenseId===t.pendingId&&(t.deps--,0===t.deps&&t.resolve())},H=(e,t,n,i=!1,o=!1,s=0)=>{for(let r=s;r<e.length;r++)R(e[r],t,n,i,o)},K=e=>6&e.shapeFlag?K(e.component.subTree):128&e.shapeFlag?e.suspense.next():p(e.anchor||e.el),G=(e,t,n)=>{null==e?t._vnode&&R(t._vnode,null,null,!0):g(t._vnode||null,e,t,null,null,null,n),ft(),gt(),t._vnode=e},J={p:g,um:R,m:F,r:U,mt:D,mc:S,pc:M,pbc:P,n:K,o:e};let Y,Z;return t&&([Y,Z]=t(J)),{render:G,hydrate:Y,createApp:bi(G,Y)}}function ji({effect:e,update:t},n){e.allowRecurse=t.allowRecurse=n}function Oi(e,t,n=!1){const o=e.children,s=t.children;if(Object(i["o"])(o)&&Object(i["o"])(s))for(let i=0;i<o.length;i++){const e=o[i];let t=s[i];1&t.shapeFlag&&!t.dynamicChildren&&((t.patchFlag<=0||32===t.patchFlag)&&(t=s[i]=to(s[i]),t.el=e.el),n||Oi(e,t)),t.type===Ii&&(t.el=e.el)}}function Ei(e){const t=e.slice(),n=[0];let i,o,s,r,a;const l=e.length;for(i=0;i<l;i++){const l=e[i];if(0!==l){if(o=n[n.length-1],e[o]<l){t[i]=o,n.push(i);continue}s=0,r=n.length-1;while(s<r)a=s+r>>1,e[n[a]]<l?s=a+1:r=a;l<e[n[s]]&&(s>0&&(t[i]=n[s-1]),n[s]=i)}}s=n.length,r=n[s-1];while(s-- >0)n[s]=r,r=t[r];return n}const Ci=e=>e.__isTeleport;const Si=Symbol(void 0),Ii=Symbol(void 0),Pi=Symbol(void 0),Ti=Symbol(void 0),Ai=[];let Li=null;function Di(e=!1){Ai.push(Li=e?null:[])}function zi(){Ai.pop(),Li=Ai[Ai.length-1]||null}let qi=1;function Ni(e){qi+=e}function Mi(e){return e.dynamicChildren=qi>0?Li||i["a"]:null,zi(),qi>0&&Li&&Li.push(e),e}function Bi(e,t,n,i,o,s){return Mi(Hi(e,t,n,i,o,s,!0))}function Vi(e,t,n,i,o){return Mi(Ki(e,t,n,i,o,!0))}function Fi(e){return!!e&&!0===e.__v_isVNode}function Ri(e,t){return e.type===t.type&&e.key===t.key}const Ui="__vInternal",$i=({key:e})=>null!=e?e:null,Wi=({ref:e,ref_key:t,ref_for:n})=>null!=e?Object(i["F"])(e)||Ne(e)||Object(i["q"])(e)?{i:xt,r:e,k:t,f:!!n}:e:null;function Hi(e,t=null,n=null,o=0,s=null,r=(e===Si?0:1),a=!1,l=!1){const c={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&$i(t),ref:t&&Wi(t),scopeId:jt,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetAnchor:null,staticCount:0,shapeFlag:r,patchFlag:o,dynamicProps:s,dynamicChildren:null,appContext:null,ctx:xt};return l?(no(c,n),128&r&&e.normalize(c)):n&&(c.shapeFlag|=Object(i["F"])(n)?8:16),qi>0&&!a&&Li&&(c.patchFlag>0||6&r)&&32!==c.patchFlag&&Li.push(c),c}const Ki=Gi;function Gi(e,t=null,n=null,o=0,s=null,r=!1){if(e&&e!==Pn||(e=Pi),Fi(e)){const i=Yi(e,t,!0);return n&&no(i,n),qi>0&&!r&&Li&&(6&i.shapeFlag?Li[Li.indexOf(e)]=i:Li.push(i)),i.patchFlag|=-2,i}if(Oo(e)&&(e=e.__vccOpts),t){t=Ji(t);let{class:e,style:n}=t;e&&!Object(i["F"])(e)&&(t.class=Object(i["L"])(e)),Object(i["w"])(n)&&(Pe(n)&&!Object(i["o"])(n)&&(n=Object(i["h"])({},n)),t.style=Object(i["N"])(n))}const a=Object(i["F"])(e)?1:zt(e)?128:Ci(e)?64:Object(i["w"])(e)?4:Object(i["q"])(e)?2:0;return Hi(e,t,n,o,s,a,r,!0)}function Ji(e){return e?Pe(e)||Ui in e?Object(i["h"])({},e):e:null}function Yi(e,t,n=!1){const{props:o,ref:s,patchFlag:r,children:a}=e,l=t?io(o||{},t):o,c={__v_isVNode:!0,__v_skip:!0,type:e.type,props:l,key:l&&$i(l),ref:t&&t.ref?n&&s?Object(i["o"])(s)?s.concat(Wi(t)):[s,Wi(t)]:Wi(t):s,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:a,target:e.target,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Si?-1===r?16:16|r:r,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:e.transition,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Yi(e.ssContent),ssFallback:e.ssFallback&&Yi(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return c}function Zi(e=" ",t=0){return Ki(Ii,null,e,t)}function Xi(e,t){const n=Ki(Ti,null,e);return n.staticCount=t,n}function Qi(e="",t=!1){return t?(Di(),Vi(Pi,null,e)):Ki(Pi,null,e)}function eo(e){return null==e||"boolean"===typeof e?Ki(Pi):Object(i["o"])(e)?Ki(Si,null,e.slice()):"object"===typeof e?to(e):Ki(Ii,null,String(e))}function to(e){return null===e.el&&-1!==e.patchFlag||e.memo?e:Yi(e)}function no(e,t){let n=0;const{shapeFlag:o}=e;if(null==t)t=null;else if(Object(i["o"])(t))n=16;else if("object"===typeof t){if(65&o){const n=t.default;return void(n&&(n._c&&(n._d=!1),no(e,n()),n._c&&(n._d=!0)))}{n=32;const i=t._;i||Ui in t?3===i&&xt&&(1===xt.slots._?t._=1:(t._=2,e.patchFlag|=1024)):t._ctx=xt}}else Object(i["q"])(t)?(t={default:t,_ctx:xt},n=32):(t=String(t),64&o?(n=16,t=[Zi(t)]):n=8);e.children=t,e.shapeFlag|=n}function io(...e){const t={};for(let n=0;n<e.length;n++){const o=e[n];for(const e in o)if("class"===e)t.class!==o.class&&(t.class=Object(i["L"])([t.class,o.class]));else if("style"===e)t.style=Object(i["N"])([t.style,o.style]);else if(Object(i["x"])(e)){const n=t[e],s=o[e];!s||n===s||Object(i["o"])(n)&&n.includes(s)||(t[e]=n?[].concat(n,s):s)}else""!==e&&(t[e]=o[e])}return t}function oo(e,t,n,i=null){Ze(e,t,7,[n,i])}const so=gi();let ro=0;function ao(e,t,n){const o=e.type,r=(t?t.appContext:e.appContext)||so,a={uid:ro++,vnode:e,type:o,parent:t,appContext:r,root:null,next:null,subTree:null,effect:null,update:null,scope:new s(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(r.provides),accessCache:null,renderCache:[],components:null,directives:null,propsOptions:oi(o,r),emitsOptions:wt(o,r),emit:null,emitted:null,propsDefaults:i["b"],inheritAttrs:o.inheritAttrs,ctx:i["b"],data:i["b"],props:i["b"],attrs:i["b"],slots:i["b"],refs:i["b"],setupState:i["b"],setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return a.ctx={_:a},a.root=t?t.root:a,a.emit=yt.bind(null,a),e.ce&&e.ce(a),a}let lo=null;const co=()=>lo||xt,uo=e=>{lo=e,e.scope.on()},ho=()=>{lo&&lo.scope.off(),lo=null};function po(e){return 4&e.vnode.shapeFlag}let mo,fo,go=!1;function vo(e,t=!1){go=t;const{props:n,children:i}=e.vnode,o=po(e);ei(e,n,o,t),mi(e,i);const s=o?bo(e,t):void 0;return go=!1,s}function bo(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=Ae(new Proxy(e.ctx,Bn));const{setup:o}=n;if(o){const n=e.setupContext=o.length>1?ko(e):null;uo(e),j();const s=Ye(o,e,0,[e.props,n]);if(O(),ho(),Object(i["z"])(s)){if(s.then(ho,ho),t)return s.then(n=>{_o(e,n,t)}).catch(t=>{Xe(t,e,0)});e.asyncDep=s}else _o(e,s,t)}else yo(e,t)}function _o(e,t,n){Object(i["q"])(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:Object(i["w"])(t)&&(e.setupState=Ue(t)),yo(e,n)}function yo(e,t,n){const o=e.type;if(!e.render){if(!t&&mo&&!o.render){const t=o.template||Wn(e).template;if(t){0;const{isCustomElement:n,compilerOptions:s}=e.appContext.config,{delimiters:r,compilerOptions:a}=o,l=Object(i["h"])(Object(i["h"])({isCustomElement:n,delimiters:r},s),a);o.render=mo(t,l)}}e.render=o.render||i["d"],fo&&fo(e)}uo(e),j(),Fn(e),O(),ho()}function wo(e){return new Proxy(e.attrs,{get(t,n){return E(e,"get","$attrs"),t[n]}})}function ko(e){const t=t=>{e.exposed=t||{}};let n;return{get attrs(){return n||(n=wo(e))},slots:e.slots,emit:e.emit,expose:t}}function xo(e){if(e.exposed)return e.exposeProxy||(e.exposeProxy=new Proxy(Ue(Ae(e.exposed)),{get(t,n){return n in t?t[n]:n in Nn?Nn[n](e):void 0},has(e,t){return t in e||t in Nn}}))}function jo(e,t=!0){return Object(i["q"])(e)?e.displayName||e.name:e.name||t&&e.__name}function Oo(e){return Object(i["q"])(e)&&"__vccOpts"in e}const Eo=(e,t)=>Je(e,t,go);function Co(){return So().slots}function So(){const e=co();return e.setupContext||(e.setupContext=ko(e))}function Io(e,t,n){const o=arguments.length;return 2===o?Object(i["w"])(t)&&!Object(i["o"])(t)?Fi(t)?Ki(e,null,[t]):Ki(e,t):Ki(e,null,t):(o>3?n=Array.prototype.slice.call(arguments,2):3===o&&Fi(n)&&(n=[n]),Ki(e,t,n))}const Po=Symbol(""),To=()=>{{const e=Mt(Po);return e}};const Ao="3.2.47",Lo="http://www.w3.org/2000/svg",Do="undefined"!==typeof document?document:null,zo=Do&&Do.createElement("template"),qo={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,i)=>{const o=t?Do.createElementNS(Lo,e):Do.createElement(e,n?{is:n}:void 0);return"select"===e&&i&&null!=i.multiple&&o.setAttribute("multiple",i.multiple),o},createText:e=>Do.createTextNode(e),createComment:e=>Do.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>Do.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,i,o,s){const r=n?n.previousSibling:t.lastChild;if(o&&(o===s||o.nextSibling)){while(1)if(t.insertBefore(o.cloneNode(!0),n),o===s||!(o=o.nextSibling))break}else{zo.innerHTML=i?`<svg>${e}</svg>`:e;const o=zo.content;if(i){const e=o.firstChild;while(e.firstChild)o.appendChild(e.firstChild);o.removeChild(e)}t.insertBefore(o,n)}return[r?r.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}};function No(e,t,n){const i=e._vtc;i&&(t=(t?[t,...i]:[...i]).join(" ")),null==t?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}function Mo(e,t,n){const o=e.style,s=Object(i["F"])(n);if(n&&!s){if(t&&!Object(i["F"])(t))for(const e in t)null==n[e]&&Vo(o,e,"");for(const e in n)Vo(o,e,n[e])}else{const i=o.display;s?t!==n&&(o.cssText=n):t&&e.removeAttribute("style"),"_vod"in e&&(o.display=i)}}const Bo=/\s*!important$/;function Vo(e,t,n){if(Object(i["o"])(n))n.forEach(n=>Vo(e,t,n));else if(null==n&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const o=Uo(e,t);Bo.test(n)?e.setProperty(Object(i["l"])(o),n.replace(Bo,""),"important"):e[o]=n}}const Fo=["Webkit","Moz","ms"],Ro={};function Uo(e,t){const n=Ro[t];if(n)return n;let o=Object(i["e"])(t);if("filter"!==o&&o in e)return Ro[t]=o;o=Object(i["f"])(o);for(let i=0;i<Fo.length;i++){const n=Fo[i]+o;if(n in e)return Ro[t]=n}return t}const $o="http://www.w3.org/1999/xlink";function Wo(e,t,n,o,s){if(o&&t.startsWith("xlink:"))null==n?e.removeAttributeNS($o,t.slice(6,t.length)):e.setAttributeNS($o,t,n);else{const o=Object(i["E"])(t);null==n||o&&!Object(i["m"])(n)?e.removeAttribute(t):e.setAttribute(t,o?"":n)}}function Ho(e,t,n,o,s,r,a){if("innerHTML"===t||"textContent"===t)return o&&a(o,s,r),void(e[t]=null==n?"":n);if("value"===t&&"PROGRESS"!==e.tagName&&!e.tagName.includes("-")){e._value=n;const i=null==n?"":n;return e.value===i&&"OPTION"!==e.tagName||(e.value=i),void(null==n&&e.removeAttribute(t))}let l=!1;if(""===n||null==n){const o=typeof e[t];"boolean"===o?n=Object(i["m"])(n):null==n&&"string"===o?(n="",l=!0):"number"===o&&(n=0,l=!0)}try{e[t]=n}catch(c){0}l&&e.removeAttribute(t)}function Ko(e,t,n,i){e.addEventListener(t,n,i)}function Go(e,t,n,i){e.removeEventListener(t,n,i)}function Jo(e,t,n,i,o=null){const s=e._vei||(e._vei={}),r=s[t];if(i&&r)r.value=i;else{const[n,a]=Zo(t);if(i){const r=s[t]=ts(i,o);Ko(e,n,r,a)}else r&&(Go(e,n,r,a),s[t]=void 0)}}const Yo=/(?:Once|Passive|Capture)$/;function Zo(e){let t;if(Yo.test(e)){let n;t={};while(n=e.match(Yo))e=e.slice(0,e.length-n[0].length),t[n[0].toLowerCase()]=!0}const n=":"===e[2]?e.slice(3):Object(i["l"])(e.slice(2));return[n,t]}let Xo=0;const Qo=Promise.resolve(),es=()=>Xo||(Qo.then(()=>Xo=0),Xo=Date.now());function ts(e,t){const n=e=>{if(e._vts){if(e._vts<=n.attached)return}else e._vts=Date.now();Ze(ns(e,n.value),t,5,[e])};return n.value=e,n.attached=es(),n}function ns(e,t){if(Object(i["o"])(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(e=>t=>!t._stopped&&e&&e(t))}return t}const is=/^on[a-z]/,os=(e,t,n,o,s=!1,r,a,l,c)=>{"class"===t?No(e,o,s):"style"===t?Mo(e,n,o):Object(i["x"])(t)?Object(i["v"])(t)||Jo(e,t,n,o,a):("."===t[0]?(t=t.slice(1),1):"^"===t[0]?(t=t.slice(1),0):ss(e,t,o,s))?Ho(e,t,o,r,a,l,c):("true-value"===t?e._trueValue=o:"false-value"===t&&(e._falseValue=o),Wo(e,t,o,s))};function ss(e,t,n,o){return o?"innerHTML"===t||"textContent"===t||!!(t in e&&is.test(t)&&Object(i["q"])(n)):"spellcheck"!==t&&"draggable"!==t&&"translate"!==t&&("form"!==t&&(("list"!==t||"INPUT"!==e.tagName)&&(("type"!==t||"TEXTAREA"!==e.tagName)&&((!is.test(t)||!Object(i["F"])(n))&&t in e))))}"undefined"!==typeof HTMLElement&&HTMLElement;function rs(e){const t=co();if(!t)return;const n=t.ut=(n=e(t.proxy))=>{Array.from(document.querySelectorAll(`[data-v-owner="${t.uid}"]`)).forEach(e=>ls(e,n))},i=()=>{const i=e(t.proxy);as(t.subTree,i),n(i)};Vt(i),vn(()=>{const e=new MutationObserver(i);e.observe(t.subTree.el.parentNode,{childList:!0}),wn(()=>e.disconnect())})}function as(e,t){if(128&e.shapeFlag){const n=e.suspense;e=n.activeBranch,n.pendingBranch&&!n.isHydrating&&n.effects.push(()=>{as(n.activeBranch,t)})}while(e.component)e=e.component.subTree;if(1&e.shapeFlag&&e.el)ls(e.el,t);else if(e.type===Si)e.children.forEach(e=>as(e,t));else if(e.type===Ti){let{el:n,anchor:i}=e;while(n){if(ls(n,t),n===i)break;n=n.nextSibling}}}function ls(e,t){if(1===e.nodeType){const n=e.style;for(const e in t)n.setProperty("--"+e,t[e])}}const cs="transition",us="animation",ds=(e,{slots:t})=>Io(Yt,gs(e),t);ds.displayName="Transition";const hs={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},ps=ds.props=Object(i["h"])({},Yt.props,hs),ms=(e,t=[])=>{Object(i["o"])(e)?e.forEach(e=>e(...t)):e&&e(...t)},fs=e=>!!e&&(Object(i["o"])(e)?e.some(e=>e.length>1):e.length>1);function gs(e){const t={};for(const i in e)i in hs||(t[i]=e[i]);if(!1===e.css)return t;const{name:n="v",type:o,duration:s,enterFromClass:r=n+"-enter-from",enterActiveClass:a=n+"-enter-active",enterToClass:l=n+"-enter-to",appearFromClass:c=r,appearActiveClass:u=a,appearToClass:d=l,leaveFromClass:h=n+"-leave-from",leaveActiveClass:p=n+"-leave-active",leaveToClass:m=n+"-leave-to"}=e,f=vs(s),g=f&&f[0],v=f&&f[1],{onBeforeEnter:b,onEnter:_,onEnterCancelled:y,onLeave:w,onLeaveCancelled:k,onBeforeAppear:x=b,onAppear:j=_,onAppearCancelled:O=y}=t,E=(e,t,n)=>{ys(e,t?d:l),ys(e,t?u:a),n&&n()},C=(e,t)=>{e._isLeaving=!1,ys(e,h),ys(e,m),ys(e,p),t&&t()},S=e=>(t,n)=>{const i=e?j:_,s=()=>E(t,e,n);ms(i,[t,s]),ws(()=>{ys(t,e?c:r),_s(t,e?d:l),fs(i)||xs(t,o,g,s)})};return Object(i["h"])(t,{onBeforeEnter(e){ms(b,[e]),_s(e,r),_s(e,a)},onBeforeAppear(e){ms(x,[e]),_s(e,c),_s(e,u)},onEnter:S(!1),onAppear:S(!0),onLeave(e,t){e._isLeaving=!0;const n=()=>C(e,t);_s(e,h),Cs(),_s(e,p),ws(()=>{e._isLeaving&&(ys(e,h),_s(e,m),fs(w)||xs(e,o,v,n))}),ms(w,[e,n])},onEnterCancelled(e){E(e,!1),ms(y,[e])},onAppearCancelled(e){E(e,!0),ms(O,[e])},onLeaveCancelled(e){C(e),ms(k,[e])}})}function vs(e){if(null==e)return null;if(Object(i["w"])(e))return[bs(e.enter),bs(e.leave)];{const t=bs(e);return[t,t]}}function bs(e){const t=Object(i["R"])(e);return t}function _s(e,t){t.split(/\s+/).forEach(t=>t&&e.classList.add(t)),(e._vtc||(e._vtc=new Set)).add(t)}function ys(e,t){t.split(/\s+/).forEach(t=>t&&e.classList.remove(t));const{_vtc:n}=e;n&&(n.delete(t),n.size||(e._vtc=void 0))}function ws(e){requestAnimationFrame(()=>{requestAnimationFrame(e)})}let ks=0;function xs(e,t,n,i){const o=e._endId=++ks,s=()=>{o===e._endId&&i()};if(n)return setTimeout(s,n);const{type:r,timeout:a,propCount:l}=js(e,t);if(!r)return i();const c=r+"end";let u=0;const d=()=>{e.removeEventListener(c,h),s()},h=t=>{t.target===e&&++u>=l&&d()};setTimeout(()=>{u<l&&d()},a+1),e.addEventListener(c,h)}function js(e,t){const n=window.getComputedStyle(e),i=e=>(n[e]||"").split(", "),o=i(cs+"Delay"),s=i(cs+"Duration"),r=Os(o,s),a=i(us+"Delay"),l=i(us+"Duration"),c=Os(a,l);let u=null,d=0,h=0;t===cs?r>0&&(u=cs,d=r,h=s.length):t===us?c>0&&(u=us,d=c,h=l.length):(d=Math.max(r,c),u=d>0?r>c?cs:us:null,h=u?u===cs?s.length:l.length:0);const p=u===cs&&/\b(transform|all)(,|$)/.test(i(cs+"Property").toString());return{type:u,timeout:d,propCount:h,hasTransform:p}}function Os(e,t){while(e.length<t.length)e=e.concat(e);return Math.max(...t.map((t,n)=>Es(t)+Es(e[n])))}function Es(e){return 1e3*Number(e.slice(0,-1).replace(",","."))}function Cs(){return document.body.offsetHeight}const Ss=new WeakMap,Is=new WeakMap,Ps={name:"TransitionGroup",props:Object(i["h"])({},ps,{tag:String,moveClass:String}),setup(e,{slots:t}){const n=co(),i=Kt();let o,s;return _n(()=>{if(!o.length)return;const t=e.moveClass||(e.name||"v")+"-move";if(!Ds(o[0].el,n.vnode.el,t))return;o.forEach(Ts),o.forEach(As);const i=o.filter(Ls);Cs(),i.forEach(e=>{const n=e.el,i=n.style;_s(n,t),i.transform=i.webkitTransform=i.transitionDuration="";const o=n._moveCb=e=>{e&&e.target!==n||e&&!/transform$/.test(e.propertyName)||(n.removeEventListener("transitionend",o),n._moveCb=null,ys(n,t))};n.addEventListener("transitionend",o)})}),()=>{const r=Te(e),a=gs(r);let l=r.tag||Si;o=s,s=t.default?nn(t.default()):[];for(let e=0;e<s.length;e++){const t=s[e];null!=t.key&&tn(t,Xt(t,a,i,n))}if(o)for(let e=0;e<o.length;e++){const t=o[e];tn(t,Xt(t,a,i,n)),Ss.set(t,t.el.getBoundingClientRect())}return Ki(l,null,s)}}};Ps.props;function Ts(e){const t=e.el;t._moveCb&&t._moveCb(),t._enterCb&&t._enterCb()}function As(e){Is.set(e,e.el.getBoundingClientRect())}function Ls(e){const t=Ss.get(e),n=Is.get(e),i=t.left-n.left,o=t.top-n.top;if(i||o){const t=e.el.style;return t.transform=t.webkitTransform=`translate(${i}px,${o}px)`,t.transitionDuration="0s",e}}function Ds(e,t,n){const i=e.cloneNode();e._vtc&&e._vtc.forEach(e=>{e.split(/\s+/).forEach(e=>e&&i.classList.remove(e))}),n.split(/\s+/).forEach(e=>e&&i.classList.add(e)),i.style.display="none";const o=1===t.nodeType?t:t.parentNode;o.appendChild(i);const{hasTransform:s}=js(i);return o.removeChild(i),s}const zs=e=>{const t=e.props["onUpdate:modelValue"]||!1;return Object(i["o"])(t)?e=>Object(i["n"])(t,e):t};function qs(e){e.target.composing=!0}function Ns(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const Ms={created(e,{modifiers:{lazy:t,trim:n,number:o}},s){e._assign=zs(s);const r=o||s.props&&"number"===s.props.type;Ko(e,t?"change":"input",t=>{if(t.target.composing)return;let o=e.value;n&&(o=o.trim()),r&&(o=Object(i["J"])(o)),e._assign(o)}),n&&Ko(e,"change",()=>{e.value=e.value.trim()}),t||(Ko(e,"compositionstart",qs),Ko(e,"compositionend",Ns),Ko(e,"change",Ns))},mounted(e,{value:t}){e.value=null==t?"":t},beforeUpdate(e,{value:t,modifiers:{lazy:n,trim:o,number:s}},r){if(e._assign=zs(r),e.composing)return;if(document.activeElement===e&&"range"!==e.type){if(n)return;if(o&&e.value.trim()===t)return;if((s||"number"===e.type)&&Object(i["J"])(e.value)===t)return}const a=null==t?"":t;e.value!==a&&(e.value=a)}},Bs={deep:!0,created(e,t,n){e._assign=zs(n),Ko(e,"change",()=>{const t=e._modelValue,n=$s(e),o=e.checked,s=e._assign;if(Object(i["o"])(t)){const e=Object(i["I"])(t,n),r=-1!==e;if(o&&!r)s(t.concat(n));else if(!o&&r){const n=[...t];n.splice(e,1),s(n)}}else if(Object(i["D"])(t)){const e=new Set(t);o?e.add(n):e.delete(n),s(e)}else s(Ws(e,o))})},mounted:Vs,beforeUpdate(e,t,n){e._assign=zs(n),Vs(e,t,n)}};function Vs(e,{value:t,oldValue:n},o){e._modelValue=t,Object(i["o"])(t)?e.checked=Object(i["I"])(t,o.props.value)>-1:Object(i["D"])(t)?e.checked=t.has(o.props.value):t!==n&&(e.checked=Object(i["H"])(t,Ws(e,!0)))}const Fs={created(e,{value:t},n){e.checked=Object(i["H"])(t,n.props.value),e._assign=zs(n),Ko(e,"change",()=>{e._assign($s(e))})},beforeUpdate(e,{value:t,oldValue:n},o){e._assign=zs(o),t!==n&&(e.checked=Object(i["H"])(t,o.props.value))}},Rs={deep:!0,created(e,{value:t,modifiers:{number:n}},o){const s=Object(i["D"])(t);Ko(e,"change",()=>{const t=Array.prototype.filter.call(e.options,e=>e.selected).map(e=>n?Object(i["J"])($s(e)):$s(e));e._assign(e.multiple?s?new Set(t):t:t[0])}),e._assign=zs(o)},mounted(e,{value:t}){Us(e,t)},beforeUpdate(e,t,n){e._assign=zs(n)},updated(e,{value:t}){Us(e,t)}};function Us(e,t){const n=e.multiple;if(!n||Object(i["o"])(t)||Object(i["D"])(t)){for(let o=0,s=e.options.length;o<s;o++){const s=e.options[o],r=$s(s);if(n)Object(i["o"])(t)?s.selected=Object(i["I"])(t,r)>-1:s.selected=t.has(r);else if(Object(i["H"])($s(s),t))return void(e.selectedIndex!==o&&(e.selectedIndex=o))}n||-1===e.selectedIndex||(e.selectedIndex=-1)}}function $s(e){return"_value"in e?e._value:e.value}function Ws(e,t){const n=t?"_trueValue":"_falseValue";return n in e?e[n]:t}const Hs=["ctrl","shift","alt","meta"],Ks={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&0!==e.button,middle:e=>"button"in e&&1!==e.button,right:e=>"button"in e&&2!==e.button,exact:(e,t)=>Hs.some(n=>e[n+"Key"]&&!t.includes(n))},Gs=(e,t)=>(n,...i)=>{for(let e=0;e<t.length;e++){const i=Ks[t[e]];if(i&&i(n,t))return}return e(n,...i)},Js={esc:"escape",space:" ",up:"arrow-up",left:"arrow-left",right:"arrow-right",down:"arrow-down",delete:"backspace"},Ys=(e,t)=>n=>{if(!("key"in n))return;const o=Object(i["l"])(n.key);return t.some(e=>e===o||Js[e]===o)?e(n):void 0},Zs={beforeMount(e,{value:t},{transition:n}){e._vod="none"===e.style.display?"":e.style.display,n&&t?n.beforeEnter(e):Xs(e,t)},mounted(e,{value:t},{transition:n}){n&&t&&n.enter(e)},updated(e,{value:t,oldValue:n},{transition:i}){!t!==!n&&(i?t?(i.beforeEnter(e),Xs(e,!0),i.enter(e)):i.leave(e,()=>{Xs(e,!1)}):Xs(e,t))},beforeUnmount(e,{value:t}){Xs(e,t)}};function Xs(e,t){e.style.display=t?e._vod:"none"}const Qs=Object(i["h"])({patchProp:os},qo);let er;function tr(){return er||(er=ki(Qs))}const nr=(...e)=>{const t=tr().createApp(...e);const{mount:n}=t;return t.mount=e=>{const o=ir(e);if(!o)return;const s=t._component;Object(i["q"])(s)||s.render||s.template||(s.template=o.innerHTML),o.innerHTML="";const r=n(o,!1,o instanceof SVGElement);return o instanceof Element&&(o.removeAttribute("v-cloak"),o.setAttribute("data-v-app","")),r},t};function ir(e){if(Object(i["F"])(e)){const t=document.querySelector(e);return t}return e}var or=n("11ec");const sr="undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__;var rr=n("d96b");function ar(){return lr(rr["a"]),rr["a"]}function lr(e){const t=e.__SENTRY__=e.__SENTRY__||{};return t.version=t.version||or["a"],t[or["a"]]=t[or["a"]]||{}}var cr=n("73a0");const ur=1e3;function dr(){return Date.now()/ur}function hr(){const{performance:e}=rr["a"];if(!e||!e.now)return dr;const t=Date.now()-e.now(),n=void 0==e.timeOrigin?t:e.timeOrigin;return()=>(n+e.now())/ur}const pr=hr();let mr;(()=>{const{performance:e}=rr["a"];if(!e||!e.now)return void(mr="none");const t=36e5,n=e.now(),i=Date.now(),o=e.timeOrigin?Math.abs(e.timeOrigin+n-i):t,s=o<t,r=e.timing&&e.timing.navigationStart,a="number"===typeof r,l=a?Math.abs(r+n-i):t,c=l<t;s||c?o<=l?(mr="timeOrigin",e.timeOrigin):mr="navigationStart":mr="dateNow"})();var fr=n("7c08");function gr(){const e=rr["a"],t=e.crypto||e.msCrypto;let n=()=>16*Math.random();try{if(t&&t.randomUUID)return t.randomUUID().replace(/-/g,"");t&&t.getRandomValues&&(n=()=>{const e=new Uint8Array(1);return t.getRandomValues(e),e[0]})}catch(i){}return([1e7]+1e3+4e3+8e3+1e11).replace(/[018]/g,e=>(e^(15&n())>>e/4).toString(16))}function vr(e){return e.exception&&e.exception.values?e.exception.values[0]:void 0}function br(e){const{message:t,event_id:n}=e;if(t)return t;const i=vr(e);return i?i.type&&i.value?`${i.type}: ${i.value}`:i.type||i.value||n||"<unknown>":n||"<unknown>"}function _r(e,t,n){const i=e.exception=e.exception||{},o=i.values=i.values||[],s=o[0]=o[0]||{};s.value||(s.value=t||""),s.type||(s.type=n||"Error")}function yr(e,t){const n=vr(e);if(!n)return;const i={type:"generic",handled:!0},o=n.mechanism;if(n.mechanism={...i,...o,...t},t&&"data"in t){const e={...o&&o.data,...t.data};n.mechanism.data=e}}function wr(e){if(kr(e))return!0;try{Object(cr["a"])(e,"__sentry_captured__",!0)}catch(t){}return!1}function kr(e){try{return e.__sentry_captured__}catch(t){}}function xr(e){const t=pr(),n={sid:gr(),init:!0,timestamp:t,started:t,duration:0,status:"ok",errors:0,ignoreDuration:!1,toJSON:()=>Er(n)};return e&&jr(n,e),n}function jr(e,t={}){if(t.user&&(!e.ipAddress&&t.user.ip_address&&(e.ipAddress=t.user.ip_address),e.did||t.did||(e.did=t.user.id||t.user.email||t.user.username)),e.timestamp=t.timestamp||pr(),t.abnormal_mechanism&&(e.abnormal_mechanism=t.abnormal_mechanism),t.ignoreDuration&&(e.ignoreDuration=t.ignoreDuration),t.sid&&(e.sid=32===t.sid.length?t.sid:gr()),void 0!==t.init&&(e.init=t.init),!e.did&&t.did&&(e.did=""+t.did),"number"===typeof t.started&&(e.started=t.started),e.ignoreDuration)e.duration=void 0;else if("number"===typeof t.duration)e.duration=t.duration;else{const t=e.timestamp-e.started;e.duration=t>=0?t:0}t.release&&(e.release=t.release),t.environment&&(e.environment=t.environment),!e.ipAddress&&t.ipAddress&&(e.ipAddress=t.ipAddress),!e.userAgent&&t.userAgent&&(e.userAgent=t.userAgent),"number"===typeof t.errors&&(e.errors=t.errors),t.status&&(e.status=t.status)}function Or(e,t){let n={};t?n={status:t}:"ok"===e.status&&(n={status:"exited"}),jr(e,n)}function Er(e){return Object(cr["c"])({sid:""+e.sid,init:e.init,started:new Date(1e3*e.started).toISOString(),timestamp:new Date(1e3*e.timestamp).toISOString(),status:e.status,errors:e.errors,did:"number"===typeof e.did||"string"===typeof e.did?""+e.did:void 0,duration:e.duration,abnormal_mechanism:e.abnormal_mechanism,attrs:{release:e.release,environment:e.environment,ip_address:e.ipAddress,user_agent:e.userAgent}})}var Cr=n("710b"),Sr=n("f37b");function Ir(){return gr()}function Pr(){return gr().substring(16)}function Tr(e,t,n=2){if(!t||"object"!==typeof t||n<=0)return t;if(e&&t&&0===Object.keys(t).length)return e;const i={...e};for(const o in t)Object.prototype.hasOwnProperty.call(t,o)&&(i[o]=Tr(i[o],t[o],n-1));return i}const Ar="_sentrySpan";function Lr(e,t){t?Object(cr["a"])(e,Ar,t):delete e[Ar]}function Dr(e){return e[Ar]}const zr=100;class qr{constructor(){this._notifyingListeners=!1,this._scopeListeners=[],this._eventProcessors=[],this._breadcrumbs=[],this._attachments=[],this._user={},this._tags={},this._extra={},this._contexts={},this._sdkProcessingMetadata={},this._propagationContext={traceId:Ir(),spanId:Pr()}}clone(){const e=new qr;return e._breadcrumbs=[...this._breadcrumbs],e._tags={...this._tags},e._extra={...this._extra},e._contexts={...this._contexts},this._contexts.flags&&(e._contexts.flags={values:[...this._contexts.flags.values]}),e._user=this._user,e._level=this._level,e._session=this._session,e._transactionName=this._transactionName,e._fingerprint=this._fingerprint,e._eventProcessors=[...this._eventProcessors],e._requestSession=this._requestSession,e._attachments=[...this._attachments],e._sdkProcessingMetadata={...this._sdkProcessingMetadata},e._propagationContext={...this._propagationContext},e._client=this._client,e._lastEventId=this._lastEventId,Lr(e,Dr(this)),e}setClient(e){this._client=e}setLastEventId(e){this._lastEventId=e}getClient(){return this._client}lastEventId(){return this._lastEventId}addScopeListener(e){this._scopeListeners.push(e)}addEventProcessor(e){return this._eventProcessors.push(e),this}setUser(e){return this._user=e||{email:void 0,id:void 0,ip_address:void 0,username:void 0},this._session&&jr(this._session,{user:e}),this._notifyScopeListeners(),this}getUser(){return this._user}getRequestSession(){return this._requestSession}setRequestSession(e){return this._requestSession=e,this}setTags(e){return this._tags={...this._tags,...e},this._notifyScopeListeners(),this}setTag(e,t){return this._tags={...this._tags,[e]:t},this._notifyScopeListeners(),this}setExtras(e){return this._extra={...this._extra,...e},this._notifyScopeListeners(),this}setExtra(e,t){return this._extra={...this._extra,[e]:t},this._notifyScopeListeners(),this}setFingerprint(e){return this._fingerprint=e,this._notifyScopeListeners(),this}setLevel(e){return this._level=e,this._notifyScopeListeners(),this}setTransactionName(e){return this._transactionName=e,this._notifyScopeListeners(),this}setContext(e,t){return null===t?delete this._contexts[e]:this._contexts[e]=t,this._notifyScopeListeners(),this}setSession(e){return e?this._session=e:delete this._session,this._notifyScopeListeners(),this}getSession(){return this._session}update(e){if(!e)return this;const t="function"===typeof e?e(this):e,[n,i]=t instanceof Nr?[t.getScopeData(),t.getRequestSession()]:Object(Cr["i"])(t)?[e,e.requestSession]:[],{tags:o,extra:s,user:r,contexts:a,level:l,fingerprint:c=[],propagationContext:u}=n||{};return this._tags={...this._tags,...o},this._extra={...this._extra,...s},this._contexts={...this._contexts,...a},r&&Object.keys(r).length&&(this._user=r),l&&(this._level=l),c.length&&(this._fingerprint=c),u&&(this._propagationContext=u),i&&(this._requestSession=i),this}clear(){return this._breadcrumbs=[],this._tags={},this._extra={},this._user={},this._contexts={},this._level=void 0,this._transactionName=void 0,this._fingerprint=void 0,this._requestSession=void 0,this._session=void 0,Lr(this,void 0),this._attachments=[],this.setPropagationContext({traceId:Ir()}),this._notifyScopeListeners(),this}addBreadcrumb(e,t){const n="number"===typeof t?t:zr;if(n<=0)return this;const i={timestamp:dr(),...e},o=this._breadcrumbs;return o.push(i),this._breadcrumbs=o.length>n?o.slice(-n):o,this._notifyScopeListeners(),this}getLastBreadcrumb(){return this._breadcrumbs[this._breadcrumbs.length-1]}clearBreadcrumbs(){return this._breadcrumbs=[],this._notifyScopeListeners(),this}addAttachment(e){return this._attachments.push(e),this}clearAttachments(){return this._attachments=[],this}getScopeData(){return{breadcrumbs:this._breadcrumbs,attachments:this._attachments,contexts:this._contexts,tags:this._tags,extra:this._extra,user:this._user,level:this._level,fingerprint:this._fingerprint||[],eventProcessors:this._eventProcessors,propagationContext:this._propagationContext,sdkProcessingMetadata:this._sdkProcessingMetadata,transactionName:this._transactionName,span:Dr(this)}}setSDKProcessingMetadata(e){return this._sdkProcessingMetadata=Tr(this._sdkProcessingMetadata,e,2),this}setPropagationContext(e){return this._propagationContext={spanId:Pr(),...e},this}getPropagationContext(){return this._propagationContext}captureException(e,t){const n=t&&t.event_id?t.event_id:gr();if(!this._client)return Sr["c"].warn("No client configured on scope - will not capture exception!"),n;const i=new Error("Sentry syntheticException");return this._client.captureException(e,{originalException:e,syntheticException:i,...t,event_id:n},this),n}captureMessage(e,t,n){const i=n&&n.event_id?n.event_id:gr();if(!this._client)return Sr["c"].warn("No client configured on scope - will not capture message!"),i;const o=new Error(e);return this._client.captureMessage(e,t,{originalException:e,syntheticException:o,...n,event_id:i},this),i}captureEvent(e,t){const n=t&&t.event_id?t.event_id:gr();return this._client?(this._client.captureEvent(e,{...t,event_id:n},this),n):(Sr["c"].warn("No client configured on scope - will not capture event!"),n)}_notifyScopeListeners(){this._notifyingListeners||(this._notifyingListeners=!0,this._scopeListeners.forEach(e=>{e(this)}),this._notifyingListeners=!1)}}const Nr=qr;function Mr(){return Object(rr["b"])("defaultCurrentScope",()=>new Nr)}function Br(){return Object(rr["b"])("defaultIsolationScope",()=>new Nr)}class Vr{constructor(e,t){let n,i;n=e||new Nr,i=t||new Nr,this._stack=[{scope:n}],this._isolationScope=i}withScope(e){const t=this._pushScope();let n;try{n=e(t)}catch(i){throw this._popScope(),i}return Object(Cr["n"])(n)?n.then(e=>(this._popScope(),e),e=>{throw this._popScope(),e}):(this._popScope(),n)}getClient(){return this.getStackTop().client}getScope(){return this.getStackTop().scope}getIsolationScope(){return this._isolationScope}getStackTop(){return this._stack[this._stack.length-1]}_pushScope(){const e=this.getScope().clone();return this._stack.push({client:this.getClient(),scope:e}),e}_popScope(){return!(this._stack.length<=1)&&!!this._stack.pop()}}function Fr(){const e=ar(),t=lr(e);return t.stack=t.stack||new Vr(Mr(),Br())}function Rr(e){return Fr().withScope(e)}function Ur(e,t){const n=Fr();return n.withScope(()=>(n.getStackTop().scope=e,t(e)))}function $r(e){return Fr().withScope(()=>e(Fr().getIsolationScope()))}function Wr(){return{withIsolationScope:$r,withScope:Rr,withSetScope:Ur,withSetIsolationScope:(e,t)=>$r(t),getCurrentScope:()=>Fr().getScope(),getIsolationScope:()=>Fr().getIsolationScope()}}function Hr(e){const t=lr(e);return t.acs?t.acs:Wr()}function Kr(){const e=ar(),t=Hr(e);return t.getCurrentScope()}function Gr(){const e=ar(),t=Hr(e);return t.getIsolationScope()}function Jr(){return Object(rr["b"])("globalScope",()=>new Nr)}function Yr(...e){const t=ar(),n=Hr(t);if(2===e.length){const[t,i]=e;return t?n.withSetScope(t,i):n.withScope(i)}return n.withScope(e[0])}function Zr(){return Kr().getClient()}function Xr(e){const t=e.getPropagationContext(),{traceId:n,spanId:i,parentSpanId:o}=t,s=Object(cr["c"])({trace_id:n,span_id:i,parent_span_id:o});return s}const Qr=[];function ea(e){const t={};return e.forEach(e=>{const{name:n}=e,i=t[n];i&&!i.isDefaultInstance&&e.isDefaultInstance||(t[n]=e)}),Object.values(t)}function ta(e){const t=e.defaultIntegrations||[],n=e.integrations;let i;if(t.forEach(e=>{e.isDefaultInstance=!0}),Array.isArray(n))i=[...t,...n];else if("function"===typeof n){const e=n(t);i=Array.isArray(e)?e:[e]}else i=t;const o=ea(i),s=o.findIndex(e=>"Debug"===e.name);if(s>-1){const[e]=o.splice(s,1);o.push(e)}return o}function na(e,t){const n={};return t.forEach(t=>{t&&oa(e,t,n)}),n}function ia(e,t){for(const n of t)n&&n.afterAllSetup&&n.afterAllSetup(e)}function oa(e,t,n){if(n[t.name])sr&&Sr["c"].log("Integration skipped because it was already installed: "+t.name);else{if(n[t.name]=t,-1===Qr.indexOf(t.name)&&"function"===typeof t.setupOnce&&(t.setupOnce(),Qr.push(t.name)),t.setup&&"function"===typeof t.setup&&t.setup(e),"function"===typeof t.preprocessEvent){const n=t.preprocessEvent.bind(t);e.on("preprocessEvent",(t,i)=>n(t,i,e))}if("function"===typeof t.processEvent){const n=t.processEvent.bind(t),i=Object.assign((t,i)=>n(t,i,e),{id:t.name});e.addEventProcessor(i)}sr&&Sr["c"].log("Integration installed: "+t.name)}}function sa(e){return e}const ra=[/^Script error\.?$/,/^Javascript error: Script error\.? on line 0$/,/^ResizeObserver loop completed with undelivered notifications.$/,/^Cannot redefine property: googletag$/,"undefined is not an object (evaluating 'a.L')",'can\'t redefine non-configurable property "solana"',"vv().getRestrictions is not a function. (In 'vv().getRestrictions(1,a)', 'vv().getRestrictions' is undefined)","Can't find variable: _AutofillCallbackHandler",/^Non-Error promise rejection captured with value: Object Not Found Matching Id:\d+, MethodName:simulateEvent, ParamCount:\d+$/],aa="InboundFilters",la=(e={})=>({name:aa,processEvent(t,n,i){const o=i.getOptions(),s=ua(e,o);return da(t,s)?null:t}}),ca=sa(la);function ua(e={},t={}){return{allowUrls:[...e.allowUrls||[],...t.allowUrls||[]],denyUrls:[...e.denyUrls||[],...t.denyUrls||[]],ignoreErrors:[...e.ignoreErrors||[],...t.ignoreErrors||[],...e.disableErrorDefaults?[]:ra],ignoreTransactions:[...e.ignoreTransactions||[],...t.ignoreTransactions||[]],ignoreInternal:void 0===e.ignoreInternal||e.ignoreInternal}}function da(e,t){return t.ignoreInternal&&va(e)?(sr&&Sr["c"].warn("Event dropped due to being internal Sentry Error.\nEvent: "+br(e)),!0):ha(e,t.ignoreErrors)?(sr&&Sr["c"].warn("Event dropped due to being matched by `ignoreErrors` option.\nEvent: "+br(e)),!0):ya(e)?(sr&&Sr["c"].warn("Event dropped due to not having an error message, error type or stacktrace.\nEvent: "+br(e)),!0):pa(e,t.ignoreTransactions)?(sr&&Sr["c"].warn("Event dropped due to being matched by `ignoreTransactions` option.\nEvent: "+br(e)),!0):ma(e,t.denyUrls)?(sr&&Sr["c"].warn(`Event dropped due to being matched by \`denyUrls\` option.\nEvent: ${br(e)}.\nUrl: ${_a(e)}`),!0):!fa(e,t.allowUrls)&&(sr&&Sr["c"].warn(`Event dropped due to not being matched by \`allowUrls\` option.\nEvent: ${br(e)}.\nUrl: ${_a(e)}`),!0)}function ha(e,t){return!(e.type||!t||!t.length)&&ga(e).some(e=>Object(fr["c"])(e,t))}function pa(e,t){if("transaction"!==e.type||!t||!t.length)return!1;const n=e.transaction;return!!n&&Object(fr["c"])(n,t)}function ma(e,t){if(!t||!t.length)return!1;const n=_a(e);return!!n&&Object(fr["c"])(n,t)}function fa(e,t){if(!t||!t.length)return!0;const n=_a(e);return!n||Object(fr["c"])(n,t)}function ga(e){const t=[];let n;e.message&&t.push(e.message);try{n=e.exception.values[e.exception.values.length-1]}catch(i){}return n&&n.value&&(t.push(n.value),n.type&&t.push(`${n.type}: ${n.value}`)),t}function va(e){try{return"SentryError"===e.exception.values[0].type}catch(t){}return!1}function ba(e=[]){for(let t=e.length-1;t>=0;t--){const n=e[t];if(n&&"<anonymous>"!==n.filename&&"[native code]"!==n.filename)return n.filename||null}return null}function _a(e){try{let n;try{n=e.exception.values[0].stacktrace.frames}catch(t){}return n?ba(n):null}catch(n){return sr&&Sr["c"].error("Cannot extract url for event "+br(e)),null}}function ya(e){return!e.type&&(!(!e.exception||!e.exception.values||0===e.exception.values.length)&&(!e.message&&!e.exception.values.some(e=>e.stacktrace||e.type&&"Error"!==e.type||e.value)))}let wa;const ka="FunctionToString",xa=new WeakMap,ja=()=>({name:ka,setupOnce(){wa=Function.prototype.toString;try{Function.prototype.toString=function(...e){const t=Object(cr["f"])(this),n=xa.has(Zr())&&void 0!==t?t:this;return wa.apply(n,e)}}catch(e){}},setup(e){xa.set(e,!0)}}),Oa=sa(ja);var Ea=n("2c81");const Ca="Dedupe",Sa=()=>{let e;return{name:Ca,processEvent(t){if(t.type)return t;try{if(Pa(t,e))return sr&&Sr["c"].warn("Event dropped due to being a duplicate of previously captured event."),null}catch(n){}return e=t}}},Ia=sa(Sa);function Pa(e,t){return!!t&&(!!Ta(e,t)||!!Aa(e,t))}function Ta(e,t){const n=e.message,i=t.message;return!(!n&&!i)&&(!(n&&!i||!n&&i)&&(n===i&&(!!Da(e,t)&&!!La(e,t))))}function Aa(e,t){const n=za(t),i=za(e);return!(!n||!i)&&(n.type===i.type&&n.value===i.value&&(!!Da(e,t)&&!!La(e,t)))}function La(e,t){let n=Object(Ea["c"])(e),i=Object(Ea["c"])(t);if(!n&&!i)return!0;if(n&&!i||!n&&i)return!1;if(n=n,i=i,i.length!==n.length)return!1;for(let o=0;o<i.length;o++){const e=i[o],t=n[o];if(e.filename!==t.filename||e.lineno!==t.lineno||e.colno!==t.colno||e.function!==t.function)return!1}return!0}function Da(e,t){let n=e.fingerprint,i=t.fingerprint;if(!n&&!i)return!0;if(n&&!i||!n&&i)return!1;n=n,i=i;try{return!(n.join("")!==i.join(""))}catch(o){return!1}}function za(e){return e.exception&&e.exception.values&&e.exception.values[0]}var qa=n("d01f");const Na=rr["a"];function Ma(){if(!("fetch"in Na))return!1;try{return new Headers,new Request("http://www.example.com"),new Response,!0}catch(e){return!1}}function Ba(e){return e&&/^function\s+\w+\(\)\s+\{\s+\[native code\]\s+\}$/.test(e.toString())}function Va(){if("string"===typeof EdgeRuntime)return!0;if(!Ma())return!1;if(Ba(Na.fetch))return!0;let e=!1;const t=Na.document;if(t&&"function"===typeof t.createElement)try{const n=t.createElement("iframe");n.hidden=!0,t.head.appendChild(n),n.contentWindow&&n.contentWindow.fetch&&(e=Ba(n.contentWindow.fetch)),t.head.removeChild(n)}catch(n){qa["a"]&&Sr["c"].warn("Could not create sandbox iframe for pure fetch check, bailing to window.fetch: ",n)}return e}function Fa(e,t){!0===t.debug&&(sr?Sr["c"].enable():Object(Sr["b"])(()=>{console.warn("[Sentry] Cannot initialize SDK with `debug` option using a non-debug bundle.")}));const n=Kr();n.update(t.initialScope);const i=new e(t);return Ra(i),i.init(),i}function Ra(e){Kr().setClient(e)}const Ua="production";var $a;function Wa(e){return new Ka(t=>{t(e)})}function Ha(e){return new Ka((t,n)=>{n(e)})}(function(e){const t=0;e[e["PENDING"]=t]="PENDING";const n=1;e[e["RESOLVED"]=n]="RESOLVED";const i=2;e[e["REJECTED"]=i]="REJECTED"})($a||($a={}));class Ka{constructor(e){Ka.prototype.__init.call(this),Ka.prototype.__init2.call(this),Ka.prototype.__init3.call(this),Ka.prototype.__init4.call(this),this._state=$a.PENDING,this._handlers=[];try{e(this._resolve,this._reject)}catch(t){this._reject(t)}}then(e,t){return new Ka((n,i)=>{this._handlers.push([!1,t=>{if(e)try{n(e(t))}catch(o){i(o)}else n(t)},e=>{if(t)try{n(t(e))}catch(o){i(o)}else i(e)}]),this._executeHandlers()})}catch(e){return this.then(e=>e,e)}finally(e){return new Ka((t,n)=>{let i,o;return this.then(t=>{o=!1,i=t,e&&e()},t=>{o=!0,i=t,e&&e()}).then(()=>{o?n(i):t(i)})})}__init(){this._resolve=e=>{this._setResult($a.RESOLVED,e)}}__init2(){this._reject=e=>{this._setResult($a.REJECTED,e)}}__init3(){this._setResult=(e,t)=>{this._state===$a.PENDING&&(Object(Cr["n"])(t)?t.then(this._resolve,this._reject):(this._state=e,this._value=t,this._executeHandlers()))}}__init4(){this._executeHandlers=()=>{if(this._state===$a.PENDING)return;const e=this._handlers.slice();this._handlers=[],e.forEach(e=>{e[0]||(this._state===$a.RESOLVED&&e[1](this._value),this._state===$a.REJECTED&&e[2](this._value),e[0]=!0)})}}}function Ga(e,t,n,i=0){return new Ka((o,s)=>{const r=e[i];if(null===t||"function"!==typeof r)o(t);else{const a=r({...t},n);sr&&r.id&&null===a&&Sr["c"].log(`Event processor "${r.id}" dropped event`),Object(Cr["n"])(a)?a.then(t=>Ga(e,t,n,i+1).then(o)).then(null,s):Ga(e,a,n,i+1).then(o).then(null,s)}})}let Ja,Ya,Za;function Xa(e){const t=rr["a"]._sentryDebugIds;if(!t)return{};const n=Object.keys(t);return Za&&n.length===Ya||(Ya=n.length,Za=n.reduce((n,i)=>{Ja||(Ja={});const o=Ja[i];if(o)n[o[0]]=o[1];else{const o=e(i);for(let e=o.length-1;e>=0;e--){const s=o[e],r=s&&s.filename,a=t[i];if(r&&a){n[r]=a,Ja[i]=[r,a];break}}}return n},{})),Za}var Qa=n("d920");const el="sentry.source",tl="sentry.sample_rate",nl="sentry.op",il="sentry.origin",ol="sentry.measurement_unit",sl="sentry.measurement_value",rl="sentry.profile_id",al="sentry.exclusive_time",ll="sentry-",cl=/^sentry-/;function ul(e){const t=dl(e);if(!t)return;const n=Object.entries(t).reduce((e,[t,n])=>{if(t.match(cl)){const i=t.slice(ll.length);e[i]=n}return e},{});return Object.keys(n).length>0?n:void 0}function dl(e){if(e&&(Object(Cr["l"])(e)||Array.isArray(e)))return Array.isArray(e)?e.reduce((e,t)=>{const n=hl(t);return Object.entries(n).forEach(([t,n])=>{e[t]=n}),e},{}):hl(e)}function hl(e){return e.split(",").map(e=>e.split("=").map(e=>decodeURIComponent(e.trim()))).reduce((e,[t,n])=>(t&&n&&(e[t]=n),e),{})}function pl(e){if("boolean"===typeof __SENTRY_TRACING__&&!__SENTRY_TRACING__)return!1;const t=Zr(),n=e||t&&t.getOptions();return!!n&&(n.enableTracing||"tracesSampleRate"in n||"tracesSampler"in n)}const ml="_sentryMetrics";function fl(e){const t=e[ml];if(!t)return;const n={};for(const[,[i,o]]of t){const e=n[i]||(n[i]=[]);e.push(Object(cr["c"])(o))}return n}const gl=0,vl=1;new RegExp("^[ \\t]*([0-9a-f]{32})?-?([0-9a-f]{16})?-?([01])?[ \\t]*$");const bl=0,_l=1;let yl=!1;function wl(e){const{spanId:t,traceId:n}=e.spanContext(),{data:i,op:o,parent_span_id:s,status:r,origin:a}=Ol(e);return Object(cr["c"])({parent_span_id:s,span_id:t,trace_id:n,data:i,op:o,status:r,origin:a})}function kl(e){const{spanId:t,traceId:n,isRemote:i}=e.spanContext(),o=i?t:Ol(e).parent_span_id,s=i?Pr():t;return Object(cr["c"])({parent_span_id:o,span_id:s,trace_id:n})}function xl(e){return"number"===typeof e?jl(e):Array.isArray(e)?e[0]+e[1]/1e9:e instanceof Date?jl(e.getTime()):pr()}function jl(e){const t=e>9999999999;return t?e/1e3:e}function Ol(e){if(Cl(e))return e.getSpanJSON();try{const{spanId:t,traceId:n}=e.spanContext();if(El(e)){const{attributes:i,startTime:o,name:s,endTime:r,parentSpanId:a,status:l}=e;return Object(cr["c"])({span_id:t,trace_id:n,data:i,description:s,parent_span_id:a,start_timestamp:xl(o),timestamp:xl(r)||void 0,status:Il(l),op:i[nl],origin:i[il],_metrics_summary:fl(e)})}return{span_id:t,trace_id:n}}catch(t){return{}}}function El(e){const t=e;return!!t.attributes&&!!t.startTime&&!!t.name&&!!t.endTime&&!!t.status}function Cl(e){return"function"===typeof e.getSpanJSON}function Sl(e){const{traceFlags:t}=e.spanContext();return t===_l}function Il(e){if(e&&e.code!==gl)return e.code===vl?"ok":e.message||"unknown_error"}const Pl="_sentryChildSpans",Tl="_sentryRootSpan";function Al(e,t){const n=e[Tl]||e;Object(cr["a"])(t,Tl,n),e[Pl]?e[Pl].add(t):Object(cr["a"])(e,Pl,new Set([t]))}function Ll(e){const t=new Set;function n(e){if(!t.has(e)&&Sl(e)){t.add(e);const i=e[Pl]?Array.from(e[Pl]):[];for(const e of i)n(e)}}return n(e),Array.from(t)}function Dl(e){return e[Tl]||e}function zl(){const e=ar(),t=Hr(e);return t.getActiveSpan?t.getActiveSpan():Dr(Kr())}function ql(){yl||(Object(Sr["b"])(()=>{console.warn("[Sentry] Deprecation warning: Returning null from `beforeSendSpan` will be disallowed from SDK version 9.0.0 onwards. The callback will only support mutating spans. To drop certain spans, configure the respective integrations directly.")}),yl=!0)}const Nl="_frozenDsc";function Ml(e,t){const n=e;Object(cr["a"])(n,Nl,t)}function Bl(e,t){const n=t.getOptions(),{publicKey:i}=t.getDsn()||{},o=Object(cr["c"])({environment:n.environment||Ua,release:n.release,public_key:i,trace_id:e});return t.emit("createDsc",o),o}function Vl(e,t){const n=t.getPropagationContext();return n.dsc||Bl(n.traceId,e)}function Fl(e){const t=Zr();if(!t)return{};const n=Dl(e),i=n[Nl];if(i)return i;const o=n.spanContext().traceState,s=o&&o.get("sentry.dsc"),r=s&&ul(s);if(r)return r;const a=Bl(e.spanContext().traceId,t),l=Ol(n),c=l.data||{},u=c[tl];null!=u&&(a.sample_rate=""+u);const d=c[el],h=l.description;return"url"!==d&&h&&(a.transaction=h),pl()&&(a.sampled=String(Sl(n))),t.emit("createDsc",a,n),a}function Rl(e,t){const{fingerprint:n,span:i,breadcrumbs:o,sdkProcessingMetadata:s}=t;Wl(e,t),i&&Gl(e,i),Jl(e,n),Hl(e,o),Kl(e,s)}function Ul(e,t){const{extra:n,tags:i,user:o,contexts:s,level:r,sdkProcessingMetadata:a,breadcrumbs:l,fingerprint:c,eventProcessors:u,attachments:d,propagationContext:h,transactionName:p,span:m}=t;$l(e,"extra",n),$l(e,"tags",i),$l(e,"user",o),$l(e,"contexts",s),e.sdkProcessingMetadata=Tr(e.sdkProcessingMetadata,a,2),r&&(e.level=r),p&&(e.transactionName=p),m&&(e.span=m),l.length&&(e.breadcrumbs=[...e.breadcrumbs,...l]),c.length&&(e.fingerprint=[...e.fingerprint,...c]),u.length&&(e.eventProcessors=[...e.eventProcessors,...u]),d.length&&(e.attachments=[...e.attachments,...d]),e.propagationContext={...e.propagationContext,...h}}function $l(e,t,n){e[t]=Tr(e[t],n,1)}function Wl(e,t){const{extra:n,tags:i,user:o,contexts:s,level:r,transactionName:a}=t,l=Object(cr["c"])(n);l&&Object.keys(l).length&&(e.extra={...l,...e.extra});const c=Object(cr["c"])(i);c&&Object.keys(c).length&&(e.tags={...c,...e.tags});const u=Object(cr["c"])(o);u&&Object.keys(u).length&&(e.user={...u,...e.user});const d=Object(cr["c"])(s);d&&Object.keys(d).length&&(e.contexts={...d,...e.contexts}),r&&(e.level=r),a&&"transaction"!==e.type&&(e.transaction=a)}function Hl(e,t){const n=[...e.breadcrumbs||[],...t];e.breadcrumbs=n.length?n:void 0}function Kl(e,t){e.sdkProcessingMetadata={...e.sdkProcessingMetadata,...t}}function Gl(e,t){e.contexts={trace:kl(t),...e.contexts},e.sdkProcessingMetadata={dynamicSamplingContext:Fl(t),...e.sdkProcessingMetadata};const n=Dl(t),i=Ol(n).description;i&&!e.transaction&&"transaction"===e.type&&(e.transaction=i)}function Jl(e,t){e.fingerprint=e.fingerprint?Array.isArray(e.fingerprint)?e.fingerprint:[e.fingerprint]:[],t&&(e.fingerprint=e.fingerprint.concat(t)),e.fingerprint&&!e.fingerprint.length&&delete e.fingerprint}function Yl(e,t,n,i,o,s){const{normalizeDepth:r=3,normalizeMaxBreadth:a=1e3}=e,l={...t,event_id:t.event_id||n.event_id||gr(),timestamp:t.timestamp||dr()},c=n.integrations||e.integrations.map(e=>e.name);Zl(l,e),ec(l,c),o&&o.emit("applyFrameMetadata",t),void 0===t.type&&Xl(l,e.stackParser);const u=nc(i,n.captureContext);n.mechanism&&yr(l,n.mechanism);const d=o?o.getEventProcessors():[],h=Jr().getScopeData();if(s){const e=s.getScopeData();Ul(h,e)}if(u){const e=u.getScopeData();Ul(h,e)}const p=[...n.attachments||[],...h.attachments];p.length&&(n.attachments=p),Rl(l,h);const m=[...d,...h.eventProcessors],f=Ga(m,l,n);return f.then(e=>(e&&Ql(e),"number"===typeof r&&r>0?tc(e,r,a):e))}function Zl(e,t){const{environment:n,release:i,dist:o,maxValueLength:s=250}=t;e.environment=e.environment||n||Ua,!e.release&&i&&(e.release=i),!e.dist&&o&&(e.dist=o),e.message&&(e.message=Object(fr["d"])(e.message,s));const r=e.exception&&e.exception.values&&e.exception.values[0];r&&r.value&&(r.value=Object(fr["d"])(r.value,s));const a=e.request;a&&a.url&&(a.url=Object(fr["d"])(a.url,s))}function Xl(e,t){const n=Xa(t);try{e.exception.values.forEach(e=>{e.stacktrace.frames.forEach(e=>{n&&e.filename&&(e.debug_id=n[e.filename])})})}catch(i){}}function Ql(e){const t={};try{e.exception.values.forEach(e=>{e.stacktrace.frames.forEach(e=>{e.debug_id&&(e.abs_path?t[e.abs_path]=e.debug_id:e.filename&&(t[e.filename]=e.debug_id),delete e.debug_id)})})}catch(i){}if(0===Object.keys(t).length)return;e.debug_meta=e.debug_meta||{},e.debug_meta.images=e.debug_meta.images||[];const n=e.debug_meta.images;Object.entries(t).forEach(([e,t])=>{n.push({type:"sourcemap",code_file:e,debug_id:t})})}function ec(e,t){t.length>0&&(e.sdk=e.sdk||{},e.sdk.integrations=[...e.sdk.integrations||[],...t])}function tc(e,t,n){if(!e)return null;const i={...e,...e.breadcrumbs&&{breadcrumbs:e.breadcrumbs.map(e=>({...e,...e.data&&{data:Object(Qa["a"])(e.data,t,n)}}))},...e.user&&{user:Object(Qa["a"])(e.user,t,n)},...e.contexts&&{contexts:Object(Qa["a"])(e.contexts,t,n)},...e.extra&&{extra:Object(Qa["a"])(e.extra,t,n)}};return e.contexts&&e.contexts.trace&&i.contexts&&(i.contexts.trace=e.contexts.trace,e.contexts.trace.data&&(i.contexts.trace.data=Object(Qa["a"])(e.contexts.trace.data,t,n))),e.spans&&(i.spans=e.spans.map(e=>({...e,...e.data&&{data:Object(Qa["a"])(e.data,t,n)}}))),e.contexts&&e.contexts.flags&&i.contexts&&(i.contexts.flags=Object(Qa["a"])(e.contexts.flags,3,n)),i}function nc(e,t){if(!t)return e;const n=e?e.clone():new Nr;return n.update(t),n}function ic(e){if(e)return oc(e)||rc(e)?{captureContext:e}:e}function oc(e){return e instanceof Nr||"function"===typeof e}const sc=["user","level","extra","contexts","tags","fingerprint","requestSession","propagationContext"];function rc(e){return Object.keys(e).some(e=>sc.includes(e))}function ac(e,t){return Kr().captureException(e,ic(t))}function lc(e,t){return Kr().captureEvent(e,t)}function cc(e){const t=Zr(),n=Gr(),i=Kr(),{release:o,environment:s=Ua}=t&&t.getOptions()||{},{userAgent:r}=rr["a"].navigator||{},a=xr({release:o,environment:s,user:i.getUser()||n.getUser(),...r&&{userAgent:r},...e}),l=n.getSession();return l&&"ok"===l.status&&jr(l,{status:"exited"}),uc(),n.setSession(a),i.setSession(a),a}function uc(){const e=Gr(),t=Kr(),n=t.getSession()||e.getSession();n&&Or(n),dc(),e.setSession(),t.setSession()}function dc(){const e=Gr(),t=Kr(),n=Zr(),i=t.getSession()||e.getSession();i&&n&&n.captureSession(i)}function hc(e=!1){e?uc():dc()}const pc=/^(?:(\w+):)\/\/(?:(\w+)(?::(\w+)?)?@)([\w.-]+)(?::(\d+))?\/(.+)/;function mc(e){return"http"===e||"https"===e}function fc(e,t=!1){const{host:n,path:i,pass:o,port:s,projectId:r,protocol:a,publicKey:l}=e;return`${a}://${l}${t&&o?":"+o:""}@${n}${s?":"+s:""}/${i?i+"/":i}${r}`}function gc(e){const t=pc.exec(e);if(!t)return void Object(Sr["b"])(()=>{console.error("Invalid Sentry Dsn: "+e)});const[n,i,o="",s="",r="",a=""]=t.slice(1);let l="",c=a;const u=c.split("/");if(u.length>1&&(l=u.slice(0,-1).join("/"),c=u.pop()),c){const e=c.match(/^\d+/);e&&(c=e[0])}return vc({host:s,pass:o,path:l,projectId:c,port:r,protocol:n,publicKey:i})}function vc(e){return{protocol:e.protocol,publicKey:e.publicKey||"",pass:e.pass||"",host:e.host,port:e.port||"",path:e.path||"",projectId:e.projectId}}function bc(e){if(!qa["a"])return!0;const{port:t,projectId:n,protocol:i}=e,o=["protocol","publicKey","host","projectId"],s=o.find(t=>!e[t]&&(Sr["c"].error(`Invalid Sentry Dsn: ${t} missing`),!0));return!s&&(n.match(/^\d+$/)?mc(i)?!t||!isNaN(parseInt(t,10))||(Sr["c"].error("Invalid Sentry Dsn: Invalid port "+t),!1):(Sr["c"].error("Invalid Sentry Dsn: Invalid protocol "+i),!1):(Sr["c"].error("Invalid Sentry Dsn: Invalid projectId "+n),!1))}function _c(e){const t="string"===typeof e?gc(e):vc(e);if(t&&bc(t))return t}const yc="7";function wc(e){const t=e.protocol?e.protocol+":":"",n=e.port?":"+e.port:"";return`${t}//${e.host}${n}${e.path?"/"+e.path:""}/api/`}function kc(e){return`${wc(e)}${e.projectId}/envelope/`}function xc(e,t){const n={sentry_version:yc};return e.publicKey&&(n.sentry_key=e.publicKey),t&&(n.sentry_client=`${t.name}/${t.version}`),new URLSearchParams(n).toString()}function jc(e,t,n){return t||`${kc(e)}?${xc(e,n)}`}function Oc(e,t=[]){return[e,t]}function Ec(e,t){const[n,i]=e;return[n,[...i,t]]}function Cc(e,t){const n=e[1];for(const i of n){const e=i[0].type,n=t(i,e);if(n)return!0}return!1}function Sc(e){return rr["a"].__SENTRY__&&rr["a"].__SENTRY__.encodePolyfill?rr["a"].__SENTRY__.encodePolyfill(e):(new TextEncoder).encode(e)}function Ic(e){const[t,n]=e;let i=JSON.stringify(t);function o(e){"string"===typeof i?i="string"===typeof e?i+e:[Sc(i),e]:i.push("string"===typeof e?Sc(e):e)}for(const r of n){const[e,t]=r;if(o(`\n${JSON.stringify(e)}\n`),"string"===typeof t||t instanceof Uint8Array)o(t);else{let e;try{e=JSON.stringify(t)}catch(s){e=JSON.stringify(Object(Qa["a"])(t))}o(e)}}return"string"===typeof i?i:Pc(i)}function Pc(e){const t=e.reduce((e,t)=>e+t.length,0),n=new Uint8Array(t);let i=0;for(const o of e)n.set(o,i),i+=o.length;return n}function Tc(e){const t={type:"span"};return[t,e]}function Ac(e){const t="string"===typeof e.data?Sc(e.data):e.data;return[Object(cr["c"])({type:"attachment",length:t.length,filename:e.filename,content_type:e.contentType,attachment_type:e.attachmentType}),t]}const Lc={session:"session",sessions:"session",attachment:"attachment",transaction:"transaction",event:"error",client_report:"internal",user_report:"default",profile:"profile",profile_chunk:"profile",replay_event:"replay",replay_recording:"replay",check_in:"monitor",feedback:"feedback",span:"span",statsd:"metric_bucket",raw_security:"security"};function Dc(e){return Lc[e]}function zc(e){if(!e||!e.sdk)return;const{name:t,version:n}=e.sdk;return{name:t,version:n}}function qc(e,t,n,i){const o=e.sdkProcessingMetadata&&e.sdkProcessingMetadata.dynamicSamplingContext;return{event_id:e.event_id,sent_at:(new Date).toISOString(),...t&&{sdk:t},...!!n&&i&&{dsn:fc(i)},...o&&{trace:Object(cr["c"])({...o})}}}function Nc(e,t){return t?(e.sdk=e.sdk||{},e.sdk.name=e.sdk.name||t.name,e.sdk.version=e.sdk.version||t.version,e.sdk.integrations=[...e.sdk.integrations||[],...t.integrations||[]],e.sdk.packages=[...e.sdk.packages||[],...t.packages||[]],e):e}function Mc(e,t,n,i){const o=zc(n),s={sent_at:(new Date).toISOString(),...o&&{sdk:o},...!!i&&t&&{dsn:fc(t)}},r="aggregates"in e?[{type:"sessions"},e]:[{type:"session"},e.toJSON()];return Oc(s,[r])}function Bc(e,t,n,i){const o=zc(n),s=e.type&&"replay_event"!==e.type?e.type:"event";Nc(e,n&&n.sdk);const r=qc(e,o,i,t);delete e.sdkProcessingMetadata;const a=[{type:s},e];return Oc(r,[a])}function Vc(e,t){function n(e){return!!e.trace_id&&!!e.public_key}const i=Fl(e[0]),o=t&&t.getDsn(),s=t&&t.getOptions().tunnel,r={sent_at:(new Date).toISOString(),...n(i)&&{trace:i},...!!s&&o&&{dsn:fc(o)}},a=t&&t.getOptions().beforeSendSpan,l=a?e=>{const t=a(Ol(e));return t||ql(),t}:e=>Ol(e),c=[];for(const u of e){const e=l(u);e&&c.push(Tc(e))}return Oc(r,c)}function Fc(e,t,n){const i=[{type:"client_report"},{timestamp:n||dr(),discarded_events:e}];return Oc(t?{dsn:t}:{},[i])}class Rc extends Error{constructor(e,t="warn"){super(e),this.message=e,this.name=new.target.prototype.constructor.name,Object.setPrototypeOf(this,new.target.prototype),this.logLevel=t}}function Uc(e){if("boolean"===typeof e)return Number(e);const t="string"===typeof e?parseFloat(e):e;if(!("number"!==typeof t||isNaN(t)||t<0||t>1))return t;sr&&Sr["c"].warn(`[Tracing] Given sample rate is invalid. Sample rate must be a boolean or a number between 0 and 1. Got ${JSON.stringify(e)} of type ${JSON.stringify(typeof e)}.`)}const $c="Not capturing exception because it's already been captured.";class Wc{constructor(e){if(this._options=e,this._integrations={},this._numProcessing=0,this._outcomes={},this._hooks={},this._eventProcessors=[],e.dsn?this._dsn=_c(e.dsn):sr&&Sr["c"].warn("No DSN provided, client will not send events."),this._dsn){const t=jc(this._dsn,e.tunnel,e._metadata?e._metadata.sdk:void 0);this._transport=e.transport({tunnel:this._options.tunnel,recordDroppedEvent:this.recordDroppedEvent.bind(this),...e.transportOptions,url:t})}const t=["enableTracing","tracesSampleRate","tracesSampler"],n=t.find(t=>t in e&&void 0==e[t]);n&&Object(Sr["b"])(()=>{console.warn(`[Sentry] Deprecation warning: \`${n}\` is set to undefined, which leads to tracing being enabled. In v9, a value of \`undefined\` will result in tracing being disabled.`)})}captureException(e,t,n){const i=gr();if(wr(e))return sr&&Sr["c"].log($c),i;const o={event_id:i,...t};return this._process(this.eventFromException(e,o).then(e=>this._captureEvent(e,o,n))),o.event_id}captureMessage(e,t,n,i){const o={event_id:gr(),...n},s=Object(Cr["h"])(e)?e:String(e),r=Object(Cr["j"])(e)?this.eventFromMessage(s,t,o):this.eventFromException(e,o);return this._process(r.then(e=>this._captureEvent(e,o,i))),o.event_id}captureEvent(e,t,n){const i=gr();if(t&&t.originalException&&wr(t.originalException))return sr&&Sr["c"].log($c),i;const o={event_id:i,...t},s=e.sdkProcessingMetadata||{},r=s.capturedSpanScope;return this._process(this._captureEvent(e,o,r||n)),o.event_id}captureSession(e){"string"!==typeof e.release?sr&&Sr["c"].warn("Discarded session because of missing or non-string release"):(this.sendSession(e),jr(e,{init:!1}))}getDsn(){return this._dsn}getOptions(){return this._options}getSdkMetadata(){return this._options._metadata}getTransport(){return this._transport}flush(e){const t=this._transport;return t?(this.emit("flush"),this._isClientDoneProcessing(e).then(n=>t.flush(e).then(e=>n&&e))):Wa(!0)}close(e){return this.flush(e).then(e=>(this.getOptions().enabled=!1,this.emit("close"),e))}getEventProcessors(){return this._eventProcessors}addEventProcessor(e){this._eventProcessors.push(e)}init(){(this._isEnabled()||this._options.integrations.some(({name:e})=>e.startsWith("Spotlight")))&&this._setupIntegrations()}getIntegrationByName(e){return this._integrations[e]}addIntegration(e){const t=this._integrations[e.name];oa(this,e,this._integrations),t||ia(this,[e])}sendEvent(e,t={}){this.emit("beforeSendEvent",e,t);let n=Bc(e,this._dsn,this._options._metadata,this._options.tunnel);for(const o of t.attachments||[])n=Ec(n,Ac(o));const i=this.sendEnvelope(n);i&&i.then(t=>this.emit("afterSendEvent",e,t),null)}sendSession(e){const t=Mc(e,this._dsn,this._options._metadata,this._options.tunnel);this.sendEnvelope(t)}recordDroppedEvent(e,t,n){if(this._options.sendClientReports){const i="number"===typeof n?n:1,o=`${e}:${t}`;sr&&Sr["c"].log(`Recording outcome: "${o}"${i>1?` (${i} times)`:""}`),this._outcomes[o]=(this._outcomes[o]||0)+i}}on(e,t){const n=this._hooks[e]=this._hooks[e]||[];return n.push(t),()=>{const e=n.indexOf(t);e>-1&&n.splice(e,1)}}emit(e,...t){const n=this._hooks[e];n&&n.forEach(e=>e(...t))}sendEnvelope(e){return this.emit("beforeEnvelope",e),this._isEnabled()&&this._transport?this._transport.send(e).then(null,e=>(sr&&Sr["c"].error("Error while sending envelope:",e),e)):(sr&&Sr["c"].error("Transport disabled"),Wa({}))}_setupIntegrations(){const{integrations:e}=this._options;this._integrations=na(this,e),ia(this,e)}_updateSessionFromEvent(e,t){let n=!1,i=!1;const o=t.exception&&t.exception.values;if(o){i=!0;for(const e of o){const t=e.mechanism;if(t&&!1===t.handled){n=!0;break}}}const s="ok"===e.status,r=s&&0===e.errors||s&&n;r&&(jr(e,{...n&&{status:"crashed"},errors:e.errors||Number(i||n)}),this.captureSession(e))}_isClientDoneProcessing(e){return new Ka(t=>{let n=0;const i=1,o=setInterval(()=>{0==this._numProcessing?(clearInterval(o),t(!0)):(n+=i,e&&n>=e&&(clearInterval(o),t(!1)))},i)})}_isEnabled(){return!1!==this.getOptions().enabled&&void 0!==this._transport}_prepareEvent(e,t,n=Kr(),i=Gr()){const o=this.getOptions(),s=Object.keys(this._integrations);return!t.integrations&&s.length>0&&(t.integrations=s),this.emit("preprocessEvent",e,t),e.type||i.setLastEventId(e.event_id||t.event_id),Yl(o,e,t,n,this,i).then(e=>{if(null===e)return e;e.contexts={trace:Xr(n),...e.contexts};const t=Vl(this,n);return e.sdkProcessingMetadata={dynamicSamplingContext:t,...e.sdkProcessingMetadata},e})}_captureEvent(e,t={},n){return this._processEvent(e,t,n).then(e=>e.event_id,e=>{if(sr){const t=e;"log"===t.logLevel?Sr["c"].log(t.message):Sr["c"].warn(t)}})}_processEvent(e,t,n){const i=this.getOptions(),{sampleRate:o}=i,s=Jc(e),r=Gc(e),a=e.type||"error",l=`before send for type \`${a}\``,c="undefined"===typeof o?void 0:Uc(o);if(r&&"number"===typeof c&&Math.random()>c)return this.recordDroppedEvent("sample_rate","error",e),Ha(new Rc(`Discarding event because it's not included in the random sample (sampling rate = ${o})`,"log"));const u="replay_event"===a?"replay":a,d=e.sdkProcessingMetadata||{},h=d.capturedSpanIsolationScope;return this._prepareEvent(e,t,n,h).then(n=>{if(null===n)throw this.recordDroppedEvent("event_processor",u,e),new Rc("An event processor returned `null`, will not send event.","log");const o=t.data&&!0===t.data.__sentry__;if(o)return n;const s=Kc(this,i,n,t);return Hc(s,l)}).then(i=>{if(null===i){if(this.recordDroppedEvent("before_send",u,e),s){const t=e.spans||[],n=1+t.length;this.recordDroppedEvent("before_send","span",n)}throw new Rc(l+" returned `null`, will not send event.","log")}const o=n&&n.getSession();if(!s&&o&&this._updateSessionFromEvent(o,i),s){const e=i.sdkProcessingMetadata&&i.sdkProcessingMetadata.spanCountBeforeProcessing||0,t=i.spans?i.spans.length:0,n=e-t;n>0&&this.recordDroppedEvent("before_send","span",n)}const r=i.transaction_info;if(s&&r&&i.transaction!==e.transaction){const e="custom";i.transaction_info={...r,source:e}}return this.sendEvent(i,t),i}).then(null,e=>{if(e instanceof Rc)throw e;throw this.captureException(e,{data:{__sentry__:!0},originalException:e}),new Rc("Event processing pipeline threw an error, original event will not be sent. Details have been sent as a new event.\nReason: "+e)})}_process(e){this._numProcessing++,e.then(e=>(this._numProcessing--,e),e=>(this._numProcessing--,e))}_clearOutcomes(){const e=this._outcomes;return this._outcomes={},Object.entries(e).map(([e,t])=>{const[n,i]=e.split(":");return{reason:n,category:i,quantity:t}})}_flushOutcomes(){sr&&Sr["c"].log("Flushing outcomes...");const e=this._clearOutcomes();if(0===e.length)return void(sr&&Sr["c"].log("No outcomes to send"));if(!this._dsn)return void(sr&&Sr["c"].log("No dsn provided, will not send outcomes"));sr&&Sr["c"].log("Sending outcomes:",e);const t=Fc(e,this._options.tunnel&&fc(this._dsn));this.sendEnvelope(t)}}function Hc(e,t){const n=t+" must return `null` or a valid event.";if(Object(Cr["n"])(e))return e.then(e=>{if(!Object(Cr["i"])(e)&&null!==e)throw new Rc(n);return e},e=>{throw new Rc(`${t} rejected with ${e}`)});if(!Object(Cr["i"])(e)&&null!==e)throw new Rc(n);return e}function Kc(e,t,n,i){const{beforeSend:o,beforeSendTransaction:s,beforeSendSpan:r}=t;if(Gc(n)&&o)return o(n,i);if(Jc(n)){if(n.spans&&r){const t=[];for(const i of n.spans){const n=r(i);n?t.push(n):(ql(),e.recordDroppedEvent("before_send","span"))}n.spans=t}if(s){if(n.spans){const e=n.spans.length;n.sdkProcessingMetadata={...n.sdkProcessingMetadata,spanCountBeforeProcessing:e}}return s(n,i)}}return n}function Gc(e){return void 0===e.type}function Jc(e){return"transaction"===e.type}function Yc(){return"npm"}function Zc(e,t,n=[t],i="npm"){const o=e._metadata||{};o.sdk||(o.sdk={name:"sentry.javascript."+t,packages:n.map(e=>({name:`${i}:@sentry/${e}`,version:or["a"]})),version:or["a"]}),e._metadata=o}const Xc="undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__;function Qc(e,t){const n=nu(e,t),i={type:au(t),value:lu(t)};return n.length&&(i.stacktrace={frames:n}),void 0===i.type&&""===i.value&&(i.value="Unrecoverable error caught"),i}function eu(e,t,n,i){const o=Zr(),s=o&&o.getOptions().normalizeDepth,r=fu(t),a={__serialized__:Object(Qa["b"])(t,s)};if(r)return{exception:{values:[Qc(e,r)]},extra:a};const l={exception:{values:[{type:Object(Cr["f"])(t)?t.constructor.name:i?"UnhandledRejection":"Error",value:pu(t,{isUnhandledRejection:i})}]},extra:a};if(n){const t=nu(e,n);t.length&&(l.exception.values[0].stacktrace={frames:t})}return l}function tu(e,t){return{exception:{values:[Qc(e,t)]}}}function nu(e,t){const n=t.stacktrace||t.stack||"",i=ou(t),o=su(t);try{return e(n,i,o)}catch(s){}return[]}const iu=/Minified React error #\d+;/i;function ou(e){return e&&iu.test(e.message)?1:0}function su(e){return"number"===typeof e.framesToPop?e.framesToPop:0}function ru(e){return"undefined"!==typeof WebAssembly&&"undefined"!==typeof WebAssembly.Exception&&e instanceof WebAssembly.Exception}function au(e){const t=e&&e.name;if(!t&&ru(e)){const t=e.message&&Array.isArray(e.message)&&2==e.message.length;return t?e.message[0]:"WebAssembly.Exception"}return t}function lu(e){const t=e&&e.message;return t?t.error&&"string"===typeof t.error.message?t.error.message:ru(e)&&Array.isArray(e.message)&&2==e.message.length?e.message[1]:t:"No error message"}function cu(e,t,n,i){const o=n&&n.syntheticException||void 0,s=du(e,t,o,i);return yr(s),s.level="error",n&&n.event_id&&(s.event_id=n.event_id),Wa(s)}function uu(e,t,n="info",i,o){const s=i&&i.syntheticException||void 0,r=hu(e,t,s,o);return r.level=n,i&&i.event_id&&(r.event_id=i.event_id),Wa(r)}function du(e,t,n,i,o){let s;if(Object(Cr["e"])(t)&&t.error){const n=t;return tu(e,n.error)}if(Object(Cr["a"])(t)||Object(Cr["b"])(t)){const o=t;if("stack"in t)s=tu(e,t);else{const t=o.name||(Object(Cr["a"])(o)?"DOMError":"DOMException"),r=o.message?`${t}: ${o.message}`:t;s=hu(e,r,n,i),_r(s,r)}return"code"in o&&(s.tags={...s.tags,"DOMException.code":""+o.code}),s}if(Object(Cr["d"])(t))return tu(e,t);if(Object(Cr["i"])(t)||Object(Cr["f"])(t)){const i=t;return s=eu(e,i,n,o),yr(s,{synthetic:!0}),s}return s=hu(e,t,n,i),_r(s,""+t,void 0),yr(s,{synthetic:!0}),s}function hu(e,t,n,i){const o={};if(i&&n){const i=nu(e,n);i.length&&(o.exception={values:[{value:t,stacktrace:{frames:i}}]})}if(Object(Cr["h"])(t)){const{__sentry_template_string__:e,__sentry_template_values__:n}=t;return o.logentry={message:e,params:n},o}return o.message=t,o}function pu(e,{isUnhandledRejection:t}){const n=Object(cr["d"])(e),i=t?"promise rejection":"exception";if(Object(Cr["e"])(e))return`Event \`ErrorEvent\` captured as ${i} with message \`${e.message}\``;if(Object(Cr["f"])(e)){const t=mu(e);return`Event \`${t}\` (type=${e.type}) captured as ${i}`}return`Object captured as ${i} with keys: ${n}`}function mu(e){try{const t=Object.getPrototypeOf(e);return t?t.constructor.name:void 0}catch(t){}}function fu(e){for(const t in e)if(Object.prototype.hasOwnProperty.call(e,t)){const n=e[t];if(n instanceof Error)return n}}const gu=rr["a"];let vu=0;function bu(){return vu>0}function _u(){vu++,setTimeout(()=>{vu--})}function yu(e,t={}){function n(e){return"function"===typeof e}if(!n(e))return e;try{const t=e.__sentry_wrapped__;if(t)return"function"===typeof t?t:e;if(Object(cr["f"])(e))return e}catch(o){return e}const i=function(...n){try{const i=n.map(e=>yu(e,t));return e.apply(this,i)}catch(i){throw _u(),Yr(e=>{e.addEventProcessor(e=>(t.mechanism&&(_r(e,void 0,void 0),yr(e,t.mechanism)),e.extra={...e.extra,arguments:n},e)),ac(i)}),i}};try{for(const t in e)Object.prototype.hasOwnProperty.call(e,t)&&(i[t]=e[t])}catch(s){}Object(cr["g"])(i,e),Object(cr["a"])(e,"__sentry_wrapped__",i);try{const t=Object.getOwnPropertyDescriptor(i,"name");t.configurable&&Object.defineProperty(i,"name",{get(){return e.name}})}catch(r){}return i}function wu(e,{metadata:t,tunnel:n,dsn:i}){const o={event_id:e.event_id,sent_at:(new Date).toISOString(),...t&&t.sdk&&{sdk:{name:t.sdk.name,version:t.sdk.version}},...!!n&&!!i&&{dsn:fc(i)}},s=ku(e);return Oc(o,[s])}function ku(e){const t={type:"user_report"};return[t,e]}class xu extends Wc{constructor(e){const t={parentSpanIsAlwaysRootSpan:!0,...e},n=gu.SENTRY_SDK_SOURCE||Yc();Zc(t,"browser",["browser"],n),super(t),t.sendClientReports&&gu.document&&gu.document.addEventListener("visibilitychange",()=>{"hidden"===gu.document.visibilityState&&this._flushOutcomes()})}eventFromException(e,t){return cu(this._options.stackParser,e,t,this._options.attachStacktrace)}eventFromMessage(e,t="info",n){return uu(this._options.stackParser,e,t,n,this._options.attachStacktrace)}captureUserFeedback(e){if(!this._isEnabled())return void(Xc&&Sr["c"].warn("SDK not enabled, will not capture user feedback."));const t=wu(e,{metadata:this.getSdkMetadata(),dsn:this.getDsn(),tunnel:this.getOptions().tunnel});this.sendEnvelope(t)}_prepareEvent(e,t,n){return e.platform=e.platform||"javascript",super._prepareEvent(e,t,n)}}const ju={},Ou={};function Eu(e,t){ju[e]=ju[e]||[],ju[e].push(t)}function Cu(e,t){if(!Ou[e]){Ou[e]=!0;try{t()}catch(n){qa["a"]&&Sr["c"].error("Error while instrumenting "+e,n)}}}function Su(e,t){const n=e&&ju[e];if(n)for(const o of n)try{o(t)}catch(i){qa["a"]&&Sr["c"].error(`Error while triggering instrumentation handler.\nType: ${e}\nName: ${Object(Ea["d"])(o)}\nError:`,i)}}const Iu=rr["a"],Pu=1e3;let Tu,Au,Lu;function Du(e){const t="dom";Eu(t,e),Cu(t,zu)}function zu(){if(!Iu.document)return;const e=Su.bind(null,"dom"),t=Mu(e,!0);Iu.document.addEventListener("click",t,!1),Iu.document.addEventListener("keypress",t,!1),["EventTarget","Node"].forEach(t=>{const n=Iu,i=n[t],o=i&&i.prototype;o&&o.hasOwnProperty&&o.hasOwnProperty("addEventListener")&&(Object(cr["e"])(o,"addEventListener",(function(t){return function(n,i,o){if("click"===n||"keypress"==n)try{const i=this.__sentry_instrumentation_handlers__=this.__sentry_instrumentation_handlers__||{},s=i[n]=i[n]||{refCount:0};if(!s.handler){const i=Mu(e);s.handler=i,t.call(this,n,i,o)}s.refCount++}catch(s){}return t.call(this,n,i,o)}})),Object(cr["e"])(o,"removeEventListener",(function(e){return function(t,n,i){if("click"===t||"keypress"==t)try{const n=this.__sentry_instrumentation_handlers__||{},o=n[t];o&&(o.refCount--,o.refCount<=0&&(e.call(this,t,o.handler,i),o.handler=void 0,delete n[t]),0===Object.keys(n).length&&delete this.__sentry_instrumentation_handlers__)}catch(o){}return e.call(this,t,n,i)}})))})}function qu(e){if(e.type!==Au)return!1;try{if(!e.target||e.target._sentryId!==Lu)return!1}catch(t){}return!0}function Nu(e,t){return"keypress"===e&&(!t||!t.tagName||"INPUT"!==t.tagName&&"TEXTAREA"!==t.tagName&&!t.isContentEditable)}function Mu(e,t=!1){return n=>{if(!n||n["_sentryCaptured"])return;const i=Bu(n);if(Nu(n.type,i))return;Object(cr["a"])(n,"_sentryCaptured",!0),i&&!i._sentryId&&Object(cr["a"])(i,"_sentryId",gr());const o="keypress"===n.type?"input":n.type;if(!qu(n)){const s={event:n,name:o,global:t};e(s),Au=n.type,Lu=i?i._sentryId:void 0}clearTimeout(Tu),Tu=Iu.setTimeout(()=>{Lu=void 0,Au=void 0},Pu)}}function Bu(e){try{return e.target}catch(t){return null}}const Vu="__sentry_xhr_v3__";function Fu(e){const t="xhr";Eu(t,e),Cu(t,Ru)}function Ru(){if(!Iu.XMLHttpRequest)return;const e=XMLHttpRequest.prototype;e.open=new Proxy(e.open,{apply(e,t,n){const i=1e3*pr(),o=Object(Cr["l"])(n[0])?n[0].toUpperCase():void 0,s=Uu(n[1]);if(!o||!s)return e.apply(t,n);t[Vu]={method:o,url:s,request_headers:{}},"POST"===o&&s.match(/sentry_key/)&&(t.__sentry_own_request__=!0);const r=()=>{const e=t[Vu];if(e&&4===t.readyState){try{e.status_code=t.status}catch(n){}const o={endTimestamp:1e3*pr(),startTimestamp:i,xhr:t};Su("xhr",o)}};return"onreadystatechange"in t&&"function"===typeof t.onreadystatechange?t.onreadystatechange=new Proxy(t.onreadystatechange,{apply(e,t,n){return r(),e.apply(t,n)}}):t.addEventListener("readystatechange",r),t.setRequestHeader=new Proxy(t.setRequestHeader,{apply(e,t,n){const[i,o]=n,s=t[Vu];return s&&Object(Cr["l"])(i)&&Object(Cr["l"])(o)&&(s.request_headers[i.toLowerCase()]=o),e.apply(t,n)}}),e.apply(t,n)}}),e.send=new Proxy(e.send,{apply(e,t,n){const i=t[Vu];if(!i)return e.apply(t,n);void 0!==n[0]&&(i.body=n[0]);const o={startTimestamp:1e3*pr(),xhr:t};return Su("xhr",o),e.apply(t,n)}})}function Uu(e){if(Object(Cr["l"])(e))return e;try{return e.toString()}catch(t){}}const $u=rr["a"];function Wu(){const e=$u.chrome,t=e&&e.app&&e.app.runtime,n="history"in $u&&!!$u.history.pushState&&!!$u.history.replaceState;return!t&&n}let Hu;function Ku(e){const t="history";Eu(t,e),Cu(t,Gu)}function Gu(){if(!Wu())return;const e=Iu.onpopstate;function t(e){return function(...t){const n=t.length>2?t[2]:void 0;if(n){const e=Hu,t=String(n);Hu=t;const i={from:e,to:t};Su("history",i)}return e.apply(this,t)}}Iu.onpopstate=function(...t){const n=Iu.location.href,i=Hu;Hu=n;const o={from:i,to:n};if(Su("history",o),e)try{return e.apply(this,t)}catch(s){}},Object(cr["e"])(Iu.history,"pushState",t),Object(cr["e"])(Iu.history,"replaceState",t)}function Ju(e){const t="console";Eu(t,e),Cu(t,Yu)}function Yu(){"console"in rr["a"]&&Sr["a"].forEach((function(e){e in rr["a"].console&&Object(cr["e"])(rr["a"].console,e,(function(t){return Sr["d"][e]=t,function(...t){const n={args:t,level:e};Su("console",n);const i=Sr["d"][e];i&&i.apply(rr["a"].console,t)}}))}))}function Zu(e,t){const n="fetch";Eu(n,e),Cu(n,()=>Xu(void 0,t))}function Xu(e,t=!1){t&&!Va()||Object(cr["e"])(rr["a"],"fetch",(function(t){return function(...n){const{method:i,url:o}=td(n),s={args:n,fetchData:{method:i,url:o},startTimestamp:1e3*pr()};e||Su("fetch",{...s});const r=(new Error).stack;return t.apply(rr["a"],n).then(async t=>(e?e(t):Su("fetch",{...s,endTimestamp:1e3*pr(),response:t}),t),e=>{throw Su("fetch",{...s,endTimestamp:1e3*pr(),error:e}),Object(Cr["d"])(e)&&void 0===e.stack&&(e.stack=r,Object(cr["a"])(e,"framesToPop",1)),e})}}))}function Qu(e,t){return!!e&&"object"===typeof e&&!!e[t]}function ed(e){return"string"===typeof e?e:e?Qu(e,"url")?e.url:e.toString?e.toString():"":""}function td(e){if(0===e.length)return{method:"GET",url:""};if(2===e.length){const[t,n]=e;return{url:ed(t),method:Qu(n,"method")?String(n.method).toUpperCase():"GET"}}const t=e[0];return{url:ed(t),method:Qu(t,"method")?String(t.method).toUpperCase():"GET"}}const nd=100;function id(e,t){const n=Zr(),i=Gr();if(!n)return;const{beforeBreadcrumb:o=null,maxBreadcrumbs:s=nd}=n.getOptions();if(s<=0)return;const r=dr(),a={timestamp:r,...e},l=o?Object(Sr["b"])(()=>o(a,t)):a;null!==l&&(n.emit&&n.emit("beforeAddBreadcrumb",l,t),i.addBreadcrumb(l,s))}var od=n("1edf");function sd(e){return"warn"===e?"warning":["fatal","error","warning","log","info","debug"].includes(e)?e:"log"}function rd(e){return void 0===e?void 0:e>=400&&e<500?"warning":e>=500?"error":void 0}function ad(e){if(!e)return{};const t=e.match(/^(([^:/?#]+):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?$/);if(!t)return{};const n=t[6]||"",i=t[8]||"";return{host:t[4],path:t[5],protocol:t[2],search:n,hash:i,relative:t[5]+n+i}}const ld=1024,cd="Breadcrumbs",ud=(e={})=>{const t={console:!0,dom:!0,fetch:!0,history:!0,sentry:!0,xhr:!0,...e};return{name:cd,setup(e){t.console&&Ju(md(e)),t.dom&&Du(pd(e,t.dom)),t.xhr&&Fu(fd(e)),t.fetch&&Zu(gd(e)),t.history&&Ku(vd(e)),t.sentry&&e.on("beforeSendEvent",hd(e))}}},dd=sa(ud);function hd(e){return function(t){Zr()===e&&id({category:"sentry."+("transaction"===t.type?"transaction":"event"),event_id:t.event_id,level:t.level,message:br(t)},{event:t})}}function pd(e,t){return function(n){if(Zr()!==e)return;let i,o,s="object"===typeof t?t.serializeAttribute:void 0,r="object"===typeof t&&"number"===typeof t.maxStringLength?t.maxStringLength:void 0;r&&r>ld&&(Xc&&Sr["c"].warn(`\`dom.maxStringLength\` cannot exceed ${ld}, but a value of ${r} was configured. Sentry will use ${ld} instead.`),r=ld),"string"===typeof s&&(s=[s]);try{const e=n.event,t=bd(e)?e.target:e;i=Object(od["c"])(t,{keyAttrs:s,maxStringLength:r}),o=Object(od["a"])(t)}catch(l){i="<unknown>"}if(0===i.length)return;const a={category:"ui."+n.name,message:i};o&&(a.data={"ui.component_name":o}),id(a,{event:n.event,name:n.name,global:n.global})}}function md(e){return function(t){if(Zr()!==e)return;const n={category:"console",data:{arguments:t.args,logger:"console"},level:sd(t.level),message:Object(fr["a"])(t.args," ")};if("assert"===t.level){if(!1!==t.args[0])return;n.message="Assertion failed: "+(Object(fr["a"])(t.args.slice(1)," ")||"console.assert"),n.data.arguments=t.args.slice(1)}id(n,{input:t.args,level:t.level})}}function fd(e){return function(t){if(Zr()!==e)return;const{startTimestamp:n,endTimestamp:i}=t,o=t.xhr[Vu];if(!n||!i||!o)return;const{method:s,url:r,status_code:a,body:l}=o,c={method:s,url:r,status_code:a},u={xhr:t.xhr,input:l,startTimestamp:n,endTimestamp:i},d=rd(a);id({category:"xhr",data:c,type:"http",level:d},u)}}function gd(e){return function(t){if(Zr()!==e)return;const{startTimestamp:n,endTimestamp:i}=t;if(i&&(!t.fetchData.url.match(/sentry_key/)||"POST"!==t.fetchData.method))if(t.error){const e=t.fetchData,o={data:t.error,input:t.args,startTimestamp:n,endTimestamp:i};id({category:"fetch",data:e,level:"error",type:"http"},o)}else{const e=t.response,o={...t.fetchData,status_code:e&&e.status},s={input:t.args,response:e,startTimestamp:n,endTimestamp:i},r=rd(o.status_code);id({category:"fetch",data:o,type:"http",level:r},s)}}}function vd(e){return function(t){if(Zr()!==e)return;let n=t.from,i=t.to;const o=ad(gu.location.href);let s=n?ad(n):void 0;const r=ad(i);s&&s.path||(s=o),o.protocol===r.protocol&&o.host===r.host&&(i=r.relative),o.protocol===s.protocol&&o.host===s.host&&(n=s.relative),id({category:"navigation",data:{from:n,to:i}})}}function bd(e){return!!e&&!!e.target}const _d=["EventTarget","Window","Node","ApplicationCache","AudioTrackList","BroadcastChannel","ChannelMergerNode","CryptoOperation","EventSource","FileReader","HTMLUnknownElement","IDBDatabase","IDBRequest","IDBTransaction","KeyOperation","MediaController","MessagePort","ModalWindow","Notification","SVGElementInstance","Screen","SharedWorker","TextTrack","TextTrackCue","TextTrackList","WebSocket","WebSocketWorker","Worker","XMLHttpRequest","XMLHttpRequestEventTarget","XMLHttpRequestUpload"],yd="BrowserApiErrors",wd=(e={})=>{const t={XMLHttpRequest:!0,eventTarget:!0,requestAnimationFrame:!0,setInterval:!0,setTimeout:!0,...e};return{name:yd,setupOnce(){t.setTimeout&&Object(cr["e"])(gu,"setTimeout",xd),t.setInterval&&Object(cr["e"])(gu,"setInterval",xd),t.requestAnimationFrame&&Object(cr["e"])(gu,"requestAnimationFrame",jd),t.XMLHttpRequest&&"XMLHttpRequest"in gu&&Object(cr["e"])(XMLHttpRequest.prototype,"send",Od);const e=t.eventTarget;if(e){const t=Array.isArray(e)?e:_d;t.forEach(Ed)}}}},kd=sa(wd);function xd(e){return function(...t){const n=t[0];return t[0]=yu(n,{mechanism:{data:{function:Object(Ea["d"])(e)},handled:!1,type:"instrument"}}),e.apply(this,t)}}function jd(e){return function(t){return e.apply(this,[yu(t,{mechanism:{data:{function:"requestAnimationFrame",handler:Object(Ea["d"])(e)},handled:!1,type:"instrument"}})])}}function Od(e){return function(...t){const n=this,i=["onload","onerror","onprogress","onreadystatechange"];return i.forEach(e=>{e in n&&"function"===typeof n[e]&&Object(cr["e"])(n,e,(function(t){const n={mechanism:{data:{function:e,handler:Object(Ea["d"])(t)},handled:!1,type:"instrument"}},i=Object(cr["f"])(t);return i&&(n.mechanism.data.handler=Object(Ea["d"])(i)),yu(t,n)}))}),e.apply(this,t)}}function Ed(e){const t=gu,n=t[e],i=n&&n.prototype;i&&i.hasOwnProperty&&i.hasOwnProperty("addEventListener")&&(Object(cr["e"])(i,"addEventListener",(function(t){return function(n,i,o){try{Cd(i)&&(i.handleEvent=yu(i.handleEvent,{mechanism:{data:{function:"handleEvent",handler:Object(Ea["d"])(i),target:e},handled:!1,type:"instrument"}}))}catch(s){}return t.apply(this,[n,yu(i,{mechanism:{data:{function:"addEventListener",handler:Object(Ea["d"])(i),target:e},handled:!1,type:"instrument"}}),o])}})),Object(cr["e"])(i,"removeEventListener",(function(e){return function(t,n,i){try{const o=n.__sentry_wrapped__;o&&e.call(this,t,o,i)}catch(o){}return e.call(this,t,n,i)}})))}function Cd(e){return"function"===typeof e.handleEvent}const Sd=sa(()=>({name:"BrowserSession",setupOnce(){"undefined"!==typeof gu.document?(cc({ignoreDuration:!0}),hc(),Ku(({from:e,to:t})=>{void 0!==e&&e!==t&&(cc({ignoreDuration:!0}),hc())})):Xc&&Sr["c"].warn("Using the `browserSessionIntegration` in non-browser environments is not supported.")}}));let Id=null;function Pd(e){const t="error";Eu(t,e),Cu(t,Td)}function Td(){Id=rr["a"].onerror,rr["a"].onerror=function(e,t,n,i,o){const s={column:i,error:o,line:n,msg:e,url:t};return Su("error",s),!!Id&&Id.apply(this,arguments)},rr["a"].onerror.__SENTRY_INSTRUMENTED__=!0}let Ad=null;function Ld(e){const t="unhandledrejection";Eu(t,e),Cu(t,Dd)}function Dd(){Ad=rr["a"].onunhandledrejection,rr["a"].onunhandledrejection=function(e){const t=e;return Su("unhandledrejection",t),!Ad||Ad.apply(this,arguments)},rr["a"].onunhandledrejection.__SENTRY_INSTRUMENTED__=!0}const zd="GlobalHandlers",qd=(e={})=>{const t={onerror:!0,onunhandledrejection:!0,...e};return{name:zd,setupOnce(){Error.stackTraceLimit=50},setup(e){t.onerror&&(Md(e),Ud("onerror")),t.onunhandledrejection&&(Bd(e),Ud("onunhandledrejection"))}}},Nd=sa(qd);function Md(e){Pd(t=>{const{stackParser:n,attachStacktrace:i}=$d();if(Zr()!==e||bu())return;const{msg:o,url:s,line:r,column:a,error:l}=t,c=Rd(du(n,l||o,void 0,i,!1),s,r,a);c.level="error",lc(c,{originalException:l,mechanism:{handled:!1,type:"onerror"}})})}function Bd(e){Ld(t=>{const{stackParser:n,attachStacktrace:i}=$d();if(Zr()!==e||bu())return;const o=Vd(t),s=Object(Cr["j"])(o)?Fd(o):du(n,o,void 0,i,!0);s.level="error",lc(s,{originalException:o,mechanism:{handled:!1,type:"onunhandledrejection"}})})}function Vd(e){if(Object(Cr["j"])(e))return e;try{if("reason"in e)return e.reason;if("detail"in e&&"reason"in e.detail)return e.detail.reason}catch(t){}return e}function Fd(e){return{exception:{values:[{type:"UnhandledRejection",value:"Non-Error promise rejection captured with value: "+String(e)}]}}}function Rd(e,t,n,i){const o=e.exception=e.exception||{},s=o.values=o.values||[],r=s[0]=s[0]||{},a=r.stacktrace=r.stacktrace||{},l=a.frames=a.frames||[],c=i,u=n,d=Object(Cr["l"])(t)&&t.length>0?t:Object(od["b"])();return 0===l.length&&l.push({colno:c,filename:d,function:Ea["a"],in_app:!0,lineno:u}),e}function Ud(e){Xc&&Sr["c"].log("Global Handler attached: "+e)}function $d(){const e=Zr(),t=e&&e.getOptions()||{stackParser:()=>[],attachStacktrace:!1};return t}const Wd=sa(()=>({name:"HttpContext",preprocessEvent(e){if(!gu.navigator&&!gu.location&&!gu.document)return;const t=e.request&&e.request.url||gu.location&&gu.location.href,{referrer:n}=gu.document||{},{userAgent:i}=gu.navigator||{},o={...e.request&&e.request.headers,...n&&{Referer:n},...i&&{"User-Agent":i}},s={...e.request,...t&&{url:t},headers:o};e.request=s}}));function Hd(e,t,n=250,i,o,s,r){if(!s.exception||!s.exception.values||!r||!Object(Cr["g"])(r.originalException,Error))return;const a=s.exception.values.length>0?s.exception.values[s.exception.values.length-1]:void 0;a&&(s.exception.values=Yd(Kd(e,t,o,r.originalException,i,s.exception.values,a,0),n))}function Kd(e,t,n,i,o,s,r,a){if(s.length>=n+1)return s;let l=[...s];if(Object(Cr["g"])(i[o],Error)){Gd(r,a);const s=e(t,i[o]),c=l.length;Jd(s,o,c,a),l=Kd(e,t,n,i[o],o,[s,...l],s,c)}return Array.isArray(i.errors)&&i.errors.forEach((i,s)=>{if(Object(Cr["g"])(i,Error)){Gd(r,a);const c=e(t,i),u=l.length;Jd(c,`errors[${s}]`,u,a),l=Kd(e,t,n,i,o,[c,...l],c,u)}}),l}function Gd(e,t){e.mechanism=e.mechanism||{type:"generic",handled:!0},e.mechanism={...e.mechanism,..."AggregateError"===e.type&&{is_exception_group:!0},exception_id:t}}function Jd(e,t,n,i){e.mechanism=e.mechanism||{type:"generic",handled:!0},e.mechanism={...e.mechanism,type:"chained",source:t,exception_id:n,parent_id:i}}function Yd(e,t){return e.map(e=>(e.value&&(e.value=Object(fr["d"])(e.value,t)),e))}const Zd="cause",Xd=5,Qd="LinkedErrors",eh=(e={})=>{const t=e.limit||Xd,n=e.key||Zd;return{name:Qd,preprocessEvent(e,i,o){const s=o.getOptions();Hd(Qc,s.stackParser,s.maxValueLength,n,t,e,i)}}},th=sa(eh),nh=30,ih=50;function oh(e,t,n,i){const o={filename:e,function:"<anonymous>"===t?Ea["a"]:t,in_app:!0};return void 0!==n&&(o.lineno=n),void 0!==i&&(o.colno=i),o}const sh=/^\s*at (\S+?)(?::(\d+))(?::(\d+))\s*$/i,rh=/^\s*at (?:(.+?\)(?: \[.+\])?|.*?) ?\((?:address at )?)?(?:async )?((?:<anonymous>|[-a-z]+:|.*bundle|\/)?.*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,ah=/\((\S*)(?::(\d+))(?::(\d+))\)/,lh=e=>{const t=sh.exec(e);if(t){const[,e,n,i]=t;return oh(e,Ea["a"],+n,+i)}const n=rh.exec(e);if(n){const e=n[2]&&0===n[2].indexOf("eval");if(e){const e=ah.exec(n[2]);e&&(n[2]=e[1],n[3]=e[2],n[4]=e[3])}const[t,i]=gh(n[1]||Ea["a"],n[2]);return oh(i,t,n[3]?+n[3]:void 0,n[4]?+n[4]:void 0)}},ch=[nh,lh],uh=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)?((?:[-a-z]+)?:\/.*?|\[native code\]|[^@]*(?:bundle|\d+\.js)|\/[\w\-. /=]+)(?::(\d+))?(?::(\d+))?\s*$/i,dh=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i,hh=e=>{const t=uh.exec(e);if(t){const e=t[3]&&t[3].indexOf(" > eval")>-1;if(e){const e=dh.exec(t[3]);e&&(t[1]=t[1]||"eval",t[3]=e[1],t[4]=e[2],t[5]="")}let n=t[3],i=t[1]||Ea["a"];return[i,n]=gh(i,n),oh(n,i,t[4]?+t[4]:void 0,t[5]?+t[5]:void 0)}},ph=[ih,hh],mh=[ch,ph],fh=Object(Ea["b"])(...mh),gh=(e,t)=>{const n=-1!==e.indexOf("safari-extension"),i=-1!==e.indexOf("safari-web-extension");return n||i?[-1!==e.indexOf("@")?e.split("@")[0]:Ea["a"],n?"safari-extension:"+t:"safari-web-extension:"+t]:[e,t]},vh="undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__,bh={};function _h(e){const t=bh[e];if(t)return t;let n=Iu[e];if(Ba(n))return bh[e]=n.bind(Iu);const i=Iu.document;if(i&&"function"===typeof i.createElement)try{const t=i.createElement("iframe");t.hidden=!0,i.head.appendChild(t);const o=t.contentWindow;o&&o[e]&&(n=o[e]),i.head.removeChild(t)}catch(o){vh&&Sr["c"].warn(`Could not create sandbox iframe for ${e} check, bailing to window.${e}: `,o)}return n?bh[e]=n.bind(Iu):n}function yh(e){bh[e]=void 0}function wh(e){const t=[];function n(){return void 0===e||t.length<e}function i(e){return t.splice(t.indexOf(e),1)[0]||Promise.resolve(void 0)}function o(e){if(!n())return Ha(new Rc("Not adding Promise because buffer limit was reached."));const o=e();return-1===t.indexOf(o)&&t.push(o),o.then(()=>i(o)).then(null,()=>i(o).then(null,()=>{})),o}function s(e){return new Ka((n,i)=>{let o=t.length;if(!o)return n(!0);const s=setTimeout(()=>{e&&e>0&&n(!1)},e);t.forEach(e=>{Wa(e).then(()=>{--o||(clearTimeout(s),n(!0))},i)})})}return{$:t,add:o,drain:s}}const kh=6e4;function xh(e,t=Date.now()){const n=parseInt(""+e,10);if(!isNaN(n))return 1e3*n;const i=Date.parse(""+e);return isNaN(i)?kh:i-t}function jh(e,t){return e[t]||e.all||0}function Oh(e,t,n=Date.now()){return jh(e,t)>n}function Eh(e,{statusCode:t,headers:n},i=Date.now()){const o={...e},s=n&&n["x-sentry-rate-limits"],r=n&&n["retry-after"];if(s)for(const a of s.trim().split(",")){const[e,t,,,n]=a.split(":",5),s=parseInt(e,10),r=1e3*(isNaN(s)?60:s);if(t)for(const a of t.split(";"))"metric_bucket"===a&&n&&!n.split(";").includes("custom")||(o[a]=i+r);else o.all=i+r}else r?o.all=i+xh(r,i):429===t&&(o.all=i+6e4);return o}const Ch=64;function Sh(e,t,n=wh(e.bufferSize||Ch)){let i={};const o=e=>n.drain(e);function s(o){const s=[];if(Cc(o,(t,n)=>{const o=Dc(n);if(Oh(i,o)){const i=Ih(t,n);e.recordDroppedEvent("ratelimit_backoff",o,i)}else s.push(t)}),0===s.length)return Wa({});const r=Oc(o[0],s),a=t=>{Cc(r,(n,i)=>{const o=Ih(n,i);e.recordDroppedEvent(t,Dc(i),o)})},l=()=>t({body:Ic(r)}).then(e=>(void 0!==e.statusCode&&(e.statusCode<200||e.statusCode>=300)&&sr&&Sr["c"].warn(`Sentry responded with status code ${e.statusCode} to sent event.`),i=Eh(i,e),e),e=>{throw a("network_error"),e});return n.add(l).then(e=>e,e=>{if(e instanceof Rc)return sr&&Sr["c"].error("Skipped sending event because buffer is full."),a("queue_overflow"),Wa({});throw e})}return{send:s,flush:o}}function Ih(e,t){if("event"===t||"transaction"===t)return Array.isArray(e)?e[1]:void 0}function Ph(e,t=_h("fetch")){let n=0,i=0;function o(o){const s=o.body.length;n+=s,i++;const r={body:o.body,method:"POST",referrerPolicy:"origin",headers:e.headers,keepalive:n<=6e4&&i<15,...e.fetchOptions};if(!t)return yh("fetch"),Ha("No fetch implementation available");try{return t(e.url,r).then(e=>(n-=s,i--,{statusCode:e.status,headers:{"x-sentry-rate-limits":e.headers.get("X-Sentry-Rate-Limits"),"retry-after":e.headers.get("Retry-After")}}))}catch(a){return yh("fetch"),n-=s,i--,Ha(a)}}return Sh(e,o)}function Th(e){const t=[ca(),Oa(),kd(),dd(),Nd(),th(),Ia(),Wd()];return!1!==e.autoSessionTracking&&t.push(Sd()),t}function Ah(e={}){const t={defaultIntegrations:Th(e),release:"string"===typeof __SENTRY_RELEASE__?__SENTRY_RELEASE__:gu.SENTRY_RELEASE&&gu.SENTRY_RELEASE.id?gu.SENTRY_RELEASE.id:void 0,autoSessionTracking:!0,sendClientReports:!0};return null==e.defaultIntegrations&&delete e.defaultIntegrations,{...t,...e}}function Lh(){const e="undefined"!==typeof gu.window&&gu;if(!e)return!1;const t=e.chrome?"chrome":"browser",n=e[t],i=n&&n.runtime&&n.runtime.id,o=gu.location&&gu.location.href||"",s=["chrome-extension:","moz-extension:","ms-browser-extension:","safari-web-extension:"],r=!!i&&gu===gu.top&&s.some(e=>o.startsWith(e+"//")),a="undefined"!==typeof e.nw;return!!i&&!r&&!a}function Dh(e={}){const t=Ah(e);if(!t.skipBrowserExtensionCheck&&Lh())return void Object(Sr["b"])(()=>{console.error("[Sentry] You cannot run Sentry this way in a browser extension, check: https://docs.sentry.io/platforms/javascript/best-practices/browser-extensions/")});Xc&&(Ma()||Sr["c"].warn("No Fetch API detected. The Sentry SDK requires a Fetch API compatible environment to send events. Please add a Fetch API polyfill."));const n={...t,stackParser:Object(Ea["e"])(t.stackParser||fh),integrations:ta(t),transport:t.transport||Ph};return Fa(xu,n)}const zh=["activate","mount","update"],qh="undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__,Nh=/(?:^|[-_])(\w)/g,Mh=e=>e.replace(Nh,e=>e.toUpperCase()).replace(/[-_]/g,""),Bh="<Root>",Vh="<Anonymous>",Fh=(e,t)=>e.repeat(t),Rh=(e,t)=>{if(!e)return Vh;if(e.$root===e)return Bh;if(!e.$options)return Vh;const n=e.$options;let i=n.name||n._componentTag||n.__name;const o=n.__file;if(!i&&o){const e=o.match(/([^/\\]+)\.vue$/);e&&(i=e[1])}return(i?`<${Mh(i)}>`:Vh)+(o&&!1!==t?" at "+o:"")},Uh=e=>{if(e&&(e._isVue||e.__isVue)&&e.$parent){const t=[];let n=0;while(e){if(t.length>0){const i=t[t.length-1];if(i.constructor===e.constructor){n++,e=e.$parent;continue}n>0&&(t[t.length-1]=[i,n],n=0)}t.push(e),e=e.$parent}const i=t.map((e,t)=>""+((0===t?"---\x3e ":Fh(" ",5+2*t))+(Array.isArray(e)?`${Rh(e[0])}... (${e[1]} recursive calls)`:Rh(e)))).join("\n");return"\n\nfound in\n\n"+i}return`\n\n(found in ${Rh(e)})`},$h=(e,t)=>{const{errorHandler:n,warnHandler:i,silent:o}=e.config;e.config.errorHandler=(s,r,a)=>{const l=Rh(r,!1),c=r?Uh(r):"",u={componentName:l,lifecycleHook:a,trace:c};if(t.attachProps&&r&&(r.$options&&r.$options.propsData?u.propsData=r.$options.propsData:r.$props&&(u.propsData=r.$props)),setTimeout(()=>{ac(s,{captureContext:{contexts:{vue:u}},mechanism:{handled:!1}})}),"function"===typeof n&&e.config.errorHandler&&n.call(e,s,r,a),t.logErrors){const e="undefined"!==typeof console,t=`Error in ${a}: "${s&&s.toString()}"`;i?i.call(null,t,r,c):e&&!o&&Object(Sr["b"])(()=>{console.error(`[Vue warn]: ${t}${c}`)})}}};function Wh(e){if(!sr)return;const{description:t="< unknown name >",op:n="< unknown op >",parent_span_id:i}=Ol(e),{spanId:o}=e.spanContext(),s=Sl(e),r=Dl(e),a=r===e,l=`[Tracing] Starting ${s?"sampled":"unsampled"} ${a?"root ":""}span`,c=["op: "+n,"name: "+t,"ID: "+o];if(i&&c.push("parent ID: "+i),!a){const{op:e,description:t}=Ol(r);c.push("root ID: "+r.spanContext().spanId),e&&c.push("root op: "+e),t&&c.push("root description: "+t)}Sr["c"].log(`${l}\n  ${c.join("\n  ")}`)}function Hh(e){if(!sr)return;const{description:t="< unknown name >",op:n="< unknown op >"}=Ol(e),{spanId:i}=e.spanContext(),o=Dl(e),s=o===e,r=`[Tracing] Finishing "${n}" ${s?"root ":""}span "${t}" with ID ${i}`;Sr["c"].log(r)}function Kh(e,t){if(!pl(e))return[!1];let n;n="function"===typeof e.tracesSampler?e.tracesSampler(t):void 0!==t.parentSampled?t.parentSampled:"undefined"!==typeof e.tracesSampleRate?e.tracesSampleRate:1;const i=Uc(n);if(void 0===i)return sr&&Sr["c"].warn("[Tracing] Discarding transaction because of invalid sample rate."),[!1];if(!i)return sr&&Sr["c"].log("[Tracing] Discarding transaction because "+("function"===typeof e.tracesSampler?"tracesSampler returned 0 or false":"a negative sampling decision was inherited or tracesSampleRate is set to 0")),[!1,i];const o=Math.random()<i;return o?[!0,i]:(sr&&Sr["c"].log(`[Tracing] Discarding transaction because it's not included in the random sample (sampling rate = ${Number(n)})`),[!1,i])}class Gh{constructor(e={}){this._traceId=e.traceId||Ir(),this._spanId=e.spanId||Pr()}spanContext(){return{spanId:this._spanId,traceId:this._traceId,traceFlags:bl}}end(e){}setAttribute(e,t){return this}setAttributes(e){return this}setStatus(e){return this}updateName(e){return this}isRecording(){return!1}addEvent(e,t,n){return this}addLink(e){return this}addLinks(e){return this}recordException(e,t){}}function Jh(e){if(!e||0===e.length)return;const t={};return e.forEach(e=>{const n=e.attributes||{},i=n[ol],o=n[sl];"string"===typeof i&&"number"===typeof o&&(t[e.name]={value:o,unit:i})}),t}const Yh="_sentryScope",Zh="_sentryIsolationScope";function Xh(e,t,n){e&&(Object(cr["a"])(e,Zh,n),Object(cr["a"])(e,Yh,t))}function Qh(e){return{scope:e[Yh],isolationScope:e[Zh]}}const ep=1e3;class tp{constructor(e={}){this._traceId=e.traceId||Ir(),this._spanId=e.spanId||Pr(),this._startTime=e.startTimestamp||pr(),this._attributes={},this.setAttributes({[il]:"manual",[nl]:e.op,...e.attributes}),this._name=e.name,e.parentSpanId&&(this._parentSpanId=e.parentSpanId),"sampled"in e&&(this._sampled=e.sampled),e.endTimestamp&&(this._endTime=e.endTimestamp),this._events=[],this._isStandaloneSpan=e.isStandalone,this._endTime&&this._onSpanEnded()}addLink(e){return this}addLinks(e){return this}recordException(e,t){}spanContext(){const{_spanId:e,_traceId:t,_sampled:n}=this;return{spanId:e,traceId:t,traceFlags:n?_l:bl}}setAttribute(e,t){return void 0===t?delete this._attributes[e]:this._attributes[e]=t,this}setAttributes(e){return Object.keys(e).forEach(t=>this.setAttribute(t,e[t])),this}updateStartTime(e){this._startTime=xl(e)}setStatus(e){return this._status=e,this}updateName(e){return this._name=e,this.setAttribute(el,"custom"),this}end(e){this._endTime||(this._endTime=xl(e),Hh(this),this._onSpanEnded())}getSpanJSON(){return Object(cr["c"])({data:this._attributes,description:this._name,op:this._attributes[nl],parent_span_id:this._parentSpanId,span_id:this._spanId,start_timestamp:this._startTime,status:Il(this._status),timestamp:this._endTime,trace_id:this._traceId,origin:this._attributes[il],_metrics_summary:fl(this),profile_id:this._attributes[rl],exclusive_time:this._attributes[al],measurements:Jh(this._events),is_segment:this._isStandaloneSpan&&Dl(this)===this||void 0,segment_id:this._isStandaloneSpan?Dl(this).spanContext().spanId:void 0})}isRecording(){return!this._endTime&&!!this._sampled}addEvent(e,t,n){sr&&Sr["c"].log("[Tracing] Adding an event to span:",e);const i=np(t)?t:n||pr(),o=np(t)?{}:t||{},s={name:e,time:xl(i),attributes:o};return this._events.push(s),this}isStandaloneSpan(){return!!this._isStandaloneSpan}_onSpanEnded(){const e=Zr();e&&e.emit("spanEnd",this);const t=this._isStandaloneSpan||this===Dl(this);if(!t)return;if(this._isStandaloneSpan)return void(this._sampled?sp(Vc([this],e)):(sr&&Sr["c"].log("[Tracing] Discarding standalone span because its trace was not chosen to be sampled."),e&&e.recordDroppedEvent("sample_rate","span")));const n=this._convertSpanToTransaction();if(n){const e=Qh(this).scope||Kr();e.captureEvent(n)}}_convertSpanToTransaction(){if(!ip(Ol(this)))return;this._name||(sr&&Sr["c"].warn("Transaction has no name, falling back to `<unlabeled transaction>`."),this._name="<unlabeled transaction>");const{scope:e,isolationScope:t}=Qh(this),n=e||Kr(),i=n.getClient()||Zr();if(!0!==this._sampled)return sr&&Sr["c"].log("[Tracing] Discarding transaction because its trace was not chosen to be sampled."),void(i&&i.recordDroppedEvent("sample_rate","transaction"));const o=Ll(this).filter(e=>e!==this&&!op(e)),s=o.map(e=>Ol(e)).filter(ip),r=this._attributes[el],a={contexts:{trace:wl(this)},spans:s.length>ep?s.sort((e,t)=>e.start_timestamp-t.start_timestamp).slice(0,ep):s,start_timestamp:this._startTime,timestamp:this._endTime,transaction:this._name,type:"transaction",sdkProcessingMetadata:{capturedSpanScope:e,capturedSpanIsolationScope:t,...Object(cr["c"])({dynamicSamplingContext:Fl(this)})},_metrics_summary:fl(this),...r&&{transaction_info:{source:r}}},l=Jh(this._events),c=l&&Object.keys(l).length;return c&&(sr&&Sr["c"].log("[Measurements] Adding measurements to transaction event",JSON.stringify(l,void 0,2)),a.measurements=l),a}}function np(e){return e&&"number"===typeof e||e instanceof Date||Array.isArray(e)}function ip(e){return!!e.start_timestamp&&!!e.timestamp&&!!e.span_id&&!!e.trace_id}function op(e){return e instanceof tp&&e.isStandaloneSpan()}function sp(e){const t=Zr();if(!t)return;const n=e[1];n&&0!==n.length?t.sendEnvelope(e):t.recordDroppedEvent("before_send","span")}const rp="__SENTRY_SUPPRESS_TRACING__";function ap(e){const t=dp();if(t.startInactiveSpan)return t.startInactiveSpan(e);const n=up(e),{forceTransaction:i,parentSpan:o}=e,s=e.scope?t=>Yr(e.scope,t):void 0!==o?e=>lp(o,e):e=>e();return s(()=>{const t=Kr(),o=mp(t),s=e.onlyIfParent&&!o;return s?new Gh:cp({parentSpan:o,spanArguments:n,forceTransaction:i,scope:t})})}function lp(e,t){const n=dp();return n.withActiveSpan?n.withActiveSpan(e,t):Yr(n=>(Lr(n,e||void 0),t(n)))}function cp({parentSpan:e,spanArguments:t,forceTransaction:n,scope:i}){if(!pl())return new Gh;const o=Gr();let s;if(e&&!n)s=pp(e,i,t),Al(e,s);else if(e){const n=Fl(e),{traceId:o,spanId:r}=e.spanContext(),a=Sl(e);s=hp({traceId:o,parentSpanId:r,...t},i,a),Ml(s,n)}else{const{traceId:e,dsc:n,parentSpanId:r,sampled:a}={...o.getPropagationContext(),...i.getPropagationContext()};s=hp({traceId:e,parentSpanId:r,...t},i,a),n&&Ml(s,n)}return Wh(s),Xh(s,i,o),s}function up(e){const t=e.experimental||{},n={isStandalone:t.standalone,...e};if(e.startTime){const t={...n};return t.startTimestamp=xl(e.startTime),delete t.startTime,t}return n}function dp(){const e=ar();return Hr(e)}function hp(e,t,n){const i=Zr(),o=i&&i.getOptions()||{},{name:s="",attributes:r}=e,[a,l]=t.getScopeData().sdkProcessingMetadata[rp]?[!1]:Kh(o,{name:s,parentSampled:n,attributes:r,transactionContext:{name:s,parentSampled:n}}),c=new tp({...e,attributes:{[el]:"custom",...e.attributes},sampled:a});return void 0!==l&&c.setAttribute(tl,l),i&&i.emit("spanStart",c),c}function pp(e,t,n){const{spanId:i,traceId:o}=e.spanContext(),s=!t.getScopeData().sdkProcessingMetadata[rp]&&Sl(e),r=s?new tp({...n,parentSpanId:i,traceId:o,sampled:s}):new Gh({traceId:o});Al(e,r);const a=Zr();return a&&(a.emit("spanStart",r),n.endTimestamp&&a.emit("spanEnd",r)),r}function mp(e){const t=Dr(e);if(!t)return;const n=Zr(),i=n?n.getOptions():{};return i.parentSpanIsAlwaysRootSpan?Dl(t):t}const fp="ui.vue",gp={activate:["activated","deactivated"],create:["beforeCreate","created"],unmount:["beforeUnmount","unmounted"],destroy:["beforeDestroy","destroyed"],mount:["beforeMount","mounted"],update:["beforeUpdate","updated"]};function vp(e,t,n){e.$_sentryRootSpanTimer&&clearTimeout(e.$_sentryRootSpanTimer),e.$_sentryRootSpanTimer=setTimeout(()=>{e.$root&&e.$root.$_sentryRootSpan&&(e.$root.$_sentryRootSpan.end(t),e.$root.$_sentryRootSpan=void 0)},n)}function bp(e,t){function n(e){return e.replace(/^<([^\s]*)>(?: at [^\s]*)?$/,"$1")}const i=e.some(e=>n(t)===n(e));return i}const _p=e=>{const t=(e.hooks||[]).concat(zh).filter((e,t,n)=>n.indexOf(e)===t),n={};for(const i of t){const t=gp[i];if(t)for(const o of t)n[o]=function(){const n=this.$root===this;n&&(this.$_sentryRootSpan=this.$_sentryRootSpan||ap({name:"Application Render",op:fp+".render",attributes:{[il]:"auto.ui.vue"},onlyIfParent:!0}));const s=Rh(this,!1),r=Array.isArray(e.trackComponents)?bp(e.trackComponents,s):e.trackComponents;if(n||r)if(this.$_sentrySpans=this.$_sentrySpans||{},o==t[0]){const e=this.$root&&this.$root.$_sentryRootSpan||zl();if(e){const e=this.$_sentrySpans[i];e&&e.end(),this.$_sentrySpans[i]=ap({name:"Vue "+s,op:`${fp}.${i}`,attributes:{[il]:"auto.ui.vue"},onlyIfParent:!0})}}else{const t=this.$_sentrySpans[i];if(!t)return;t.end(),vp(this,pr(),e.timeout)}};else qh&&Sr["c"].warn("Unknown hook: "+i)}return n},yp=rr["a"],wp={Vue:yp.Vue,attachProps:!0,logErrors:!0,attachErrorHandler:!0,hooks:zh,timeout:2e3,trackComponents:!1},kp="Vue",xp=sa((e={})=>({name:kp,setup(t){const n={...wp,...t.getOptions(),...e};if(n.Vue||n.app)if(n.app){const e=Array.isArray(n.app)?n.app:[n.app];e.forEach(e=>jp(e,n))}else n.Vue&&jp(n.Vue,n);else Object(Sr["b"])(()=>{console.warn("[@sentry/vue]: Misconfigured SDK. Vue specific errors will not be captured. Update your `Sentry.init` call with an appropriate config option: `app` (Application Instance - Vue 3) or `Vue` (Vue Constructor - Vue 2).")})}})),jp=(e,t)=>{if(qh){const t=e,n=t._instance&&t._instance.isMounted;!0===n&&Object(Sr["b"])(()=>{console.warn("[@sentry/vue]: Misconfigured SDK. Vue app is already mounted. Make sure to call `app.mount()` after `Sentry.init()`.")})}t.attachErrorHandler&&$h(e,t),pl(t)&&e.mixin(_p({...t,...t.tracingOptions}))};function Op(e={}){const t={_metadata:{sdk:{name:"sentry.javascript.vue",packages:[{name:"npm:@sentry/vue",version:or["a"]}],version:or["a"]}},defaultIntegrations:[...Th(e),xp()],...e};return Dh(t)}var Ep=n("f9d5"),Cp=n.n(Ep);n("4413");const Sp={class:"hello"},Ip=Hi("h1",null,"ModuleNotFound Loaded!",-1),Pp=[Ip];function Tp(e,t,n,i,o,s){return Di(),Bi("div",Sp,Pp)}var Ap={name:"ModuleNotFound",mounted(){console.log("SelectStone mounted")}},Lp=n("6b0d"),Dp=n.n(Lp);const zp=Dp()(Ap,[["render",Tp]]);var qp=zp;const Np={key:0,id:"step1",class:"wizard"},Mp={key:0,class:"alert alert-danger"},Bp={class:"form-row"},Vp=Hi("label",{class:"col3 col-form-label"},"Project naam",-1),Fp={class:"col1"},Rp={key:0,class:"input-validation-icon"},Up=Hi("img",{src:"/projects/rde/templates/frontend/images/check_green.svg",alt:"",width:"20"},null,-1),$p=[Up],Wp={class:"col7"},Hp={key:0,class:"input-error-message"},Kp={class:"col1"},Gp=Hi("a",{class:"question-mark-inline fa fa-info-circle"},null,-1),Jp=Hi("label",{class:"col3 col-form-label"},"Project referentie",-1),Yp={class:"col1"},Zp={key:0,class:"input-validation-icon"},Xp=Hi("img",{src:"/projects/rde/templates/frontend/images/check_green.svg",alt:"",width:"20"},null,-1),Qp=[Xp],em={class:"col7"},tm=["readonly"],nm={class:"col1"},im=Hi("a",{class:"question-mark-inline fa fa-info-circle"},null,-1),om={class:"col3 col-form-label"},sm={class:"col1"},rm={key:0,class:"input-validation-icon"},am=Hi("img",{src:"/projects/rde/templates/frontend/images/check_green.svg",alt:"",width:"20"},null,-1),lm=[am],cm={class:"col7"},um=Hi("div",{class:"col1"},null,-1),dm={key:0},hm=Hi("div",{class:"col4"},null,-1),pm={class:"col8",style:{"padding-top":"10px"}},mm=["href"],fm=Hi("span",{class:"fa fa-chevron-right",style:{color:"#888888"}},null,-1),gm=Hi("label",{class:"col3 col-form-label"},"Kleur",-1),vm={class:"col1"},bm={key:0,class:"input-validation-icon"},_m=Hi("img",{src:"/projects/rde/templates/frontend/images/check_green.svg",alt:"",width:"20"},null,-1),ym=[_m],wm={class:"col7"},km={class:"col1"},xm=Hi("a",{class:"question-mark-inline fa fa-info-circle"},null,-1),jm=Hi("div",{class:"col4"},null,-1),Om={class:"col8",style:{"padding-top":"10px"}},Em=["href"],Cm=Hi("span",{class:"fa fa-chevron-right",style:{color:"#888888"}},null,-1),Sm=Hi("label",{class:"col3 col-form-label"},"Model",-1),Im={class:"col1"},Pm={key:0,class:"input-validation-icon"},Tm=Hi("img",{src:"/projects/rde/templates/frontend/images/check_green.svg",alt:"",width:"20"},null,-1),Am=[Tm],Lm={class:"col7"},Dm={class:"col1"},zm=Hi("a",{class:"question-mark-inline fa fa-info-circle"},null,-1),qm=["innerHTML"],Nm=Hi("label",{class:"col3 col-form-label"},"Diepte",-1),Mm={class:"col1"},Bm={key:0,class:"input-validation-icon"},Vm=Hi("img",{src:"/projects/rde/templates/frontend/images/check_green.svg",alt:"",width:"20"},null,-1),Fm=[Vm],Rm={class:"col7"},Um={class:"select"},$m=["disabled"],Wm=Hi("option",{value:""}," Kies diepte... ",-1),Hm=["value"],Km={class:"col1"},Gm=Hi("a",{class:"question-mark-inline fa fa-info-circle"},null,-1),Jm=Hi("label",{class:"col3 col-form-label"},"Dikte",-1),Ym={class:"col1"},Zm={key:0,class:"input-validation-icon"},Xm=Hi("img",{src:"/projects/rde/templates/frontend/images/check_green.svg",alt:"",width:"20"},null,-1),Qm=[Xm],ef={class:"col7"},tf={class:"select"},nf=["disabled"],of=Hi("option",{value:""}," Kies dikte... ",-1),sf=["value"],rf={class:"col1"},af=Hi("a",{class:"question-mark-inline fa fa-info-circle"},null,-1),lf=Hi("label",{class:"col3 col-form-label"},"Op maat gemaakt",-1),cf={class:"col1"},uf={key:0,class:"input-validation-icon"},df=Hi("img",{src:"/projects/rde/templates/frontend/images/check_green.svg",alt:"",width:"20"},null,-1),hf=[df],pf={class:"col7 select_custom_wrapper"},mf={class:"select_custom"},ff=Hi("label",null,"A",-1),gf={class:"select select_custom_depth"},vf=["disabled"],bf=Hi("option",{value:""}," Kies diepte... ",-1),_f=["value"],yf=Hi("label",null,"B",-1),wf={class:"select select_custom_thickness"},kf=["disabled"],xf=Hi("option",{value:""}," Kies dikte... ",-1),jf=["value"],Of=Hi("label",null,"C",-1),Ef={class:"select select_custom_height"},Cf=["disabled"],Sf=Hi("option",{value:""}," Kies hoogte... ",-1),If=["value"],Pf=Hi("label",null,"D",-1),Tf={class:"select select_custom_width_click"},Af=["disabled"],Lf=Hi("option",{value:""}," Breedte klik... ",-1),Df=["value"],zf=Hi("label",null,"E",-1),qf={class:"select select_custom_height_click"},Nf=["disabled"],Mf=Hi("option",{value:""}," Hoogte klik... ",-1),Bf=["value"],Vf={class:"custom_img"},Ff=["src"],Rf={class:"col1"},Uf=Hi("a",{class:"question-mark-inline fa fa-info-circle"},null,-1),$f={class:"col3 col-form-label"},Wf={style:{color:"red"}},Hf={class:"col1"},Kf={key:0,class:"input-validation-icon"},Gf=Hi("img",{src:"/projects/rde/templates/frontend/images/check_green.svg",alt:"",width:"20"},null,-1),Jf=[Gf],Yf={class:"col7 select_custom_wrapper"},Zf={class:"select_custom"},Xf=Hi("label",null,"A",-1),Qf=["readonly"],eg=Hi("label",null,"B",-1),tg=["readonly"],ng=Hi("label",null,"C",-1),ig=["readonly"],og=Hi("label",null,"D",-1),sg=["readonly"],rg=Hi("label",null,"E",-1),ag=["readonly"],lg={class:"custom_img"},cg=["src"],ug={class:"col1"},dg=Hi("a",{class:"question-mark-inline fa fa-info-circle"},null,-1),hg={class:"col3 col-form-label"},pg={class:"col1"},mg={key:0,class:"input-validation-icon"},fg=Hi("img",{src:"/projects/rde/templates/frontend/images/check_green.svg",alt:"",width:"20"},null,-1),gg=[fg],vg={class:"col7"},bg={class:"col1"},_g=Hi("a",{class:"question-mark-inline fa fa-info-circle"},null,-1),yg={key:7,class:"form-row"},wg={class:"col4 col-form-label"},kg=Hi("br",null,null,-1),xg=["href"],jg=Hi("span",{class:"fa fa-download"},null,-1),Og={class:"col7 col12-xs"},Eg=["src"],Cg=Hi("div",{class:"col1"},null,-1),Sg={key:0,class:"col12 col-form-label",style:{"padding-top":"15px"}},Ig={class:"wizard_stonealert"},Pg=Hi("a",{class:"fa fa-exclamation-circle",title:"Let op"},null,-1),Tg=Hi("br",null,null,-1),Ag=Hi("br",null,null,-1),Lg=["value"],Dg=Hi("i",{class:"fa fa-chevron-left"},null,-1),zg=["disabled"],qg=Hi("i",{class:"fa fa-chevron-right"},null,-1);function Ng(e,t,n,o,s,r){const a=In("GsdPopper"),l=In("gsd-select"),c=In("color-select"),u=In("ral-color-select"),d=In("gsd-select-grouped"),h=In("NaturalStoneWallCopingFlat");return s.loading?Qi("",!0):(Di(),Bi("div",Np,[s.errors.length>0?(Di(),Bi("div",Mp,[Zi(" Er zijn foutmeldingen opgetreden, controleer uw invoer: "),Hi("ul",null,[(Di(!0),Bi(Si,null,Ln(s.errors,e=>(Di(),Bi("li",{key:e},Object(i["P"])(e),1))),128))])])):Qi("",!0),Hi("form",{method:"post",ref:r.setForm},[Hi("div",Bp,[Vp,Hi("div",Fp,[s.is_valid.projectName?(Di(),Bi("div",Rp,$p)):Qi("",!0)]),Hi("div",Wp,[En(Hi("input",{type:"text","onUpdate:modelValue":t[0]||(t[0]=e=>s.quotation.projectName=e),name:"projectName",placeholder:"Project naam",class:Object(i["L"])(["form-input",{"has-error":!s.is_valid}]),autofocus:"",maxlength:"30"},null,2),[[Ms,s.quotation.projectName]]),s.input_errors.projectName?(Di(),Bi("p",Hp,Object(i["P"])(s.input_errors.projectName),1)):Qi("",!0)]),Hi("div",Kp,[Ki(a,{content:"Naam van project of offerte"},{default:St(()=>[Gp]),_:1})])]),Hi("div",{class:Object(i["L"])(["form-row",{disabled:!s.is_active_projectReference}])},[Jp,Hi("div",Yp,[s.is_active_projectReference?(Di(),Bi("div",Zp,Qp)):Qi("",!0)]),Hi("div",em,[En(Hi("input",{type:"text","onUpdate:modelValue":t[1]||(t[1]=e=>s.quotation.projectReference=e),name:"projectReference",readonly:!s.is_active_projectReference,placeholder:"Project referentie",class:Object(i["L"])(["form-input",{"has-error":!s.is_valid}]),maxlength:"20"},null,10,tm),[[Ms,s.quotation.projectReference]])]),Hi("div",nm,[Ki(a,{content:"Uw referentie"},{default:St(()=>[im]),_:1})])],2),Hi("div",{class:Object(i["L"])(["form-row",{disabled:!s.is_active_brandId}])},[Hi("label",om,Object(i["P"])(r.brandTextTitle),1),Hi("div",sm,[s.is_valid.brandId&&s.is_active_brandId?(Di(),Bi("div",rm,lm)):Qi("",!0)]),Hi("div",cm,[Ki(l,{ref:"brandId",name:"brandId",no_selection_text:r.brandTextEmptyoption,options:s.brands,value:s.quotation.brandId,disabled:!s.is_active_brandId,onChangeValue:r.setBrandId},null,8,["no_selection_text","options","value","disabled","onChangeValue"])]),um,""!=r.materialLink?(Di(),Bi("div",dm,[hm,Hi("div",pm,[Hi("a",{href:r.materialLink,target:"_blank",style:{color:"#333"}},[Zi("Meer informatie over de mogelijke materialen "),fm],8,mm)])])):Qi("",!0)],2),Hi("div",{class:Object(i["L"])(["form-row",{disabled:!s.is_active_colorId}])},[gm,Hi("div",vm,[s.is_valid.colorId&&s.is_active_colorId?(Di(),Bi("div",bm,ym)):Qi("",!0)]),Hi("div",wm,[En(Ki(c,{ref:"colorSelect",quotation:s.quotation,possible_colors:s.possible_colors,color:s.color,is_active_color_id:s.is_active_colorId,onChangeColorId:r.setColorId},null,8,["quotation","possible_colors","color","is_active_color_id","onChangeColorId"]),[[Zs,!s.isIsosill]]),En(Ki(u,{"is-active":s.is_active_colorId,modelValue:s.quotation,"onUpdate:modelValue":t[2]||(t[2]=e=>s.quotation=e),onRalColorChanged:r.setRalColor},null,8,["is-active","modelValue","onRalColorChanged"]),[[Zs,s.isIsosill]])]),Hi("div",km,[Ki(a,{content:r.colorTextInfo},{default:St(()=>[xm]),_:1},8,["content"])]),jm,Hi("div",Om,[Hi("a",{href:r.colorLink,target:"_blank",style:{color:"#333"}},[Zi("Meer informatie over de mogelijke kleuren "),Cm],8,Em)])],2),s.showModel&&!s.showDiepteDikte?(Di(),Bi("div",{key:0,class:Object(i["L"])(["form-row",{disabled:!s.is_active_sizeId}])},[Sm,Hi("div",Im,[s.is_valid.sizeId&&s.is_active_sizeId?(Di(),Bi("div",Pm,Am)):Qi("",!0)]),Hi("div",Lm,[Ki(d,{name:"sizeId",no_selection_text:"Kies een model...",options:s.possible_sizes,value:s.quotation.sizeId,disabled:!s.is_active_sizeId,onChangeValue:r.setSizeId},null,8,["options","value","disabled","onChangeValue"])]),Hi("div",Dm,[Ki(a,null,{content:St(()=>[Hi("div",{innerHTML:r.sizeTextInfo},null,8,qm)]),default:St(()=>[zm]),_:1})])],2)):Qi("",!0),s.showNaturalStoneWallCopingFlat?(Di(),Vi(h,{key:1,name:"NaturalStoneWallCopingFlatSizeId",sizeId:s.quotation.sizeId,sizes:s.sizes,possible_sizes:s.possible_sizes,disabled:!s.is_active_sizeId,onChangeValue:r.setSizeId},null,8,["sizeId","sizes","possible_sizes","disabled","onChangeValue"])):Qi("",!0),s.showDiepteDikte?(Di(),Bi("div",{key:2,class:Object(i["L"])(["form-row",{disabled:!s.is_active_depth}])},[Nm,En(Hi("input",{name:"sizeId","onUpdate:modelValue":t[3]||(t[3]=e=>s.quotation.sizeId=e),type:"hidden"},null,512),[[Ms,s.quotation.sizeId]]),Hi("div",Mm,[s.is_valid.depth&&s.is_active_depth?(Di(),Bi("div",Bm,Fm)):Qi("",!0)]),Hi("div",Rm,[Hi("div",Um,[En(Hi("select",{name:"depth",class:Object(i["L"])(["depth",{"has-error":s.errors.hasOwnProperty("depth")}]),"onUpdate:modelValue":t[4]||(t[4]=e=>s.depth=e),disabled:!s.is_active_depth},[Wm,(Di(!0),Bi(Si,null,Ln(s.possible_depths,e=>(Di(),Bi("option",{value:e,key:e},Object(i["P"])(e),9,Hm))),128))],10,$m),[[Rs,s.depth]])])]),Hi("div",Km,[Ki(a,{content:"Kies uw diepte."},{default:St(()=>[Gm]),_:1})])],2)):Qi("",!0),!s.showDiepteDikte||s.showCustomStone||s.showCustomStoneNatuursteen?Qi("",!0):(Di(),Bi("div",{key:3,class:Object(i["L"])(["form-row",{disabled:!s.is_active_thickness}])},[Jm,Hi("div",Ym,[s.is_valid.thickness&&s.is_active_thickness?(Di(),Bi("div",Zm,Qm)):Qi("",!0)]),Hi("div",ef,[Hi("div",null,[Hi("div",tf,[En(Hi("select",{name:"thickness",class:Object(i["L"])(["thickness",{"has-error":s.errors.hasOwnProperty("thickness")}]),"onUpdate:modelValue":t[5]||(t[5]=e=>s.thickness=e),disabled:!s.is_active_thickness},[of,(Di(!0),Bi(Si,null,Ln(s.possible_thickness,e=>(Di(),Bi("option",{value:e,key:e},Object(i["P"])(e),9,sf))),128))],10,nf),[[Rs,s.thickness]])])])]),Hi("div",rf,[Ki(a,{content:"Kies uw dikte. Dikte voorkant / Dikte achterkant"},{default:St(()=>[af]),_:1})])],2)),s.showDiepteDikte&&s.showCustomStone?(Di(),Bi("div",{key:4,class:Object(i["L"])(["form-row",{disabled:!s.is_active_custom_depth}])},[lf,Hi("div",cf,[s.is_valid.custom_depth&&s.is_active_custom_depth?(Di(),Bi("div",uf,hf)):Qi("",!0)]),Hi("div",pf,[Hi("div",mf,[Hi("div",null,[ff,Hi("div",gf,[En(Hi("select",{name:"custom_depth",class:Object(i["L"])(["custom_depth",{"has-error":s.errors.hasOwnProperty("custom_depth")}]),"onUpdate:modelValue":t[6]||(t[6]=e=>s.custom_depth=e),disabled:!s.is_active_custom_depth},[bf,(Di(!0),Bi(Si,null,Ln(s.possible_custom_depths,e=>(Di(),Bi("option",{value:e,key:e},Object(i["P"])(e),9,_f))),128))],10,vf),[[Rs,s.custom_depth]])])]),Hi("div",null,[yf,Hi("div",wf,[En(Hi("select",{name:"custom_thickness",class:Object(i["L"])(["custom_thickness",{"has-error":s.errors.hasOwnProperty("custom_thickness")}]),"onUpdate:modelValue":t[7]||(t[7]=e=>s.custom_thickness=e),disabled:!s.is_active_custom_thickness},[xf,(Di(!0),Bi(Si,null,Ln(s.possible_custom_thicknesses,e=>(Di(),Bi("option",{value:e,key:e},Object(i["P"])(e),9,jf))),128))],10,kf),[[Rs,s.custom_thickness]])])]),Hi("div",null,[Of,Hi("div",Ef,[En(Hi("select",{name:"custom_height",class:Object(i["L"])(["custom_height",{"has-error":s.errors.hasOwnProperty("custom_height")}]),"onUpdate:modelValue":t[8]||(t[8]=e=>s.custom_height=e),disabled:!s.is_active_custom_height},[Sf,(Di(!0),Bi(Si,null,Ln(s.possible_custom_heights,e=>(Di(),Bi("option",{value:e,key:e},Object(i["P"])(e),9,If))),128))],10,Cf),[[Rs,s.custom_height]])])]),Hi("div",null,[Pf,Hi("div",Tf,[En(Hi("select",{name:"custom_width_click",class:Object(i["L"])(["custom_width_click",{"has-error":s.errors.hasOwnProperty("custom_width_click")}]),"onUpdate:modelValue":t[9]||(t[9]=e=>s.custom_width_click=e),disabled:!s.is_active_custom_width_click},[Lf,(Di(!0),Bi(Si,null,Ln(s.possible_custom_width_clicks,e=>(Di(),Bi("option",{value:e,key:e},Object(i["P"])(e),9,Df))),128))],10,Af),[[Rs,s.custom_width_click]])])]),Hi("div",null,[zf,Hi("div",qf,[En(Hi("select",{name:"custom_height_click",class:Object(i["L"])(["custom_height_click",{"has-error":s.errors.hasOwnProperty("custom_height_click")}]),"onUpdate:modelValue":t[10]||(t[10]=e=>s.custom_height_click=e),disabled:!s.is_active_custom_height_click},[Mf,(Di(!0),Bi(Si,null,Ln(s.possible_custom_height_clicks,e=>(Di(),Bi("option",{value:e,key:e},Object(i["P"])(e),9,Bf))),128))],10,Nf),[[Rs,s.custom_height_click]])])])]),Hi("div",Vf,[Hi("img",{src:s.customimage},null,8,Ff)])]),Hi("div",Rf,[Ki(a,{content:"Selecteer uw afmetingen. Op de afbeelding ziet uw welke letter bij welke afmeting behoort."},{default:St(()=>[Uf]),_:1})])],2)):Qi("",!0),s.showDiepteDikte&&s.showCustomStoneNatuursteen?(Di(),Bi("div",{key:5,class:Object(i["L"])(["form-row",{disabled:!s.is_active_custom_depth}])},[Hi("label",$f,[Zi("Op maat gemaakt "),Hi("div",Wf,Object(i["P"])(s.customStoneNatuursteenError),1)]),Hi("div",Hf,[s.is_valid.custom_depth&&s.is_active_custom_depth?(Di(),Bi("div",Kf,Jf)):Qi("",!0)]),Hi("div",Yf,[Hi("div",Zf,[Hi("div",null,[Xf,En(Hi("input",{type:"number","onUpdate:modelValue":t[11]||(t[11]=e=>s.custom_depth=e),name:"custom_depth",min:"1",placeholder:"Kies diepte",class:"form-input custom_input",autofocus:"",maxlength:"30",readonly:!s.is_active_custom_depth},null,8,Qf),[[Ms,s.custom_depth]])]),Hi("div",null,[eg,En(Hi("input",{type:"number","onUpdate:modelValue":t[12]||(t[12]=e=>s.custom_thickness=e),name:"custom_thickness",min:"1",placeholder:"Kies dikte",class:"form-input custom_input",autofocus:"",maxlength:"30",readonly:!s.is_active_custom_thickness},null,8,tg),[[Ms,s.custom_thickness]])]),Hi("div",null,[ng,En(Hi("input",{type:"number","onUpdate:modelValue":t[13]||(t[13]=e=>s.custom_height=e),name:"custom_height",min:"1",placeholder:"Kies hoogte",class:"form-input custom_input",autofocus:"",maxlength:"30",readonly:!s.is_active_custom_height},null,8,ig),[[Ms,s.custom_height]])]),Hi("div",null,[og,En(Hi("input",{type:"number","onUpdate:modelValue":t[14]||(t[14]=e=>s.custom_width_click=e),name:"custom_width_click",min:"0",placeholder:"Kies breedte",class:"form-input custom_input",autofocus:"",maxlength:"30",readonly:!s.is_active_custom_width_click},null,8,sg),[[Ms,s.custom_width_click]])]),Hi("div",null,[rg,En(Hi("input",{type:"number","onUpdate:modelValue":t[15]||(t[15]=e=>s.custom_height_click=e),name:"custom_height_click",min:"0",placeholder:"Hoogte klik",class:"form-input custom_input",autofocus:"",maxlength:"30",readonly:!s.is_active_custom_height_click},null,8,ag),[[Ms,s.custom_height_click]])])]),Hi("div",lg,[Hi("img",{src:s.customimage},null,8,cg)])]),Hi("div",ug,[Ki(a,{content:"Selecteer uw afmetingen. Op de afbeelding ziet uw welke letter bij welke afmeting behoort."},{default:St(()=>[dg]),_:1})])],2)):Qi("",!0),s.showEndstone?(Di(),Bi("div",{key:6,class:Object(i["L"])(["form-row",{disabled:!s.is_active_endstone}])},[Hi("label",hg,Object(i["P"])(s.isIsosill?"Type uiteinde":"Eindsteen"),1),Hi("div",pg,[s.is_valid.endstone&&s.is_active_endstone?(Di(),Bi("div",mg,gg)):Qi("",!0)]),Hi("div",vg,[Ki(l,{ref:"endstone",name:"endstone",no_selection_text:"Kies een elementeinde...",options:r.possible_endstonetypes,value:s.quotation.endstone,disabled:!s.is_active_endstone,onChangeValue:r.setEndstone},null,8,["options","value","disabled","onChangeValue"])]),Hi("div",bg,[Ki(a,{content:s.isIsosill?"Bij Isosill kiest u een type uiteinde: vlak, met opstaande zijkant of met stucprofiel. Dit heeft invloed op de aansluiting tegen het gevel- of pleisterwerk.":"Eindstenen zijn raamdorpelstenen met opstaande zijkantjes tegen de neggekant aan. Dit is ter voorkoming van okselvlekken op de gevel langs het kozijn. Wanneer u de optie 'Nee (geglazuurde zijkantjes)' kiest, zijn de zijkantjes ter plaatse van de neggekanten geglazuurd."},{default:St(()=>[_g]),_:1},8,["content"])])],2)):Qi("",!0),s.all_valid?(Di(),Bi("div",yg,[Hi("label",wg,[Zi(" Uw selectie"),kg,null!=s.stone.pdfLocation&&""!=s.stone.pdfLocation?(Di(),Bi("a",{key:0,href:s.stone.pdfLocation,class:"btn btn-secondary",target:"_blank",style:{"margin-bottom":"15px"}},[Zi("Doorsnede PDF "),jg],8,xg)):Qi("",!0)]),Hi("div",Og,[Hi("img",{src:r.stoneimage,class:"stoneimage"},null,8,Eg)]),Cg,s.stone&&""!=s.stone.alert?(Di(),Bi("label",Sg,[Hi("span",Ig,[Pg,Zi(" "+Object(i["P"])(s.stone.alert),1)])])):Qi("",!0)])):Qi("",!0),Tg,Ag,Hi("input",{type:"hidden",value:s.stone.stoneId,name:"stoneId"},null,8,Lg),Hi("button",{onClick:t[16]||(t[16]=Gs(e=>r.clickPrev(),["prevent"])),type:"button",name:"prev",id:"prev",class:"btn",style:{float:"left"}},[Dg,Zi(" Vorige stap")]),Hi("button",{onClick:t[17]||(t[17]=Gs(e=>r.clickNext(),["prevent"])),type:"button",name:"next",id:"next",class:"btn",style:{float:"right"},disabled:!s.all_valid},[Zi("Doorgaan "),qg],8,zg)],512)]))}n("14d9"),n("3c65");const Mg=["src"],Bg={key:1,class:"color-select-name"},Vg={key:2,class:"color-select-name"},Fg=Hi("span",{class:"fa fa-chevron-down"},null,-1),Rg={key:0,class:"color-select"},Ug={key:0,class:"groupname"},$g=["onClick"],Wg=["src"],Hg=["value"];function Kg(e,t,n,o,s,r){return Di(),Bi("div",{ref:r.setColorEl},[Hi("a",{href:"",onClick:t[0]||(t[0]=Gs(e=>r.showSelection(),["prevent"])),id:"color",class:Object(i["L"])(["color-select-btn",{"color-open":s.colorOpen}])},[""!=n.quotation.colorId?(Di(),Bi("div",{key:0,class:Object(i["L"])(["imagesmallfile",{noimage:!r.getImage(n.color)}]),style:Object(i["N"])(r.isColorhex(n.color)?"background-color: "+n.color.hexcolor:"")},[r.getImage(n.color)?(Di(),Bi("img",{key:0,src:r.getImage(n.color)},null,8,Mg)):Qi("",!0)],6)):Qi("",!0),""==n.quotation.colorId?(Di(),Bi("div",Bg," Kies een kleur... ")):(Di(),Bi("div",Vg,Object(i["P"])(n.color.name)+" "+Object(i["P"])(""!=n.color.short&&null!=n.color.short?" - "+n.color.short:""),1)),Fg],2),s.colorOpen?(Di(),Bi("div",Rg,[(Di(!0),Bi(Si,null,Ln(n.possible_colors,(e,t)=>(Di(),Bi("div",{key:t},[s.natuursteenBetonIds.includes(n.quotation.stoneCategoryId)?Qi("",!0):(Di(),Bi("div",Ug,Object(i["P"])(t),1)),Hi("ul",null,[(Di(!0),Bi(Si,null,Ln(e,e=>(Di(),Bi("li",{class:Object(i["L"])({"color-select-active":n.quotation.colorId==e.colorId}),key:e},[Hi("a",{href:"#",onClick:t=>r.clickSelection(e)},[Hi("div",{class:"imagesmallfile",style:Object(i["N"])(r.isColorhex(e)?"background-color: "+e.hexcolor:"")},[r.getImage(e)?(Di(),Bi("img",{key:0,src:r.getImage(e)},null,8,Wg)):Qi("",!0)],4),Zi(" "+Object(i["P"])(e.name)+" "+Object(i["P"])(""!=e.short&&null!=e.short?" - "+e.short:""),1)],8,$g)],2))),128))])]))),128))])):Qi("",!0),Hi("input",{type:"hidden",name:"colorId",value:n.color.colorId},null,8,Hg)],512)}var Gg={props:["quotation","possible_colors","color","is_active_color_id"],data(){return{colorOpen:!1,colorEl:null,natuursteenBetonIds:["7","8","9","10","11","12","13","14","15","16","17","18","19","20","21","22"]}},mounted(){document.addEventListener("mouseup",this.clickOutside)},methods:{showSelection(){if(!this.is_active_color_id)return!1;this.colorOpen=!0},hideSelection(){this.colorOpen=!1},clickSelection(e){event.preventDefault(),this.$emit("changeColorId",e.colorId),this.hideSelection()},clickOutside(e){var t=this.colorEl;t===e.target||t.contains(e.target)||this.hideSelection()},setColorEl(e){e&&(this.colorEl=e)},getImage(e){return null!==e.filename&&""!==e.filename?"/uploads/rde/stonecolors/"+e.filename:(null===e.hexcolor||""===e.hexcolor)&&(null!==e.image&&""!==e.image&&e.image)},isColorhex(e){return(null===e.filename||""===e.filename)&&(null!==e.hexcolor&&""!==e.hexcolor)}}};n("76a5");const Jg=Dp()(Gg,[["render",Kg]]);var Yg=Jg;function Zg(e,t,n,o,s,r){const a=In("Popper");return Di(),Vi(a,io(e.$attrs,{hover:"",disableClickAway:"",arrow:""}),{content:St(t=>[Dn(e.$slots,"content",Object(i["M"])(Ji(t)))]),default:St(()=>[Dn(e.$slots,"default")]),_:3},16)}function Xg(e,t,n){var i,o,s,r,a;function l(){var c=Date.now()-r;c<t&&c>=0?i=setTimeout(l,t-c):(i=null,n||(a=e.apply(s,o),s=o=null))}null==t&&(t=100);var c=function(){s=this,o=arguments,r=Date.now();var c=n&&!i;return i||(i=setTimeout(l,t)),c&&(a=e.apply(s,o),s=o=null),a};return c.clear=function(){i&&(clearTimeout(i),i=null)},c.flush=function(){i&&(a=e.apply(s,o),s=o=null,clearTimeout(i),i=null)},c}Xg.debounce=Xg;var Qg=Xg;function ev(e,t,n){Ne(e)?Rt(e,(e,i)=>{null===i||void 0===i||i.removeEventListener(t,n),null===e||void 0===e||e.addEventListener(t,n)}):vn(()=>{e.addEventListener(t,n)}),yn(()=>{var i;null===(i=Fe(e))||void 0===i||i.removeEventListener(t,n)})}function tv(e,t){const n="pointerdown";if("undefined"===typeof window||!window)return;const i=n=>{const i=Fe(e);i&&(i===n.target||n.composedPath().includes(i)||t(n))};return ev(window,n,i)}function nv(e,t,n){let i=null;const o=Me(!1);vn(()=>{(void 0!==e.content||n.value)&&(o.value=!0),i=new MutationObserver(s),i.observe(t.value,{childList:!0,subtree:!0})}),yn(()=>i.disconnect()),Rt(n,e=>{o.value=!!e});const s=()=>{e.content?o.value=!0:o.value=!1};return{hasContent:o}}function iv(e,t){var n=e.getBoundingClientRect(),i=1,o=1;return{width:n.width/i,height:n.height/o,top:n.top/o,right:n.right/i,bottom:n.bottom/o,left:n.left/i,x:n.left/i,y:n.top/o}}function ov(e){if(null==e)return window;if("[object Window]"!==e.toString()){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function sv(e){var t=ov(e),n=t.pageXOffset,i=t.pageYOffset;return{scrollLeft:n,scrollTop:i}}function rv(e){var t=ov(e).Element;return e instanceof t||e instanceof Element}function av(e){var t=ov(e).HTMLElement;return e instanceof t||e instanceof HTMLElement}function lv(e){if("undefined"===typeof ShadowRoot)return!1;var t=ov(e).ShadowRoot;return e instanceof t||e instanceof ShadowRoot}function cv(e){return{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}}function uv(e){return e!==ov(e)&&av(e)?cv(e):sv(e)}function dv(e){return e?(e.nodeName||"").toLowerCase():null}function hv(e){return((rv(e)?e.ownerDocument:e.document)||window.document).documentElement}function pv(e){return iv(hv(e)).left+sv(e).scrollLeft}function mv(e){return ov(e).getComputedStyle(e)}function fv(e){var t=mv(e),n=t.overflow,i=t.overflowX,o=t.overflowY;return/auto|scroll|overlay|hidden/.test(n+o+i)}function gv(e){var t=e.getBoundingClientRect(),n=t.width/e.offsetWidth||1,i=t.height/e.offsetHeight||1;return 1!==n||1!==i}function vv(e,t,n){void 0===n&&(n=!1);var i=av(t);av(t)&&gv(t);var o=hv(t),s=iv(e),r={scrollLeft:0,scrollTop:0},a={x:0,y:0};return(i||!i&&!n)&&(("body"!==dv(t)||fv(o))&&(r=uv(t)),av(t)?(a=iv(t),a.x+=t.clientLeft,a.y+=t.clientTop):o&&(a.x=pv(o))),{x:s.left+r.scrollLeft-a.x,y:s.top+r.scrollTop-a.y,width:s.width,height:s.height}}function bv(e){var t=iv(e),n=e.offsetWidth,i=e.offsetHeight;return Math.abs(t.width-n)<=1&&(n=t.width),Math.abs(t.height-i)<=1&&(i=t.height),{x:e.offsetLeft,y:e.offsetTop,width:n,height:i}}function _v(e){return"html"===dv(e)?e:e.assignedSlot||e.parentNode||(lv(e)?e.host:null)||hv(e)}function yv(e){return["html","body","#document"].indexOf(dv(e))>=0?e.ownerDocument.body:av(e)&&fv(e)?e:yv(_v(e))}function wv(e,t){var n;void 0===t&&(t=[]);var i=yv(e),o=i===(null==(n=e.ownerDocument)?void 0:n.body),s=ov(i),r=o?[s].concat(s.visualViewport||[],fv(i)?i:[]):i,a=t.concat(r);return o?a:a.concat(wv(_v(r)))}function kv(e){return["table","td","th"].indexOf(dv(e))>=0}function xv(e){return av(e)&&"fixed"!==mv(e).position?e.offsetParent:null}function jv(e){var t=-1!==navigator.userAgent.toLowerCase().indexOf("firefox"),n=-1!==navigator.userAgent.indexOf("Trident");if(n&&av(e)){var i=mv(e);if("fixed"===i.position)return null}var o=_v(e);while(av(o)&&["html","body"].indexOf(dv(o))<0){var s=mv(o);if("none"!==s.transform||"none"!==s.perspective||"paint"===s.contain||-1!==["transform","perspective"].indexOf(s.willChange)||t&&"filter"===s.willChange||t&&s.filter&&"none"!==s.filter)return o;o=o.parentNode}return null}function Ov(e){var t=ov(e),n=xv(e);while(n&&kv(n)&&"static"===mv(n).position)n=xv(n);return n&&("html"===dv(n)||"body"===dv(n)&&"static"===mv(n).position)?t:n||jv(e)||t}var Ev="top",Cv="bottom",Sv="right",Iv="left",Pv="auto",Tv=[Ev,Cv,Sv,Iv],Av="start",Lv="end",Dv="clippingParents",zv="viewport",qv="popper",Nv="reference",Mv=Tv.reduce((function(e,t){return e.concat([t+"-"+Av,t+"-"+Lv])}),[]),Bv=[].concat(Tv,[Pv]).reduce((function(e,t){return e.concat([t,t+"-"+Av,t+"-"+Lv])}),[]),Vv="beforeRead",Fv="read",Rv="afterRead",Uv="beforeMain",$v="main",Wv="afterMain",Hv="beforeWrite",Kv="write",Gv="afterWrite",Jv=[Vv,Fv,Rv,Uv,$v,Wv,Hv,Kv,Gv];function Yv(e){var t=new Map,n=new Set,i=[];function o(e){n.add(e.name);var s=[].concat(e.requires||[],e.requiresIfExists||[]);s.forEach((function(e){if(!n.has(e)){var i=t.get(e);i&&o(i)}})),i.push(e)}return e.forEach((function(e){t.set(e.name,e)})),e.forEach((function(e){n.has(e.name)||o(e)})),i}function Zv(e){var t=Yv(e);return Jv.reduce((function(e,n){return e.concat(t.filter((function(e){return e.phase===n})))}),[])}function Xv(e){var t;return function(){return t||(t=new Promise((function(n){Promise.resolve().then((function(){t=void 0,n(e())}))}))),t}}function Qv(e){return e.split("-")[0]}function eb(e){var t=e.reduce((function(e,t){var n=e[t.name];return e[t.name]=n?Object.assign({},n,t,{options:Object.assign({},n.options,t.options),data:Object.assign({},n.data,t.data)}):t,e}),{});return Object.keys(t).map((function(e){return t[e]}))}function tb(e){var t=ov(e),n=hv(e),i=t.visualViewport,o=n.clientWidth,s=n.clientHeight,r=0,a=0;return i&&(o=i.width,s=i.height,/^((?!chrome|android).)*safari/i.test(navigator.userAgent)||(r=i.offsetLeft,a=i.offsetTop)),{width:o,height:s,x:r+pv(e),y:a}}var nb=Math.max,ib=Math.min,ob=Math.round;function sb(e){var t,n=hv(e),i=sv(e),o=null==(t=e.ownerDocument)?void 0:t.body,s=nb(n.scrollWidth,n.clientWidth,o?o.scrollWidth:0,o?o.clientWidth:0),r=nb(n.scrollHeight,n.clientHeight,o?o.scrollHeight:0,o?o.clientHeight:0),a=-i.scrollLeft+pv(e),l=-i.scrollTop;return"rtl"===mv(o||n).direction&&(a+=nb(n.clientWidth,o?o.clientWidth:0)-s),{width:s,height:r,x:a,y:l}}function rb(e,t){var n=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(n&&lv(n)){var i=t;do{if(i&&e.isSameNode(i))return!0;i=i.parentNode||i.host}while(i)}return!1}function ab(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function lb(e){var t=iv(e);return t.top=t.top+e.clientTop,t.left=t.left+e.clientLeft,t.bottom=t.top+e.clientHeight,t.right=t.left+e.clientWidth,t.width=e.clientWidth,t.height=e.clientHeight,t.x=t.left,t.y=t.top,t}function cb(e,t){return t===zv?ab(tb(e)):av(t)?lb(t):ab(sb(hv(e)))}function ub(e){var t=wv(_v(e)),n=["absolute","fixed"].indexOf(mv(e).position)>=0,i=n&&av(e)?Ov(e):e;return rv(i)?t.filter((function(e){return rv(e)&&rb(e,i)&&"body"!==dv(e)})):[]}function db(e,t,n){var i="clippingParents"===t?ub(e):[].concat(t),o=[].concat(i,[n]),s=o[0],r=o.reduce((function(t,n){var i=cb(e,n);return t.top=nb(i.top,t.top),t.right=ib(i.right,t.right),t.bottom=ib(i.bottom,t.bottom),t.left=nb(i.left,t.left),t}),cb(e,s));return r.width=r.right-r.left,r.height=r.bottom-r.top,r.x=r.left,r.y=r.top,r}function hb(e){return e.split("-")[1]}function pb(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function mb(e){var t,n=e.reference,i=e.element,o=e.placement,s=o?Qv(o):null,r=o?hb(o):null,a=n.x+n.width/2-i.width/2,l=n.y+n.height/2-i.height/2;switch(s){case Ev:t={x:a,y:n.y-i.height};break;case Cv:t={x:a,y:n.y+n.height};break;case Sv:t={x:n.x+n.width,y:l};break;case Iv:t={x:n.x-i.width,y:l};break;default:t={x:n.x,y:n.y}}var c=s?pb(s):null;if(null!=c){var u="y"===c?"height":"width";switch(r){case Av:t[c]=t[c]-(n[u]/2-i[u]/2);break;case Lv:t[c]=t[c]+(n[u]/2-i[u]/2);break}}return t}function fb(){return{top:0,right:0,bottom:0,left:0}}function gb(e){return Object.assign({},fb(),e)}function vb(e,t){return t.reduce((function(t,n){return t[n]=e,t}),{})}function bb(e,t){void 0===t&&(t={});var n=t,i=n.placement,o=void 0===i?e.placement:i,s=n.boundary,r=void 0===s?Dv:s,a=n.rootBoundary,l=void 0===a?zv:a,c=n.elementContext,u=void 0===c?qv:c,d=n.altBoundary,h=void 0!==d&&d,p=n.padding,m=void 0===p?0:p,f=gb("number"!==typeof m?m:vb(m,Tv)),g=u===qv?Nv:qv,v=e.rects.popper,b=e.elements[h?g:u],_=db(rv(b)?b:b.contextElement||hv(e.elements.popper),r,l),y=iv(e.elements.reference),w=mb({reference:y,element:v,strategy:"absolute",placement:o}),k=ab(Object.assign({},v,w)),x=u===qv?k:y,j={top:_.top-x.top+f.top,bottom:x.bottom-_.bottom+f.bottom,left:_.left-x.left+f.left,right:x.right-_.right+f.right},O=e.modifiersData.offset;if(u===qv&&O){var E=O[o];Object.keys(j).forEach((function(e){var t=[Sv,Cv].indexOf(e)>=0?1:-1,n=[Ev,Cv].indexOf(e)>=0?"y":"x";j[e]+=E[n]*t}))}return j}var _b={placement:"bottom",modifiers:[],strategy:"absolute"};function yb(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return!t.some((function(e){return!(e&&"function"===typeof e.getBoundingClientRect)}))}function wb(e){void 0===e&&(e={});var t=e,n=t.defaultModifiers,i=void 0===n?[]:n,o=t.defaultOptions,s=void 0===o?_b:o;return function(e,t,n){void 0===n&&(n=s);var o={placement:"bottom",orderedModifiers:[],options:Object.assign({},_b,s),modifiersData:{},elements:{reference:e,popper:t},attributes:{},styles:{}},r=[],a=!1,l={state:o,setOptions:function(n){var r="function"===typeof n?n(o.options):n;u(),o.options=Object.assign({},s,o.options,r),o.scrollParents={reference:rv(e)?wv(e):e.contextElement?wv(e.contextElement):[],popper:wv(t)};var a=Zv(eb([].concat(i,o.options.modifiers)));return o.orderedModifiers=a.filter((function(e){return e.enabled})),c(),l.update()},forceUpdate:function(){if(!a){var e=o.elements,t=e.reference,n=e.popper;if(yb(t,n)){o.rects={reference:vv(t,Ov(n),"fixed"===o.options.strategy),popper:bv(n)},o.reset=!1,o.placement=o.options.placement,o.orderedModifiers.forEach((function(e){return o.modifiersData[e.name]=Object.assign({},e.data)}));for(var i=0;i<o.orderedModifiers.length;i++)if(!0!==o.reset){var s=o.orderedModifiers[i],r=s.fn,c=s.options,u=void 0===c?{}:c,d=s.name;"function"===typeof r&&(o=r({state:o,options:u,name:d,instance:l})||o)}else o.reset=!1,i=-1}}},update:Xv((function(){return new Promise((function(e){l.forceUpdate(),e(o)}))})),destroy:function(){u(),a=!0}};if(!yb(e,t))return l;function c(){o.orderedModifiers.forEach((function(e){var t=e.name,n=e.options,i=void 0===n?{}:n,s=e.effect;if("function"===typeof s){var a=s({state:o,name:t,instance:l,options:i}),c=function(){};r.push(a||c)}}))}function u(){r.forEach((function(e){return e()})),r=[]}return l.setOptions(n).then((function(e){!a&&n.onFirstUpdate&&n.onFirstUpdate(e)})),l}}var kb={passive:!0};function xb(e){var t=e.state,n=e.instance,i=e.options,o=i.scroll,s=void 0===o||o,r=i.resize,a=void 0===r||r,l=ov(t.elements.popper),c=[].concat(t.scrollParents.reference,t.scrollParents.popper);return s&&c.forEach((function(e){e.addEventListener("scroll",n.update,kb)})),a&&l.addEventListener("resize",n.update,kb),function(){s&&c.forEach((function(e){e.removeEventListener("scroll",n.update,kb)})),a&&l.removeEventListener("resize",n.update,kb)}}var jb={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:xb,data:{}};function Ob(e){var t=e.state,n=e.name;t.modifiersData[n]=mb({reference:t.rects.reference,element:t.rects.popper,strategy:"absolute",placement:t.placement})}var Eb={name:"popperOffsets",enabled:!0,phase:"read",fn:Ob,data:{}},Cb={top:"auto",right:"auto",bottom:"auto",left:"auto"};function Sb(e){var t=e.x,n=e.y,i=window,o=i.devicePixelRatio||1;return{x:ob(ob(t*o)/o)||0,y:ob(ob(n*o)/o)||0}}function Ib(e){var t,n=e.popper,i=e.popperRect,o=e.placement,s=e.variation,r=e.offsets,a=e.position,l=e.gpuAcceleration,c=e.adaptive,u=e.roundOffsets,d=!0===u?Sb(r):"function"===typeof u?u(r):r,h=d.x,p=void 0===h?0:h,m=d.y,f=void 0===m?0:m,g=r.hasOwnProperty("x"),v=r.hasOwnProperty("y"),b=Iv,_=Ev,y=window;if(c){var w=Ov(n),k="clientHeight",x="clientWidth";w===ov(n)&&(w=hv(n),"static"!==mv(w).position&&"absolute"===a&&(k="scrollHeight",x="scrollWidth")),w=w,o!==Ev&&(o!==Iv&&o!==Sv||s!==Lv)||(_=Cv,f-=w[k]-i.height,f*=l?1:-1),o!==Iv&&(o!==Ev&&o!==Cv||s!==Lv)||(b=Sv,p-=w[x]-i.width,p*=l?1:-1)}var j,O=Object.assign({position:a},c&&Cb);return l?Object.assign({},O,(j={},j[_]=v?"0":"",j[b]=g?"0":"",j.transform=(y.devicePixelRatio||1)<=1?"translate("+p+"px, "+f+"px)":"translate3d("+p+"px, "+f+"px, 0)",j)):Object.assign({},O,(t={},t[_]=v?f+"px":"",t[b]=g?p+"px":"",t.transform="",t))}function Pb(e){var t=e.state,n=e.options,i=n.gpuAcceleration,o=void 0===i||i,s=n.adaptive,r=void 0===s||s,a=n.roundOffsets,l=void 0===a||a,c={placement:Qv(t.placement),variation:hb(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:o};null!=t.modifiersData.popperOffsets&&(t.styles.popper=Object.assign({},t.styles.popper,Ib(Object.assign({},c,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:r,roundOffsets:l})))),null!=t.modifiersData.arrow&&(t.styles.arrow=Object.assign({},t.styles.arrow,Ib(Object.assign({},c,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:l})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})}var Tb={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:Pb,data:{}};function Ab(e){var t=e.state;Object.keys(t.elements).forEach((function(e){var n=t.styles[e]||{},i=t.attributes[e]||{},o=t.elements[e];av(o)&&dv(o)&&(Object.assign(o.style,n),Object.keys(i).forEach((function(e){var t=i[e];!1===t?o.removeAttribute(e):o.setAttribute(e,!0===t?"":t)})))}))}function Lb(e){var t=e.state,n={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,n.popper),t.styles=n,t.elements.arrow&&Object.assign(t.elements.arrow.style,n.arrow),function(){Object.keys(t.elements).forEach((function(e){var i=t.elements[e],o=t.attributes[e]||{},s=Object.keys(t.styles.hasOwnProperty(e)?t.styles[e]:n[e]),r=s.reduce((function(e,t){return e[t]="",e}),{});av(i)&&dv(i)&&(Object.assign(i.style,r),Object.keys(o).forEach((function(e){i.removeAttribute(e)})))}))}}var Db={name:"applyStyles",enabled:!0,phase:"write",fn:Ab,effect:Lb,requires:["computeStyles"]},zb=[jb,Eb,Tb,Db],qb=wb({defaultModifiers:zb});function Nb(e){return"x"===e?"y":"x"}function Mb(e,t,n){return nb(e,ib(t,n))}function Bb(e){var t=e.state,n=e.options,i=e.name,o=n.mainAxis,s=void 0===o||o,r=n.altAxis,a=void 0!==r&&r,l=n.boundary,c=n.rootBoundary,u=n.altBoundary,d=n.padding,h=n.tether,p=void 0===h||h,m=n.tetherOffset,f=void 0===m?0:m,g=bb(t,{boundary:l,rootBoundary:c,padding:d,altBoundary:u}),v=Qv(t.placement),b=hb(t.placement),_=!b,y=pb(v),w=Nb(y),k=t.modifiersData.popperOffsets,x=t.rects.reference,j=t.rects.popper,O="function"===typeof f?f(Object.assign({},t.rects,{placement:t.placement})):f,E={x:0,y:0};if(k){if(s||a){var C="y"===y?Ev:Iv,S="y"===y?Cv:Sv,I="y"===y?"height":"width",P=k[y],T=k[y]+g[C],A=k[y]-g[S],L=p?-j[I]/2:0,D=b===Av?x[I]:j[I],z=b===Av?-j[I]:-x[I],q=t.elements.arrow,N=p&&q?bv(q):{width:0,height:0},M=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:fb(),B=M[C],V=M[S],F=Mb(0,x[I],N[I]),R=_?x[I]/2-L-F-B-O:D-F-B-O,U=_?-x[I]/2+L+F+V+O:z+F+V+O,$=t.elements.arrow&&Ov(t.elements.arrow),W=$?"y"===y?$.clientTop||0:$.clientLeft||0:0,H=t.modifiersData.offset?t.modifiersData.offset[t.placement][y]:0,K=k[y]+R-H-W,G=k[y]+U-H;if(s){var J=Mb(p?ib(T,K):T,P,p?nb(A,G):A);k[y]=J,E[y]=J-P}if(a){var Y="x"===y?Ev:Iv,Z="x"===y?Cv:Sv,X=k[w],Q=X+g[Y],ee=X-g[Z],te=Mb(p?ib(Q,K):Q,X,p?nb(ee,G):ee);k[w]=te,E[w]=te-X}}t.modifiersData[i]=E}}var Vb={name:"preventOverflow",enabled:!0,phase:"main",fn:Bb,requiresIfExists:["offset"]},Fb={left:"right",right:"left",bottom:"top",top:"bottom"};function Rb(e){return e.replace(/left|right|bottom|top/g,(function(e){return Fb[e]}))}var Ub={start:"end",end:"start"};function $b(e){return e.replace(/start|end/g,(function(e){return Ub[e]}))}function Wb(e,t){void 0===t&&(t={});var n=t,i=n.placement,o=n.boundary,s=n.rootBoundary,r=n.padding,a=n.flipVariations,l=n.allowedAutoPlacements,c=void 0===l?Bv:l,u=hb(i),d=u?a?Mv:Mv.filter((function(e){return hb(e)===u})):Tv,h=d.filter((function(e){return c.indexOf(e)>=0}));0===h.length&&(h=d);var p=h.reduce((function(t,n){return t[n]=bb(e,{placement:n,boundary:o,rootBoundary:s,padding:r})[Qv(n)],t}),{});return Object.keys(p).sort((function(e,t){return p[e]-p[t]}))}function Hb(e){if(Qv(e)===Pv)return[];var t=Rb(e);return[$b(e),t,$b(t)]}function Kb(e){var t=e.state,n=e.options,i=e.name;if(!t.modifiersData[i]._skip){for(var o=n.mainAxis,s=void 0===o||o,r=n.altAxis,a=void 0===r||r,l=n.fallbackPlacements,c=n.padding,u=n.boundary,d=n.rootBoundary,h=n.altBoundary,p=n.flipVariations,m=void 0===p||p,f=n.allowedAutoPlacements,g=t.options.placement,v=Qv(g),b=v===g,_=l||(b||!m?[Rb(g)]:Hb(g)),y=[g].concat(_).reduce((function(e,n){return e.concat(Qv(n)===Pv?Wb(t,{placement:n,boundary:u,rootBoundary:d,padding:c,flipVariations:m,allowedAutoPlacements:f}):n)}),[]),w=t.rects.reference,k=t.rects.popper,x=new Map,j=!0,O=y[0],E=0;E<y.length;E++){var C=y[E],S=Qv(C),I=hb(C)===Av,P=[Ev,Cv].indexOf(S)>=0,T=P?"width":"height",A=bb(t,{placement:C,boundary:u,rootBoundary:d,altBoundary:h,padding:c}),L=P?I?Sv:Iv:I?Cv:Ev;w[T]>k[T]&&(L=Rb(L));var D=Rb(L),z=[];if(s&&z.push(A[S]<=0),a&&z.push(A[L]<=0,A[D]<=0),z.every((function(e){return e}))){O=C,j=!1;break}x.set(C,z)}if(j)for(var q=m?3:1,N=function(e){var t=y.find((function(t){var n=x.get(t);if(n)return n.slice(0,e).every((function(e){return e}))}));if(t)return O=t,"break"},M=q;M>0;M--){var B=N(M);if("break"===B)break}t.placement!==O&&(t.modifiersData[i]._skip=!0,t.placement=O,t.reset=!0)}}var Gb={name:"flip",enabled:!0,phase:"main",fn:Kb,requiresIfExists:["offset"],data:{_skip:!1}};function Jb(e,t,n){var i=Qv(e),o=[Iv,Ev].indexOf(i)>=0?-1:1,s="function"===typeof n?n(Object.assign({},t,{placement:e})):n,r=s[0],a=s[1];return r=r||0,a=(a||0)*o,[Iv,Sv].indexOf(i)>=0?{x:a,y:r}:{x:r,y:a}}function Yb(e){var t=e.state,n=e.options,i=e.name,o=n.offset,s=void 0===o?[0,0]:o,r=Bv.reduce((function(e,n){return e[n]=Jb(n,t.rects,s),e}),{}),a=r[t.placement],l=a.x,c=a.y;null!=t.modifiersData.popperOffsets&&(t.modifiersData.popperOffsets.x+=l,t.modifiersData.popperOffsets.y+=c),t.modifiersData[i]=r}var Zb={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:Yb},Xb=function(e,t){return e="function"===typeof e?e(Object.assign({},t.rects,{placement:t.placement})):e,gb("number"!==typeof e?e:vb(e,Tv))};function Qb(e){var t,n=e.state,i=e.name,o=e.options,s=n.elements.arrow,r=n.modifiersData.popperOffsets,a=Qv(n.placement),l=pb(a),c=[Iv,Sv].indexOf(a)>=0,u=c?"height":"width";if(s&&r){var d=Xb(o.padding,n),h=bv(s),p="y"===l?Ev:Iv,m="y"===l?Cv:Sv,f=n.rects.reference[u]+n.rects.reference[l]-r[l]-n.rects.popper[u],g=r[l]-n.rects.reference[l],v=Ov(s),b=v?"y"===l?v.clientHeight||0:v.clientWidth||0:0,_=f/2-g/2,y=d[p],w=b-h[u]-d[m],k=b/2-h[u]/2+_,x=Mb(y,k,w),j=l;n.modifiersData[i]=(t={},t[j]=x,t.centerOffset=x-k,t)}}function e_(e){var t=e.state,n=e.options,i=n.element,o=void 0===i?"[data-popper-arrow]":i;null!=o&&("string"!==typeof o||(o=t.elements.popper.querySelector(o),o))&&rb(t.elements.popper,o)&&(t.elements.arrow=o)}var t_={name:"arrow",enabled:!0,phase:"main",fn:Qb,effect:e_,requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};const n_=e=>parseInt(e,10);function i_({arrowPadding:e,emit:t,locked:n,offsetDistance:i,offsetSkid:o,placement:s,popperNode:r,triggerNode:a}){const l=xe({isOpen:!1,popperInstance:null}),c=e=>{var t;null===(t=l.popperInstance)||void 0===t||t.setOptions(t=>({...t,modifiers:[...t.modifiers,{name:"eventListeners",enabled:e}]}))},u=()=>c(!0),d=()=>c(!1),h=()=>{l.isOpen&&(l.isOpen=!1,t("close:popper"))},p=()=>{l.isOpen||(l.isOpen=!0,t("open:popper"))};Rt([()=>l.isOpen,s],async([e])=>{e?(await m(),u()):d()});const m=async()=>{await ct(),l.popperInstance=qb(a.value,r.value,{placement:s.value,modifiers:[Vb,Gb,{name:"flip",enabled:!n.value},t_,{name:"arrow",options:{padding:n_(e.value)}},Zb,{name:"offset",options:{offset:[n_(o.value),n_(i.value)]}}]}),l.popperInstance.update()};return yn(()=>{var e;null===(e=l.popperInstance)||void 0===e||e.destroy()}),{...$e(l),open:p,close:h}}const o_={id:"arrow","data-popper-arrow":""};function s_(e,t){return Di(),Bi("div",o_)}function r_(e,t){void 0===t&&(t={});var n=t.insertAt;if(e&&"undefined"!==typeof document){var i=document.head||document.getElementsByTagName("head")[0],o=document.createElement("style");o.type="text/css","top"===n&&i.firstChild?i.insertBefore(o,i.firstChild):i.appendChild(o),o.styleSheet?o.styleSheet.cssText=e:o.appendChild(document.createTextNode(e))}}var a_='\n#arrow[data-v-20b7fd4a],\n  #arrow[data-v-20b7fd4a]::before {\n    transition: background 250ms ease-in-out;\n    position: absolute;\n    width: calc(10px - var(--popper-theme-border-width, 0px));\n    height: calc(10px - var(--popper-theme-border-width, 0px));\n    box-sizing: border-box;\n    background: var(--popper-theme-background-color);\n}\n#arrow[data-v-20b7fd4a] {\n    visibility: hidden;\n}\n#arrow[data-v-20b7fd4a]::before {\n    visibility: visible;\n    content: "";\n    transform: rotate(45deg);\n}\n\n  /* Top arrow */\n.popper[data-popper-placement^="top"] > #arrow[data-v-20b7fd4a] {\n    bottom: -5px;\n}\n.popper[data-popper-placement^="top"] > #arrow[data-v-20b7fd4a]::before {\n    border-right: var(--popper-theme-border-width)\n      var(--popper-theme-border-style) var(--popper-theme-border-color);\n    border-bottom: var(--popper-theme-border-width)\n      var(--popper-theme-border-style) var(--popper-theme-border-color);\n}\n\n  /* Bottom arrow */\n.popper[data-popper-placement^="bottom"] > #arrow[data-v-20b7fd4a] {\n    top: -5px;\n}\n.popper[data-popper-placement^="bottom"] > #arrow[data-v-20b7fd4a]::before {\n    border-left: var(--popper-theme-border-width)\n      var(--popper-theme-border-style) var(--popper-theme-border-color);\n    border-top: var(--popper-theme-border-width)\n      var(--popper-theme-border-style) var(--popper-theme-border-color);\n}\n\n  /* Left arrow */\n.popper[data-popper-placement^="left"] > #arrow[data-v-20b7fd4a] {\n    right: -5px;\n}\n.popper[data-popper-placement^="left"] > #arrow[data-v-20b7fd4a]::before {\n    border-right: var(--popper-theme-border-width)\n      var(--popper-theme-border-style) var(--popper-theme-border-color);\n    border-top: var(--popper-theme-border-width)\n      var(--popper-theme-border-style) var(--popper-theme-border-color);\n}\n\n  /* Right arrow */\n.popper[data-popper-placement^="right"] > #arrow[data-v-20b7fd4a] {\n    left: -5px;\n}\n';r_(a_);const l_={};l_.render=s_,l_.__scopeId="data-v-20b7fd4a";var c_=l_;const u_=["onKeyup"];var d_={props:{placement:{type:String,default:"bottom",validator:function(e){return["auto","auto-start","auto-end","top","top-start","top-end","bottom","bottom-start","bottom-end","right","right-start","right-end","left","left-start","left-end"].includes(e)}},disableClickAway:{type:Boolean,default:!1},offsetSkid:{type:String,default:"0"},offsetDistance:{type:String,default:"12"},hover:{type:Boolean,default:!1},show:{type:Boolean,default:null},disabled:{type:Boolean,default:!1},openDelay:{type:[Number,String],default:0},closeDelay:{type:[Number,String],default:0},zIndex:{type:[Number,String],default:9999},arrow:{type:Boolean,default:!1},arrowPadding:{type:String,default:"0"},interactive:{type:Boolean,default:!0},locked:{type:Boolean,default:!1},content:{type:String,default:null}},emits:["open:popper","close:popper"],setup(e,{emit:t}){const n=e;rs(t=>({c81fc0a4:e.zIndex}));const o=Co(),s=Me(null),r=Me(null),a=Me(null),l=Me(!1);vn(()=>{const e=o.default();if(e&&e.length>1)return console.error(`[Popper]: The <Popper> component expects only one child element at its root. You passed ${e.length} child nodes.`)});const{arrowPadding:c,closeDelay:u,content:d,disableClickAway:h,disabled:p,interactive:m,locked:f,offsetDistance:g,offsetSkid:v,openDelay:b,placement:_,show:y}=$e(n),{isOpen:w,open:k,close:x}=i_({arrowPadding:c,emit:t,locked:f,offsetDistance:g,offsetSkid:v,placement:_,popperNode:r,triggerNode:a}),{hasContent:j}=nv(o,r,d),O=Eo(()=>null!==y.value),E=Eo(()=>p.value||!j.value),C=Eo(()=>w.value&&!E.value),S=Eo(()=>!h.value&&!O.value),I=Eo(()=>m.value?`border: ${g.value}px solid transparent; margin: -${g.value}px;`:null),P=Qg.debounce(k,b.value),T=Qg.debounce(x,u.value),A=async()=>{E.value||O.value||(T.clear(),P())},L=async()=>{O.value||(P.clear(),T())},D=()=>{w.value?L():A()};return Rt([j,p],([e,t])=>{!w.value||e&&!t||x()}),Rt(w,e=>{e?l.value=!0:Qg.debounce(()=>{l.value=!1},200)}),Bt(()=>{O.value&&(y.value?P():T())}),Bt(()=>{S.value&&tv(s,L)}),(t,n)=>(Di(),Bi("div",{class:"inline-block",style:Object(i["N"])(Fe(I)),onMouseleave:n[2]||(n[2]=t=>e.hover&&L()),ref:(e,t)=>{t["popperContainerNode"]=e,s.value=e}},[Hi("div",{ref:(e,t)=>{t["triggerNode"]=e,a.value=e},onMouseover:n[0]||(n[0]=t=>e.hover&&A()),onClick:D,onFocus:A,onKeyup:Ys(L,["esc"])},[Dn(t.$slots,"default")],40,u_),Ki(ds,{name:"fade"},{default:St(()=>[En(Hi("div",{onClick:n[1]||(n[1]=e=>!Fe(m)&&L()),class:"popper",ref:(e,t)=>{t["popperNode"]=e,r.value=e}},[Dn(t.$slots,"content",{close:Fe(x),isOpen:l.value},()=>[Zi(Object(i["P"])(Fe(d)),1)]),e.arrow?(Di(),Vi(c_,{key:0})):Qi("",!0)],512),[[Zs,Fe(C)]])]),_:3})],36))}},h_="\n.inline-block[data-v-5784ed69] {\n    display: inline-block;\n}\n.popper[data-v-5784ed69] {\n    transition: background 250ms ease-in-out;\n    background: var(--popper-theme-background-color);\n    padding: var(--popper-theme-padding);\n    color: var(--popper-theme-text-color);\n    border-radius: var(--popper-theme-border-radius);\n    border-width: var(--popper-theme-border-width);\n    border-style: var(--popper-theme-border-style);\n    border-color: var(--popper-theme-border-color);\n    box-shadow: var(--popper-theme-box-shadow);\n    z-index: var(--c81fc0a4);\n}\n.popper[data-v-5784ed69]:hover,\n  .popper:hover > #arrow[data-v-5784ed69]::before {\n    background: var(--popper-theme-background-color-hover);\n}\n.inline-block[data-v-5784ed69] {\n    display: inline-block;\n}\n.fade-enter-active[data-v-5784ed69],\n  .fade-leave-active[data-v-5784ed69] {\n    transition: opacity 0.2s ease;\n}\n.fade-enter-from[data-v-5784ed69],\n  .fade-leave-to[data-v-5784ed69] {\n    opacity: 0;\n}\n";r_(h_),d_.__scopeId="data-v-5784ed69";var p_=(()=>{const e=d_;return e.install=t=>{t.component("Popper",e)},e})(),m_=on({name:"GsdPopper",components:{Popper:p_}});n("9476");const f_=Dp()(m_,[["render",Zg]]);var g_=f_;const v_=e=>(Et("data-v-0f4a4e39"),e=e(),Ct(),e),b_=["id"],__=["src","alt"],y_={key:1,class:"vue-gsd-select-name"},w_={key:2,class:"vue-gsd-select-name"},k_=v_(()=>Hi("span",{class:"fa fa-chevron-down"},null,-1)),x_={key:0,class:"vue-gsd-select"},j_={class:"groupname"},O_=["onClick"],E_={key:0,class:"imagesmallfile"},C_=["src"],S_=["name","id","value"];function I_(e,t,n,o,s,r){return Di(),Bi("div",{ref:r.setMainEl},[Hi("a",{href:"",onClick:t[0]||(t[0]=Gs(e=>r.show(),["prevent"])),id:n.name,class:Object(i["L"])(["vue-gsd-select-btn",{"vue-gsd-open":s.open}])},[null!=s.selectedOption&&s.selectedOption.image&&""!==s.selectedOption.value?(Di(),Bi("div",{key:0,class:Object(i["L"])(["imagesmallfile",{noimage:null==s.selectedOption.image||""===s.selectedOption.image}])},[null!=s.selectedOption&&""!==s.selectedOption.image?(Di(),Bi("img",{key:0,src:s.selectedOption.image,alt:s.selectedOption.name},null,8,__)):Qi("",!0)],2)):Qi("",!0),null==s.selectedOption||""===s.selectedOption.value?(Di(),Bi("div",y_,Object(i["P"])(n.no_selection_text),1)):(Di(),Bi("div",w_,Object(i["P"])(s.selectedOption.name),1)),k_],10,b_),s.open?(Di(),Bi("div",x_,[(Di(!0),Bi(Si,null,Ln(n.options,(e,t)=>(Di(),Bi("div",{key:t},[Hi("div",j_,Object(i["P"])(t),1),Hi("ul",null,[(Di(!0),Bi(Si,null,Ln(e,e=>(Di(),Bi("li",{class:Object(i["L"])({"vue-gsd-select-active":e.value===n.value}),key:e.value},[Hi("a",{href:"#",onClick:t=>r.clickSelection(e)},[e.image?(Di(),Bi("div",E_,[null!=e.image&&""!==e.image?(Di(),Bi("img",{key:0,src:e.image},null,8,C_)):Qi("",!0)])):Qi("",!0),Zi(" "+Object(i["P"])(e.name),1)],8,O_)],2))),128))])]))),128))])):Qi("",!0),Hi("input",{type:"hidden",name:n.name,id:n.name,value:s.selectedOptionValue},null,8,S_)],512)}var P_={props:["name","options","value","no_selection_text","disabled"],data(){return{open:!1,mainEl:null,selectedOption:null,selectedOptionValue:""}},watch:{options(){this.updateSelection()},value(){this.updateSelection()}},mounted(){this.updateSelection(),document.addEventListener("mouseup",this.clickOutside)},methods:{updateSelection(){for(var e in this.selectedOption=null,this.selectedOptionValue="",this.options){let t=this.options[e];for(let e=0;e<t.length;e++){let n=t[e];n.value===this.value&&(this.selectedOption=n,this.selectedOptionValue=n.value)}}},show(){if(this.disabled)return!1;this.open=!0},hide(){this.open=!1},clickSelection(e){event.preventDefault(),this.selectedOption=e,this.selectedOptionValue=e.value,this.$emit("changeValue",this.selectedOptionValue,1),this.hide()},clickOutside(e){var t=this.mainEl;t===e.target||t.contains(e.target)||this.hide()},setMainEl(e){e&&(this.mainEl=e)}}};n("75f9");const T_=Dp()(P_,[["render",I_],["__scopeId","data-v-0f4a4e39"]]);var A_=T_;const L_=e=>(Et("data-v-3c4dd073"),e=e(),Ct(),e),D_=["id"],z_=["src","alt"],q_={key:1,class:"vue-gsd-select-name"},N_={key:2,class:"vue-gsd-select-name"},M_=L_(()=>Hi("span",{class:"fa fa-chevron-down"},null,-1)),B_={key:0,class:"vue-gsd-select"},V_=["onClick"],F_={key:0,class:"imagesmallfile"},R_=["src"],U_=["name","id","value"];function $_(e,t,n,o,s,r){return Di(),Bi("div",{ref:r.setMainEl},[Hi("a",{href:"",onClick:t[0]||(t[0]=Gs(e=>r.show(),["prevent"])),id:n.name,class:Object(i["L"])(["vue-gsd-select-btn",{"vue-gsd-open":s.open}])},[null!=s.selectedOption&&s.selectedOption.image&&""!==s.selectedOption.value?(Di(),Bi("div",{key:0,class:Object(i["L"])(["imagesmallfile",{noimage:null==s.selectedOption.image||""===s.selectedOption.image}])},[null!=s.selectedOption&&""!==s.selectedOption.image?(Di(),Bi("img",{key:0,src:s.selectedOption.image,alt:s.selectedOption.name},null,8,z_)):Qi("",!0)],2)):Qi("",!0),null==s.selectedOption||""===s.selectedOption.value?(Di(),Bi("div",q_,Object(i["P"])(n.no_selection_text),1)):(Di(),Bi("div",N_,Object(i["P"])(s.selectedOption.name),1)),M_],10,D_),s.open?(Di(),Bi("div",B_,[Hi("ul",null,[(Di(!0),Bi(Si,null,Ln(n.options,e=>(Di(),Bi("li",{class:Object(i["L"])({"vue-gsd-select-active":e.value===n.value}),key:e.value},[Hi("a",{href:"#",onClick:t=>r.clickSelection(e)},[e.image?(Di(),Bi("div",F_,[null!=e.image&&""!==e.image?(Di(),Bi("img",{key:0,src:e.image},null,8,R_)):Qi("",!0)])):Qi("",!0),Zi(" "+Object(i["P"])(e.name),1)],8,V_)],2))),128))])])):Qi("",!0),Hi("input",{type:"hidden",name:n.name,id:n.name,value:s.selectedOptionValue},null,8,U_)],512)}var W_={props:["name","options","value","no_selection_text","disabled"],data(){return{open:!1,mainEl:null,selectedOption:null,selectedOptionValue:""}},watch:{options(){this.updateSelection()},value(){this.updateSelection()}},mounted(){this.updateSelection(),document.addEventListener("mouseup",this.clickOutside)},methods:{updateSelection(){for(var e in this.selectedOption=null,this.selectedOptionValue="",this.options){let t=this.options[e];if(t.value===this.value){this.selectedOption=t,this.selectedOptionValue=t.value;break}}},show(){if(this.disabled)return!1;this.open=!0},hide(){this.open=!1},clickSelection(e){event.preventDefault(),this.selectedOption=e,this.selectedOptionValue=e.value,this.$emit("changeValue",this.selectedOptionValue,1),this.hide()},clickOutside(e){var t=this.mainEl;t===e.target||t.contains(e.target)||this.hide()},setMainEl(e){e&&(this.mainEl=e)}}};n("613d");const H_=Dp()(W_,[["render",$_],["__scopeId","data-v-3c4dd073"]]);var K_=H_;const G_=["value"],J_=Hi("label",{class:"col3 col-form-label"},"Breedte",-1),Y_={class:"col1"},Z_={key:0,class:"input-validation-icon"},X_=Hi("img",{src:"/projects/rde/templates/frontend/images/check_green.svg",alt:"",width:"20"},null,-1),Q_=[X_],ey={class:"col7"},ty={class:"col1"},ny=Hi("a",{class:"question-mark-inline fa fa-info-circle"},null,-1),iy=Hi("label",{class:"col3 col-form-label"},"Dikte",-1),oy={class:"col1"},sy={key:0,class:"input-validation-icon"},ry=Hi("img",{src:"/projects/rde/templates/frontend/images/check_green.svg",alt:"",width:"20"},null,-1),ay=[ry],ly={class:"col7"},cy={class:"col1"},uy=Hi("a",{class:"question-mark-inline fa fa-info-circle"},null,-1);function dy(e,t,n,o,s,r){const a=In("gsd-select"),l=In("GsdPopper");return Di(),Bi("div",null,[Hi("input",{type:"hidden",value:this.sizeId,name:"sizeId"},null,8,G_),Hi("div",{class:Object(i["L"])(["form-row",{disabled:n.disabled}])},[J_,Hi("div",Y_,[null==s.width||n.disabled?Qi("",!0):(Di(),Bi("div",Z_,Q_))]),Hi("div",ey,[Ki(a,{name:"width",no_selection_text:"Kies een breedte...",options:s.width_values,value:s.width,disabled:n.disabled,onChangeValue:r.setWidth},null,8,["options","value","disabled","onChangeValue"])]),Hi("div",ty,[Ki(l,null,{content:St(()=>[Zi(" Selecteer uw breedte. ")]),default:St(()=>[ny]),_:1})])],2),Hi("div",{class:Object(i["L"])(["form-row",{disabled:n.disabled}])},[iy,Hi("div",oy,[null==s.thickness||n.disabled?Qi("",!0):(Di(),Bi("div",sy,ay))]),Hi("div",ly,[Ki(a,{name:"thickness",no_selection_text:"Kies een dikte...",options:s.thickness_values,value:s.thickness,disabled:n.disabled,onChangeValue:r.setThickness},null,8,["options","value","disabled","onChangeValue"])]),Hi("div",cy,[Ki(l,null,{content:St(()=>[Zi(" Selecteer uw dikte. ")]),default:St(()=>[uy]),_:1})])],2)])}var hy={name:"NaturalStoneWallCopingFlat",components:{GsdSelect:K_,GsdPopper:g_},props:["sizes","sizeId","possible_sizes","disabled"],data(){return{width:null,width_values:[],thickness:null,thickness_values:[],possibleSizeIds:[],activeSize:null}},watch:{sizes(){this.updateSelection()},possible_sizes(){this.updateSelection()}},mounted(){this.updateSelection()},methods:{updateSelection(){this.width_values=[],this.thickness_values=[],this.usedWidths=[],this.usedThickness=[],this.possibleSizeIds=[];for(let e in this.possible_sizes)for(let t in this.possible_sizes[e])this.possibleSizeIds.push(this.possible_sizes[e][t].value);if(this.activeSize=null,null!=this.sizeId)for(let e in this.sizes){let t=this.sizes[e];if(t.sizeId==this.sizeId){-1!==this.possibleSizeIds.indexOf(t.sizeId)&&(this.activeSize=t);break}}for(let e in this.sizes){let t=this.sizes[e];if(-1!==this.possibleSizeIds.indexOf(t.sizeId)){if(-1===this.usedWidths.indexOf(t.length)){this.usedWidths.push(t.length);let e={name:10*t.length+" mm",value:t.length};this.width_values.push(e)}if(-1===this.usedThickness.indexOf(t.height)){this.usedThickness.push(t.height);let e={name:10*t.height+" mm",value:t.height};this.thickness_values.push(e)}null!=this.activeSize?(this.width=this.activeSize.length,this.thickness=this.activeSize.height):(this.width=null,this.thickness=null)}}},setWidth(e){this.width=e,this.changeValue()},setThickness(e){this.thickness=e,this.changeValue()},changeValue(){let e=null;for(let t in this.sizes){let n=this.sizes[t];if(-1!==this.possibleSizeIds.indexOf(n.sizeId)&&(n.height==this.thickness&&n.length==this.width)){e=n.sizeId;break}}this.$emit("changeValue",e,1)}}};const py=Dp()(hy,[["render",dy]]);var my=py;class fy{static handleError(e,t){return"error"in t&&(e.$swal("Foutmelding",t.error.message,"error").then((function(){"redirect"in t.error&&(location.href=t.error.redirect)})),!0)}}const gy=[{code:"1000",name:"Groenbeige",hex:"#D0C8B0"},{code:"1001",name:"Beige",hex:"#EAE6CA"},{code:"1002",name:"Zandgeel",hex:"#E3D9C6"},{code:"1003",name:"Signaalgeel",hex:"#F6B90A"},{code:"1004",name:"Goudgeel",hex:"#E5A90A"},{code:"1005",name:"Honinggeel",hex:"#DDAF27"},{code:"1006",name:"Maisgeel",hex:"#E3A857"},{code:"1007",name:"Narcisgeel",hex:"#F5A300"},{code:"1011",name:"Bruinbeige",hex:"#D6AE74"},{code:"1012",name:"Citroengeel",hex:"#F3D54D"},{code:"1013",name:"Parelwit",hex:"#F4F4F0"},{code:"1014",name:"Ivoor",hex:"#F0EADA"},{code:"1015",name:"Lichtivoor",hex:"#F2E8D5"},{code:"1016",name:"Zwavelgeel",hex:"#F3E500"},{code:"1017",name:"Saffraangeel",hex:"#F5A900"},{code:"1018",name:"Zinkgeel",hex:"#F8C300"},{code:"1019",name:"Grijsbeige",hex:"#B9A28A"},{code:"1020",name:"Olijfgeel",hex:"#A38C15"},{code:"1021",name:"Koolzaadgeel",hex:"#FAD201"},{code:"1023",name:"Verkeersgeel",hex:"#F5A700"},{code:"1024",name:"Okergeel",hex:"#C08F32"},{code:"1026",name:"Lichtgeel",hex:"#F7F000"},{code:"1027",name:"Chroomgeel",hex:"#FFE500"},{code:"1028",name:"Melisgeel",hex:"#FFDB00"},{code:"1032",name:"Goudbruin",hex:"#B88A50"},{code:"1033",name:"Zonnegeel",hex:"#F39F18"},{code:"1034",name:"Pastelgeel",hex:"#EDB315"},{code:"1035",name:"Parelmoergoud",hex:"#D9A441"},{code:"1036",name:"Parelmoerlichtgoud",hex:"#EB9E52"},{code:"1037",name:"Dieporanje",hex:"#DA6E00"},{code:"2000",name:"Geeloranje",hex:"#E25303"},{code:"2001",name:"Roodoranje",hex:"#DF440D"},{code:"2002",name:"Vermiljoen",hex:"#E13E28"},{code:"2003",name:"Pasteloranje",hex:"#F44600"},{code:"2004",name:"Zuiver oranje",hex:"#FF4200"},{code:"2005",name:"Helder oranje",hex:"#FF5B00"},{code:"2007",name:"Licht oranje",hex:"#FF7F00"},{code:"2008",name:"Signaaloranje",hex:"#F54021"},{code:"2009",name:"Verkeersoranje",hex:"#EC542F"},{code:"2010",name:"Zalmoranje",hex:"#E55137"},{code:"2011",name:"Perzikkleurig",hex:"#E4A686"},{code:"2012",name:"Parelmoeroranje",hex:"#E1CCB1"},{code:"3000",name:"Vuurrood",hex:"#AF2B1E"},{code:"3001",name:"Signaalrood",hex:"#A52019"},{code:"3002",name:"Karmijnrood",hex:"#9B111E"},{code:"3003",name:"Robijnrood",hex:"#8F0F1D"},{code:"3004",name:"Purperrood",hex:"#75151E"},{code:"3005",name:"Wijnrood",hex:"#5E1919"},{code:"3007",name:"Zwartrood",hex:"#221B1B"},{code:"3009",name:"Oxiderood",hex:"#A22623"},{code:"3011",name:"Bruinrood",hex:"#7A2225"},{code:"3012",name:"Beigerood",hex:"#9B5E5C"},{code:"3013",name:"Tomatenrood",hex:"#8C1B2F"},{code:"3014",name:"Oudroze",hex:"#C6846D"},{code:"3015",name:"Lichtroze",hex:"#D36E70"},{code:"3016",name:"Koraalrood",hex:"#A63D4B"},{code:"3017",name:"Rozerood",hex:"#CB555D"},{code:"3018",name:"Aardbeirood",hex:"#C73F4A"},{code:"3020",name:"Verkeersrood",hex:"#BB1E10"},{code:"3022",name:"Zalmrood",hex:"#CF6955"},{code:"3024",name:"Lichtrood",hex:"#FF2D21"},{code:"3026",name:"Fluorescerend rood",hex:"#FF2A13"},{code:"3027",name:"Framboosrood",hex:"#AB273C"},{code:"3028",name:"Zuiverrood",hex:"#CC2C24"},{code:"3031",name:"Orientrood",hex:"#A63437"},{code:"3032",name:"Parelmoerrood",hex:"#701D23"},{code:"3033",name:"Parelmoerlichtrood",hex:"#A53B2D"},{code:"4001",name:"Roodlila",hex:"#816183"},{code:"4002",name:"Roodpaars",hex:"#8D3C4B"},{code:"4003",name:"Heidepaars",hex:"#C4618C"},{code:"4004",name:"Bordeauxpaars",hex:"#651E38"},{code:"4005",name:"Blauwlila",hex:"#76689A"},{code:"4006",name:"Verkeerspurper",hex:"#903373"},{code:"4007",name:"Paars",hex:"#47243C"},{code:"4008",name:"Signaalpaars",hex:"#844C82"},{code:"4009",name:"Pastelpaars",hex:"#9D8692"},{code:"4010",name:"Telemagenta",hex:"#C60058"},{code:"4011",name:"Parelmoerdonkerviolet",hex:"#A18594"},{code:"4012",name:"Parelmoerlichtviolet",hex:"#C9ADA1"},{code:"5000",name:"Paarsblauw",hex:"#2A2C41"},{code:"5001",name:"Groenblauw",hex:"#1E213D"},{code:"5002",name:"Ultramarijnblauw",hex:"#18171C"},{code:"5003",name:"Saffierblauw",hex:"#1E2460"},{code:"5004",name:"Zwartblauw",hex:"#3E5F8A"},{code:"5005",name:"Signaalblauw",hex:"#252C7C"},{code:"5007",name:"Brillantblauw",hex:"#41678D"},{code:"5008",name:"Grijsblauw",hex:"#313C48"},{code:"5009",name:"Azuurblauw",hex:"#2E5978"},{code:"5010",name:"Gentiaanblauw",hex:"#13447C"},{code:"5011",name:"Staalblauw",hex:"#232C3F"},{code:"5012",name:"Lichtblauw",hex:"#2986CC"},{code:"5013",name:"Kobaltblauw",hex:"#0E294B"},{code:"5014",name:"Duifblauw",hex:"#4D6F97"},{code:"5015",name:"Hemelsblauw",hex:"#2186BA"},{code:"5017",name:"Verkeersblauw",hex:"#0D4F8B"},{code:"5018",name:"Turkooisblauw",hex:"#1A5784"},{code:"5019",name:"Capriblauw",hex:"#0B4152"},{code:"5020",name:"Oceaanblauw",hex:"#0F5688"},{code:"5021",name:"Waterblauw",hex:"#1C6686"},{code:"5022",name:"Nachtsblauw",hex:"#152532"},{code:"5023",name:"Verblauw",hex:"#49678D"},{code:"5024",name:"Pastelblauw",hex:"#5D9B9B"},{code:"5025",name:"Parelmoerblauw",hex:"#2A6478"},{code:"5026",name:"Parelmoernachtblauw",hex:"#0D7EB8"},{code:"6000",name:"Patinagroen",hex:"#3A7460"},{code:"6001",name:"Smaragdgroen",hex:"#366735"},{code:"6002",name:"Loofgroen",hex:"#325928"},{code:"6003",name:"Olijfgroen",hex:"#50533C"},{code:"6004",name:"Blauwgroen",hex:"#024442"},{code:"6005",name:"Mosgroen",hex:"#2D4030"},{code:"6006",name:"Grijsolijfgroen",hex:"#424632"},{code:"6007",name:"Flessengroen",hex:"#2C5545"},{code:"6008",name:"Bruingroen",hex:"#39352A"},{code:"6009",name:"Dennengroen",hex:"#343B29"},{code:"6010",name:"Grasgroen",hex:"#39513E"},{code:"6011",name:"Resedagroen",hex:"#4B6D41"},{code:"6012",name:"Zwartgroen",hex:"#1E3A2F"},{code:"6013",name:"Rietgroen",hex:"#5C705E"},{code:"6014",name:"Geelolijfgroen",hex:"#47402E"},{code:"6015",name:"Zwartolijfgroen",hex:"#3B3C36"},{code:"6016",name:"Turkooisgroen",hex:"#1E5945"},{code:"6017",name:"Meigroen",hex:"#4D6F39"},{code:"6018",name:"Geelgroen",hex:"#6C7C59"},{code:"6019",name:"Witgroen",hex:"#D4D8D1"},{code:"6020",name:"Chroomoxidegroen",hex:"#2D3D36"},{code:"6021",name:"Bleekgroen",hex:"#7D8B55"},{code:"6022",name:"Bruinolijfgroen",hex:"#474135"},{code:"6024",name:"Verkeersgroen",hex:"#308446"},{code:"6025",name:"Varengroen",hex:"#3D642D"},{code:"6026",name:"Opalgroen",hex:"#0F4336"},{code:"6027",name:"Lichtgroen",hex:"#C8E3C7"},{code:"6028",name:"Pijnboomgroen",hex:"#2E554A"},{code:"6029",name:"Mintgroen",hex:"#277D66"},{code:"6032",name:"Signaalgroen",hex:"#1F8A59"},{code:"6033",name:"Mintturkoois",hex:"#365F4A"},{code:"6034",name:"Pastelturkoois",hex:"#7FB5B5"},{code:"6035",name:"Parelmoerdonkergroen",hex:"#1F3A3D"},{code:"6036",name:"Parelmoerlichtgroen",hex:"#C9D9CD"},{code:"6037",name:"Zuivergroen",hex:"#008F39"},{code:"6038",name:"Felgroen",hex:"#00BB2D"},{code:"7000",name:"Pelsgrijs",hex:"#78858B"},{code:"7001",name:"Zilvergrijs",hex:"#8A9597"},{code:"7002",name:"Olijfgrijs",hex:"#7E7B52"},{code:"7003",name:"Mosgrijs",hex:"#6C7059"},{code:"7004",name:"Signaalgrijs",hex:"#969992"},{code:"7005",name:"Muisgrijs",hex:"#5F6259"},{code:"7006",name:"Beigegrijs",hex:"#575D57"},{code:"7008",name:"Khakigrijs",hex:"#4D5645"},{code:"7009",name:"Groengrijs",hex:"#4C514A"},{code:"7010",name:"Zeildoekgrijs",hex:"#434B4D"},{code:"7011",name:"IJzergrijs",hex:"#4E5452"},{code:"7012",name:"Bazaltgrijs",hex:"#464531"},{code:"7013",name:"Bruingrijs",hex:"#434750"},{code:"7015",name:"Leigrijs",hex:"#4D5D5B"},{code:"7016",name:"Antracietgrijs",hex:"#3E3B32"},{code:"7021",name:"Zwartgrijs",hex:"#262C2A"},{code:"7022",name:"Umbragrijs",hex:"#332F2C"},{code:"7023",name:"Betongrijs",hex:"#878A85"},{code:"7024",name:"Grafietgrijs",hex:"#474A51"},{code:"7026",name:"Granietgrijs",hex:"#2F353B"},{code:"7030",name:"Steengrijs",hex:"#8B8C7A"},{code:"7031",name:"Blauwgrijs",hex:"#5D6970"},{code:"7032",name:"Kiezelgrijs",hex:"#6C6960"},{code:"7033",name:"Cementgrijs",hex:"#9C9C9C"},{code:"7034",name:"Geelgrijs",hex:"#7E7B6E"},{code:"7035",name:"Lichtgrijs",hex:"#C5C7C4"},{code:"7036",name:"Platinagrijs",hex:"#8F8B66"},{code:"7037",name:"Stofgrijs",hex:"#7F7F7E"},{code:"7038",name:"Agaatgrijs",hex:"#B5B8B1"},{code:"7039",name:"Kwartsgrijs",hex:"#6C6E6B"},{code:"7040",name:"Venstergrijs",hex:"#9DA1AA"},{code:"7042",name:"Verkeersgrijs A",hex:"#8D948D"},{code:"7043",name:"Verkeersgrijs B",hex:"#4E5452"},{code:"7044",name:"Zijdegrijs",hex:"#BDBDB7"},{code:"7045",name:"Telegrijs 1",hex:"#91969A"},{code:"7046",name:"Telegrijs 2",hex:"#82898E"},{code:"7047",name:"Telegrijs 4",hex:"#CFD0CF"},{code:"7048",name:"Parelmoergrijs",hex:"#888175"},{code:"8000",name:"Groenbruin",hex:"#826C34"},{code:"8001",name:"Okerbruin",hex:"#955F20"},{code:"8002",name:"Signaalbruin",hex:"#6C3B2A"},{code:"8003",name:"Leembruin",hex:"#734222"},{code:"8004",name:"Koperbruin",hex:"#8E402A"},{code:"8007",name:"Reebruin",hex:"#59351F"},{code:"8008",name:"Olijfbruin",hex:"#6F4F28"},{code:"8011",name:"Nussbaum",hex:"#5B3A29"},{code:"8012",name:"Roodbruin",hex:"#592321"},{code:"8014",name:"Sepiabruin",hex:"#382C1E"},{code:"8015",name:"Kastanjebruin",hex:"#633A34"},{code:"8016",name:"Mahoniebruin",hex:"#4C2F27"},{code:"8017",name:"Chocoladebruin",hex:"#45322E"},{code:"8019",name:"Grijsbruin",hex:"#403A3A"},{code:"8022",name:"Zwartbruin",hex:"#212121"},{code:"8023",name:"Oranjebruin",hex:"#A65E2E"},{code:"8024",name:"Beigebruin",hex:"#79553D"},{code:"8025",name:"Bleekbruin",hex:"#755C48"},{code:"8028",name:"Terrabruin",hex:"#4E3B31"},{code:"8029",name:"Parelmoerkoper",hex:"#763C28"},{code:"9001",name:"Crèmewit",hex:"#F8F4E6"},{code:"9002",name:"Grijslicht",hex:"#E7EBDA"},{code:"9003",name:"Signaalwit",hex:"#F4F4F4"},{code:"9004",name:"Signaalzwart",hex:"#0E0E0E"},{code:"9005",name:"Gitzwart",hex:"#0A0A0A"},{code:"9006",name:"Wit aluminium",hex:"#A5A5A5"},{code:"9007",name:"Grijs aluminium",hex:"#8F8F8F"},{code:"9010",name:"Zuiverwit",hex:"#FFFFFF"},{code:"9011",name:"Grafietzwart",hex:"#1C1C1C"},{code:"9016",name:"Verkeerswit",hex:"#F6F6F6"},{code:"9017",name:"Verkeerszwart",hex:"#1E1E1E"},{code:"9018",name:"Papyruswit",hex:"#DEDED4"},{code:"9022",name:"Parelmoerlichtgrijs",hex:"#9C9C9C"},{code:"9023",name:"Parelmoerdonkergrijs",hex:"#7E7E7E"}],vy=e=>(Et("data-v-6ac5413a"),e=e(),Ct(),e),by={class:"ral-selector"},_y={class:"dropdown"},yy=vy(()=>Hi("span",{class:"fa fa-chevron-down"},null,-1)),wy={key:0,class:"dropdown-body"},ky={class:"options"},xy=["onClick"],jy=["value"];var Oy={__name:"RalColorSelect",props:{modelValue:Object,isActive:Boolean},emits:["ralColorChanged"],setup(e,{emit:t}){const n=e;function o(e){const t=document.querySelector(".dropdown-body");t&&!t.contains(e.target)&&(s.value=!1)}vn(()=>{if(document.addEventListener("click",o),!n.modelValue.ralColor)return;const e=gy.find(e=>e.code===n.modelValue.ralColor);e&&(r.value=e)}),wn(()=>document.removeEventListener("click",o));const s=Me(!1),r=Me(n.modelValue.ralColor||null),a=Me(""),l=()=>{n.isActive&&(s.value=!s.value)},c=e=>{r.value=e,t("ralColorChanged",e),s.value=!1,a.value=""},u=Eo(()=>a.value?gy.filter(e=>`${e.code} ${e.name}`.toLowerCase().includes(a.value.toLowerCase())):gy);return(e,t)=>{var n,o;return Di(),Bi("div",by,[Hi("div",_y,[Hi("div",{class:"dropdown-header",onClick:l},[Hi("div",null,[Hi("div",{class:"color-swatch",style:Object(i["N"])({backgroundColor:(null===(n=r.value)||void 0===n?void 0:n.hex)||"#fff"})},null,4),Zi(" "+Object(i["P"])(r.value?`${r.value.code} - ${r.value.name}`:"Kies een RAL kleur"),1)]),yy]),s.value?(Di(),Bi("div",wy,[En(Hi("input",{type:"text","onUpdate:modelValue":t[0]||(t[0]=e=>a.value=e),class:"search-input",placeholder:"Zoek op naam of code..."},null,512),[[Ms,a.value]]),Hi("ul",ky,[(Di(!0),Bi(Si,null,Ln(Fe(u),e=>(Di(),Bi("li",{key:e.code,onClick:t=>c(e),class:"option"},[Hi("div",{class:"color-swatch",style:Object(i["N"])({backgroundColor:e.hex})},null,4),Zi(" "+Object(i["P"])(e.code)+" - "+Object(i["P"])(e.name),1)],8,xy))),128))])])):Qi("",!0)]),Hi("input",{type:"hidden",name:"ralColor",value:null===(o=r.value)||void 0===o?void 0:o.code},null,8,jy)])}}};n("1b5c");const Ey=Dp()(Oy,[["__scopeId","data-v-6ac5413a"]]);var Cy=Ey,Sy={name:"SelectStone",components:{RalColorSelect:Cy,GsdSelectGrouped:A_,GsdSelect:K_,GsdPopper:g_,NaturalStoneWallCopingFlat:my,"color-select":Yg},data(){return{loading:!0,is_valid:{},all_valid:!1,is_active:{},errors:[],input_errors:{},form:null,brands:{},colors:{},color_ids:[],sizes:{},sizes_ids:[],possible_sizes:[],possible_colors:[],quotation:{},color:!1,stone:!1,customimage:"",showModel:!0,showDiepteDikte:!1,showCustomStone:!1,showCustomStoneNatuursteen:!1,showNaturalStoneWallCopingFlat:!1,showEndstone:!1,material:"",variant:"",isIsosill:!1,isAdmin:!1,depth:"",possible_depths:[],thickness:"",possible_thickness:[],custom_size_id:"",custom_depth:"",possible_custom_depths:[],custom_thickness:"",possible_custom_thicknesses:[],custom_height:"",possible_custom_heights:[],custom_width_click:"",possible_custom_width_clicks:[],custom_height_click:"",possible_custom_height_clicks:[],customStoneNatuursteenError:"",is_active_projectReference:!1,is_active_brandId:!1,is_active_colorId:!1,is_active_sizeId:!1,is_active_endstone:!1,is_active_depth:!1,is_active_thickness:!1,is_active_custom_depth:!1,is_active_custom_thickness:!1,is_active_custom_height:!1,is_active_custom_width_click:!1,is_active_custom_height_click:!1}},created(){this.fetchData()},mounted(){},watch:{"quotation.projectName":{handler(e,t){this.projectNameChanged(void 0===t)},flush:"post"},"quotation.brandId":{handler(e,t){this.brandIdChanged(void 0===t)},flush:"post"},"quotation.colorId":{handler(e,t){this.colorIdChanged(void 0===t)},flush:"post"},"quotation.sizeId":{handler(e,t){this.sizeIdChanged(void 0===t)},flush:"post"},"quotation.endstone":{handler(){this.endstoneChanged()},flush:"post"},depth:{handler(){this.depthChanged()},flush:"post"},thickness:{handler(){this.thicknessChanged()},flush:"post"},custom_depth:{handler(){this.customChanged()},flush:"post"},custom_thickness:{handler(e,t){this.customChanged(void 0===t)},flush:"post"},custom_height:{handler(){this.customChanged()},flush:"post"},custom_width_click:{handler(){this.customChanged()},flush:"post"},custom_height_click:{handler(){this.customChanged()},flush:"post"}},computed:{brandTextTitle(){return["beton","natuursteen"].includes(this.material)?"Materiaal":"Merk"},brandTextEmptyoption(){return["beton","natuursteen"].includes(this.material)?"Kies materiaal...":"Kies merk..."},colorTextInfo(){return"keramische"===this.material?"St. Joris en Wienerberger kennen vele verschillende kleuren. Voor speciale kleuren kunt u altijd contact opnemen.":"Selecteer uw kleur"},sizeTextInfo(){return"natuursteen"===this.material?'Kies uw model. Meer info <a href="/prefab-raamdorpels/neggemaattabel" target="_blank">hier</a>':"Er zijn maar liefst 11 verschillende modellen waardoor het moeilijk is om het juiste model te kiezen. Klik op het informatie bolletje om meer informatie over de verschillende maten te verkijgen."},materialLink(){return"natuursteen"===this.material?"/natuursteen/soorten":"beton"===this.material?"":"/prefab-raamdorpels/modellen"},colorLink(){return"natuursteen"===this.material?"spekband"===this.variant?"/natuursteen/spekbanden":"muurafdekker"===this.variant?"/natuursteen/muurafdekkers":"vensterbank"===this.variant?"/natuursteen/vensterbank":"/natuursteen/raamdorpels":"beton"===this.material?"":"/prefab-raamdorpels/kleuren"},possible_endstonetypes(){var e=[];if(this.isIsosill)return e.push({value:"flat",name:"Vlak"}),e.push({value:"standingside",name:"Opstaande zijkant"}),e.push({value:"stuc",name:"Stucprofiel"}),e;for(var t=!1,n=!1,i=0;i<this.sizes.length;i++)this.sizes[i].brandId==this.quotation.brandId&&this.sizes[i].stone.colorId==this.quotation.colorId&&this.sizes[i].stone.sizeId==this.quotation.sizeId&&"false"!=this.sizes[i].stone.endstone&&("left"==this.sizes[i].stone.endstone||"right"==this.sizes[i].stone.endstone?t=!0:"leftg"!=this.sizes[i].stone.endstone&&"rightg"!=this.sizes[i].stone.endstone||(n=!0));return t&&e.push({value:"true",name:"Ja (opstaande zijkantjes)"}),n&&e.push({value:"true_grooves",name:"Ja (groeven)"}),this.color&&!this.color.glaced?e.push({value:"false",name:"Nee (ongeglazuurde zijkantjes)"}):null!=this.stone&&"0"==this.stone.endstones_required&&e.push({value:"false",name:"Nee (geglazuurde zijkantjes)"}),e},stoneimage(){let e="geen.jpg";if(""==this.stone.image||null==this.stone.image||"false"!==this.quotation.endstone&&!1!==this.quotation.endstone){for(let t=0;t<this.sizes.length;t++)if(this.sizes[t].brandId==this.quotation.brandId&&this.sizes[t].stone.colorId==this.quotation.colorId&&this.sizes[t].stone.sizeId==this.quotation.sizeId&&"false"!=this.sizes[t].stone.endstone&&""!=this.sizes[t].stone.image&&null!=this.sizes[t].stone.image){if("true_grooves"==this.quotation.endstone&&("leftg"==this.sizes[t].stone.endstone||"rightg"==this.sizes[t].stone.endstone)){e=this.sizes[t].stone.image;break}if((1==this.quotation.endstone||"true"==this.quotation.endstone)&&("left"==this.sizes[t].stone.endstone||"right"==this.sizes[t].stone.endstone)){e=this.sizes[t].stone.image;break}}}else e=this.stone.image;return"//www.raamdorpel.nl/images/thresholds/"+e}},methods:{fetchData(){fetch("?action=wizard&step=1&json=1",{headers:{"Content-type":"application/json"}}).then(e=>e.json()).then(e=>{if(!fy.handleError(this,e)){for(var t in this.quotation=e.data.quotation,this.colors=e.data.colors,this.sizes=e.data.sizes,this.showModel=e.data.showModel,this.showDiepteDikte=e.data.showDiepteDikte,this.showEndstone=e.data.showEndstone,this.showNaturalStoneWallCopingFlat=e.data.showNaturalStoneWallCopingFlat,this.material=e.data.material,this.variant=e.data.variant,this.isIsosill=e.data.isIsosill,this.isAdmin=e.data.isAdmin,this.brands=[],e.data.brands){let n=e.data.brands[t];this.brands.push({name:n.name,value:n.brandId})}if(this.showDiepteDikte&&""!=this.quotation.sizeId)for(var n=0;n<this.sizes.length;n++)if(this.sizes[n].sizeId==this.quotation.sizeId){var i=this.sizes[n].name.split(" x ");this.depth=i[0],this.thickness=i[1];break}this.quotation.custom_stone&&(this.depth="Op maat gemaakt",this.custom_depth=this.quotation.custom_stone.depth,this.custom_thickness=this.quotation.custom_stone.thickness,this.custom_height=this.quotation.custom_stone.height,this.custom_width_click=this.quotation.custom_stone.width_click,this.custom_height_click=this.quotation.custom_stone.height_click),this.loading=!1}}).catch(e=>{this.errors=[],this.errors.push("Server fout opgetreden. Probeer het later nog eens, of neem contact op ("+e+")")})},postData(e){const t=new FormData(this.form);t.append(e,1),fetch("?action=wizard&step=1&json=1",{method:"POST",headers:{Accept:"application/json"},body:t}).then(e=>e.json()).then(e=>{fy.handleError(this,e)||(this.errors=e.data.errors,"redirect"in e&&(location.href=e.redirect))}).catch(e=>{this.errors=[],this.errors.push("Server fout opgetreden. Probeer het later nog eens, of neem contact op ("+e+")")})},clickPrev(){this.postData("prev")},clickNext(){this.postData("next")},getPossibleColors(){var e={Ongeglazuurd:[],"Geglazuurd - standaard":[],"Geglazuurd - speciaal":[]};this.color_ids=[];for(var t=0;t<this.colors.length;t++){var n;if(this.colors[t].brandId==this.quotation.brandId)n="true"===this.colors[t].glaced?"true"===this.colors[t].common?e["Geglazuurd - standaard"]:e["Geglazuurd - speciaal"]:e["Ongeglazuurd"],n.push(this.colors[t]),this.color_ids.push(this.colors[t].colorId)}return 0==e["Ongeglazuurd"].length&&delete e["Ongeglazuurd"],0==e["Geglazuurd - standaard"].length&&delete e["Geglazuurd - standaard"],0==e["Geglazuurd - speciaal"].length&&delete e["Geglazuurd - speciaal"],e},getPossibleSizesOptions(){var e={"Standaard maten":[],"Speciale maten":[]};this.sizes_ids=[];for(var t=0;t<this.sizes.length;t++){var n;if(this.sizes[t].brandId==this.quotation.brandId&&this.sizes[t].stone.colorId==this.quotation.colorId)if(n="true"===this.sizes[t].common?e["Standaard maten"]:e["Speciale maten"],-1===this.sizes_ids.indexOf(this.sizes[t].sizeId)){let e=this.sizes[t];this.sizes_ids.push(e.sizeId);let i={name:e.name,value:e.sizeId};n.push(i)}}return 0==e["Standaard maten"].length&&delete e["Standaard maten"],0==e["Speciale maten"].length&&delete e["Speciale maten"],e},getPossibleDepths(){var e=[];this.custom_size_id="";for(var t=0;t<this.sizes.length;t++)if(this.sizes[t].brandId==this.quotation.brandId&&this.sizes[t].stone.colorId==this.quotation.colorId){if(this.sizes[t].name.indexOf("Op maat gemaakt")>-1){this.custom_size_id=this.sizes[t].sizeId;continue}var n=this.sizes[t].name.split(" x ");e.includes(n[0])||e.push(n[0])}return e.sort(),""!=this.custom_size_id&&e.unshift("Op maat gemaakt"),e},getPossibleThickness(){for(var e=[],t=0;t<this.sizes.length;t++)if(this.sizes[t].brandId==this.quotation.brandId&&this.sizes[t].stone.colorId==this.quotation.colorId){var n=this.sizes[t].name.split(" x ");this.depth!=n[0]||2!=n.length||e.includes(n[1])||e.push(n[1])}return e.sort(),e},getPossibleCustomDepth(){const e=[];for(let t=80;t<=350;t+=10)e.push(t);return e},getPossibleCustomThickness(){for(var e=[],t=30;t<=120;t+=10)e.push(t);return e},getPossibleCustomHeight(){var e=[];if(6==this.quotation.brandId||7==this.quotation.brandId)for(var t=parseInt(this.custom_thickness),n=t;n<=t+70;n+=5)n>=t+10&&e.push(n);else if(5==this.quotation.brandId)for(var i=this.custom_thickness;i<=this.custom_thickness+50;i+=5)i>=this.custom_thickness&&e.push(i);else for(var o=this.custom_thickness;o<=this.custom_thickness+50;o+=5)e.push(o);return e},getPossibleCustomWidthClicks(){const e=this.custom_depth-40,t=[];for(let n=0;n<=e;n+=5)t.push(n);return t},getPossibleCustomHeightClicks(){var e=[];if(5==this.quotation.brandId)this.custom_height_click=parseInt(this.custom_height)-parseInt(this.custom_thickness),e.push(this.custom_height_click);else for(var t=parseInt(this.custom_height)-parseInt(this.custom_thickness)-10,n=0;n<=t;n+=5)e.push(n);return e},projectNameChanged(e){this.is_valid["projectName"]=!1,this.quotation.projectName&&""!=this.quotation.projectName?(this.is_valid["projectName"]=!0,this.is_active_projectReference=!0,this.is_active_brandId=!0,delete this.input_errors["projectName"]):(this.is_active_projectReference=!1,this.is_active_brandId=!1,this.is_active_colorId=!1,this.quotation.colorId="",this.quotation.sizeId="",this.is_active_sizeId=!1,this.quotation.depth="",this.is_active_depth=!1,this.quotation.thickness="",this.is_active_thickness=!1,this.quotation.endstone="",this.is_active_endstone=!1,e||(this.input_errors["projectName"]="Project naam is verplicht")),this.allValid()},brandIdChanged(e){if(this.is_active_brandId){this.is_valid["brandId"]=!1;var t=!0;if(this.quotation.brandId&&""!==this.quotation.brandId)if(this.is_valid["brandId"]=!0,this.is_active_colorId=!0,this.is_valid["colorId"]=!1,this.is_valid["sizeId"]=!1,this.is_valid["sizeId"]=!1,this.is_valid["endstone"]=!1,this.is_valid["custom_depth"]=!1,this.possible_colors=this.getPossibleColors(),1==this.color_ids.length){this.quotation.colorId=this.color_ids[0];for(var n=0;n<this.colors.length;n++)if(this.colors[n].colorId==this.quotation.colorId){this.color=this.colors[n];break}this.is_active_colorId=!0,t=!1}else{var i=!1;for(n=0;n<this.colors.length;n++)if(this.colors[n].colorId==this.quotation.colorId){this.color=this.colors[n],i=!0;break}i||(this.quotation.colorId="",this.color=!1)}else this.is_active_colorId=!1,this.quotation.colorId="",this.quotation.sizeId="",this.is_active_sizeId=!1,this.quotation.depth="",this.is_active_depth=!1,this.quotation.thickness="",this.is_active_thickness=!1,this.quotation.endstone="",this.is_active_endstone=!1,this.input_errors["brandId"]="Merk is verplicht";e||(t&&(this.quotation.colorId=""),this.quotation.sizeId="",this.is_active_sizeId=!1,this.quotation.endstone="",this.is_active_endstone=!1,this.depth="",this.custom_depth="",this.custom_thickness="",this.custom_height="",this.is_active_depth=!1,this.is_active_custom_depth=!1,this.is_active_custom_thickness=!1,this.is_active_custom_height=!1),this.allValid()}},setColorId(e){this.quotation.colorId=e},setRalColor(e){this.quotation.ralColor=e},setSizeId(e){this.quotation.sizeId=e},setBrandId(e){this.quotation.brandId=e},setEndstone(e){this.quotation.endstone=e},colorIdChanged(e){if(this.is_active_colorId){this.is_valid["colorId"]=!1;var t=!0;if(this.quotation.colorId&&""!=this.quotation.colorId){for(var n=0;n<this.colors.length;n++)if(this.colors[n].colorId==this.quotation.colorId){this.color=this.colors[n];break}if(this.is_valid["colorId"]=!0,this.is_active_sizeId=!0,this.possible_sizes=this.getPossibleSizesOptions(),console.log(this.possible_sizes),this.showDiepteDikte){this.is_active_depth=!0,this.possible_depths=this.getPossibleDepths();for(var i=!1,o=0;o<this.possible_depths.length;o++){var s=this.possible_depths[o];if(s==this.depth){i=!0;break}}i||(this.depth="",this.is_valid["depth"]=!1)}this.showModel||this.showNaturalStoneWallCopingFlat||(this.quotation.endstone="false",this.endstoneChanged()),e||1!=this.sizes_ids.length||(this.quotation.sizeId=this.sizes_ids[0],this.is_active_sizeId=!0,t=!1,this.sizeIdChanged(!1))}else this.is_active_sizeId=!1,this.quotation.sizeId="",this.quotation.depth="",this.is_active_depth=!1,this.quotation.thickness="",this.is_active_thickness=!1,this.quotation.endstone="",this.depth="",this.input_errors["colorId"]="Merk is verplicht";e||(t&&(this.quotation.sizeId=""),(this.showModel||this.showNaturalStoneWallCopingFlat)&&(this.quotation.endstone=""),this.is_active_endstone=!1,this.depth="",this.custom_depth="",this.custom_thickness="",this.custom_height="",this.is_active_custom_depth=!1,this.is_active_custom_thickness=!1,this.is_active_custom_height=!1,this.is_valid["custom_depth"]=!1,this.is_valid["endstone"]=!1),this.allValid()}},sizeIdChanged(e){if(this.is_active_sizeId){if(this.is_valid["sizeId"]=!1,this.quotation.sizeId&&""!=this.quotation.sizeId){for(var t=0;t<this.sizes.length;t++)if(this.sizes[t].brandId==this.quotation.brandId&&this.sizes[t].stone.colorId==this.quotation.colorId&&this.sizes[t].stone.sizeId==this.quotation.sizeId&&"false"==this.sizes[t].stone.endstone){this.stone=this.sizes[t].stone;break}this.is_valid["sizeId"]=!0,this.is_active_endstone=!0,this.showDiepteDikte&&(this.is_active_depth=!0),this.showEndstone||(this.quotation.endstone="false",this.endstoneChanged())}else this.quotation.endstone="",this.is_active_endstone=!1,this.input_errors["sizeId"]="Model is verplicht",this.stone={};!e&&this.showEndstone&&(this.quotation.endstone=""),this.allValid(),this.mayOrder()}},depthChanged(){if(this.is_active_depth&&this.showDiepteDikte){if(this.is_valid["depth"]=!1,""==this.depth)this.input_errors["depth"]="Diepte",this.thickness="",this.is_active_thickness=!1,this.is_valid["thickness"]=!1,this.showCustomStone=!1,this.showCustomStoneNatuursteen=!1,this.custom_depth="",this.custom_thickness="",this.custom_height="",this.is_valid["custom_depth"]=!1;else{"Op maat gemaakt"===this.depth?"beton"===this.material&&"raamdorpel"===this.variant?(this.quotation.sizeId=this.custom_size_id,this.showCustomStone=!0,this.is_active_custom_depth=!0,this.is_active_custom_thickness=!0,this.possible_custom_depths=this.getPossibleCustomDepth(),this.possible_custom_thicknesses=this.getPossibleCustomThickness()):(this.quotation.sizeId=this.custom_size_id,this.showCustomStoneNatuursteen=!0,this.is_active_custom_depth=!0,this.is_active_custom_thickness=!0,this.possible_custom_depths=this.getPossibleCustomDepth(),this.possible_custom_thicknesses=this.getPossibleCustomThickness(),5==this.quotation.brandId?this.customimage="/projects/rde/templates/frontend/images/opmaatgemaakt_budget.png":this.customimage="/projects/rde/templates/frontend/images/opmaatgemaakt_default.png"):(this.showCustomStone=!1,this.showCustomStoneNatuursteen=!1,this.custom_depth="",this.custom_thickness="",this.custom_height="",this.is_valid["custom_depth"]=!1),this.is_valid["depth"]=!0,this.is_active_thickness=!0,this.possible_thickness=this.getPossibleThickness();for(var e=!1,t=0;t<this.possible_thickness.length;t++){var n=this.possible_thickness[t];if(n==this.thickness){e=!0,this.thicknessChanged();break}}e||(this.thickness="")}this.allValid()}},thicknessChanged(e){if(this.is_active_thickness&&this.showDiepteDikte){if(this.is_valid["thickness"]=!1,""==this.thickness)this.quotation.endstone="",this.is_active_endstone=!1,this.input_errors["depth"]="Dikte";else{this.is_valid["thickness"]=!0,this.is_active_thickness=!0,this.quotation.sizeId="";for(var t=0;t<this.sizes.length;t++)if(this.sizes[t].brandId==this.quotation.brandId&&this.sizes[t].stone.colorId==this.quotation.colorId){var n=this.sizes[t].name.split(" x ");if(n[0]==this.depth&&n[1]==this.thickness){this.quotation.sizeId=this.sizes[t].sizeId;break}}this.showEndstone||(this.quotation.endstone="false",this.endstoneChanged())}!e&&this.showEndstone&&(this.quotation.endstone=""),this.allValid()}},customChanged(){this.showCustomStoneNatuursteen?this.customChangedNatuursteen():this.customChangedOther()},customChangedNatuursteen(){if(this.is_active_thickness){this.is_valid["thickness"]=!1;var e=this.possible_custom_depths[0],t=this.possible_custom_depths[this.possible_custom_depths.length-1];if(""==this.custom_depth||!(this.custom_depth>=e&&this.custom_depth<=t))return this.customStoneNatuursteenError="A tussen "+e+" en "+t,this.is_active_custom_thickness=!1,this.is_active_custom_height=!1,this.is_active_custom_width_click=!1,this.is_active_custom_height_click=!1,void this.allValid();if(this.is_active_custom_thickness=!0,e=this.possible_custom_thicknesses[0],t=this.possible_custom_thicknesses[this.possible_custom_thicknesses.length-1],""==this.custom_thickness||!(this.custom_thickness>=e&&this.custom_thickness<=t))return this.customStoneNatuursteenError="B tussen "+e+" en "+t,this.is_active_custom_height=!1,this.is_active_custom_width_click=!1,this.is_active_custom_height_click=!1,void this.allValid();if(this.is_active_custom_height=!0,this.possible_custom_heights=this.getPossibleCustomHeight(),1==this.possible_custom_heights.length&&(this.custom_height=this.possible_custom_heights[0]),e=this.possible_custom_heights[0],t=this.possible_custom_heights[this.possible_custom_heights.length-1],""==this.custom_height||!(parseInt(this.custom_height)>=e&&parseInt(this.custom_height)<=t))return this.customStoneNatuursteenError="C tussen "+e+" en "+t,this.is_valid["custom_depth"]=!1,this.is_active_custom_width_click=!1,this.is_active_custom_height_click=!1,void this.allValid();if(this.possible_custom_width_clicks=this.getPossibleCustomWidthClicks(),this.is_active_custom_width_click=!0,1==this.possible_custom_width_clicks.length&&(this.custom_width_click=this.possible_custom_width_clicks[0]),e=this.possible_custom_width_clicks[0],t=this.possible_custom_width_clicks[this.possible_custom_width_clicks.length-1],""==this.custom_width_click||!(this.custom_width_click>=e&&this.custom_width_click<=t))return this.customStoneNatuursteenError="D tussen "+e+" en "+t,this.is_valid["custom_depth"]=!1,this.is_active_custom_height_click=!1,void this.allValid();if(this.is_active_custom_height_click=!0,this.possible_custom_height_clicks=this.getPossibleCustomHeightClicks(),1==this.possible_custom_height_clicks.length&&(this.custom_height_click=this.possible_custom_height_clicks[0]),e=this.possible_custom_height_clicks[0],t=this.possible_custom_height_clicks[this.possible_custom_height_clicks.length-1],""===this.custom_height_click||!(this.custom_height_click>=e&&this.custom_height_click<=t))return this.customStoneNatuursteenError="E tussen "+e+" en "+t,this.is_valid["custom_depth"]=!1,void this.allValid();this.customStoneNatuursteenError="",this.is_valid["thickness"]=!0,this.is_valid["custom_depth"]=!0,this.is_active_thickness=!0,this.allValid()}},customChangedOther(){if(this.is_active_thickness){if(this.is_valid["thickness"]=!1,""==this.custom_depth||""==this.custom_thickness)this.is_active_custom_height=!1,this.is_active_custom_width_click=!1,this.is_active_custom_height_click=!1;else{this.is_active_custom_height=!0,this.possible_custom_heights=this.getPossibleCustomHeight();for(var e=!1,t=0;t<this.possible_custom_heights.length;t++){var n=this.possible_custom_heights[t];if(n==this.custom_height){e=!0;break}}if(e||(this.custom_height="",this.is_valid["custom_depth"]=!1,this.is_active_custom_width_click=!1,this.is_active_custom_height_click=!1),""!=this.custom_height){this.is_active_custom_width_click=!0,this.possible_custom_width_clicks=this.getPossibleCustomWidthClicks(),this.is_active_custom_height_click=!0,this.possible_custom_height_clicks=this.getPossibleCustomHeightClicks(),1==this.possible_custom_width_clicks.length&&(this.custom_width_click=this.possible_custom_width_clicks[0]),e=!1;for(var i=0;i<this.possible_custom_width_clicks.length;i++){var o=this.possible_custom_width_clicks[i];if(o==this.custom_width_click){e=!0;break}}if(e||(this.custom_width_click="",this.is_valid["custom_depth"]=!1),e){for(1==this.possible_custom_height_clicks.length&&(this.custom_height_click=this.possible_custom_height_clicks[0]),e=!1,i=0;i<this.possible_custom_height_clicks.length;i++)if(o=this.possible_custom_height_clicks[i],o==this.custom_height_click){e=!0;break}e||(this.custom_height_click="",this.is_valid["custom_depth"]=!1)}""!=this.custom_width_click&&""!=this.custom_height_click&&"0"!=this.custom_height_click&&(this.is_valid["thickness"]=!0,this.is_valid["custom_depth"]=!0,this.is_active_thickness=!0)}}this.allValid()}},customNatuursteenChanged(){if(this.is_active_thickness){if(this.is_valid["thickness"]=!1,""==this.custom_depth||""==this.custom_thickness)this.is_active_custom_height=!1,this.is_active_custom_width_click=!1,this.is_active_custom_height_click=!1;else{this.is_active_custom_height=!0,this.possible_custom_heights=this.getPossibleCustomHeight();for(var e=!1,t=0;t<this.possible_custom_heights.length;t++){var n=this.possible_custom_heights[t];if(n==this.custom_height){e=!0;break}}if(e||(this.custom_height="",this.is_valid["custom_depth"]=!1,this.is_active_custom_width_click=!1,this.is_active_custom_height_click=!1),""!=this.custom_height){for(this.is_active_custom_width_click=!0,this.possible_custom_width_clicks=this.getPossibleCustomWidthClicks(),this.is_active_custom_height_click=!0,this.possible_custom_height_clicks=this.getPossibleCustomHeightClicks(),1==this.possible_custom_width_clicks.length&&(this.custom_width_click=this.possible_custom_width_clicks[0]),e=!1,t=0;t<this.possible_custom_width_clicks.length;t++){var i=this.possible_custom_width_clicks[t];if(i==this.custom_width_click){e=!0;break}}if(e||(this.custom_width_click="",this.is_valid["custom_depth"]=!1),e){for(1==this.possible_custom_height_clicks.length&&(this.custom_height_click=this.possible_custom_height_clicks[0]),e=!1,t=0;t<this.possible_custom_height_clicks.length;t++)if(i=this.possible_custom_height_clicks[t],i==this.custom_height_click){e=!0;break}e||(this.custom_height_click="",this.is_valid["custom_depth"]=!1)}""!=this.custom_width_click&&""!=this.custom_height_click&&"0"!=this.custom_height_click&&(this.is_valid["thickness"]=!0,this.is_valid["custom_depth"]=!0,this.is_active_thickness=!0)}}this.allValid()}},endstoneChanged(){this.is_active_endstone&&(this.is_valid["endstone"]=!1,""===this.quotation.endstone?this.input_errors["endstone"]="Eindsteen is verplicht":(this.is_valid["endstone"]=!0,this.is_active_endstone=!0),this.allValid(),this.mayOrder())},allValid(){"natuursteen"!==this.material||"vensterbank"!==this.variant||""!=this.quotation.projectName&&""!=this.quotation.sizeId?""==this.quotation.projectName||!this.showCustomStone&&!this.showCustomStoneNatuursteen&&this.showEndstone&&""===this.quotation.endstone?this.all_valid=!1:!this.showCustomStone||""!=this.custom_thickness&&""!=this.custom_width_click&&""!==this.custom_height_click?this.showCustomStoneNatuursteen&&!this.is_valid["custom_depth"]?this.all_valid=!1:this.showCustomStone||this.showCustomStoneNatuursteen||!this.showDiepteDikte||""!=this.depth&&""!=this.thickness?this.all_valid=!0:this.all_valid=!1:this.all_valid=!1:this.all_valid=!1},setForm(e){this.form=e},mayOrder(){if(this.all_valid&&"0"==this.stone.may_order&&!this.isAdmin){let e="De huidige keuze is helaas niet meer beschikbaar. Neem contact op, we adviseren u graag een passend alternatief.";return""!=this.stone.may_not_order_message&&(e=this.stone.may_not_order_message),this.$swal("",e,"error"),this.all_valid=!1,!1}return!0}}};const Iy=Dp()(Sy,[["render",Ng]]);var Py=Iy;const Ty={class:"wizard_inleiding"},Ay=["disabled"],Ly=Hi("i",{class:"fa fa-chevron-right"},null,-1),Dy=Hi("br",null,null,-1),zy=Hi("br",null,null,-1),qy=["innerHTML"],Ny={key:0,id:"step0",class:"wizard"},My={key:0,class:"alert alert-danger"},By=Hi("div",{class:"row"},[Hi("div",{class:"col12"},[Hi("div",{style:{"font-weight":"bold",padding:"5px"}}," Uw materiaal ")])],-1),Vy={class:"row",id:"material_material"},Fy=["onClick"],Ry=["src"],Uy={class:"material_title"},$y={key:0,class:"row"},Wy=Hi("div",{class:"col12"},[Hi("div",{style:{"font-weight":"bold",padding:"5px"}}," Uw productgroep ")],-1),Hy={class:"variants"},Ky=["onClick"],Gy=["src"],Jy={class:"material_title"},Yy={key:1,class:"row"},Zy=Hi("div",{class:"col12"},[Hi("div",{style:{"font-weight":"bold",padding:"5px"}}," Uw model ")],-1),Xy={class:"model"},Qy=["onClick"],ew=["src"],tw={class:"material_title"},nw=Hi("br",null,null,-1),iw=Hi("br",null,null,-1),ow=["disabled"],sw=Hi("i",{class:"fa fa-chevron-right"},null,-1);function rw(e,t,n,o,s,r){return Di(),Bi(Si,null,[Hi("div",Ty,[Hi("button",{onClick:[t[0]||(t[0]=Gs(e=>r.clickNext(),["prevent"])),t[1]||(t[1]=e=>r.validate())],type:"button",name:"next",id:"next_t",class:"btn",style:{float:"right","font-size":"18px"},disabled:!s.all_valid},[Zi("Doorgaan "),Ly],8,Ay),Zi(" In slechts een paar stappen stelt u een vrijblijvende offerte samen."),Dy,zy,Hi("span",{innerHTML:r.getTitle()},null,8,qy)]),s.loading?Qi("",!0):(Di(),Bi("div",Ny,[s.errors.length>0?(Di(),Bi("div",My,[Zi(" Er zijn foutmeldingen opgetreden, controleer uw invoer: "),Hi("ul",null,[(Di(!0),Bi(Si,null,Ln(s.errors,e=>(Di(),Bi("li",{key:e},Object(i["P"])(e),1))),128))])])):Qi("",!0),Hi("form",{method:"post",ref:r.setForm},[By,Hi("div",Vy,[(Di(!0),Bi(Si,null,Ln(r.getCategories(),t=>(Di(),Bi("div",{key:t.id,class:Object(i["L"])(["col12-xs",{col3:4===r.getCategories().length,col4:3===r.getCategories().length}])},[Hi("div",{class:Object(i["L"])(["material material_material",{material_disabled:e.disabledMaterials[t.id]}]),onClick:e=>r.clickMaterial(t)},[Hi("img",{src:"/uploads/rde/stonecategoryimages/"+t.imagefilename},null,8,Ry),Hi("div",Uy,Object(i["P"])(t.shortname),1)],10,Fy)],2))),128))]),s.show_material_type?(Di(),Bi("div",$y,[Wy,Hi("div",Hy,[(Di(!0),Bi(Si,null,Ln(r.getCategories(this.categoryMaterial),e=>(Di(),Bi("div",{key:e.id,class:Object(i["L"])(["col12-xs",{col4:3==r.getCategories(this.categoryMaterial).length||r.getCategories(this.categoryMaterial).length>=4,col3:4==r.getCategories(this.categoryMaterial).length,material_disabled:s.disabledTypes[e.id]}])},[Hi("div",{class:"material material_type",onClick:t=>r.clickType(e)},[Hi("img",{src:"/uploads/rde/stonecategoryimages/"+e.imagefilename},null,8,Gy),Hi("div",Jy,Object(i["P"])(e.shortname),1)],8,Ky)],2))),128))])])):Qi("",!0),s.show_material_model?(Di(),Bi("div",Yy,[Zy,Hi("div",Xy,[(Di(!0),Bi(Si,null,Ln(r.getCategories(this.categoryType),e=>(Di(),Bi("div",{key:e.id,class:Object(i["L"])(["col12-xs",{col4:3==r.getCategories(this.categoryType).length,col3:4==r.getCategories(this.categoryType).length,material_disabled:s.disabledModels[e.id]}])},[Hi("div",{class:"material material_type",onClick:t=>r.clickModel(e)},[Hi("img",{src:"/uploads/rde/stonecategoryimages/"+e.imagefilename},null,8,ew),Hi("div",tw,Object(i["P"])(e.shortname),1)],8,Qy)],2))),128))])])):Qi("",!0),nw,iw,Hi("button",{onClick:[t[2]||(t[2]=Gs(e=>r.clickNext(),["prevent"])),t[3]||(t[3]=e=>r.validate())],type:"button",name:"next",id:"next",class:"btn",style:{float:"right"},disabled:!s.all_valid},[Zi("Doorgaan "),sw],8,ow)],512)]))],64)}var aw={name:"Material",data(){return{loading:!0,form:null,all_valid:!1,errors:[],quotation:{},categories:{},isAdmin:!1,categoryMaterial:"",categoryType:"",categoryModel:"",fullName:"",disabledMaterial:{},disabledTypes:{},disabledModels:{},show_material_type:!1,show_material_model:!1}},mounted(){},created(){this.fetchData()},watch:{},computed:{},methods:{fetchData(){fetch("?action=wizard&step=0&json=1",{headers:{"Content-type":"application/json"}}).then(e=>e.json()).then(e=>{fy.handleError(this,e)||(this.quotation=e.data.quotation,this.categories=e.data.categories,this.isAdmin=e.data.isAdmin,this.initDisabledMaterials(),""!==this.quotation.stoneCategoryId&&null!==this.quotation.stoneCategoryId&&this.init(this.quotation.stoneCategoryId),this.loading=!1)}).catch(e=>{this.errors=[],this.errors.push("Server fout opgetreden. Probeer het later nog eens, of neem contact op ("+e+")")})},postData(){fetch("?action=wizard&step=0&json=1",{method:"POST",headers:{"Content-type":"application/json",Accept:"application/json"},body:JSON.stringify({stoneCategoryId:this.quotation.stoneCategoryId})}).then(e=>e.json()).then(e=>{fy.handleError(this,e)||(this.errors=e.data.errors,"redirect"in e?location.href=e.redirect:(this.domestic_readonly=!1,this.street_readonly=!1,this.quotation.street_city_manual=!0))}).catch(e=>{this.errors=[],this.errors.push("Server fout opgetreden. Probeer het later nog eens, of neem contact op ("+e+")")})},clickNext(){this.postData()},validate:function(){this.all_valid=!0},setForm(e){this.form=e},initDisabledMaterials(){this.disabledMaterials=[],this.isAdmin||(this.disabledMaterials[3]=!0,this.disabledMaterials[14]=!0)},clickMaterial(e){if(!this.isAdmin&&(3==e.id||14==e.id))return;this.categoryMaterial=e,this.categoryType="",this.categoryModel="",this.disabledMaterials=[];let t=this.getCategories();for(let n in t){let t=this.categories[n];t.id!==e.id&&(this.disabledMaterials[t.id]=!0)}this.show_material_type=!0,this.disabledTypes=[],this.disabledModels=[],this.all_valid=!1},clickType(e){this.quotation.stoneCategoryId=e.id,this.categoryType=e,this.disabledTypes=[];let t=this.getCategories(this.categoryMaterial);for(let n in t){let e=t[n];e.id!==this.categoryType.id&&(this.disabledTypes[e.id]=!0)}this.disabledModels=[],this.show_material_model=this.getCategories(e).length>0,this.all_valid=!this.show_material_model},clickModel(e){this.quotation.stoneCategoryId=e.id,this.categoryModel=e,this.disabledModels=[];let t=this.getCategories(this.categoryType);for(let n in t){let e=t[n];e.id!==this.categoryModel.id&&(this.disabledModels[e.id]=!0)}this.all_valid=!0},getCategories(e){if(void 0===e)return this.categories;for(let t in this.categories){let n=this.categories[t];if(void 0!==n.children){if(n.id===e.id)return n.children;for(let t in n.children){let i=n.children[t];if(void 0!==i.children){if(i.id===e.id)return i.children;for(let t in i.children){let n=i.children[t];if(n.id===e.id)return n.children}}}}}return[]},init(e){let t,n=[];for(let i in this.categories){let e=this.categories[i];if(e.level=0,n[e.id]=e,void 0!==e.children)for(let t in e.children){let i=e.children[t];if(i.level=1,n[i.id]=i,void 0!==i.children)for(let e in i.children){let t=i.children[e];t.level=2,n[t.id]=t}}}for(let i in n){let o=n[i];if(o.id==e){t=o;break}}if(2==t.level){let e=n[t.parent_id];this.clickMaterial(n[e.parent_id]),this.clickType(e),this.clickModel(t)}else this.clickMaterial(n[t.parent_id]),this.clickType(t)},getTitle(){return""!==this.categoryModel?"<b>Uw selectie: </b><b style='color: #ce000c'>"+this.categoryModel.name+"</b>":""!==this.categoryType?"<b>Uw selectie: </b><b style='color: #ce000c'>"+this.categoryType.name+"</b>":""!==this.categoryMaterial?"<b>Selecteer een productgroep</b>":"<b>Selecteer uw materiaal en productgroep</b>"}}};const lw=Dp()(aw,[["render",rw]]);var cw=lw;const uw={key:0,id:"step2-popup-bg"},dw=Hi("div",{class:"wizard_inleiding"}," Vul in onderstaand lijst uw elementmaten in. U kunt ook tabs gebruiken bij de invoer. ",-1),hw={key:1,class:"alert alert-danger"},pw={key:2,id:"step2",class:"wizard"},mw={key:0,class:"form-row"},fw=Hi("label",{class:"col3 col-form-label"},"Speling",-1),gw={class:"col1"},vw={key:0,class:"input-validation-icon"},bw=Hi("img",{src:"/projects/rde/templates/frontend/images/check_green.svg",alt:"",width:"20"},null,-1),_w=[bw],yw={class:"col7"},ww={class:"select"},kw=Hi("option",{value:""}," Kies de gewenste speling... ",-1),xw=["value"],jw={class:"col1"},Ow=Hi("a",{class:"question-mark-inline fa fa-info-circle"},null,-1),Ew={key:1,class:"form-row"},Cw=Hi("label",{class:"col3 col-form-label"},"Muurdikte (mm)",-1),Sw={class:"col1"},Iw={key:0,class:"input-validation-icon"},Pw=Hi("img",{src:"/projects/rde/templates/frontend/images/check_green.svg",alt:"",width:"20"},null,-1),Tw=[Pw],Aw={class:"col7"},Lw={class:"col1"},Dw=Hi("a",{class:"question-mark-inline fa fa-info-circle"},null,-1),zw={class:"form-row"},qw=Hi("label",{class:"col3 col-form-label"},"Elementen",-1),Nw=Hi("div",{class:"col1"},null,-1),Mw=Hi("label",{class:"col7 col-form-label"},"Voer uw elementmaten in.",-1),Bw=Hi("div",{class:"col1"},null,-1),Vw={class:"col12 scroller-x"},Fw={id:"elementtable"},Rw={class:"header-row"},Uw=Hi("td",null," Kenmerk ",-1),$w=Hi("td",{style:{width:"100px"}}," Aantal ",-1),Ww={style:{width:"120px"}},Hw={style:{width:"120px"}},Kw=Hi("a",{class:"question-mark-inline fa fa-info-circle"},null,-1),Gw={key:0},Jw={key:1},Yw=Hi("p",null,[Zi("U kunt hier aangeven aan welke kant een verstek komt."),Hi("br"),Zi(' (Hier vind u een handige reken voorbeeld "pdf")')],-1),Zw=Hi("ul",null,[Hi("li",null,[Zi("Hartklik gemeten "),Hi("ul",null,[Hi("li",null,[Hi("a",{href:"/downloads/verstekken-invulformulier-uitwendige-hoek.pdf",target:"_blank"},"uitwendige hoek.pdf")]),Hi("li",null,[Hi("a",{href:"/downloads/verstekken-invulformulier-inwendige-hoek.pdf",target:"_blank"},"inwendige hoek.pdf")]),Hi("li",null,[Hi("a",{href:"/downloads/verstekken-invulformulier-uitwendige-hoek-van-675-135-graden.pdf",target:"_blank"},"uitwendige hoek van 67,5 (135) graden.pdf")]),Hi("li",null,[Hi("a",{href:"/downloads/verstekken-invulformulier-uitwendige-hoek-van-60-120-graden.pdf",target:"_blank"},"uitwendige hoek van 60 (120) graden.pdf")])])]),Hi("li",null,[Zi("Tot aan de punt gemeten "),Hi("ul",null,[Hi("li",null,[Hi("a",{href:"/downloads/verstekken/verstekken-informatie-uitwendige-hoek-tot-aan-de-punt-gemeten.pdf",target:"_blank"},"uitwendige hoek tot aan de punt gemeten.pdf")]),Hi("li",null,[Hi("a",{href:"/downloads/verstekken/verstekken-informatie-inwendige-hoek-tot-aan-de-punt-gemeten.pdf",target:"_blank"},"inwendige hoek tot aan de punt gemeten.pdf")])])])],-1),Xw=[Yw,Zw],Qw={key:0,style:{width:"120px"}},ek=Hi("br",null,null,-1),tk=Hi("a",{class:"question-mark-inline fa fa-info-circle"},null,-1),nk=Hi("br",null,null,-1),ik=Hi("br",null,null,-1),ok={key:1,style:{width:"120px"}},sk=Hi("br",null,null,-1),rk=Hi("a",{class:"question-mark-inline fa fa-info-circle"},null,-1),ak={style:{"min-width":"140px"}},lk=Hi("br",null,null,-1),ck=Hi("a",{class:"question-mark-inline fa fa-info-circle"},null,-1),uk=["onUpdate:modelValue","name"],dk=["onUpdate:modelValue","name"],hk=["onUpdate:modelValue","name"],pk=["onUpdate:modelValue","name","id","onKeydown","onKeyup","onChange"],mk=["onUpdate:modelValue","name","onKeydown","onChange"],fk=["onUpdate:modelValue","name","onKeydown","onChange"],gk=["value","onClick"],vk={key:0},bk=["value","onClick"],_k={key:1},yk=["value","onClick"],wk=["onUpdate:modelValue","name"],kk=["onUpdate:modelValue","name"],xk=["onUpdate:modelValue","name"],jk=["onClick"],Ok=Hi("i",{class:"fa fa-remove"},null,-1),Ek=[Ok],Ck=["onClick"],Sk=Hi("i",{class:"fa fa-plus"},null,-1),Ik=[Sk],Pk={class:"header-row"},Tk=Hi("td",null,"Totalen ",-1),Ak=Hi("td",null,null,-1),Lk=Hi("td",null,null,-1),Dk={key:0},zk={key:1},qk=Hi("br",null,null,-1),Nk=Hi("br",null,null,-1),Mk=Hi("i",{class:"fa fa-plus"},null,-1),Bk=Hi("br",null,null,-1),Vk=Hi("br",null,null,-1),Fk=Hi("i",{class:"fa fa-chevron-left"},null,-1),Rk=Hi("i",{class:"fa fa-chevron-right"},null,-1),Uk={class:"questionbox_title"},$k={id:"mitreElement"},Wk={class:"questionbox_content"},Hk=Hi("p",null," Kies aan welke uiteinden van het element u een (verstek)hoek wilt.",-1),Kk={class:"verstekhoekImgAfstand"},Gk={for:"mitrebox_0"},Jk={class:"mitre_example mitre_example_none"},Yk=["title"],Zk=Hi("div",{class:"mitre_line"},null,-1),Xk=[Zk],Qk=Hi("div",{class:"mitre_label"},"Geen",-1),ex={class:"verstekhoekImgAfstand"},tx=["disabled"],nx={for:"mitrebox_1"},ix=Xi('<div class="mitre_example mitre_example_left"><div title="Verstekhoek aan linker elementeinde"><div class="mitre_line"></div><div class="mitre_schuin_left"></div></div></div><div class="mitre_of">OF</div>',2),ox={class:"mitre_example mitre_example_left_top"},sx=["title"],rx=Hi("div",{class:"mitre_line"},null,-1),ax=Hi("div",{class:"mitre_schuin_left_top"},null,-1),lx=[rx,ax],cx=Hi("div",{class:"mitre_label"},"Links",-1),ux={class:"verstekhoekImgAfstand"},dx=["disabled"],hx={for:"mitrebox_2"},px={class:"mitre_example mitre_example_right"},mx=["title"],fx=Hi("div",{class:"mitre_line"},null,-1),gx=Hi("div",{class:"mitre_schuin_right"},null,-1),vx=[fx,gx],bx=Hi("div",{class:"mitre_of"},"OF",-1),_x={class:"mitre_example mitre_example_right_top"},yx=["title"],wx=Hi("div",{class:"mitre_line"},null,-1),kx=Hi("div",{class:"mitre_schuin_right_top"},null,-1),xx=[wx,kx],jx=Hi("div",{class:"mitre_label"},"Rechts",-1),Ox={class:"verstekhoekImgAfstand"},Ex=["disabled"],Cx={for:"mitrebox_3"},Sx={class:"mitre_example mitre_example_leftright"},Ix=["title"],Px=Hi("div",{class:"mitre_line"},null,-1),Tx=Hi("div",{class:"mitre_schuin_left"},null,-1),Ax=Hi("div",{class:"mitre_schuin_right"},null,-1),Lx=[Px,Tx,Ax],Dx=Hi("div",{class:"mitre_of"},"OF",-1),zx={class:"mitre_example mitre_example_leftright_top"},qx=["title"],Nx=Hi("div",{class:"mitre_line"},null,-1),Mx=Hi("div",{class:"mitre_schuin_right_top"},null,-1),Bx=Hi("div",{class:"mitre_schuin_left_top"},null,-1),Vx=[Nx,Mx,Bx],Fx=Hi("div",{class:"mitre_label"},"Beide",-1),Rx=Hi("p",null,null,-1),Ux={class:"center"},$x={key:1,class:"questionbox",id:"flagWindowbox"},Wx={class:"questionbox_title"},Hx={id:"flagWindowElement"},Kx={class:"questionbox_content"},Gx=Hi("p",null,"Kies of het element aan geen, een of twee kanten aan een deurkozijn grenst.",-1),Jx={class:"vlagkozijnImgAfstand"},Yx=Hi("label",{for:"flagWindowbox_0"},"Geen",-1),Zx={class:"vlagkozijnImgAfstand"},Xx=["disabled"],Qx=Hi("label",{for:"flagWindowbox_1"},[Hi("img",{src:"/images/mitres/flagframe_single.gif",alt:"Enkel vlagkozijn",class:"exampleimg"})],-1),ej={class:"vlagkozijnImgAfstand"},tj=["disabled"],nj=Hi("label",{for:"flagWindowbox_2"},[Hi("img",{src:"/images/mitres/flagframe_double.gif",alt:"Dubbel vlagkozijn",class:"exampleimg"})],-1),ij={class:"center"},oj={class:"questionbox_title"},sj={id:"flagEndstoneElement"},rj={class:"questionbox_content"},aj=Hi("p",null,"Kies uw eindsteen",-1),lj={class:"endStoneRow"},cj=Xi('<label for="flageEndstone_0"><div class="endstone_example"><div title="Deurkozijn aan linker zijde"><div class="endstone_line"></div></div></div><div class="mitre_label">Geen</div></label>',1),uj={class:"endStoneRow"},dj=["disabled"],hj=Xi('<label for="flageEndstone_1"><div class="endstone_example"><div title="Deurkozijn aan linker zijde"><div class="endstone_line"></div><div class="endstone_left"></div><div class="line_left"></div></div></div><div class="mitre_label">Links</div></label>',1),pj={class:"endStoneRow"},mj=["disabled"],fj=Xi('<label for="flageEndstone_2"><div class="endstone_example"><div title="Deurkozijn aan rechter zijde"><div class="endstone_line"></div><div class="endstone_right"></div><div class="line_right"></div></div></div><div class="mitre_label">Rechts</div></label>',1),gj={class:"endStoneRow"},vj=["disabled"],bj=Xi('<label for="flageEndstone_3"><div class="endstone_example"><div title="Deurkozijn aan beide zijde"><div class="endstone_line"></div><div class="endstone_left"></div><div class="line_left"></div><div class="endstone_right"></div><div class="line_right"></div></div></div><div class="mitre_label">Beide</div></label>',1),_j={class:"center"},yj=Hi("br",null,null,-1);function wj(e,t,n,o,s,r){const a=In("GsdPopper");return Di(),Bi(Si,null,[s.showPopupBG?(Di(),Bi("div",uw)):Qi("",!0),dw,s.errors.length>0?(Di(),Bi("div",hw,[Zi(" Er zijn foutmeldingen opgetreden, controleer uw invoer: "),Hi("ul",null,[(Di(!0),Bi(Si,null,Ln(s.errors,e=>(Di(),Bi("li",{key:e},Object(i["P"])(e),1))),128))])])):Qi("",!0),s.loading?Qi("",!0):(Di(),Bi("div",pw,[Hi("form",{method:"post",ref:r.setForm},[r.showSpeling()?(Di(),Bi("div",mw,[fw,Hi("div",gw,[s.is_valid.shorter?(Di(),Bi("div",vw,_w)):Qi("",!0)]),Hi("div",yw,[Hi("div",ww,[En(Hi("select",{name:"shorter",class:Object(i["L"])(["shorter",{inputerror:s.hasShorterError}]),"onUpdate:modelValue":t[0]||(t[0]=e=>s.quotation.shorter=e)},[kw,(Di(!0),Bi(Si,null,Ln(r.possible_shorter,e=>(Di(),Bi("option",{value:e.value,key:e.value},Object(i["P"])(e.text),9,xw))),128))],2),[[Rs,s.quotation.shorter]])])]),Hi("div",jw,[Ki(a,null,{content:St(()=>[Zi(" Normaal heeft u bij nieuwbouw hier een waarde van 10 mm. Dit betekent dat u 5 mm voeg aan beide zijden van het element tegen de neggekant overhoud. Bij renovatie worden de kozijnen vaak kleiner als de bestaande muuropening gemaakt, daarom wordt hier vaak een kleinere waarde genomen. Wanneer u 0 mm kiest heeft u het element precies even breed als de kozijnbreedte. ")]),default:St(()=>[Ow]),_:1})])])):Qi("",!0),r.showMuurdikte()?(Di(),Bi("div",Ew,[Cw,Hi("div",Sw,[s.is_valid.wall_thickness?(Di(),Bi("div",Iw,Tw)):Qi("",!0)]),Hi("div",Aw,[En(Hi("input",{type:"number","onUpdate:modelValue":t[1]||(t[1]=e=>s.quotation_extra.wall_thickness=e),name:"wall_thickness",class:Object(i["L"])(["form-input inputnumber",{inputerror:s.hasWallthicknessError}])},null,2),[[Ms,s.quotation_extra.wall_thickness]])]),Hi("div",Lw,[Ki(a,null,{content:St(()=>[Zi(" Voer uw muurdikte in. Is uw muurdikte niet mogelijk? Kies dan op de vorige pagina een andere muurafdekker welke past bij uw muurdikte, of neem even contact op. ")]),default:St(()=>[Dw]),_:1})])])):Qi("",!0),Hi("div",zw,[qw,Nw,Mw,Bw,Hi("div",Vw,[Hi("table",Fw,[Hi("tr",Rw,[Uw,$w,Hi("td",Ww,Object(i["P"])(r.maatTextTitle),1),Hi("td",Hw,[Zi(Object(i["P"])(this.verstekhoektitle)+" ",1),Ki(a,null,{content:St(()=>["spekband"===this.stone.type&&1==s.quotation.brandId?(Di(),Bi("div",Gw," Geef hier aan of een hoek wilt links, rechts of aan beide kanten. ")):(Di(),Bi("div",Jw,Xw))]),default:St(()=>[Kw]),_:1})]),s.showVlagkozijn?(Di(),Bi("td",Qw,[Zi(" Vlagkozijn"),ek,Ki(a,null,{content:St(()=>[Zi(" Een vlagkozijn, melkmeisje of schouderkozijn is een bouwkundige benaming voor een kozijntype waarbij een deurkozijn aan weerszijden geflankeerd wordt door een raamopening of zijlicht."),nk,Zi(" De jukvorm van het kozijn doet denken aan een melkmeisje dat twee emmers draagt."),ik,Zi(" Met deze toepassing wordt het element tegen het kozijn geplaatst hierdoor wordt de helft van de speling erbij geteld. ")]),default:St(()=>[tk]),_:1})])):Qi("",!0),s.showKieseindsteen?(Di(),Bi("td",ok,[Zi(" Eindsteen"),sk,Ki(a,{content:"Kies een eindsteen."},{default:St(()=>[rk]),_:1})])):Qi("",!0),Hi("td",ak,[Zi(" Element lengte "),lk,Zi(" (mm) "),Ki(a,{content:"De element lengte word uitgerekend a.d.v. speling, verstekhoek e.d. De daadwerkelijk element lengte kunnen we pas uitrekenen als ook eventuele verstekhoeken bekend zijn, welke in de volgende stap gevraagd worden."},{default:St(()=>[ck]),_:1})])]),(Di(!0),Bi(Si,null,Ln(s.elements,(e,n)=>(Di(),Bi("tr",{class:"elementrow",key:n},[Hi("td",null,[En(Hi("input",{type:"hidden","onUpdate:modelValue":t=>e.mitre=t,name:"elements["+n+"][mitre]"},null,8,uk),[[Ms,e.mitre]]),En(Hi("input",{type:"hidden","onUpdate:modelValue":t=>e.flagWindow=t,name:"elements["+n+"][flagWindow]"},null,8,dk),[[Ms,e.flagWindow]]),En(Hi("input",{type:"hidden","onUpdate:modelValue":t=>e.oldkey=t,name:"elements["+n+"][oldkey]"},null,8,hk),[[Ms,e.oldkey]]),En(Hi("input",{autocomplete:"off",type:"text","onUpdate:modelValue":t=>e.referenceName=t,onFocus:t[2]||(t[2]=e=>e.target.select()),name:"elements["+n+"][referenceName]",id:"elements["+n+"][referenceName]",onKeydown:e=>r.navigateReferenceName(n),onKeyup:e=>r.navigateReferenceNameUp(n),onChange:t=>r.validateReferenceNameInput(e,n),placeholder:"Kenmerk...",class:Object(i["L"])(["form-input",{inputerror:e.hasReferenceNameError}]),maxlength:"12",ref_for:!0,ref:"referenceName_"+n},null,42,pk),[[Ms,e.referenceName]])]),Hi("td",null,[En(Hi("input",{autocomplete:"off",type:"text","onUpdate:modelValue":t=>e.amount=t,onFocus:t[3]||(t[3]=e=>e.target.select()),name:"elements["+n+"][amount]",ref_for:!0,ref:"amount_"+n,onKeydown:e=>r.navigateAmount(n),onChange:t=>r.validateAmountInput(e),placeholder:"Aantal...",class:"form-input inputnumber",maxlength:"4"},null,40,mk),[[Ms,e.amount]])]),Hi("td",null,[En(Hi("input",{autocomplete:"off",type:"text","onUpdate:modelValue":t=>e.inputLength=t,onFocus:t[4]||(t[4]=e=>e.target.select()),name:"elements["+n+"][inputLength]",ref_for:!0,ref:"inputLength_"+n,onKeydown:e=>r.navigateInputLength(n),onChange:t=>r.validateInputLengthInput(e,n,!0),placeholder:"Maat...",class:Object(i["L"])(["form-input inputnumber",{inputerror:e.hasInputLengthError}]),maxlength:"5"},null,42,fk),[[Ms,e.inputLength]])]),Hi("td",null,[Hi("input",{type:"button",value:r.mitreText(e),onClick:t=>r.openMitrebox(e),class:"form-input form-btn",ref_for:!0,ref:"mitrebox_"+n},null,8,gk)]),s.showVlagkozijn?(Di(),Bi("td",vk,[Hi("input",{type:"button",value:r.flagWindowText(e),onClick:t=>r.openflagWindowbox(e),class:"form-input form-btn"},null,8,bk)])):Qi("",!0),s.showKieseindsteen?(Di(),Bi("td",_k,[Hi("input",{type:"button",value:r.endstoneText(e),onClick:t=>r.openEndstoneBox(e),class:"form-input form-btn"},null,8,yk),En(Hi("input",{type:"hidden","onUpdate:modelValue":t=>e.leftEndstone=t,name:"elements["+n+"][leftEndstone]"},null,8,wk),[[Ms,e.leftEndstone]]),En(Hi("input",{type:"hidden","onUpdate:modelValue":t=>e.rightEndstone=t,name:"elements["+n+"][rightEndstone]"},null,8,kk),[[Ms,e.rightEndstone]])])):Qi("",!0),Hi("td",null,[En(Hi("input",{type:"text","onUpdate:modelValue":t=>e.elementLengthText=t,readonly:"",name:"elements["+n+"][elementLength]",class:"form-input inputnumber wizard_elementLength inputreadonly_cust"},null,8,xk),[[Ms,e.elementLengthText]]),Hi("a",{href:"#",class:"elementtable_fa",onClick:Gs(e=>r.remove(n),["prevent"]),title:"Verwijderen",ref_for:!0,ref:"remove_"+n},Ek,8,jk),Hi("a",{href:"#",class:"elementtable_fa",onClick:Gs(e=>r.addElement(n),["prevent"]),title:"Toevoegen",ref_for:!0,ref:"add_"+n},Ik,8,Ck)])]))),128)),Hi("tr",Pk,[Tk,Hi("td",null,[En(Hi("input",{type:"text","onUpdate:modelValue":t[5]||(t[5]=e=>s.amount_total=e),disabled:"",class:"form-input inputnumber inputreadonly_cust"},null,512),[[Ms,s.amount_total]])]),Ak,Lk,s.showVlagkozijn?(Di(),Bi("td",Dk)):Qi("",!0),s.showKieseindsteen?(Di(),Bi("td",zk)):Qi("",!0),Hi("td",null,[En(Hi("input",{type:"text","onUpdate:modelValue":t[6]||(t[6]=e=>s.elementLength_total=e),disabled:"",class:"form-input inputnumber wizard_elementLength inputreadonly_cust"},null,512),[[Ms,s.elementLength_total]])])])])])]),qk,Nk,Hi("button",{type:"button",name:"add",id:"add",class:"btn",onClick:t[7]||(t[7]=e=>r.addElement())},[Mk,Zi(" Element toevoegen")]),Bk,Vk,Hi("button",{onClick:t[8]||(t[8]=Gs(e=>r.clickPrev(),["prevent"])),type:"button",name:"prev",id:"prev",class:"btn",style:{float:"left"}},[Fk,Zi(" Vorige stap")]),Hi("button",{onClick:t[9]||(t[9]=Gs(e=>r.clickNext(),["prevent"])),type:"button",name:"next",id:"next",class:Object(i["L"])(["btn",{disabled:!s.all_valid}]),style:{float:"right"}},[Zi("Doorgaan "),Rk],2)],512),s.mitreboxShow?(Di(),Bi("div",{key:0,class:Object(i["L"])(r.mitreBoxClass()),id:"mitrebox"},[Hi("div",Uk,[Zi(Object(i["P"])(this.verstekhoektitle)+" - Element ",1),Hi("span",$k,Object(i["P"])(s.mitreboxElement.referenceName),1)]),Hi("div",Wk,[Hk,Hi("div",Kk,[En(Hi("input",{type:"radio","onUpdate:modelValue":t[10]||(t[10]=e=>s.mitreboxElement.mitre=e),value:"none",id:"mitrebox_0",onChange:t[11]||(t[11]=e=>r.selectMitre())},null,544),[[Fs,s.mitreboxElement.mitre]]),Hi("label",Gk,[Hi("div",Jk,[Hi("div",{id:"rd_mitre_none",title:"Geen "+this.verstekhoektitle},Xk,8,Yk)]),Qk])]),Hi("div",ex,[En(Hi("input",{type:"radio","onUpdate:modelValue":t[12]||(t[12]=e=>s.mitreboxElement.mitre=e),value:"left",id:"mitrebox_1",onChange:t[13]||(t[13]=e=>r.selectMitre()),disabled:"double"===s.mitreboxElement.flagWindow},null,40,tx),[[Fs,s.mitreboxElement.mitre]]),Hi("label",nx,[ix,Hi("div",ox,[Hi("div",{title:this.verstekhoektitle+" aan linker elementeinde"},lx,8,sx)]),cx])]),Hi("div",ux,[En(Hi("input",{type:"radio","onUpdate:modelValue":t[14]||(t[14]=e=>s.mitreboxElement.mitre=e),value:"right",id:"mitrebox_2",onChange:t[15]||(t[15]=e=>r.selectMitre()),disabled:"double"===s.mitreboxElement.flagWindow},null,40,dx),[[Fs,s.mitreboxElement.mitre]]),Hi("label",hx,[Hi("div",px,[Hi("div",{title:this.verstekhoektitle+" aan linker elementeinde"},vx,8,mx)]),bx,Hi("div",_x,[Hi("div",{title:this.verstekhoektitle+" aan rechter elementeinde"},xx,8,yx)]),jx])]),Hi("div",Ox,[En(Hi("input",{type:"radio","onUpdate:modelValue":t[16]||(t[16]=e=>s.mitreboxElement.mitre=e),value:"both",id:"mitrebox_3",onChange:t[17]||(t[17]=e=>r.selectMitre()),disabled:s.mitreboxDisableDouble},null,40,Ex),[[Fs,s.mitreboxElement.mitre]]),Hi("label",Cx,[Hi("div",Sx,[Hi("div",{title:this.verstekhoektitle+"en aan weerszijden"},Lx,8,Ix)]),Dx,Hi("div",zx,[Hi("div",{title:this.verstekhoektitle+"en aan weerszijden"},Vx,8,qx)]),Fx])]),Rx,Hi("p",Ux,[Hi("input",{type:"button",name:"btnMitre",value:"Sluiten",onClick:t[18]||(t[18]=e=>r.selectMitre()),class:"btn"})])])],2)):Qi("",!0),s.flagWindowboxShow?(Di(),Bi("div",$x,[Hi("div",Wx,[Zi("Vlagkozijn - Element "),Hi("span",Hx,Object(i["P"])(s.flagWindowboxElement.referenceName),1)]),Hi("div",Kx,[Gx,Hi("div",Jx,[En(Hi("input",{type:"radio","onUpdate:modelValue":t[19]||(t[19]=e=>s.flagWindowboxElement.flagWindow=e),value:"false",id:"flagWindowbox_0",onChange:t[20]||(t[20]=e=>r.selectflagWindow())},null,544),[[Fs,s.flagWindowboxElement.flagWindow]]),Yx]),Hi("div",Zx,[En(Hi("input",{type:"radio","onUpdate:modelValue":t[21]||(t[21]=e=>s.flagWindowboxElement.flagWindow=e),value:"single",id:"flagWindowbox_1",onChange:t[22]||(t[22]=e=>r.selectflagWindow()),disabled:"both"==s.flagWindowboxElement.mitre},null,40,Xx),[[Fs,s.flagWindowboxElement.flagWindow]]),Qx]),Hi("div",ej,[En(Hi("input",{type:"radio","onUpdate:modelValue":t[23]||(t[23]=e=>s.flagWindowboxElement.flagWindow=e),value:"double",id:"flagWindowbox_2",onChange:t[24]||(t[24]=e=>r.selectflagWindow()),disabled:"none"!=s.flagWindowboxElement.mitre},null,40,tj),[[Fs,s.flagWindowboxElement.flagWindow]]),nj]),Hi("p",ij,[Hi("input",{type:"button",name:"btnflagWindow",value:"Sluiten",onClick:t[25]||(t[25]=e=>r.selectflagWindow()),class:"btn"})])])])):Qi("",!0),s.flagEndstoneShow?(Di(),Bi("div",{key:2,class:Object(i["L"])(r.flagEndstoneBoxClass()),id:"flagEndstoneBox"},[Hi("div",oj,[Zi("Eindsteen - Element "),Hi("span",sj,Object(i["P"])(s.flagEndstoneElement.referenceName),1)]),Hi("div",rj,[aj,Hi("div",lj,[En(Hi("input",{type:"radio","onUpdate:modelValue":t[26]||(t[26]=e=>s.flagEndstoneValue=e),value:"none",id:"flageEndstone_0",onChange:t[27]||(t[27]=e=>r.selectEndstone())},null,544),[[Fs,s.flagEndstoneValue]]),cj]),Hi("div",uj,[En(Hi("input",{type:"radio","onUpdate:modelValue":t[28]||(t[28]=e=>s.flagEndstoneValue=e),value:"left",id:"flageEndstone_1",onChange:t[29]||(t[29]=e=>r.selectEndstone()),disabled:"left"==s.flagEndstoneElement.mitre||"both"==s.flagEndstoneElement.mitre},null,40,dj),[[Fs,s.flagEndstoneValue]]),hj]),Hi("div",pj,[En(Hi("input",{type:"radio","onUpdate:modelValue":t[30]||(t[30]=e=>s.flagEndstoneValue=e),value:"right",id:"flageEndstone_2",onChange:t[31]||(t[31]=e=>r.selectEndstone()),disabled:"right"==s.flagEndstoneElement.mitre||"both"==s.flagEndstoneElement.mitre},null,40,mj),[[Fs,s.flagEndstoneValue]]),fj]),Hi("div",gj,[En(Hi("input",{type:"radio","onUpdate:modelValue":t[32]||(t[32]=e=>s.flagEndstoneValue=e),value:"both",id:"flageEndstone_3",onChange:t[33]||(t[33]=e=>r.selectEndstone()),disabled:"left"==s.flagEndstoneElement.mitre||"right"==s.flagEndstoneElement.mitre||"both"==s.flagEndstoneElement.mitre},null,40,vj),[[Fs,s.flagEndstoneValue]]),bj]),Hi("p",_j,[yj,Hi("input",{type:"button",name:"btnflagWindow",value:"Sluiten",onClick:t[34]||(t[34]=e=>r.selectEndstone()),class:"btn"})])])],2)):Qi("",!0)]))],64)}class kj{static isNumeric(e){return("number"===typeof e||"string"===typeof e)&&""!==e&&!isNaN(e)}static isNumberFloat(e){return this.isNumeric(e)&&e%1!==0}static deg2rad(e){return e*Math.PI/180}}var xj={name:"Elements",components:{GsdPopper:g_},data(){return{loading:!0,is_valid:{},all_valid:!1,is_active:{},errors:[],form:null,hasShorterError:!1,hasWallthicknessError:!1,quotation:{},quotation_extra:{},elements:{},stone:{},stone_size:{},showVlagkozijn:!1,showKieseindsteen:!1,verstekhoektitle:"",element_new:{},mitreboxShow:!1,mitreboxElement:{},mitreboxDisableDouble:!1,flagWindowboxShow:!1,flagWindowboxElement:{},flagEndstoneShow:!1,flagEndstoneValue:"none",flagEndstoneElement:{},amount_total:0,elementLength_total:"",alphabetArray:["A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"],showPopupBG:!1}},created(){this.fetchData()},mounted(){},watch:{"quotation.shorter":function(){this.shorterChanged()},"quotation_extra.wall_thickness":function(){this.wallthicknessChanged()}},computed:{maatTextTitle(){return"raamdorpel"===this.stone.type?"Kozijnmaat (mm)":"Muurmaat (mm)"},possible_shorter:function(){for(var e=[],t=0;t<=10;t+=2){var n=t+" mm";0==t?n+=" - geadviseerd renovatie":10==t&&(n+=" - geadviseerd nieuwbouw"),e.push({value:t,text:n})}return e}},methods:{fetchData(){fetch("?action=wizard&step=2&json=1",{headers:{"Content-type":"application/json"}}).then(e=>e.json()).then(e=>{fy.handleError(this,e)||(this.quotation=e.data.quotation,this.quotation_extra=e.data.quotation_extra,this.stone=e.data.stone,this.stone_size=e.data.stone_size,this.errors=e.data.errors,this.elements=e.data.elements,this.element_new=e.data.element_new,this.showVlagkozijn=e.data.showVlagkozijn,this.showKieseindsteen=e.data.showKieseindsteen,this.verstekhoektitle=e.data.verstekhoektitle,this.mitreboxElement=e.data.element_new,this.flagWindowboxElement=e.data.element_new,this.flagEndstoneElement=e.data.element_new,null===this.quotation_extra.wall_thickness&&(this.quotation_extra.wall_thickness=""),this.shorterChanged(),this.wallthicknessChanged(),this.calculateTotals(),this.loading=!1)}).catch(e=>{this.errors=[],this.errors.push("Server fout opgetreden. Probeer het later nog eens, of neem contact op ("+e+")")})},postData(e){const t=new FormData(this.form);t.append(e,1),fetch("?action=wizard&step=2&json=1",{method:"POST",headers:{Accept:"application/json"},body:t}).then(e=>e.json()).then(e=>{fy.handleError(this,e)||(this.errors=e.data.errors,"redirect"in e&&(location.href=e.redirect))}).catch(e=>{this.errors=[],this.errors.push("Server fout opgetreden. Probeer het later nog eens, of neem contact op ("+e+")")})},clickPrev(){this.checkForm()&&this.postData("prev")},clickNext(){this.checkForm()&&this.postData("next")},showSpeling(){return"spekband"!==this.stone.type},showMuurdikte(){return"muurafdekker"===this.stone.type},shorterChanged(){this.is_valid["shorter"]=!1,null===this.quotation.shorter||""===this.quotation.shorter?this.is_valid["shorter"]=!1:this.is_valid["shorter"]=!0,this.calculateTotals()},getWallthicknessMin(){return 10*this.stone_size.length-130},getWallthicknessMax(){return 10*this.stone_size.length-30},wallthicknessChanged(){null===this.quotation_extra.wall_thickness||""===this.quotation_extra.wall_thickness||0==this.quotation_extra.wall_thickness?(this.is_valid["wall_thickness"]=!1,this.hasWallthicknessError=!1):this.quotation_extra.wall_thickness<this.getWallthicknessMin()||this.quotation_extra.wall_thickness>this.getWallthicknessMax()?(this.is_valid["wall_thickness"]=!1,this.hasWallthicknessError=!0):(this.is_valid["wall_thickness"]=!0,this.hasWallthicknessError=!1),this.calculateTotals()},mitreText:function(e){return"both"==e.mitre?"Beide":"left"==e.mitre?"Links":"right"==e.mitre?"Rechts":"Nee"},flagWindowText:function(e){return"single"==e.flagWindow?"Enkel":"double"==e.flagWindow?"Dubbel":"Nee"},endstoneText:function(e){return"1"==e.leftEndstone&&"1"==e.rightEndstone?"Beide":"1"==e.leftEndstone?"Links":"1"==e.rightEndstone?"Rechts":"Nee"},remove:function(e){this.elements.splice(e,1),this.calculateTotals()},addElement:function(e){event.preventDefault(),this.element_new.referenceName=this.alphabetArray[this.elements.length],this.elements.length>=26&&(this.element_new.referenceName=this.alphabetArray[Math.floor(this.elements.length/26)-1]+this.alphabetArray[this.elements.length%26]),void 0!==e?this.elements.splice(e+1,0,JSON.parse(JSON.stringify(this.element_new))):this.elements.push(JSON.parse(JSON.stringify(this.element_new))),this.$nextTick((function(){this.$refs["referenceName_"+(this.elements.length-1)][0].focus()})),this.calculateTotals()},calculateElementLength:function(e){"muurafdekker"===this.stone.type?this.calculateElementLengthMuurafdekker(e):this.calculateElementLengthDefault(e)},calculateElementLengthDefault:function(e){e.elementLength=0,e.inputLength=parseInt(e.inputLength),"none"==e.mitre&&e.inputLength>=100&&("single"==e.flagWindow?e.elementLength=e.inputLength-this.quotation.shorter/2:"double"==e.flagWindow?e.elementLength=e.inputLength:e.elementLength=e.inputLength-this.quotation.shorter),e.elementLength<0&&(e.elementLength=0),0!=e.elementLength?e.elementLengthText=e.elementLength:"none"==e.mitre?e.elementLengthText=0:e.elementLengthText=""},calculateElementLengthMuurafdekker:function(e){if(e.elementLength=0,e.elementLengthText="",""!==this.quotation_extra.wall_thickness&&""!==this.quotation.shorter){if(e.inputLength=parseInt(e.inputLength),"none"==e.mitre&&e.inputLength>=100){var t=parseInt(this.quotation_extra.wall_thickness),n=Math.round(10*this.stone_size.length-t)/2;"1"==e.leftEndstone&&"1"==e.rightEndstone?e.elementLength=e.inputLength+n+n-this.quotation.shorter/2:"1"==e.leftEndstone||"1"==e.rightEndstone?e.elementLength=e.inputLength+n-this.quotation.shorter/2:e.elementLength=e.inputLength-this.quotation.shorter,e.elementLength=Math.round(e.elementLength)}e.elementLength<0&&(e.elementLength=0),0!=e.elementLength?e.elementLengthText=e.elementLength:"none"==e.mitre?e.elementLengthText=0:e.elementLengthText=""}else e.elementLengthText=""},calculateTotals(){let e=this.elements.length>0,t=0,n=!0;this.amount_total=0;let i=0;for(var o=0;o<this.elements.length;o++){this.calculateElementLength(this.elements[o]);var s=parseInt(this.elements[o].amount);this.amount_total+=s,kj.isNumeric(this.elements[o].elementLength)&&"none"==this.elements[o].mitre?t+=s*parseInt(this.elements[o].elementLength):n=!1,e&&(0==this.elements[o].amount&&0==this.elements[o].inputLength||(this.validateAmount(this.elements[o])&&this.validateInputLength(this.elements[o],o,!1)?i++:e=!1))}0===i&&(e=!1),this.all_valid=e,this.elementLength_total=n?t:""},openMitrebox:function(e){this.mitreboxShow=!0,this.showPopupBG=!0,this.mitreboxElement=e,this.mitreboxDisableDouble=!(0==this.mitreboxElement.flagWindow||"false"==this.mitreboxElement.flagWindow)},selectMitre(){this.mitreboxShow=!1,this.showPopupBG=!1,this.calculateTotals()},openflagWindowbox:function(e){this.flagWindowboxShow=!0,this.showPopupBG=!0,this.flagWindowboxElement=e},selectflagWindow(){this.flagWindowboxShow=!1,this.showPopupBG=!1,this.calculateTotals()},openEndstoneBox:function(e){this.flagEndstoneShow=!0,this.showPopupBG=!0,this.flagEndstoneElement=e,"1"==e.leftEndstone&&"1"==e.rightEndstone?this.flagEndstoneValue="both":"1"==e.leftEndstone?this.flagEndstoneValue="left":"1"==e.rightEndstone?this.flagEndstoneValue="right":this.flagEndstoneValue="none"},selectEndstone(){"both"===this.flagEndstoneValue?(this.flagEndstoneElement.leftEndstone="1",this.flagEndstoneElement.rightEndstone="1"):"left"===this.flagEndstoneValue?(this.flagEndstoneElement.leftEndstone="1",this.flagEndstoneElement.rightEndstone="0"):"right"===this.flagEndstoneValue?(this.flagEndstoneElement.leftEndstone="0",this.flagEndstoneElement.rightEndstone="1"):(this.flagEndstoneElement.leftEndstone="0",this.flagEndstoneElement.rightEndstone="0"),this.flagEndstoneShow=!1,this.showPopupBG=!1,this.calculateTotals()},navigateReferenceNameUp:function(e){var t=/[^A-Z0-9.\-,]/gi;this.elements[e].referenceName=this.elements[e].referenceName.replace(t,"")},navigateReferenceName:function(e){"ArrowUp"==event.code?this.elements[e-1]&&this.$refs["referenceName_"+(e-1)][0].focus():"ArrowDown"==event.code?this.elements[e+1]&&this.$refs["referenceName_"+(e+1)][0].focus():"Tab"==event.code&&event.shiftKey&&this.elements[e-1]&&this.$refs["mitrebox_"+(e-1)][0].focus()},navigateAmount:function(e){"ArrowUp"==event.code?this.elements[e-1]&&this.$refs["amount_"+(e-1)][0].focus():"ArrowDown"==event.code&&this.elements[e+1]&&this.$refs["amount_"+(e+1)][0].focus()},navigateInputLength:function(e){"ArrowUp"==event.code?this.elements[e-1]&&this.$refs["inputLength_"+(e-1)][0].focus():"ArrowDown"==event.code?this.elements[e+1]&&this.$refs["inputLength_"+(e+1)][0].focus():"Tab"==event.code&&(event.shiftKey?this.$refs["inputLength_"+e][0].focus():this.elements[e+1]?this.$refs["add_"+e][0].focus():this.addElement())},validateReferenceNameInput:function(e,t){var n=!0;if(""==e.referenceName&&(n=!1,this.$swal("Foutmelding","Kenmerk mag niet leeg zijn.","error"),e.hasReferenceNameError=!0),n&&kj.isNumeric(e.referenceName.substring(0,1))&&(n=!1,this.$swal("Foutmelding","Het eerste karakter van uw kenmerk mag geen getal zijn.","error"),e.hasReferenceNameError=!0),n)for(var i=0;i<this.elements.length;i++){var o=this.elements[i];i!=t&&o.referenceName.toLowerCase()==e.referenceName.toLowerCase()&&(this.$swal("Foutmelding","Kenmerk moet uniek zijn.","error"),e.hasReferenceNameError=!0,n=!1)}return n&&(e.hasReferenceNameError=!1),n},validateAmountInput:function(e){this.validateAmount(e),this.calculateTotals()},validateAmount:function(e){return(!kj.isNumeric(e.amount)||e.amount!=parseInt(e.amount,10)||e.amount<0||e.amount>9999)&&(e.amount=1),!0},validateInputLengthInput:function(e,t){this.validateInputLength(e,t),this.calculateTotals()},validateInputLength:function(e,t,n){var i=!0;return!kj.isNumeric(e.inputLength)||e.inputLength<0?e.inputLength=0:e.inputLength!=parseInt(e.inputLength,10)&&(e.inputLength=Math.floor(e.inputLength)),e.inputLength<100&&(i=!1,n&&(this.$swal("Foutmelding","Kozijnmaat in mm moet minimaal 100 mm zijn.","error"),e.hasInputLengthError=!0)),i&&(e.hasInputLengthError=!1),i},checkForm(){if(this.showSpeling()&&(null===this.quotation.shorter||""===this.quotation.shorter))return this.$swal("Speling","Selecteer eerst de gewenste speling.","error"),this.hasShorterError=!0,!1;if(this.hasShorterError=!1,this.hasWallthicknessError=!1,this.showMuurdikte()&&!this.is_valid["wall_thickness"])return this.$swal("Muurdikte","Voer een geldige muurdikte in.<Br/>Minimale waarde: "+this.getWallthicknessMin()+" mm<Br/>Maximale waarde: "+this.getWallthicknessMax()+" mm","error"),this.hasWallthicknessError=!0,!1;for(var e=!0,t=0,n=0;n<this.elements.length;n++){var i=this.elements[n];0==i.amount&&0==i.inputLength||(t+=i.amount,this.validateInputLength(i,n,!0)||(e=!1),this.validateReferenceNameInput(i,n,!0)||(e=!1))}return 0==t&&(this.$swal("Elementen","U dient minimaal 1 element in te voeren.","error"),e=!1),this.all_valid&&e},setForm(e){this.form=e},mitreBoxClass(){let e="questionbox "+this.stone.type+" brand"+this.quotation.brandId;return"natuursteen"===this.stone.material&&(e+=" "+(this.isEzelsrug()?"ezelsrug":"no-ezelsrug")),e},flagEndstoneBoxClass(){let e="questionbox "+this.stone.type+" ";return"natuursteen"===this.stone.material&&(e+=this.isEzelsrug()?"ezelsrug":"no-ezelsrug"),e},isEzelsrug(){return 18==this.stone.category_id}}};n("fcc9");const jj=Dp()(xj,[["render",wj]]);var Oj=jj;const Ej=Hi("div",{class:"wizard_inleiding"}," Vul in onderstaand lijst uw elementmaten in. U kunt ook tabs enters gebruiken bij de invoer. ",-1),Cj={key:0,class:"alert alert-danger"},Sj={key:1,id:"step2-vensterbank",class:"wizard"},Ij={class:"scroller-x"},Pj={class:"vensterbank-head"},Tj=Hi("div",{class:"vensterbank-kenmerk"},"Kenmerk",-1),Aj=["onUpdate:modelValue","name","id","onKeydown","onKeyup","onChange"],Lj=["onClick"],Dj=Hi("i",{class:"fa fa-remove"},null,-1),zj=[Dj],qj=["onClick"],Nj={class:"vensterbank-form"},Mj={class:"form-row"},Bj=Hi("label",{class:"col3 col-form-label"},"Bovenaanzicht",-1),Vj={class:"col9"},Fj=["name","value"],Rj={key:0},Uj={class:"form-row"},$j=Hi("label",{class:"col3 col-form-label"},"Maten",-1),Wj={class:"col9"},Hj={key:0,class:"windowsillTemplate"},Kj={key:0},Gj=["onUpdate:modelValue","name","onChange","onBlur"],Jj={key:0,class:"btn-options"},Yj=["onClick"],Zj=["onClick"],Xj=["onClick"],Qj={key:1},eO=["onUpdate:modelValue","name","onChange","onBlur"],tO={key:0,class:"btn-options"},nO=["onClick"],iO=["onClick"],oO=["onClick"],sO={key:2},rO=["onUpdate:modelValue","name","onChange","onBlur"],aO={key:0,class:"btn-options"},lO=["onClick"],cO=["onClick"],uO=["onClick"],dO={key:3},hO=["onUpdate:modelValue","name","onChange","onBlur"],pO={key:0,class:"btn-options"},mO=["onClick"],fO=["onClick"],gO=["onClick"],vO={key:4},bO=["onUpdate:modelValue","name","onChange","onBlur"],_O={key:0,class:"btn-options"},yO=["onClick"],wO=["onClick"],kO=["onClick"],xO={key:5},jO=["onUpdate:modelValue","name","onChange","onBlur"],OO={key:0,class:"btn-options"},EO=["onClick"],CO=["onClick"],SO=["onClick"],IO={class:"form-row"},PO=Hi("label",{class:"col3 col-form-label"},"Overzicht",-1),TO={class:"col9 vensterbank-image"},AO=["src"],LO={class:"form-row"},DO=Hi("label",{class:"col3 col-form-label"},"Aantal",-1),zO={class:"col9"},qO=["onUpdate:modelValue","name","onChange"],NO={class:"form-row"},MO=Hi("label",{class:"col3 col-form-label"},"Opmerking",-1),BO={class:"col9"},VO=["onUpdate:modelValue","name"],FO=Hi("br",null,null,-1),RO=Hi("br",null,null,-1),UO=Hi("i",{class:"fa fa-plus"},null,-1),$O=Hi("br",null,null,-1),WO=Hi("br",null,null,-1),HO=Hi("i",{class:"fa fa-chevron-left"},null,-1),KO=["disabled"],GO=Hi("i",{class:"fa fa-chevron-right"},null,-1);function JO(e,t,n,o,s,r){const a=In("WindowsillSelect");return Di(),Bi(Si,null,[Ej,s.errors.length>0?(Di(),Bi("div",Cj,[Zi(" Er zijn foutmeldingen opgetreden, controleer uw invoer: "),Hi("ul",null,[(Di(!0),Bi(Si,null,Ln(s.errors,e=>(Di(),Bi("li",{key:e},Object(i["P"])(e),1))),128))])])):Qi("",!0),s.loading?Qi("",!0):(Di(),Bi("div",Sj,[Hi("form",{method:"post",ref:r.setForm},[Hi("div",Ij,[(Di(!0),Bi(Si,null,Ln(s.elements,(e,n)=>(Di(),Bi("div",{class:"vensterbank-element",key:n},[Hi("div",Pj,[Hi("div",null,[Tj,En(Hi("input",{autocomplete:"off",type:"text","onUpdate:modelValue":t=>e.referenceName=t,onFocus:t[0]||(t[0]=e=>e.target.select()),name:"elements["+n+"][referenceName]",id:"elements["+n+"][referenceName]",ref_for:!0,ref:"referenceName_"+n,onKeydown:e=>r.navigateReferenceName(n),onKeyup:e=>r.navigateReferenceNameUp(n),onChange:t=>r.validateReferenceNameInput(e,n),placeholder:"Kenmerk...",class:Object(i["L"])(["form-input",{inputerror:e.hasReferenceNameError}]),maxlength:"12"},null,42,Aj),[[Ms,e.referenceName]])]),Hi("div",null,[Hi("a",{href:"#",class:"elementtable_fa",onClick:e=>r.removeElement(n),title:"Verwijderen",ref_for:!0,ref:"remove_"+n},zj,8,Lj),Hi("a",{href:"#close",onClick:Gs(t=>r.toggle(e,n),["prevent"])},[Hi("i",{class:Object(i["L"])(["fa",{"fa-chevron-up":e.isShown,"fa-chevron-down":!e.isShown}])},null,2)],8,qj)])]),Hi("div",{class:Object(i["L"])(["vensterbank-content",{"vensterbank-content-hidden":!e.isShown}])},[Hi("div",Nj,[Hi("div",Mj,[Bj,Hi("div",Vj,[(Di(),Vi(a,{ref_for:!0,ref:"colorSelect",key:n,element:e,windowsillsTemplates:s.windowsillsTemplates,onChangeWindowsillSelection:t=>r.changeSelection(e,t)},{default:St(()=>[Zi(" >")]),_:2},1032,["element","windowsillsTemplates","onChangeWindowsillSelection"])),Hi("input",{type:"hidden",name:"elements["+n+"][windowsill_id]",value:e.windowsill.windowsill_id},null,8,Fj)])]),e.windowsillTemplate&&-1!=e.windowsillTemplate.id?(Di(),Bi("div",Rj,[Hi("div",Uj,[$j,Hi("div",Wj,[e.windowsillTemplate?(Di(),Bi("div",Hj,[e.windowsillTemplate.x1?(Di(),Bi("div",Kj,[Hi("label",null,"X1: "+Object(i["P"])(e.windowsillTemplate.x1),1),En(Hi("input",{type:"number",step:"1","onUpdate:modelValue":t=>e.windowsill.x1=t,name:"elements["+n+"][x1]",class:"form-input inputnumber",onChange:t=>r.validateSize(e,"x1"),onBlur:t=>e.windowsill.x1=r.formatValue(e.windowsillTemplate.x1,e.windowsill.x1)},null,40,Gj),[[Ms,e.windowsill.x1]]),Zi(" "+Object(i["P"])(r.getMetric(e.windowsillTemplate.x1))+" ",1),"Hoek"===e.windowsillTemplate.x1?(Di(),Bi("span",Jj,[Hi("button",{type:"button",class:"btn-option",onClick:t=>e.windowsill.x1=45},"45°",8,Yj),Hi("button",{type:"button",class:"btn-option",onClick:t=>e.windowsill.x1=60},"60°",8,Zj),Hi("button",{type:"button",class:"btn-option",onClick:t=>e.windowsill.x1=67.5},"67.5°",8,Xj)])):Qi("",!0)])):Qi("",!0),e.windowsillTemplate.x2?(Di(),Bi("div",Qj,[Hi("label",null,"X2: "+Object(i["P"])(e.windowsillTemplate.x2),1),En(Hi("input",{type:"number",step:"1","onUpdate:modelValue":t=>e.windowsill.x2=t,name:"elements["+n+"][x2]",class:"form-input inputnumber",onChange:t=>r.validateSize(e,"x2"),onBlur:t=>e.windowsill.x2=r.formatValue(e.windowsillTemplate.x2,e.windowsill.x2)},null,40,eO),[[Ms,e.windowsill.x2]]),Zi(" "+Object(i["P"])(r.getMetric(e.windowsillTemplate.x2))+" ",1),"Hoek"===e.windowsillTemplate.x2?(Di(),Bi("span",tO,[Hi("button",{type:"button",class:"btn-option",onClick:t=>e.windowsill.x2=45},"45°",8,nO),Hi("button",{type:"button",class:"btn-option",onClick:t=>e.windowsill.x2=60},"60°",8,iO),Hi("button",{type:"button",class:"btn-option",onClick:t=>e.windowsill.x2=67.5},"67.5°",8,oO)])):Qi("",!0)])):Qi("",!0),e.windowsillTemplate.x3?(Di(),Bi("div",sO,[Hi("label",null,"X3: "+Object(i["P"])(e.windowsillTemplate.x3),1),En(Hi("input",{type:"number",step:"1","onUpdate:modelValue":t=>e.windowsill.x3=t,name:"elements["+n+"][x3]",class:"form-input inputnumber",onChange:t=>r.validateSize(e,"x3"),onBlur:t=>e.windowsill.x3=r.formatValue(e.windowsillTemplate.x3,e.windowsill.x3)},null,40,rO),[[Ms,e.windowsill.x3]]),Zi(" "+Object(i["P"])(r.getMetric(e.windowsillTemplate.x3))+" ",1),"Hoek"===e.windowsillTemplate.x3?(Di(),Bi("span",aO,[Hi("button",{type:"button",class:"btn-option",onClick:t=>e.windowsill.x3=45},"45°",8,lO),Hi("button",{type:"button",class:"btn-option",onClick:t=>e.windowsill.x3=60},"60°",8,cO),Hi("button",{type:"button",class:"btn-option",onClick:t=>e.windowsill.x3=67.5},"67.5°",8,uO)])):Qi("",!0)])):Qi("",!0),e.windowsillTemplate.x4?(Di(),Bi("div",dO,[Hi("label",null,"X4: "+Object(i["P"])(e.windowsillTemplate.x4),1),En(Hi("input",{type:"number",step:"1","onUpdate:modelValue":t=>e.windowsill.x4=t,name:"elements["+n+"][x4]",class:"form-input inputnumber",onChange:t=>r.validateSize(e,"x4"),onBlur:t=>e.windowsill.x4=r.formatValue(e.windowsillTemplate.x4,e.windowsill.x4)},null,40,hO),[[Ms,e.windowsill.x4]]),Zi(" "+Object(i["P"])(r.getMetric(e.windowsillTemplate.x4))+" ",1),"Hoek"===e.windowsillTemplate.x4?(Di(),Bi("span",pO,[Hi("button",{type:"button",class:"btn-option",onClick:t=>e.windowsill.x4=45},"45°",8,mO),Hi("button",{type:"button",class:"btn-option",onClick:t=>e.windowsill.x4=60},"60°",8,fO),Hi("button",{type:"button",class:"btn-option",onClick:t=>e.windowsill.x4=67.5},"67.5°",8,gO)])):Qi("",!0)])):Qi("",!0),e.windowsillTemplate.x5?(Di(),Bi("div",vO,[Hi("label",null,"X5: "+Object(i["P"])(e.windowsillTemplate.x5),1),En(Hi("input",{type:"number",step:"1","onUpdate:modelValue":t=>e.windowsill.x5=t,name:"elements["+n+"][x5]",class:"form-input inputnumber",onChange:t=>r.validateSize(e,"x5"),onBlur:t=>e.windowsill.x5=r.formatValue(e.windowsillTemplate.x5,e.windowsill.x5)},null,40,bO),[[Ms,e.windowsill.x5]]),Zi(" "+Object(i["P"])(r.getMetric(e.windowsillTemplate.x5))+" ",1),"Hoek"===e.windowsillTemplate.x5?(Di(),Bi("span",_O,[Hi("button",{type:"button",class:"btn-option",onClick:t=>e.windowsill.x5=45},"45°",8,yO),Hi("button",{type:"button",class:"btn-option",onClick:t=>e.windowsill.x5=60},"60°",8,wO),Hi("button",{type:"button",class:"btn-option",onClick:t=>e.windowsill.x5=67.5},"67.5°",8,kO)])):Qi("",!0)])):Qi("",!0),e.windowsillTemplate.x6?(Di(),Bi("div",xO,[Hi("label",null,"X6: "+Object(i["P"])(e.windowsillTemplate.x6),1),En(Hi("input",{type:"number",step:"1","onUpdate:modelValue":t=>e.windowsill.x6=t,name:"elements["+n+"][x6]",class:"form-input inputnumber",onChange:t=>r.validateSize(e,"x6"),onBlur:t=>e.windowsill.x6=r.formatValue(e.windowsillTemplate.x6,e.windowsill.x6)},null,40,jO),[[Ms,e.windowsill.x6]]),Zi(" "+Object(i["P"])(r.getMetric(e.windowsillTemplate.x6))+" ",1),"Hoek"===e.windowsillTemplate.x6?(Di(),Bi("span",OO,[Hi("button",{type:"button",class:"btn-option",onClick:t=>e.windowsill.x6=45},"45°",8,EO),Hi("button",{type:"button",class:"btn-option",onClick:t=>e.windowsill.x6=60},"60°",8,CO),Hi("button",{type:"button",class:"btn-option",onClick:t=>e.windowsill.x6=67.5},"67.5°",8,SO)])):Qi("",!0)])):Qi("",!0)])):Qi("",!0)])]),Hi("div",IO,[PO,Hi("div",TO,[null!=s.windowsillsTemplates[e.windowsill.windowsill_id].imagefilename&&""!=s.windowsillsTemplates[e.windowsill.windowsill_id].imagefilename?(Di(),Bi("img",{key:0,src:"//www.raamdorpel.nl/images/thresholds/vensterbanken/"+s.windowsillsTemplates[e.windowsill.windowsill_id].imagefilename},null,8,AO)):Qi("",!0)])]),Hi("div",LO,[DO,Hi("div",zO,[En(Hi("input",{autocomplete:"off",type:"number","onUpdate:modelValue":t=>e.amount=t,onFocus:t[1]||(t[1]=e=>e.target.select()),name:"elements["+n+"][amount]",ref_for:!0,ref:"amount_"+n,onChange:t=>r.validateAmountInput(e),min:"1",placeholder:"Aantal...",class:"form-input inputnumber",maxlength:"4"},null,40,qO),[[Ms,e.amount]])])]),Hi("div",NO,[MO,Hi("div",BO,[En(Hi("textarea",{"onUpdate:modelValue":t=>e.windowsill.remark_cust=t,name:"elements["+n+"][remark_cust]",ref_for:!0,ref:"remark_cust_"+n,placeholder:"Opmerking...",class:"form-input remark_custblaock"},null,8,VO),[[Ms,e.windowsill.remark_cust]])])])])):Qi("",!0)])],2)]))),128))]),FO,RO,Hi("button",{type:"button",name:"add",id:"add",class:"btn",onClick:t[2]||(t[2]=e=>r.addElement())},[UO,Zi(" Element toevoegen")]),$O,WO,Hi("button",{onClick:t[3]||(t[3]=Gs(e=>r.clickPrev(),["prevent"])),type:"button",name:"prev",id:"prev",class:"btn",style:{float:"left"}},[HO,Zi(" Vorige stap")]),Hi("button",{onClick:t[4]||(t[4]=Gs(e=>r.clickNext(),["prevent"])),type:"button",name:"next",id:"next",class:"btn",style:{float:"right"},disabled:!r.isValid()},[Zi("Doorgaan "),GO],8,KO)],512)]))],64)}const YO=["id"],ZO={class:"imagesmallfile"},XO=["src"],QO={class:"windowsill-select-name"},eE=Hi("span",{class:"fa fa-chevron-down"},null,-1),tE={key:0},nE={class:"windowsill-select"},iE=["onClick"],oE={class:"imagesmallfile"},sE=["src"];function rE(e,t,n,o,s,r){return Di(),Bi("div",{ref:r.setWindowsillSelectEl},[Hi("a",{href:"",onClick:t[0]||(t[0]=Gs((...e)=>r.showSelection&&r.showSelection(...e),["prevent"])),class:"windowsill-select-btn",id:"windowsill-"+n.key,ref:"windowsill_"+n.key},[Hi("div",ZO,[null!=n.windowsillsTemplates[n.element.windowsill.windowsill_id].imagesmallfilename&&""!=n.windowsillsTemplates[n.element.windowsill.windowsill_id].imagesmallfilename?(Di(),Bi("img",{key:0,src:"//www.raamdorpel.nl/images/thresholds/vensterbanken/"+n.windowsillsTemplates[n.element.windowsill.windowsill_id].imagesmallfilename},null,8,XO)):Qi("",!0)]),Hi("div",QO,Object(i["P"])(n.windowsillsTemplates[n.element.windowsill.windowsill_id].name),1),eE],8,YO),s.open?(Di(),Bi("div",tE,[Hi("ul",nE,[(Di(!0),Bi(Si,null,Ln(n.windowsillsTemplates,e=>(Di(),Bi("li",{class:Object(i["L"])({"windowsill-select-active":n.element.windowsill.windowsill_id==e.id}),key:e.id},[Hi("a",{href:"#",onClick:t=>r.clickSelection(n.element,e)},[Hi("div",oE,[null!=e.imagesmallfilename&&""!=e.imagesmallfilename?(Di(),Bi("img",{key:0,src:"//www.raamdorpel.nl/images/thresholds/vensterbanken/"+e.imagesmallfilename},null,8,sE)):Qi("",!0)]),Zi(" "+Object(i["P"])(e.name),1)],8,iE)],2))),128))])])):Qi("",!0)],512)}var aE={props:["key","element","windowsillsTemplates","color","is_active_color_id"],data(){return{open:!1,windowsillSelectEl:null}},mounted(){document.addEventListener("mouseup",this.clickOutside)},methods:{showSelection:function(){this.open=!0},hideSelection:function(){this.open=!1},clickSelection:function(e,t){event.preventDefault(),this.$emit("changeWindowsillSelection",t.id),this.hideSelection()},clickOutside:function(e){var t=this.windowsillSelectEl;t===e.target||t.contains(e.target)||this.hideSelection()},setWindowsillSelectEl(e){e&&(this.windowsillSelectEl=e)}}};const lE=Dp()(aE,[["render",rE]]);var cE=lE,uE={name:"ElementsWindowsill",components:{WindowsillSelect:cE},data(){return{loading:!0,errors:[],form:null,quotation:{},quotation_extra:{},elements:{},windowsillsTemplates:{},element_new:{},alphabetArray:["A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"],windowsillSelectShow:[]}},created(){this.fetchData()},mounted(){},methods:{fetchData(){fetch("?action=wizard&step=2&json=1",{headers:{"Content-type":"application/json"}}).then(e=>e.json()).then(e=>{if(!fy.handleError(this,e)){this.quotation=e.data.quotation,this.quotation_extra=e.data.quotation_extra,this.elements=e.data.elements,this.element_new=e.data.element_new,this.windowsillsTemplates=e.data.windowsillsTemplates,this.errors=e.data.errors;for(var t=0;t<this.elements.length;t++)this.changeSelection(this.elements[t],this.elements[t].windowsill.windowsill_id);this.elements[0].isShown=!0,this.loading=!1}}).catch(e=>{this.errors=[],this.errors.push("Server fout opgetreden. Probeer het later nog eens, of neem contact op ("+e+")")})},postData(e){const t=new FormData(this.form);t.append(e,1),fetch("?action=wizard&step=2&json=1",{method:"POST",headers:{Accept:"application/json"},body:t}).then(e=>e.json()).then(e=>{fy.handleError(this,e)||(this.errors=e.data.errors,0===this.errors.length&&e.redirect&&(location.href=e.redirect))}).catch(e=>{this.errors=[],this.errors.push("Server fout opgetreden. Probeer het later nog eens, of neem contact op ("+e+")")})},clickPrev(){this.checkForm()&&this.postData("prev")},clickNext(){this.checkForm()&&this.postData("next")},toggle:function(e){e.isShown=!e.isShown},changeSelection:function(e,t){this.windowsillSelectShow=[],e.windowsill.windowsill_id=t,e.windowsillTemplate=this.windowsillsTemplates[t]},getMetric:function(e){return"Hoek"===e?"°":"mm"},validateAmountInput:function(e){this.validateAmount(e)},validateAmount:function(e){return(!kj.isNumeric(e.amount)||e.amount!=parseInt(e.amount,10)||e.amount<0||e.amount>9999)&&(e.amount=1),!0},validateSize:function(e,t){var n=1,i=1e4;"Hoek"===e.windowsillTemplate[t]&&(n=30,i=150),parseInt(e.windowsill[t])<n?e.windowsill[t]=n:parseInt(e.windowsill[t])>i&&(e.windowsill[t]=i)},validateReferenceNameInput:function(e,t){var n=!0;if(""==e.referenceName&&(n=!1,this.$swal("Foutmelding","Kenmerk mag niet leeg zijn.","error"),e.hasReferenceNameError=!0),n&&kj.isNumeric(e.referenceName.substring(0,1))&&(n=!1,this.$swal("Foutmelding","Het eerste karakter van uw kenmerk mag geen getal zijn.","error"),e.hasReferenceNameError=!0),n)for(var i=0;i<this.elements.length;i++){var o=this.elements[i];i!=t&&o.referenceName.toLowerCase()==e.referenceName.toLowerCase()&&(this.$swal("Foutmelding","Kenmerk moet uniek zijn.","error"),e.hasReferenceNameError=!0,n=!1)}return n&&(e.hasReferenceNameError=!1),n},validateWindowsillSelect:function(e){return null!=e.windowsillTemplate&&-1!=e.windowsillTemplate.id||(this.$swal("Foutmelding","Selecteer uw bovenaanzicht van element "+e.referenceName,"error"),!1)},validateLengths:function(e){return(""==e.windowsillTemplate.x1||null!==e.windowsill.x1&&""!==e.windowsill.x1&&0!==e.windowsill.x1)&&((""==e.windowsillTemplate.x2||null!==e.windowsill.x2&&""!==e.windowsill.x2&&0!==e.windowsill.x2)&&((""==e.windowsillTemplate.x3||null!==e.windowsill.x3&&""!==e.windowsill.x3&&0!==e.windowsill.x3)&&((""==e.windowsillTemplate.x4||null!==e.windowsill.x4&&""!==e.windowsill.x4&&0!==e.windowsill.x4)&&((""==e.windowsillTemplate.x5||null!==e.windowsill.x5&&""!==e.windowsill.x5&&0!==e.windowsill.x5)&&(""==e.windowsillTemplate.x6||null!==e.windowsill.x6&&""!==e.windowsill.x6&&0!==e.windowsill.x6)))))},navigateReferenceNameUp:function(e){var t=/[^A-Z0-9.\-,]/gi;this.elements[e].referenceName=this.elements[e].referenceName.replace(t,"")},navigateReferenceName:function(e){"ArrowUp"===event.code?this.elements[e-1]&&this.$refs["referenceName_"+(e-1)].focus():"ArrowDown"===event.code?this.elements[e+1]&&this.$refs["referenceName_"+(e+1)].focus():"Tab"===event.code&&event.shiftKey&&this.elements[e-1]&&this.$refs["mitrebox_"+(e-1)].focus()},removeElement:function(e){event.preventDefault(),this.elements.splice(e,1)},addElement:function(e){event.preventDefault(),this.element_new.referenceName=this.alphabetArray[this.elements.length],this.elements.length>=26&&(this.element_new.referenceName=this.alphabetArray[Math.floor(this.elements.length/26)-1]+this.alphabetArray[this.elements.length%26]),void 0!==e?this.elements.splice(e+1,0,JSON.parse(JSON.stringify(this.element_new))):this.elements.push(JSON.parse(JSON.stringify(this.element_new))),this.changeSelection(this.elements[this.elements.length-1],this.windowsillsTemplates[-1].id),this.elements[this.elements.length-1].isShown=!0},isValid(){for(var e=0;e<this.elements.length;e++){var t=this.elements[e];if(0===t.amount&&0===t.inputLength)return!1;if(null==t.windowsillTemplate||-1==t.windowsillTemplate.id)return!1}return!0},checkForm:function(){for(var e=!0,t=0,n=0;n<this.elements.length;n++){var i=this.elements[n];0!==i.amount||0!==i.inputLength?(t+=i.amount,this.validateWindowsillSelect(i,n)||(e=!1),this.validateReferenceNameInput(i,n)||(e=!1),this.validateLengths(i,n)||(e=!1)):e=!1}return 0===t?(this.$swal("Foutmelding","U dient minimaal 1 element in te voeren.","error"),e=!1):e||this.$swal("Foutmelding","U dient alle maten in te voeren.","error"),e},setForm(e){this.form=e},formatValue(e,t){const n=parseFloat(t);return"Hoek"===e&&67.5===n?67.5:Math.round(n)}}};n("ef74");const dE=Dp()(uE,[["render",JO]]);var hE=dE;const pE={key:0,id:"step2-popup-bg"},mE=Hi("div",{class:"wizard_inleiding"}," Vul in onderstaand lijst uw elementmaten in. U kunt ook tabs gebruiken bij de invoer. ",-1),fE={key:1,class:"alert alert-danger"},gE={key:2,id:"step2",class:"wizard"},vE={class:"form-row"},bE=Hi("label",{class:"col3 col-form-label"},"Elementen",-1),_E=Hi("div",{class:"col1"},null,-1),yE=Hi("label",{class:"col7 col-form-label"},"Voer uw elementmaten in.",-1),wE=Hi("div",{class:"col1"},null,-1),kE={class:"col12 scroller-x"},xE={id:"elementtable"},jE={class:"header-row"},OE=Hi("td",null," Kenmerk ",-1),EE=Hi("td",{style:{width:"73px"}}," Aantal ",-1),CE=Hi("td",{style:{width:"95px"}}," Lengte ",-1),SE=Hi("td",{style:{width:"103px"}}," Breedte (A) ",-1),IE=Hi("td",{style:{width:"103px"}}," Hoogte (B) ",-1),PE={style:{width:"95px"}},TE=Hi("a",{class:"question-mark-inline fa fa-info-circle"},null,-1),AE={key:0},LE={key:1},DE=Hi("p",null,[Zi("U kunt hier aangeven aan welke kant een verstek komt."),Hi("br"),Zi(' (Hier vind u een handige reken voorbeeld "pdf")')],-1),zE=Hi("ul",null,[Hi("li",null,[Zi("Hartklik gemeten "),Hi("ul",null,[Hi("li",null,[Hi("a",{href:"/downloads/verstekken-invulformulier-uitwendige-hoek.pdf",target:"_blank"},"uitwendige hoek.pdf")]),Hi("li",null,[Hi("a",{href:"/downloads/verstekken-invulformulier-inwendige-hoek.pdf",target:"_blank"},"inwendige hoek.pdf")]),Hi("li",null,[Hi("a",{href:"/downloads/verstekken-invulformulier-uitwendige-hoek-van-675-135-graden.pdf",target:"_blank"},"uitwendige hoek van 67,5 (135) graden.pdf")]),Hi("li",null,[Hi("a",{href:"/downloads/verstekken-invulformulier-uitwendige-hoek-van-60-120-graden.pdf",target:"_blank"},"uitwendige hoek van 60 (120) graden.pdf")])])]),Hi("li",null,[Zi("Tot aan de punt gemeten "),Hi("ul",null,[Hi("li",null,[Hi("a",{href:"/downloads/verstekken/verstekken-informatie-uitwendige-hoek-tot-aan-de-punt-gemeten.pdf",target:"_blank"},"uitwendige hoek tot aan de punt gemeten.pdf")]),Hi("li",null,[Hi("a",{href:"/downloads/verstekken/verstekken-informatie-inwendige-hoek-tot-aan-de-punt-gemeten.pdf",target:"_blank"},"inwendige hoek tot aan de punt gemeten.pdf")])])])],-1),qE=[DE,zE],NE=["onUpdate:modelValue","name"],ME=["onUpdate:modelValue","name"],BE=["onUpdate:modelValue","name","id","onKeydown","onKeyup","onChange"],VE=["onUpdate:modelValue","name","onKeydown","onChange"],FE=["onUpdate:modelValue","name","onKeydown","onChange"],RE=["onUpdate:modelValue","name","onKeydown","onChange"],UE=["onUpdate:modelValue","name","onKeydown","onChange"],$E=["value","onClick"],WE=["onClick"],HE=Hi("i",{class:"fa fa-remove"},null,-1),KE=[HE],GE=["onClick"],JE=Hi("i",{class:"fa fa-plus"},null,-1),YE=[JE],ZE={class:"header-row"},XE=Hi("td",null,"Totalen ",-1),QE={colspan:"5",style:{"text-align":"right"}},eC=Hi("i",{class:"fa fa-plus"},null,-1),tC=Hi("div",null,[Hi("img",{src:"/projects/rde/templates/frontend/images/balkje.png",style:{border:"1px solid #d6d5d5"}})],-1),nC=Hi("br",null,null,-1),iC=Hi("br",null,null,-1),oC=Hi("i",{class:"fa fa-chevron-left"},null,-1),sC=Hi("i",{class:"fa fa-chevron-right"},null,-1),rC={class:"questionbox_title"},aC={id:"mitreElement"},lC={class:"questionbox_content"},cC=Hi("p",null,[Zi(" Kies aan welke uiteinden van het element u een (verstek)hoek wilt."),Hi("br"),Zi("U ziet hier het bovenaanzicht van de balk (zichtzijde).")],-1),uC={class:"verstekhoekImgAfstand"},dC={for:"mitrebox_0"},hC={class:"mitre_example mitre_example_none"},pC=["title"],mC=Hi("div",{class:"mitre_line"},null,-1),fC=[mC],gC=Hi("div",{class:"mitre_label"},"Geen",-1),vC={class:"verstekhoekImgAfstand"},bC={for:"mitrebox_1"},_C=Xi('<div class="mitre_example mitre_example_left"><div title="Verstekhoek aan linker elementeinde"><div class="mitre_line"></div><div class="mitre_schuin_left"></div></div></div><div class="mitre_of">OF</div>',2),yC={class:"mitre_example mitre_example_left_top"},wC=["title"],kC=Hi("div",{class:"mitre_line"},null,-1),xC=Hi("div",{class:"mitre_schuin_left_top"},null,-1),jC=[kC,xC],OC=Hi("div",{class:"mitre_label"},"Links",-1),EC={class:"verstekhoekImgAfstand"},CC={for:"mitrebox_2"},SC={class:"mitre_example mitre_example_right"},IC=["title"],PC=Hi("div",{class:"mitre_line"},null,-1),TC=Hi("div",{class:"mitre_schuin_right"},null,-1),AC=[PC,TC],LC=Hi("div",{class:"mitre_of"},"OF",-1),DC={class:"mitre_example mitre_example_right_top"},zC=["title"],qC=Hi("div",{class:"mitre_line"},null,-1),NC=Hi("div",{class:"mitre_schuin_right_top"},null,-1),MC=[qC,NC],BC=Hi("div",{class:"mitre_label"},"Rechts",-1),VC={class:"verstekhoekImgAfstand"},FC={for:"mitrebox_3"},RC={class:"mitre_example mitre_example_leftright"},UC=["title"],$C=Hi("div",{class:"mitre_line"},null,-1),WC=Hi("div",{class:"mitre_schuin_left"},null,-1),HC=Hi("div",{class:"mitre_schuin_right"},null,-1),KC=[$C,WC,HC],GC=Hi("div",{class:"mitre_of"},"OF",-1),JC={class:"mitre_example mitre_example_leftright_top"},YC=["title"],ZC=Hi("div",{class:"mitre_line"},null,-1),XC=Hi("div",{class:"mitre_schuin_right_top"},null,-1),QC=Hi("div",{class:"mitre_schuin_left_top"},null,-1),eS=[ZC,XC,QC],tS=Hi("div",{class:"mitre_label"},"Beide",-1),nS=Hi("p",null,null,-1),iS={class:"center"};function oS(e,t,n,o,s,r){const a=In("GsdPopper");return Di(),Bi(Si,null,[s.showPopupBG?(Di(),Bi("div",pE)):Qi("",!0),mE,s.errors.length>0?(Di(),Bi("div",fE,[Zi(" Er zijn foutmeldingen opgetreden, controleer uw invoer: "),Hi("ul",null,[(Di(!0),Bi(Si,null,Ln(s.errors,e=>(Di(),Bi("li",{key:e},Object(i["P"])(e),1))),128))])])):Qi("",!0),s.loading?Qi("",!0):(Di(),Bi("div",gE,[Hi("form",{method:"post",ref:r.setForm},[Hi("div",vE,[bE,_E,yE,wE,Hi("div",kE,[Hi("table",xE,[Hi("tr",jE,[OE,EE,CE,SE,IE,Hi("td",PE,[Zi(Object(i["P"])(this.verstekhoektitle)+" ",1),Ki(a,null,{content:St(()=>["spekband"===this.stone.type&&1==s.quotation.brandId?(Di(),Bi("div",AE," Geef hier aan of een hoek wilt links, rechts of aan beide kanten. ")):(Di(),Bi("div",LE,qE))]),default:St(()=>[TE]),_:1})])]),(Di(!0),Bi(Si,null,Ln(s.elements,(e,n)=>(Di(),Bi("tr",{class:"elementrow",key:n},[Hi("td",null,[En(Hi("input",{type:"hidden","onUpdate:modelValue":t=>e.mitre=t,name:"elements["+n+"][mitre]"},null,8,NE),[[Ms,e.mitre]]),En(Hi("input",{type:"hidden","onUpdate:modelValue":t=>e.oldkey=t,name:"elements["+n+"][oldkey]"},null,8,ME),[[Ms,e.oldkey]]),En(Hi("input",{autocomplete:"off",type:"text","onUpdate:modelValue":t=>e.referenceName=t,onFocus:t[0]||(t[0]=e=>e.target.select()),name:"elements["+n+"][referenceName]",id:"elements["+n+"][referenceName]",onKeydown:e=>r.navigateReferenceName(n),onKeyup:e=>r.navigateReferenceNameUp(n),onChange:t=>r.validateReferenceNameInput(e,n),placeholder:"Kenmerk...",class:Object(i["L"])(["form-input",{inputerror:e.hasReferenceNameError}]),maxlength:"12",ref_for:!0,ref:"referenceName_"+n},null,42,BE),[[Ms,e.referenceName]])]),Hi("td",null,[En(Hi("input",{autocomplete:"off",type:"number",min:"0","onUpdate:modelValue":t=>e.amount=t,onFocus:t[1]||(t[1]=e=>e.target.select()),name:"elements["+n+"][amount]",ref_for:!0,ref:"amount_"+n,onKeydown:e=>r.navigateAmount(n),onChange:t=>r.validateAmountInput(e),placeholder:"Aantal...",class:"form-input inputnumber",maxlength:"4"},null,40,VE),[[Ms,e.amount]])]),Hi("td",null,[En(Hi("input",{autocomplete:"off",type:"number",min:"0","onUpdate:modelValue":t=>e.inputLength=t,onFocus:t[2]||(t[2]=e=>e.target.select()),name:"elements["+n+"][inputLength]",ref_for:!0,ref:"inputLength_"+n,onKeydown:e=>r.navigateInputLength(n),onChange:t=>r.validateInputLengthInput(e,n,!0),placeholder:"Lengte...",class:Object(i["L"])(["form-input inputnumber",{inputerror:e.hasInputLengthError}]),maxlength:"5"},null,42,FE),[[Ms,e.inputLength]])]),Hi("td",null,[En(Hi("input",{autocomplete:"off",type:"number",min:"0","onUpdate:modelValue":t=>e.width=t,onFocus:t[3]||(t[3]=e=>e.target.select()),name:"elements["+n+"][width]",ref_for:!0,ref:"inputWidth_"+n,onKeydown:e=>r.navigateInputWidth(n),onChange:t=>r.validateInputWidth(e,n,!0),placeholder:"Breedte...",class:Object(i["L"])(["form-input inputnumber",{inputerror:e.hasInputWidthError}]),maxlength:"5"},null,42,RE),[[Ms,e.width]])]),Hi("td",null,[En(Hi("input",{autocomplete:"off",type:"number",min:"0","onUpdate:modelValue":t=>e.height=t,onFocus:t[4]||(t[4]=e=>e.target.select()),name:"elements["+n+"][height]",ref_for:!0,ref:"inputHeight_"+n,onKeydown:e=>r.navigateInputHeight(n),onChange:t=>r.validateInputHeight(e,n,!0),placeholder:"Hoogte...",class:Object(i["L"])(["form-input inputnumber",{inputerror:e.hasInputHeightError}]),maxlength:"5"},null,42,UE),[[Ms,e.height]])]),Hi("td",null,[Hi("input",{type:"button",value:r.mitreText(e),onClick:t=>r.openMitrebox(e),class:"form-input form-btn",ref_for:!0,ref:"mitrebox_"+n},null,8,$E)]),Hi("td",null,[Hi("a",{href:"#",class:"elementtable_fa",onClick:e=>r.remove(n),title:"Verwijderen",ref_for:!0,ref:"remove_"+n},KE,8,WE),Hi("a",{href:"#",class:"elementtable_fa",onClick:e=>r.addElement(n),title:"Toevoegen",ref_for:!0,ref:"add_"+n},YE,8,GE)])]))),128)),Hi("tr",ZE,[XE,Hi("td",null,[En(Hi("input",{type:"text","onUpdate:modelValue":t[5]||(t[5]=e=>s.amount_total=e),disabled:"",class:"form-input inputnumber inputreadonly_cust"},null,512),[[Ms,s.amount_total]])]),Hi("td",QE,[Hi("button",{type:"button",name:"add",id:"add",class:"btn",onClick:t[6]||(t[6]=e=>r.addElement())},[eC,Zi(" Toevoegen")])])])])])]),tC,nC,iC,Hi("button",{onClick:t[7]||(t[7]=Gs(e=>r.clickPrev(),["prevent"])),type:"button",name:"prev",id:"prev",class:"btn",style:{float:"left"}},[oC,Zi(" Vorige stap")]),Hi("button",{onClick:t[8]||(t[8]=Gs(e=>r.clickNext(),["prevent"])),type:"button",name:"next",id:"next",class:Object(i["L"])(["btn",{disabled:!s.all_valid}]),style:{float:"right"}},[Zi("Doorgaan "),sC],2)],512),s.mitreboxShow?(Di(),Bi("div",{key:0,class:Object(i["L"])(r.mitreBoxClass()),id:"mitrebox"},[Hi("div",rC,[Zi(Object(i["P"])(this.verstekhoektitle)+" - Element ",1),Hi("span",aC,Object(i["P"])(s.mitreboxElement.referenceName),1)]),Hi("div",lC,[cC,Hi("div",uC,[En(Hi("input",{type:"radio","onUpdate:modelValue":t[9]||(t[9]=e=>s.mitreboxElement.mitre=e),value:"none",id:"mitrebox_0",onChange:t[10]||(t[10]=e=>r.selectMitre())},null,544),[[Fs,s.mitreboxElement.mitre]]),Hi("label",dC,[Hi("div",hC,[Hi("div",{id:"rd_mitre_none",title:"Geen "+this.verstekhoektitle},fC,8,pC)]),gC])]),Hi("div",vC,[En(Hi("input",{type:"radio","onUpdate:modelValue":t[11]||(t[11]=e=>s.mitreboxElement.mitre=e),value:"left",id:"mitrebox_1",onChange:t[12]||(t[12]=e=>r.selectMitre())},null,544),[[Fs,s.mitreboxElement.mitre]]),Hi("label",bC,[_C,Hi("div",yC,[Hi("div",{title:this.verstekhoektitle+" aan linker elementeinde"},jC,8,wC)]),OC])]),Hi("div",EC,[En(Hi("input",{type:"radio","onUpdate:modelValue":t[13]||(t[13]=e=>s.mitreboxElement.mitre=e),value:"right",id:"mitrebox_2",onChange:t[14]||(t[14]=e=>r.selectMitre())},null,544),[[Fs,s.mitreboxElement.mitre]]),Hi("label",CC,[Hi("div",SC,[Hi("div",{title:this.verstekhoektitle+" aan linker elementeinde"},AC,8,IC)]),LC,Hi("div",DC,[Hi("div",{title:this.verstekhoektitle+" aan rechter elementeinde"},MC,8,zC)]),BC])]),Hi("div",VC,[En(Hi("input",{type:"radio","onUpdate:modelValue":t[15]||(t[15]=e=>s.mitreboxElement.mitre=e),value:"both",id:"mitrebox_3",onChange:t[16]||(t[16]=e=>r.selectMitre())},null,544),[[Fs,s.mitreboxElement.mitre]]),Hi("label",FC,[Hi("div",RC,[Hi("div",{title:this.verstekhoektitle+"en aan weerszijden"},KC,8,UC)]),GC,Hi("div",JC,[Hi("div",{title:this.verstekhoektitle+"en aan weerszijden"},eS,8,YC)]),tS])]),nS,Hi("p",iS,[Hi("input",{type:"button",name:"btnMitre",value:"Sluiten",onClick:t[17]||(t[17]=e=>r.selectMitre()),class:"btn"})])])],2)):Qi("",!0)]))],64)}var sS={name:"Elements",components:{GsdPopper:g_},data(){return{loading:!0,is_valid:{},all_valid:!1,is_active:{},errors:[],form:null,quotation:{},quotation_extra:{},elements:{},stone:{},stone_size:{},verstekhoektitle:"Vestekhoek",element_new:{},mitreboxShow:!1,mitreboxElement:{},amount_total:0,alphabetArray:["A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"],showPopupBG:!1}},created(){this.fetchData()},mounted(){},watch:{},computed:{possible_shorter:function(){for(var e=[],t=0;t<=10;t+=2){var n=t+" mm";0==t?n+=" - geadviseerd renovatie":10==t&&(n+=" - geadviseerd nieuwbouw"),e.push({value:t,text:n})}return e}},methods:{fetchData(){fetch("?action=wizard&step=2&json=1",{headers:{"Content-type":"application/json"}}).then(e=>e.json()).then(e=>{fy.handleError(this,e)||(this.quotation=e.data.quotation,this.quotation_extra=e.data.quotation_extra,this.stone=e.data.stone,this.stone_size=e.data.stone_size,this.errors=e.data.errors,this.elements=e.data.elements,this.element_new=e.data.element_new,this.mitreboxElement=e.data.element_new,this.calculateTotals(),this.loading=!1)}).catch(e=>{this.errors=[],this.errors.push("Server fout opgetreden. Probeer het later nog eens, of neem contact op ("+e+")")})},postData(e){const t=new FormData(this.form);t.append(e,1),fetch("?action=wizard&step=2&json=1",{method:"POST",headers:{Accept:"application/json"},body:t}).then(e=>e.json()).then(e=>{fy.handleError(this,e)||(this.errors=e.data.errors,"redirect"in e&&(location.href=e.redirect))}).catch(e=>{this.errors=[],this.errors.push("Server fout opgetreden. Probeer het later nog eens, of neem contact op ("+e+")")})},clickPrev(){this.checkForm()&&this.postData("prev")},clickNext(){this.checkForm()&&this.postData("next")},mitreText:function(e){return"both"==e.mitre?"Beide":"left"==e.mitre?"Links":"right"==e.mitre?"Rechts":"Nee"},remove:function(e){this.elements.splice(e,1),this.calculateTotals()},addElement:function(e){event.preventDefault(),this.element_new.referenceName=this.alphabetArray[this.elements.length],this.elements.length>=26&&(this.element_new.referenceName=this.alphabetArray[Math.floor(this.elements.length/26)-1]+this.alphabetArray[this.elements.length%26]),void 0!==e?this.elements.splice(e+1,0,JSON.parse(JSON.stringify(this.element_new))):this.elements.push(JSON.parse(JSON.stringify(this.element_new))),this.$nextTick((function(){this.$refs["referenceName_"+(this.elements.length-1)][0].focus()})),this.calculateTotals()},calculateElementLength:function(e){this.calculateElementLengthDefault(e)},calculateElementLengthDefault:function(e){e.elementLength=0,e.inputLength=parseInt(e.inputLength),e.elementLength=e.inputLength,e.elementLength<0&&(e.elementLength=0)},calculateTotals(){let e=this.elements.length>0;this.amount_total=0;let t=0;for(var n=0;n<this.elements.length;n++){this.calculateElementLength(this.elements[n]);var i=parseInt(this.elements[n].amount);this.amount_total+=i,e&&(0==this.elements[n].amount&&0==this.elements[n].inputLength||(this.validateAmount(this.elements[n])&&this.validateInputLength(this.elements[n],n,!1)?t++:e=!1))}0===t&&(e=!1),this.all_valid=e},openMitrebox:function(e){this.mitreboxShow=!0,this.showPopupBG=!0,this.mitreboxElement=e},selectMitre(){this.mitreboxShow=!1,this.showPopupBG=!1,this.calculateTotals()},navigateReferenceNameUp:function(e){var t=/[^A-Z0-9.\-,]/gi;this.elements[e].referenceName=this.elements[e].referenceName.replace(t,"")},navigateReferenceName:function(e){"ArrowUp"==event.code?this.elements[e-1]&&this.$refs["referenceName_"+(e-1)][0].focus():"ArrowDown"==event.code?this.elements[e+1]&&this.$refs["referenceName_"+(e+1)][0].focus():"Tab"==event.code&&event.shiftKey&&this.elements[e-1]&&this.$refs["mitrebox_"+(e-1)][0].focus()},navigateAmount:function(e){"ArrowUp"==event.code?this.elements[e-1]&&this.$refs["amount_"+(e-1)][0].focus():"ArrowDown"==event.code&&this.elements[e+1]&&this.$refs["amount_"+(e+1)][0].focus()},navigateInputLength:function(e){"ArrowUp"==event.code?this.elements[e-1]&&this.$refs["inputLength_"+(e-1)][0].focus():"ArrowDown"==event.code&&this.elements[e+1]&&this.$refs["inputLength_"+(e+1)][0].focus()},navigateInputWidth:function(e){"ArrowUp"==event.code?this.elements[e-1]&&this.$refs["inputWidth_"+(e-1)][0].focus():"ArrowDown"==event.code&&this.elements[e+1]&&this.$refs["inputWidth_"+(e+1)][0].focus()},navigateInputHeight:function(e){"ArrowUp"==event.code?this.elements[e-1]&&this.$refs["inputHeight_"+(e-1)][0].focus():"ArrowDown"==event.code?this.elements[e+1]&&this.$refs["inputHeight_"+(e+1)][0].focus():"Tab"==event.code&&(event.shiftKey?this.$refs["inputHeight_"+e][0].focus():this.elements[e+1]?this.$refs["add_"+e][0].focus():this.addElement())},validateReferenceNameInput:function(e,t){var n=!0;if(""==e.referenceName&&(n=!1,this.$swal("Foutmelding","Kenmerk mag niet leeg zijn.","error"),e.hasReferenceNameError=!0),n&&kj.isNumeric(e.referenceName.substring(0,1))&&(n=!1,this.$swal("Foutmelding","Het eerste karakter van uw kenmerk mag geen getal zijn.","error"),e.hasReferenceNameError=!0),n)for(var i=0;i<this.elements.length;i++){var o=this.elements[i];i!=t&&o.referenceName.toLowerCase()==e.referenceName.toLowerCase()&&(this.$swal("Foutmelding","Kenmerk moet uniek zijn.","error"),e.hasReferenceNameError=!0,n=!1)}return n&&(e.hasReferenceNameError=!1),n},validateAmountInput:function(e){this.validateAmount(e),this.calculateTotals()},validateAmount:function(e){return(!kj.isNumeric(e.amount)||e.amount!=parseInt(e.amount,10)||e.amount<0||e.amount>9999)&&(e.amount=1),!0},validateInputLengthInput:function(e,t){this.validateInputLength(e,t),this.calculateTotals()},validateInputLength:function(e,t,n){var i=!0;return!kj.isNumeric(e.inputLength)||e.inputLength<0?e.inputLength=0:e.inputLength!=parseInt(e.inputLength,10)&&(e.inputLength=Math.floor(e.inputLength)),e.inputLength<100&&(i=!1,n&&(this.$swal("Foutmelding","Kozijnmaat in mm moet minimaal 100 mm zijn.","error"),e.hasInputLengthError=!0)),i&&(e.hasInputLengthError=!1),i},validateInputWidth:function(e,t,n){var i=!0;return!kj.isNumeric(e.width)||e.width<0?e.width=0:e.width!=parseInt(e.width,10)&&(e.width=Math.floor(e.width)),e.width<20?(i=!1,n&&(this.$swal("Foutmelding","Breedte in mm moet minimaal 20 mm zijn.","error"),e.hasInputWidthError=!0)):e.width>100&&e.height>200?(i=!1,n&&(this.$swal("Foutmelding","Breedte mag maximaal 100 mm zijn, wanneer de hoogte hoger is dan 200mm.","error"),e.hasInputWidthError=!0)):e.width>400&&(i=!1,n&&(this.$swal("Foutmelding","Breedte mag maximaal 400 mm zijn.","error"),e.hasInputWidthError=!0)),i&&(e.hasInputWidthError=!1),i},validateInputHeight:function(e,t,n){var i=!0;return!kj.isNumeric(e.height)||e.height<0?e.height=0:e.height!=parseInt(e.height,10)&&(e.height=Math.floor(e.height)),e.height<20?(i=!1,n&&(this.$swal("Foutmelding","Hoogte moet minimaal 20 mm zijn.","error"),e.hasInputHeightError=!0)):e.height>200&&e.width>200?(i=!1,n&&(this.$swal("Foutmelding","Hoogte mag maximaal 200 mm zijn, wanneer de breedte hoger is dan 200mm.","error"),e.hasInputHeightError=!0)):e.height>400&&(i=!1,n&&(this.$swal("Foutmelding","Hoogte mag maximaal 400 mm zijn.","error"),e.hasInputHeightError=!0)),i&&(e.hasInputHeightError=!1),i},checkForm(){for(var e=!0,t=0,n=0;n<this.elements.length;n++){var i=this.elements[n];0==i.amount&&0==i.inputLength||(t+=i.amount,this.validateInputLength(i,n,!0)||(e=!1),this.validateReferenceNameInput(i,n,!0)||(e=!1))}return 0==t&&(this.$swal("Elementen","U dient minimaal 1 element in te voeren.","error"),e=!1),this.all_valid&&e},setForm(e){this.form=e},mitreBoxClass(){let e="questionbox "+this.stone.type+" brand"+this.quotation.brandId;return"natuursteen"===this.stone.material&&(e+=this.isEzelsrug()?"ezelsrug":"no-ezelsrug"),e},flagEndstoneBoxClass(){let e="questionbox "+this.stone.type+" ";return"natuursteen"===this.stone.material&&(e+=this.isEzelsrug()?"ezelsrug":"no-ezelsrug"),e},isEzelsrug(){return 18==this.stone.category_id}}};n("cae8");const rS=Dp()(sS,[["render",oS]]);var aS=rS;const lS={key:0,id:"step3-popup-bg"},cS=Hi("div",{class:"wizard_inleiding"}," Vul in onderstaand lijst uw elementmaten in. U kunt ook tabs enters gebruiken bij de invoer. ",-1),uS={key:1,class:"alert alert-danger"},dS={key:2,id:"step3",class:"wizard"},hS={class:"wizard_inleiding"},pS={key:0},mS=Hi("ul",null,[Hi("li",null,"Hartklikmaat: hart van de klik (opstaande rand die onder het kozijn gaat / korte zijde) "),Hi("li",null,"Tot aan de punt: lange zijde tot aan de punt gemeten")],-1),fS={key:1},gS={key:0,class:"form-row"},vS=Hi("label",{class:"col3 col-form-label"},"Hoe gemeten",-1),bS={class:"col1"},_S={key:0,class:"input-validation-icon"},yS=Hi("img",{src:"/projects/rde/templates/frontend/images/check_green.svg",alt:"",width:"20"},null,-1),wS=[yS],kS={class:"col7"},xS=["value"],jS={class:"col1"},OS=Hi("a",{class:"question-mark-inline fa fa-info-circle"},null,-1),ES={key:1,class:"scroller-x"},CS={key:0},SS={class:"form-row elementrow-header"},IS={class:"col2 col-form-label",style:{"font-weight":"bold"}},PS={class:"col3 col-form-label",style:{"text-align":"right"}},TS={class:"col2"},AS=["onUpdate:modelValue","name","onChange"],LS=Hi("span",null,"mm",-1),DS=Hi("div",{class:"col3 col-form-label",style:{"text-align":"right"}}," Element maat ",-1),zS={class:"col2"},qS=["value"],NS=Hi("span",null,"mm",-1),MS={class:"form-row elementrow-drawing"},BS={class:"col2"},VS={key:0,class:"select"},FS=["onUpdate:modelValue","name"],RS=["label"],US=["value"],$S={key:1,class:"vlagkozijnlbl"},WS={class:"col1"},HS=Hi("a",{class:"question-mark-inline fa fa-info-circle"},null,-1),KS={class:"col6 col12-xs mitre_div_parent"},GS={class:"mitre_div"},JS=["src"],YS=["src"],ZS=["src"],XS={class:"col1"},QS=Hi("a",{class:"question-mark-inline fa fa-info-circle"},null,-1),eI={class:"col2"},tI={key:0,class:"select"},nI=["onUpdate:modelValue","name"],iI=["label"],oI=["value"],sI={key:1,class:"vlagkozijnlbl"},rI=Hi("br",null,null,-1),aI=Hi("br",null,null,-1),lI=Hi("i",{class:"fa fa-chevron-left"},null,-1),cI=["disabled"],uI=Hi("i",{class:"fa fa-chevron-right"},null,-1),dI={key:0,class:"questionbox",id:"mitrebox"},hI=Hi("div",{class:"questionbox_title"},"Hoe heeft u gemeten?",-1),pI={class:"questionbox_content"},mI=Hi("p",null,"Op welke wijze heeft u de raamdorpel elementen gemeten?",-1),fI=Hi("label",{for:"gemeten_0"},[Hi("img",{src:"/images/mitres/hart-klik-maat.png",alt:"Hart klik maat",class:"exampleimg"})],-1),gI=Hi("label",{for:"gemeten_1"},[Hi("img",{src:"/images/mitres/tot-aan-de-punt.png",alt:"Verstekhoek aan linker elementeinde",class:"exampleimg"})],-1),vI=Hi("p",null,null,-1),bI={class:"center"};function _I(e,t,n,o,s,r){const a=In("GsdPopper");return Di(),Bi(Si,null,[s.showPopupBG?(Di(),Bi("div",lS)):Qi("",!0),cS,s.errors.length>0?(Di(),Bi("div",uS,[Zi(" Er zijn foutmeldingen opgetreden, controleer uw invoer: "),Hi("ul",null,[(Di(!0),Bi(Si,null,Ln(s.errors,e=>(Di(),Bi("li",{key:e},Object(i["P"])(e),1))),128))])])):Qi("",!0),s.loading?Qi("",!0):(Di(),Bi("div",dS,[Hi("div",hS,[s.showGemeten?(Di(),Bi("div",pS,[Zi(" U kunt uw verstek meten op 2 manieren. "),mS,Zi(" Indien u deze gegevens niet bij de hand heeft, kunt u ervoor kiezen de offerte te berekenen op basis van 45 graden hoeken. Onderop deze pagina vindt u nog extra informatie. ")])):Qi("",!0),s.showGemeten?Qi("",!0):(Di(),Bi("div",fS," Deze elementen worden berekend a.d.h.v. de muurlengte. Er word extra lengte toevoegd i.v.m. verstek hoeken. "))]),Hi("form",{method:"post",ref:r.setForm},[s.showGemeten?(Di(),Bi("div",gS,[vS,Hi("div",bS,[s.is_valid.heartClickSize?(Di(),Bi("div",_S,wS)):Qi("",!0)]),Hi("div",kS,[Hi("input",{type:"button",value:r.gemetenText(),name:"gemeten",onClick:t[0]||(t[0]=(...e)=>r.openGemetenbox&&r.openGemetenbox(...e)),class:"form-input form-btn"},null,8,xS),En(Hi("input",{type:"hidden","onUpdate:modelValue":t[1]||(t[1]=e=>s.quotation.heartClickSize=e),name:"heartClickSize"},null,512),[[Ms,s.quotation.heartClickSize]])]),Hi("div",jS,[Ki(a,{content:"Op welke wijze heeft u de raamdorpel elementen gemeten? Hart klik maat of tot aan de punt?"},{default:St(()=>[OS]),_:1})])])):Qi("",!0),s.is_valid["heartClickSize"]?(Di(),Bi("div",ES,[(Di(!0),Bi(Si,null,Ln(s.elements,(e,n)=>(Di(),Bi("div",{class:"form-row elementrow",key:n},["none"!=e.mitre?(Di(),Bi("div",CS,[Hi("div",SS,[Hi("div",IS,Object(i["P"])(e.referenceName),1),Hi("div",PS,Object(i["P"])(r.lengthTitle),1),Hi("div",TS,[En(Hi("input",{type:"text","onUpdate:modelValue":t=>e.inputLength=t,onFocus:t[2]||(t[2]=e=>e.target.select()),name:"elements["+n+"][inputLength]",ref_for:!0,ref:"inputLength_"+n,onChange:t=>r.validateInputLengthInput(e,n,!0),placeholder:"Maat...",class:Object(i["L"])(["inputnumber form-input wizard_inputmm",{inputerror:e.hasInputLengthError}]),maxlength:"5"},null,42,AS),[[Ms,e.inputLength]]),LS]),DS,Hi("div",zS,[Hi("input",{type:"text",value:r.calcElementtotallength(e),name:"elementotallength",class:"inputnumber form-input wizard_inputmm",readonly:"",style:{"font-weight":"bold"}},null,8,qS),NS])]),Hi("div",MS,[Hi("div",BS,[null!=e.leftMitreId?(Di(),Bi("div",VS,[En(Hi("select",{class:"sizeId","onUpdate:modelValue":t=>e.leftMitreId=t,name:"elements["+n+"][leftMitreId]"},[(Di(!0),Bi(Si,null,Ln(s.mitresSelect,(e,t)=>(Di(),Bi("optgroup",{label:t,key:t},[(Di(!0),Bi(Si,null,Ln(e,e=>(Di(),Bi("option",{value:e.mitreId,key:e.mitreId},Object(i["P"])(e.angle),9,US))),128))],8,RS))),128))],8,FS),[[Rs,e.leftMitreId]])])):"single"==e.flagWindow?(Di(),Bi("div",$S,Object(i["P"])(r.vlagkozijnTitle),1)):Qi("",!0)]),Hi("div",WS,[Ki(a,null,{content:St(()=>[Zi(Object(i["P"])(r.spelingLeft(e))+" mm ",1)]),default:St(()=>[HS]),_:2},1024)]),Hi("div",KS,[Hi("div",GS,[Hi("img",{src:r.getLeftImage(e)},null,8,JS),Hi("img",{src:r.getMiddleImage(e),class:"mitre_middle"},null,8,YS),Hi("img",{src:r.getRightImage(e)},null,8,ZS)])]),Hi("div",XS,[Ki(a,null,{content:St(()=>[Zi(Object(i["P"])(r.spelingRight(e))+" mm ",1)]),default:St(()=>[QS]),_:2},1024)]),Hi("div",eI,[null!=e.rightMitreId?(Di(),Bi("div",tI,[En(Hi("select",{class:"sizeId","onUpdate:modelValue":t=>e.rightMitreId=t,name:"elements["+n+"][rightMitreId]"},[(Di(!0),Bi(Si,null,Ln(s.mitresSelect,(e,t)=>(Di(),Bi("optgroup",{label:t,key:t},[(Di(!0),Bi(Si,null,Ln(e,e=>(Di(),Bi("option",{value:e.mitreId,key:e.mitreId},Object(i["P"])(e.angle),9,oI))),128))],8,iI))),128))],8,nI),[[Rs,e.rightMitreId]])])):"single"==e.flagWindow?(Di(),Bi("div",sI,Object(i["P"])(r.vlagkozijnTitle),1)):Qi("",!0)])])])):Qi("",!0)]))),128))])):Qi("",!0),rI,aI,Hi("button",{onClick:t[3]||(t[3]=Gs(e=>r.clickPrev(),["prevent"])),type:"button",name:"prev",id:"prev",class:"btn",style:{float:"left"}},[lI,Zi(" Vorige stap")]),Hi("button",{onClick:t[4]||(t[4]=Gs(e=>r.clickNext(),["prevent"])),type:"button",name:"next",id:"next",class:"btn",style:{float:"right"},disabled:!r.validate()},[Zi("Doorgaan "),uI],8,cI)],512),s.gemetenboxShow?(Di(),Bi("div",dI,[hI,Hi("div",pI,[mI,Hi("div",null,[En(Hi("input",{type:"radio","onUpdate:modelValue":t[5]||(t[5]=e=>s.quotation.heartClickSize=e),value:"0",id:"gemeten_0",onChange:t[6]||(t[6]=e=>r.selectGemeten())},null,544),[[Fs,s.quotation.heartClickSize]]),Zi(),fI]),Hi("div",null,[En(Hi("input",{type:"radio","onUpdate:modelValue":t[7]||(t[7]=e=>s.quotation.heartClickSize=e),value:"1",id:"gemeten_1",onChange:t[8]||(t[8]=e=>r.selectGemeten()),style:{"margin-top":"-37px"}},null,544),[[Fs,s.quotation.heartClickSize]]),Zi(),gI]),vI,Hi("p",bI,[Hi("input",{type:"button",name:"btnGemeten",value:"Sluiten",onClick:t[9]||(t[9]=e=>r.selectGemeten()),class:"btn"})])])])):Qi("",!0)]))],64)}class yI{static isDevelopment(){return window.location.href.indexOf("laklapjap")>=0}}var wI={name:"Mitres",components:{GsdPopper:g_},data(){return{loading:!0,is_valid:{},is_active:{},input_errors:{},errors:[],form:null,quotation:{},quotation_extra:{},stone:{},elements:{},stone_size:{},mitresSelect:{},quotationExtraSpacing:"",showGemeten:!1,gemetenboxShow:!1,mitreimageroot:"",showPopupBG:!1}},created(){this.fetchData()},computed:{lengthTitle(){return"Ingevoerde "+("raamdorpel"===this.stone.type?"kozijnmaat":"muurmaat")},vlagkozijnTitle(){return"raamdorpel"===this.stone.type?"vlagkozijn enkel":"eindsteen"}},methods:{fetchData(){fetch("?action=wizard&step=3&json=1",{headers:{"Content-type":"application/json"}}).then(e=>e.json()).then(e=>{fy.handleError(this,e)||(this.quotation=e.data.quotation,this.quotation_extra=e.data.quotation_extra,this.elements=e.data.elements,this.mitres=e.data.mitres,this.stone_size=e.data.stone_size,this.stone=e.data.stone,this.showGemeten=e.data.showGemeten,this.mitresSelect=e.data.mitresSelect,this.quotationExtraSpacing=e.data.quotationExtraSpacing,this.errors=e.data.errors,this.validate(),this.setImageRoot(),this.loading=!1)}).catch(e=>{this.errors=[],this.errors.push("Server fout opgetreden. Probeer het later nog eens, of neem contact op ("+e+")")})},postData(e){const t=new FormData(this.form);t.append(e,1),fetch("?action=wizard&step=3&json=1",{method:"POST",headers:{Accept:"application/json"},body:t}).then(e=>e.json()).then(e=>{fy.handleError(this,e)||(this.errors=e.data.errors,"redirect"in e&&(location.href=e.redirect))}).catch(e=>{this.errors=[],this.errors.push("Server fout opgetreden. Probeer het later nog eens, of neem contact op ("+e+")")})},clickPrev(){this.postData("prev")},clickNext(){this.postData("next")},gemetenText:function(){return 1==this.quotation.heartClickSize?"Tot aan de punt":0==this.quotation.heartClickSize?"Hart klik maat":"Selecteer een meetmethode"},spelingLeft:function(e){return"right"==e.mitre&&"single"==e.flagWindow?0:"both"==e.mitre||"left"==e.mitre?this.quotationExtraSpacing:this.quotation.shorter/2},spelingRight:function(e){return"left"==e.mitre&&"single"==e.flagWindow?0:"both"==e.mitre||"right"==e.mitre?this.quotationExtraSpacing:this.quotation.shorter/2},openGemetenbox:function(){this.gemetenboxShow=!0,this.showPopupBG=!0},selectGemeten:function(){this.gemetenboxShow=!1,this.setImageRoot(),this.showPopupBG=!1,this.validate()},setImageRoot:function(){this.mitreimageroot="",yI.isDevelopment()&&(this.mitreimageroot+="http://www.raamdorpel.nl.rde.localhost"),this.mitreimageroot+="/images/mitresnew/","muurafdekker"===this.stone.type?this.mitreimageroot+="muurafdekker/":"spekband"===this.stone.type?this.mitreimageroot+="spekband/":0==this.quotation.heartClickSize?this.mitreimageroot+="raamdorpel-hartklik/":this.mitreimageroot+="raamdorpel-totaandepunt/"},validate:function(){var e=!1;if(0==this.quotation.heartClickSize||1==this.quotation.heartClickSize?(e=!0,this.is_valid["heartClickSize"]=!0):this.is_valid["heartClickSize"]=!1,e)for(var t=0;t<this.elements.length;t++)this.validateInputLengthInput(this.elements[t],t,!1)||(e=!1);return e},validateInputLengthInput:function(e,t,n){var i=!0;return!kj.isNumeric(e.inputLength)||e.inputLength<0?e.inputLength=0:e.inputLength!=parseInt(e.inputLength,10)&&(e.inputLength=Math.floor(e.inputLength)),e.inputLength<100&&(i=!1,n&&(this.$swal("Foutmelding","Kozijnmaat in mm moet minimaal 100 mm zijn.","error"),e.hasInputLengthError=!0)),i&&n&&(e.hasInputLengthError=!1),i},getLeftImage:function(e){return null!=e.leftMitreId&&this.mitres[e.leftMitreId]?this.mitreimageroot+"left/"+Math.floor(this.mitres[e.leftMitreId].angle)+".png":this.mitreimageroot+"left/90.png"},getMiddleImage:function(){return this.mitreimageroot+"middle.png"},getRightImage:function(e){return null!=e.rightMitreId&&this.mitres[e.rightMitreId]?this.mitreimageroot+"right/"+Math.floor(this.mitres[e.rightMitreId].angle)+".png":this.mitreimageroot+"right/90.png"},calcElementtotallength:function(e){return"muurafdekker"===this.stone.type?this.calculateElementLengthMuurafdekker(e):this.calculateElementLengthDefault(e)},calculateElementLengthDefault:function(e){var t=0;return t="both"==e.mitre?e.inputLength-2*this.quotationExtraSpacing:"single"==e.flagWindow?e.inputLength-this.quotationExtraSpacing:e.inputLength-this.quotationExtraSpacing-.5*this.quotation.shorter,t},calculateElementLengthMuurafdekker:function(e){var t=0,n=parseInt(this.quotation_extra.wall_thickness),i=Math.round(10*this.stone_size.length-n)/2,o=Math.round(i);if(t="both"==e.mitre?e.inputLength-2*this.quotationExtraSpacing:"single"==e.flagWindow?e.inputLength-this.quotationExtraSpacing:e.inputLength-this.quotationExtraSpacing-.5*this.quotation.shorter,"single"==e.flagWindow&&(t+=i),null!=e.leftMitreId){let n=this.mitres[e.leftMitreId].angle;t+=Math.round(o/Math.tan(kj.deg2rad(n)))}if(null!=e.rightMitreId){let n=this.mitres[e.rightMitreId].angle;t+=Math.round(o/Math.tan(kj.deg2rad(n)))}return t},setForm(e){this.form=e}}};n("f04e");const kI=Dp()(wI,[["render",_I]]);var xI=kI;const jI=Hi("div",{class:"wizard_inleiding"}," Vul in onderstaand lijst uw elementmaten in. U kunt ook tabs enters gebruiken bij de invoer. ",-1),OI={key:0,id:"step5",class:"wizard"},EI={key:0,class:"alert alert-danger"},CI={key:0},SI={class:"form-row"},II=Hi("label",{class:"col3 col-form-label"},"Prijslijst",-1),PI=Hi("div",{class:"col1"},[Hi("div",{class:"input-validation-icon"},[Hi("img",{src:"/projects/rde/templates/frontend/images/check_green.svg",alt:"",width:"20"})])],-1),TI={class:"col7"},AI={class:"select"},LI=["value"],DI={class:"col1"},zI=Hi("a",{class:"question-mark-inline fa fa-info-circle"},null,-1),qI={class:"form-row"},NI=Hi("label",{class:"col3 col-form-label"},"E-mail niet verzenden",-1),MI=Hi("div",{class:"col1"},[Hi("div",{class:"input-validation-icon"},[Hi("img",{src:"/projects/rde/templates/frontend/images/check_green.svg",alt:"",width:"20"})])],-1),BI={class:"col7"},VI={style:{"line-height":"2.5"}},FI={class:"col1"},RI=Hi("a",{class:"question-mark-inline fa fa-info-circle"},null,-1),UI={class:"form-row"},$I=Hi("label",{class:"col3 col-form-label"},"Afleveradres",-1),WI={class:"col1"},HI={key:0,class:"input-validation-icon"},KI=Hi("img",{src:"/projects/rde/templates/frontend/images/check_green.svg",alt:"",width:"20"},null,-1),GI=[KI],JI={class:"col7"},YI={class:"select"},ZI=Hi("option",{value:""},"Selecteer verzendmethode...",-1),XI=["value"],QI={class:"col1"},eP=Hi("a",{class:"question-mark-inline fa fa-info-circle"},null,-1),tP={key:1,id:"wizard_delivery"},nP=Hi("div",{class:""},[Hi("label",{class:"col3 col-form-label",style:{"font-weight":"bold","padding-bottom":"10px"}},"Nieuw afleveradres"),Hi("div",{class:"col9"})],-1),iP={class:"form-row"},oP=Hi("label",{class:"col3 col-form-label"},[Zi("Postcode + nummer "),Hi("span",{class:"form-arterisk"},"*")],-1),sP={class:"col1"},rP={key:0,class:"input-validation-icon"},aP=Hi("img",{src:"/projects/rde/templates/frontend/images/check_green.svg",alt:"",width:"20"},null,-1),lP=[aP],cP={class:"col3"},uP={class:"col2"},dP={class:"col2"},hP={class:"form-row"},pP=Hi("label",{class:"col3 col-form-label"},[Zi("Plaats + straat "),Hi("span",{class:"form-arterisk"},"*")],-1),mP={class:"col1"},fP={key:0,class:"input-validation-icon"},gP=Hi("img",{src:"/projects/rde/templates/frontend/images/check_green.svg",alt:"",width:"20"},null,-1),vP=[gP],bP={class:"col3"},_P=["readonly"],yP={class:"col4"},wP=["readonly"],kP={class:"form-row"},xP=Xi('<label class="col3 col-form-label">Land <span class="form-arterisk">*</span></label><div class="col1"><div class="input-validation-icon"><img src="/projects/rde/templates/frontend/images/check_green.svg" alt="" width="20"></div></div>',2),jP={class:"col7"},OP={class:"select"},EP=Hi("option",{value:"BE"},"België",-1),CP=Hi("option",{value:"DE"},"Duitsland",-1),SP=Hi("option",{value:"NL"},"Nederland",-1),IP=[EP,CP,SP],PP={key:0,style:{color:"#CE000C","padding-top":"5px"}},TP={class:"form-row"},AP=Hi("label",{class:"col3 col-form-label"},"Bijzonderheden levering",-1),LP=Hi("div",{class:"col1"},[Hi("div",{class:"input-validation-icon"})],-1),DP={class:"col8"},zP={style:{"line-height":"1.5",display:"flex",margin:"10px  0 10px 0"}},qP=Hi("div",null,[Zi(" Een vrachtwagen met oplegger kan de locatie goed bereiken"),Hi("br"),Zi(" Geen lengte / breedte / gewicht beperking ")],-1),NP={key:0,id:"delivery_notes_container"},MP=Hi("div",{class:"col4"},null,-1),BP={class:"col7"},VP={key:2,class:"form-row"},FP=Hi("label",{class:"col3 col-form-label"},"SMS",-1),RP=Hi("div",{class:"col1"},[Hi("div",{class:"input-validation-icon"})],-1),UP={class:"col8"},$P={style:{"line-height":"1.5",display:"flex",margin:"10px  0 10px 0"}},WP=Hi("div",null," Verstuur een SMS/Whatsapp als de chauffeur onderweg is naar het afleveradres ",-1),HP={style:{"line-height":"1.5",display:"flex",margin:"10px  0 10px 0"}},KP=Hi("div",null," Verstuur een SMS/Whatsapp als uw bestelling is afgeleverd op het afleveradres ",-1),GP={key:0},JP={style:{display:"block"}},YP=Hi("div",{style:{display:"block",margin:"10px 0"}},[Zi(" Wilt u altijd een SMS ontvangen? Dit kunt u instellen bij "),Hi("a",{href:"/mijn-instellingen"},"Mijn instellingen")],-1),ZP=Hi("br",null,null,-1),XP=Hi("br",null,null,-1),QP=Hi("i",{class:"fa fa-chevron-left"},null,-1),eT=["disabled"],tT=Hi("i",{class:"fa fa-chevron-right"},null,-1);function nT(e,t,n,o,s,r){const a=In("GsdPopper");return Di(),Bi(Si,null,[jI,s.loading?Qi("",!0):(Di(),Bi("div",OI,[s.errors.length>0?(Di(),Bi("div",EI,[Zi(" Er zijn foutmeldingen opgetreden, controleer uw invoer: "),Hi("ul",null,[(Di(!0),Bi(Si,null,Ln(s.errors,e=>(Di(),Bi("li",{key:e},Object(i["P"])(e),1))),128))])])):Qi("",!0),Hi("form",{method:"post",ref:r.setForm},[s.isAdmin?(Di(),Bi("div",CI,[Hi("div",SI,[II,PI,Hi("div",TI,[Hi("div",AI,[En(Hi("select",{"onUpdate:modelValue":t[0]||(t[0]=e=>s.quotation_extra.quotationAltPriceYear=e),name:"quotationAltPriceYear"},[(Di(!0),Bi(Si,null,Ln(s.priceyears,e=>(Di(),Bi("option",{value:e.start,key:e.start},Object(i["P"])(e.year),9,LI))),128))],512),[[Rs,s.quotation_extra.quotationAltPriceYear]])])]),Hi("div",DI,[Ki(a,{content:"Selecteer de te gebruiken prijslijst."},{default:St(()=>[zI]),_:1})])]),Hi("div",qI,[NI,MI,Hi("div",BI,[Hi("label",VI,[En(Hi("input",{type:"checkbox",value:"1",name:"NoEmail",id:"NoEmail",class:"form-checkbox","onUpdate:modelValue":t[1]||(t[1]=e=>s.quotation.NoEmail=e),"true-value":1},null,512),[[Bs,s.quotation.NoEmail]]),Zi(" niet verzenden per e-mail ")])]),Hi("div",FI,[Ki(a,{content:"Wanneer dit vinkje is gezet gaat er geen e-mail naar de klant, wel naar Bart."},{default:St(()=>[RI]),_:1})])])])):Qi("",!0),Hi("div",UI,[$I,Hi("div",WI,[s.is_valid_address?(Di(),Bi("div",HI,GI)):Qi("",!0)]),Hi("div",JI,[Hi("div",YI,[En(Hi("select",{name:"addressDeliveryId","onUpdate:modelValue":t[2]||(t[2]=e=>s.quotation_extra.addressDeliveryId=e),onChange:t[3]||(t[3]=e=>r.changeAddressDeliveryId())},[ZI,(Di(!0),Bi(Si,null,Ln(s.addresses,e=>(Di(),Bi("option",{value:e.addressId,key:e.addressId},Object(i["P"])(r.getAddressFormatted(e)),9,XI))),128))],544),[[Rs,s.quotation_extra.addressDeliveryId]])])]),Hi("div",QI,[Ki(a,{content:"Selecteer uw afleveradres, of maak een nieuwe afleveradres aan. U kunt de raamdorpels ook komen ophalen, hiermee bespaart u verzendkosten."},{default:St(()=>[eP]),_:1})])]),"NEW"==s.quotation_extra.addressDeliveryId?(Di(),Bi("div",tP,[nP,Hi("div",iP,[oP,Hi("div",sP,[s.is_valid_nr?(Di(),Bi("div",rP,lP)):Qi("",!0)]),Hi("div",cP,[En(Hi("input",{type:"text",class:"form-input","onUpdate:modelValue":t[4]||(t[4]=e=>s.quotation.zipcode=e),ref:"zipcode",onChange:t[5]||(t[5]=e=>r.validateZipcodeAndNr(!0)),name:"zipcode",id:"zipcode",placeholder:"Postcode...",required:"",maxlength:"6"},null,544),[[Ms,s.quotation.zipcode]])]),Hi("div",uP,[En(Hi("input",{type:"number",class:"form-input",name:"nr","onUpdate:modelValue":t[6]||(t[6]=e=>s.quotation.nr=e),ref:"nr",onChange:t[7]||(t[7]=e=>r.validateZipcodeAndNr(!0)),placeholder:"Nummer...",required:"required",maxlength:"5"},null,544),[[Ms,s.quotation.nr]])]),Hi("div",dP,[En(Hi("input",{type:"text",class:"form-input",name:"ext","onUpdate:modelValue":t[8]||(t[8]=e=>s.quotation.ext=e),placeholder:"Toevoeging...",maxlength:"10"},null,512),[[Ms,s.quotation.ext]])])]),Hi("div",hP,[pP,Hi("div",mP,[s.is_valid_zipcode&&s.is_valid_domestic?(Di(),Bi("div",fP,vP)):Qi("",!0)]),Hi("div",bP,[En(Hi("input",{type:"text",class:Object(i["L"])(["form-input",{inputerror:s.input_errors.street}]),"onUpdate:modelValue":t[9]||(t[9]=e=>s.quotation.street=e),ref:"street",name:"street",id:"street",placeholder:"Straat...",required:"",onChange:t[10]||(t[10]=e=>r.validateStreet(!0)),readonly:s.street_readonly},null,42,_P),[[Ms,s.quotation.street]])]),Hi("div",yP,[En(Hi("input",{type:"text",class:Object(i["L"])(["form-input",{inputerror:s.input_errors.domestic}]),"onUpdate:modelValue":t[11]||(t[11]=e=>s.quotation.domestic=e),ref:"domestic",onChange:t[12]||(t[12]=e=>r.validateDomestic(!0)),name:"domestic",id:"domestic",placeholder:"Plaats...",required:"",readonly:s.domestic_readonly},null,42,wP),[[Ms,s.quotation.domestic]]),En(Hi("input",{type:"hidden","onUpdate:modelValue":t[13]||(t[13]=e=>s.quotation.street_city_manual=e),name:"street_city_manual",id:"street_city_manual"},null,512),[[Ms,s.quotation.street_city_manual]])])]),Hi("div",kP,[xP,Hi("div",jP,[Hi("div",OP,[En(Hi("select",{name:"country",id:"country","onUpdate:modelValue":t[14]||(t[14]=e=>s.quotation.country=e),ref:"country",required:"",onChange:t[15]||(t[15]=e=>r.validateCountry(!0))},IP,544),[[Rs,s.quotation.country]])]),"NL"!=s.quotation.country?(Di(),Bi("div",PP,"Let op: levering buiten Nederland kan extra bezorgkosten met zich meebrengen.")):Qi("",!0)])])])):Qi("",!0),Hi("div",TP,[AP,LP,Hi("div",DP,[Hi("label",zP,[Hi("div",null,[En(Hi("input",{type:"checkbox",value:"1","onUpdate:modelValue":t[16]||(t[16]=e=>s.delivery_reach=e),name:"delivery_reach",id:"delivery_reach",class:"form-checkbox"},null,512),[[Bs,s.delivery_reach]])]),qP])]),s.delivery_reach?Qi("",!0):(Di(),Bi("div",NP,[MP,Hi("div",BP,[En(Hi("textarea",{class:Object(i["L"])(["form-input",{inputerror:s.input_errors.deliveryNotes}]),name:"deliveryNotes",id:"deliveryNotes",placeholder:"Toelichting bijzonderheden levering","onUpdate:modelValue":t[17]||(t[17]=e=>s.quotation.deliveryNotes=e)},null,2),[[Ms,s.quotation.deliveryNotes]])])]))]),s.showSms?(Di(),Bi("div",VP,[FP,RP,Hi("div",UP,[Hi("label",$P,[Hi("div",null,[En(Hi("input",{type:"checkbox",value:"1","onUpdate:modelValue":t[18]||(t[18]=e=>s.quotation_extra.sms=e),name:"sms",id:"sms",class:"form-checkbox","true-value":1},null,512),[[Bs,s.quotation_extra.sms]])]),WP]),Hi("label",HP,[Hi("div",null,[En(Hi("input",{type:"checkbox",value:"1","onUpdate:modelValue":t[19]||(t[19]=e=>s.quotation_extra.sms_delivered=e),name:"sms_delivered",id:"sms_delivered",class:"form-checkbox","true-value":1},null,512),[[Bs,s.quotation_extra.sms_delivered]])]),KP]),1==s.quotation_extra.sms||1==s.quotation_extra.sms_delivered?(Di(),Bi("div",GP,[Hi("div",JP,[En(Hi("input",{type:"text",class:"form-input",name:"smsnumber","onUpdate:modelValue":t[20]||(t[20]=e=>s.quotation_extra.smsnumber=e),size:"25",maxlength:"15",placeholder:"Mobiel nummer...",style:{width:"250px"}},null,512),[[Ms,s.quotation_extra.smsnumber]])]),YP])):Qi("",!0)])])):Qi("",!0),ZP,XP,Hi("button",{onClick:t[21]||(t[21]=Gs(e=>r.clickPrev(),["prevent"])),type:"button",name:"prev",id:"prev",class:"btn",style:{float:"left"},formnovalidate:""},[QP,Zi(" Vorige stap")]),Hi("button",{onClick:[t[22]||(t[22]=Gs(e=>r.clickNext(),["prevent"])),t[23]||(t[23]=e=>r.validate(!0,e))],type:"button",name:"next",id:"next",class:"btn",style:{float:"right"},disabled:!s.all_valid},[Zi("Doorgaan "),tT],8,eT)],512)]))],64)}var iT={name:"DeliveryAddress",components:{GsdPopper:g_},data(){return{loading:!0,form:null,is_valid:{},all_valid:!1,is_active:{},errors:[],input_errors:{},isAdmin:!1,priceyears:{},quotation:{},quotation_extra:{},addresses:{},delivery_reach:!1,is_valid_address:!1,is_valid_street:!1,is_valid_nr:!1,is_valid_zipcode:!1,is_valid_domestic:!1,street_readonly:!0,domestic_readonly:!0,showSms:!1}},mounted(){},created(){this.fetchData()},watch:{},computed:{},methods:{fetchData(){fetch("?action=wizard&step=5&json=1",{headers:{"Content-type":"application/json"}}).then(e=>e.json()).then(e=>{fy.handleError(this,e)||(this.quotation=e.data.quotation,this.quotation_extra=e.data.quotation_extra,this.elements=e.data.elements,this.addresses=e.data.addresses,this.priceyears=e.data.priceyears,this.isAdmin=e.data.isAdmin,this.delivery_reach=e.data.delivery_reach,this.showSms=e.data.showSms,this.validate(!1),this.loading=!1)}).catch(e=>{this.errors=[],this.errors.push("Server fout opgetreden. Probeer het later nog eens, of neem contact op ("+e+")")})},postData(e){const t=new FormData(this.form);t.append(e,1),fetch("?action=wizard&step=5&json=1",{method:"POST",headers:{Accept:"application/json"},body:t}).then(e=>e.json()).then(e=>{fy.handleError(this,e)||(this.errors=e.data.errors,"redirect"in e&&(location.href=e.redirect),this.domestic_readonly=!1,this.street_readonly=!1,this.quotation.street_city_manual=!0)}).catch(e=>{this.errors=[],this.errors.push("Server fout opgetreden. Probeer het later nog eens, of neem contact op ("+e+")")})},clickPrev(){this.postData("prev")},clickNext(){this.postData("next")},validate:function(e,t){this.validateZipcodeAndNr(!1),"NEW"!==this.quotation_extra.addressDeliveryId||this.is_valid_zipcode&&this.is_valid_nr?(this.delivery_reach||""!==this.quotation.deliveryNotes?this.input_errors.deliveryNotes=!1:e&&(this.input_errors.deliveryNotes=!0),this.all_valid=!0,!this.all_valid&&t&&t.preventDefault()):this.all_valid=!1},validateZipcodeAndNr:function(e){if(this.is_valid_address=!0,"NEW"===this.quotation_extra.addressDeliveryId){if(this.quotation.zipcode&&""!=this.quotation.zipcode?(this.is_valid_zipcode=!0,e&&(this.input_errors.zipcode=!1)):(this.is_valid_zipcode=!1,e&&(this.input_errors.zipcode=!0)),this.quotation.nr&&""!=this.quotation.nr?(this.is_valid_nr=!0,e&&(this.input_errors.nr=!1)):(this.is_valid_nr=!1,e&&(this.input_errors.nr=!0)),this.is_valid_zipcode&&this.is_valid_nr){if("NL"!==this.quotation.country){this.domestic_readonly=!1,this.street_readonly=!1;let t=!0;return this.validateStreet(e)||(t=!1),this.validateDomestic(e)||(t=!1),void(this.all_valid=t)}this.quotation.street_city_manual?(this.domestic_readonly=!1,this.street_readonly=!1):(this.domestic_readonly=!0,this.street_readonly=!0,fetch("?action=postcodeapi&zipcode="+this.quotation.zipcode+"&nr="+this.quotation.nr).then(e=>e.json()).then(e=>{e.error?this.errors.push(e.error):(e.success?"open"===e.success?(this.domestic_readonly=!1,this.street_readonly=!1,this.quotation.street_city_manual=!0):(this.quotation.street=e.data.street,this.quotation.domestic=e.data.city):this.quotation.street_city_manual||(this.quotation.street="",this.quotation.domestic=""),this.all_valid=!0)}).catch(e=>{this.errors=[],this.errors.push("Server fout opgetreden. Probeer het later nog eens, of neem contact op ("+e+")")}))}}else""==this.quotation_extra.addressDeliveryId?this.is_valid_address=!1:this.is_valid_address=!0},validateStreet:function(e){var t=!0;return this.quotation.street&&""!==this.quotation.street?e&&(this.input_errors.street=!1):(t=!1,e&&(this.input_errors.street=!0)),this.is_valid_street=t,e&&this.validate(!1),t},validateDomestic:function(e){var t=!0;return this.quotation.domestic&&""!==this.quotation.domestic?e&&(this.input_errors.domestic=!1):(t=!1,e&&(this.input_errors.domestic=!0)),this.is_valid_domestic=t,e&&this.validate(!1),t},validateCountry:function(e){return e&&this.validate(!1),!0},getAddressFormatted:function(e){var t="";return 20357==e.addressId?t="Ophalen - Raambrug 9, 5531 AG Bladel, Nederland":"NEW"===e.addressId?t="Nieuw afleveradres":(t="Afleveren - ","visit"===e.type&&(t+="Bezoekadres - "),""!=e.title&&null!=e.title&&(t+=e.title+" - "),t+=e.street+" "+e.nr,""!=e.extension&&null!=e.extension&&(t+=" "+e.extension),t+=", "+e.zipcode+" "+e.domestic+", "+e.country),t},setForm(e){this.form=e},changeAddressDeliveryId(){"NEW"===this.quotation_extra.addressDeliveryId?(this.quotation.street="",this.quotation.nr="",this.quotation.zipcode="",this.quotation.domestic="",this.quotation.ext=""):this.validate(!1)}}};const oT=Dp()(iT,[["render",nT]]);var sT=oT;const rT={step0:cw,step1:Py,step2:Oj,step2Windowsill:hE,step2Beam:aS,step3:xI,step5:sT},aT=document.getElementById("app").dataset.step,lT={data:()=>({step:aT}),computed:{CurrentComponent(){return yI.isDevelopment()&&console.log(rT[this.step].name),rT[this.step]||qp}},render(){return Io(this.CurrentComponent)}},cT=nr(lT);cT.use(Cp.a),Op({app:cT,dsn:"https://<EMAIL>/4508438545498192",enabled:!0,integrations:[]}),cT.mount("#app")},"56ef":function(e,t,n){var i=n("d066"),o=n("e330"),s=n("241c"),r=n("7418"),a=n("825a"),l=o([].concat);e.exports=i("Reflect","ownKeys")||function(e){var t=s.f(a(e)),n=r.f;return n?l(t,n(e)):t}},5926:function(e,t,n){var i=n("b42e");e.exports=function(e){var t=+e;return t!==t||0===t?0:i(t)}},"59ed":function(e,t,n){var i=n("1626"),o=n("0d51"),s=TypeError;e.exports=function(e){if(i(e))return e;throw s(o(e)+" is not a function")}},"5c6c":function(e,t){e.exports=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}}},"5e77":function(e,t,n){var i=n("83ab"),o=n("1a2d"),s=Function.prototype,r=i&&Object.getOwnPropertyDescriptor,a=o(s,"name"),l=a&&"something"===function(){}.name,c=a&&(!i||i&&r(s,"name").configurable);e.exports={EXISTS:a,PROPER:l,CONFIGURABLE:c}},"613d":function(e,t,n){"use strict";n("1517")},"61d1":function(e,t,n){},6374:function(e,t,n){var i=n("da84"),o=Object.defineProperty;e.exports=function(e,t){try{o(i,e,{value:t,configurable:!0,writable:!0})}catch(n){i[e]=t}return t}},"69f3":function(e,t,n){var i,o,s,r=n("cdce"),a=n("da84"),l=n("861d"),c=n("9112"),u=n("1a2d"),d=n("c6cd"),h=n("f772"),p=n("d012"),m="Object already initialized",f=a.TypeError,g=a.WeakMap,v=function(e){return s(e)?o(e):i(e,{})},b=function(e){return function(t){var n;if(!l(t)||(n=o(t)).type!==e)throw f("Incompatible receiver, "+e+" required");return n}};if(r||d.state){var _=d.state||(d.state=new g);_.get=_.get,_.has=_.has,_.set=_.set,i=function(e,t){if(_.has(e))throw f(m);return t.facade=e,_.set(e,t),t},o=function(e){return _.get(e)||{}},s=function(e){return _.has(e)}}else{var y=h("state");p[y]=!0,i=function(e,t){if(u(e,y))throw f(m);return t.facade=e,c(e,y,t),t},o=function(e){return u(e,y)?e[y]:{}},s=function(e){return u(e,y)}}e.exports={set:i,get:o,has:s,enforce:v,getterFor:b}},"6b0d":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=(e,t)=>{const n=e.__vccOpts||e;for(const[i,o]of t)n[i]=o;return n}},"710b":function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return l})),n.d(t,"c",(function(){return m})),n.d(t,"d",(function(){return o})),n.d(t,"e",(function(){return r})),n.d(t,"f",(function(){return p})),n.d(t,"g",(function(){return b})),n.d(t,"h",(function(){return u})),n.d(t,"i",(function(){return h})),n.d(t,"j",(function(){return d})),n.d(t,"k",(function(){return f})),n.d(t,"l",(function(){return c})),n.d(t,"m",(function(){return v})),n.d(t,"n",(function(){return g})),n.d(t,"o",(function(){return _}));const i=Object.prototype.toString;function o(e){switch(i.call(e)){case"[object Error]":case"[object Exception]":case"[object DOMException]":case"[object WebAssembly.Exception]":return!0;default:return b(e,Error)}}function s(e,t){return i.call(e)===`[object ${t}]`}function r(e){return s(e,"ErrorEvent")}function a(e){return s(e,"DOMError")}function l(e){return s(e,"DOMException")}function c(e){return s(e,"String")}function u(e){return"object"===typeof e&&null!==e&&"__sentry_template_string__"in e&&"__sentry_template_values__"in e}function d(e){return null===e||u(e)||"object"!==typeof e&&"function"!==typeof e}function h(e){return s(e,"Object")}function p(e){return"undefined"!==typeof Event&&b(e,Event)}function m(e){return"undefined"!==typeof Element&&b(e,Element)}function f(e){return s(e,"RegExp")}function g(e){return Boolean(e&&e.then&&"function"===typeof e.then)}function v(e){return h(e)&&"nativeEvent"in e&&"preventDefault"in e&&"stopPropagation"in e}function b(e,t){try{return e instanceof t}catch(n){return!1}}function _(e){return!("object"!==typeof e||null===e||!e.__isVue&&!e._isVue)}},7234:function(e,t){e.exports=function(e){return null===e||void 0===e}},"73a0":function(e,t,n){"use strict";n.d(t,"a",(function(){return c})),n.d(t,"b",(function(){return h})),n.d(t,"c",(function(){return g})),n.d(t,"d",(function(){return f})),n.d(t,"e",(function(){return l})),n.d(t,"f",(function(){return d})),n.d(t,"g",(function(){return u}));var i=n("1edf"),o=n("d01f"),s=n("710b"),r=n("f37b"),a=n("7c08");function l(e,t,n){if(!(t in e))return;const i=e[t],s=n(i);"function"===typeof s&&u(s,i);try{e[t]=s}catch(a){o["a"]&&r["c"].log(`Failed to replace method "${t}" in object`,e)}}function c(e,t,n){try{Object.defineProperty(e,t,{value:n,writable:!0,configurable:!0})}catch(i){o["a"]&&r["c"].log(`Failed to add non-enumerable property "${t}" to object`,e)}}function u(e,t){try{const n=t.prototype||{};e.prototype=t.prototype=n,c(e,"__sentry_original__",t)}catch(n){}}function d(e){return e.__sentry_original__}function h(e){if(Object(s["d"])(e))return{message:e.message,name:e.name,stack:e.stack,...m(e)};if(Object(s["f"])(e)){const t={type:e.type,target:p(e.target),currentTarget:p(e.currentTarget),...m(e)};return"undefined"!==typeof CustomEvent&&Object(s["g"])(e,CustomEvent)&&(t.detail=e.detail),t}return e}function p(e){try{return Object(s["c"])(e)?Object(i["c"])(e):Object.prototype.toString.call(e)}catch(t){return"<unknown>"}}function m(e){if("object"===typeof e&&null!==e){const t={};for(const n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t}return{}}function f(e,t=40){const n=Object.keys(h(e));n.sort();const i=n[0];if(!i)return"[object has no keys]";if(i.length>=t)return Object(a["d"])(i,t);for(let o=n.length;o>0;o--){const e=n.slice(0,o).join(", ");if(!(e.length>t))return o===n.length?e:Object(a["d"])(e,t)}return""}function g(e){const t=new Map;return v(e,t)}function v(e,t){if(b(e)){const n=t.get(e);if(void 0!==n)return n;const i={};t.set(e,i);for(const o of Object.getOwnPropertyNames(e))"undefined"!==typeof e[o]&&(i[o]=v(e[o],t));return i}if(Array.isArray(e)){const n=t.get(e);if(void 0!==n)return n;const i=[];return t.set(e,i),e.forEach(e=>{i.push(v(e,t))}),i}return e}function b(e){if(!Object(s["i"])(e))return!1;try{const t=Object.getPrototypeOf(e).constructor.name;return!t||"Object"===t}catch(t){return!0}}},7418:function(e,t){t.f=Object.getOwnPropertySymbols},"75f9":function(e,t,n){"use strict";n("61d1")},"768d":function(e,t,n){},"76a5":function(e,t,n){"use strict";n("d882")},7839:function(e,t){e.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},"7b0b":function(e,t,n){var i=n("1d80"),o=Object;e.exports=function(e){return o(i(e))}},"7c08":function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return s})),n.d(t,"c",(function(){return l})),n.d(t,"d",(function(){return o}));var i=n("710b");function o(e,t=0){return"string"!==typeof e||0===t||e.length<=t?e:e.slice(0,t)+"..."}function s(e,t){let n=e;const i=n.length;if(i<=150)return n;t>i&&(t=i);let o=Math.max(t-60,0);o<5&&(o=0);let s=Math.min(o+140,i);return s>i-5&&(s=i),s===i&&(o=Math.max(s-140,0)),n=n.slice(o,s),o>0&&(n="'{snip} "+n),s<i&&(n+=" {snip}"),n}function r(e,t){if(!Array.isArray(e))return"";const n=[];for(let s=0;s<e.length;s++){const t=e[s];try{Object(i["o"])(t)?n.push("[VueViewModel]"):n.push(String(t))}catch(o){n.push("[value cannot be serialized]")}}return n.join(t)}function a(e,t,n=!1){return!!Object(i["l"])(e)&&(Object(i["k"])(t)?t.test(e):!!Object(i["l"])(t)&&(n?e===t:e.includes(t)))}function l(e,t=[],n=!1){return t.some(t=>a(e,t,n))}},"825a":function(e,t,n){var i=n("861d"),o=String,s=TypeError;e.exports=function(e){if(i(e))return e;throw s(o(e)+" is not an object")}},"83ab":function(e,t,n){var i=n("d039");e.exports=!i((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]}))},"861d":function(e,t,n){var i=n("1626"),o=n("8ea1"),s=o.all;e.exports=o.IS_HTMLDDA?function(e){return"object"==typeof e?null!==e:i(e)||e===s}:function(e){return"object"==typeof e?null!==e:i(e)}},8925:function(e,t,n){var i=n("e330"),o=n("1626"),s=n("c6cd"),r=i(Function.toString);o(s.inspectSource)||(s.inspectSource=function(e){return r(e)}),e.exports=s.inspectSource},"8ea1":function(e,t){var n="object"==typeof document&&document.all,i="undefined"==typeof n&&void 0!==n;e.exports={all:n,IS_HTMLDDA:i}},"90e3":function(e,t,n){var i=n("e330"),o=0,s=Math.random(),r=i(1..toString);e.exports=function(e){return"Symbol("+(void 0===e?"":e)+")_"+r(++o+s,36)}},9112:function(e,t,n){var i=n("83ab"),o=n("9bf2"),s=n("5c6c");e.exports=i?function(e,t,n){return o.f(e,t,s(1,n))}:function(e,t,n){return e[t]=n,e}},"921d":function(e,t,n){},9476:function(e,t,n){"use strict";n("768d")},"94ca":function(e,t,n){var i=n("d039"),o=n("1626"),s=/#|\.prototype\./,r=function(e,t){var n=l[a(e)];return n==u||n!=c&&(o(t)?i(t):!!t)},a=r.normalize=function(e){return String(e).replace(s,".").toLowerCase()},l=r.data={},c=r.NATIVE="N",u=r.POLYFILL="P";e.exports=r},"9bf2":function(e,t,n){var i=n("83ab"),o=n("0cfb"),s=n("aed9"),r=n("825a"),a=n("a04b"),l=TypeError,c=Object.defineProperty,u=Object.getOwnPropertyDescriptor,d="enumerable",h="configurable",p="writable";t.f=i?s?function(e,t,n){if(r(e),t=a(t),r(n),"function"===typeof e&&"prototype"===t&&"value"in n&&p in n&&!n[p]){var i=u(e,t);i&&i[p]&&(e[t]=n.value,n={configurable:h in n?n[h]:i[h],enumerable:d in n?n[d]:i[d],writable:!1})}return c(e,t,n)}:c:function(e,t,n){if(r(e),t=a(t),r(n),o)try{return c(e,t,n)}catch(i){}if("get"in n||"set"in n)throw l("Accessors not supported");return"value"in n&&(e[t]=n.value),e}},"9ff4":function(e,t,n){"use strict";(function(e){function i(e,t){const n=Object.create(null),i=e.split(",");for(let o=0;o<i.length;o++)n[i[o]]=!0;return t?e=>!!n[e.toLowerCase()]:e=>!!n[e]}n.d(t,"a",(function(){return E})),n.d(t,"b",(function(){return O})),n.d(t,"c",(function(){return S})),n.d(t,"d",(function(){return C})),n.d(t,"e",(function(){return te})),n.d(t,"f",(function(){return oe})),n.d(t,"g",(function(){return le})),n.d(t,"h",(function(){return A})),n.d(t,"i",(function(){return he})),n.d(t,"j",(function(){return re})),n.d(t,"k",(function(){return z})),n.d(t,"l",(function(){return ie})),n.d(t,"m",(function(){return _})),n.d(t,"n",(function(){return ae})),n.d(t,"o",(function(){return q})),n.d(t,"p",(function(){return X})),n.d(t,"q",(function(){return F})),n.d(t,"r",(function(){return s})),n.d(t,"s",(function(){return f})),n.d(t,"t",(function(){return Y})),n.d(t,"u",(function(){return N})),n.d(t,"v",(function(){return T})),n.d(t,"w",(function(){return $})),n.d(t,"x",(function(){return P})),n.d(t,"y",(function(){return J})),n.d(t,"z",(function(){return W})),n.d(t,"A",(function(){return V})),n.d(t,"B",(function(){return Z})),n.d(t,"C",(function(){return g})),n.d(t,"D",(function(){return M})),n.d(t,"E",(function(){return b})),n.d(t,"F",(function(){return R})),n.d(t,"G",(function(){return U})),n.d(t,"H",(function(){return w})),n.d(t,"I",(function(){return k})),n.d(t,"J",(function(){return ce})),n.d(t,"K",(function(){return i})),n.d(t,"L",(function(){return d})),n.d(t,"M",(function(){return h})),n.d(t,"N",(function(){return r})),n.d(t,"O",(function(){return L})),n.d(t,"P",(function(){return x})),n.d(t,"Q",(function(){return se})),n.d(t,"R",(function(){return ue})),n.d(t,"S",(function(){return G}));const o="Infinity,undefined,NaN,isFinite,isNaN,parseFloat,parseInt,decodeURI,decodeURIComponent,encodeURI,encodeURIComponent,Math,Number,Date,Array,Object,Boolean,String,RegExp,Map,Set,JSON,Intl,BigInt",s=i(o);function r(e){if(q(e)){const t={};for(let n=0;n<e.length;n++){const i=e[n],o=R(i)?u(i):r(i);if(o)for(const e in o)t[e]=o[e]}return t}return R(e)||$(e)?e:void 0}const a=/;(?![^(]*\))/g,l=/:([^]+)/,c=/\/\*.*?\*\//gs;function u(e){const t={};return e.replace(c,"").split(a).forEach(e=>{if(e){const n=e.split(l);n.length>1&&(t[n[0].trim()]=n[1].trim())}}),t}function d(e){let t="";if(R(e))t=e;else if(q(e))for(let n=0;n<e.length;n++){const i=d(e[n]);i&&(t+=i+" ")}else if($(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}function h(e){if(!e)return null;let{class:t,style:n}=e;return t&&!R(t)&&(e.class=d(t)),n&&(e.style=r(n)),e}const p="html,body,base,head,link,meta,style,title,address,article,aside,footer,header,hgroup,h1,h2,h3,h4,h5,h6,nav,section,div,dd,dl,dt,figcaption,figure,picture,hr,img,li,main,ol,p,pre,ul,a,b,abbr,bdi,bdo,br,cite,code,data,dfn,em,i,kbd,mark,q,rp,rt,ruby,s,samp,small,span,strong,sub,sup,time,u,var,wbr,area,audio,map,track,video,embed,object,param,source,canvas,script,noscript,del,ins,caption,col,colgroup,table,thead,tbody,td,th,tr,button,datalist,fieldset,form,input,label,legend,meter,optgroup,option,output,progress,select,textarea,details,dialog,menu,summary,template,blockquote,iframe,tfoot",m="svg,animate,animateMotion,animateTransform,circle,clipPath,color-profile,defs,desc,discard,ellipse,feBlend,feColorMatrix,feComponentTransfer,feComposite,feConvolveMatrix,feDiffuseLighting,feDisplacementMap,feDistantLight,feDropShadow,feFlood,feFuncA,feFuncB,feFuncG,feFuncR,feGaussianBlur,feImage,feMerge,feMergeNode,feMorphology,feOffset,fePointLight,feSpecularLighting,feSpotLight,feTile,feTurbulence,filter,foreignObject,g,hatch,hatchpath,image,line,linearGradient,marker,mask,mesh,meshgradient,meshpatch,meshrow,metadata,mpath,path,pattern,polygon,polyline,radialGradient,rect,set,solidcolor,stop,switch,symbol,text,textPath,title,tspan,unknown,use,view",f=i(p),g=i(m),v="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",b=i(v);function _(e){return!!e||""===e}function y(e,t){if(e.length!==t.length)return!1;let n=!0;for(let i=0;n&&i<e.length;i++)n=w(e[i],t[i]);return n}function w(e,t){if(e===t)return!0;let n=B(e),i=B(t);if(n||i)return!(!n||!i)&&e.getTime()===t.getTime();if(n=U(e),i=U(t),n||i)return e===t;if(n=q(e),i=q(t),n||i)return!(!n||!i)&&y(e,t);if(n=$(e),i=$(t),n||i){if(!n||!i)return!1;const o=Object.keys(e).length,s=Object.keys(t).length;if(o!==s)return!1;for(const n in e){const i=e.hasOwnProperty(n),o=t.hasOwnProperty(n);if(i&&!o||!i&&o||!w(e[n],t[n]))return!1}}return String(e)===String(t)}function k(e,t){return e.findIndex(e=>w(e,t))}const x=e=>R(e)?e:null==e?"":q(e)||$(e)&&(e.toString===H||!F(e.toString))?JSON.stringify(e,j,2):String(e),j=(e,t)=>t&&t.__v_isRef?j(e,t.value):N(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((e,[t,n])=>(e[t+" =>"]=n,e),{})}:M(t)?{[`Set(${t.size})`]:[...t.values()]}:!$(t)||q(t)||J(t)?t:String(t),O={},E=[],C=()=>{},S=()=>!1,I=/^on[^a-z]/,P=e=>I.test(e),T=e=>e.startsWith("onUpdate:"),A=Object.assign,L=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},D=Object.prototype.hasOwnProperty,z=(e,t)=>D.call(e,t),q=Array.isArray,N=e=>"[object Map]"===K(e),M=e=>"[object Set]"===K(e),B=e=>"[object Date]"===K(e),V=e=>"[object RegExp]"===K(e),F=e=>"function"===typeof e,R=e=>"string"===typeof e,U=e=>"symbol"===typeof e,$=e=>null!==e&&"object"===typeof e,W=e=>$(e)&&F(e.then)&&F(e.catch),H=Object.prototype.toString,K=e=>H.call(e),G=e=>K(e).slice(8,-1),J=e=>"[object Object]"===K(e),Y=e=>R(e)&&"NaN"!==e&&"-"!==e[0]&&""+parseInt(e,10)===e,Z=i(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),X=i("bind,cloak,else-if,else,for,html,if,model,on,once,pre,show,slot,text,memo"),Q=e=>{const t=Object.create(null);return n=>{const i=t[n];return i||(t[n]=e(n))}},ee=/-(\w)/g,te=Q(e=>e.replace(ee,(e,t)=>t?t.toUpperCase():"")),ne=/\B([A-Z])/g,ie=Q(e=>e.replace(ne,"-$1").toLowerCase()),oe=Q(e=>e.charAt(0).toUpperCase()+e.slice(1)),se=Q(e=>e?"on"+oe(e):""),re=(e,t)=>!Object.is(e,t),ae=(e,t)=>{for(let n=0;n<e.length;n++)e[n](t)},le=(e,t,n)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,value:n})},ce=e=>{const t=parseFloat(e);return isNaN(t)?e:t},ue=e=>{const t=R(e)?Number(e):NaN;return isNaN(t)?e:t};let de;const he=()=>de||(de="undefined"!==typeof globalThis?globalThis:"undefined"!==typeof self?self:"undefined"!==typeof window?window:"undefined"!==typeof e?e:{})}).call(this,n("c8ba"))},a04b:function(e,t,n){var i=n("c04e"),o=n("d9b5");e.exports=function(e){var t=i(e,"string");return o(t)?t:t+""}},aed9:function(e,t,n){var i=n("83ab"),o=n("d039");e.exports=i&&o((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype}))},b42e:function(e,t){var n=Math.ceil,i=Math.floor;e.exports=Math.trunc||function(e){var t=+e;return(t>0?i:n)(t)}},b622:function(e,t,n){var i=n("da84"),o=n("5692"),s=n("1a2d"),r=n("90e3"),a=n("04f8"),l=n("fdbf"),c=i.Symbol,u=o("wks"),d=l?c["for"]||c:c&&c.withoutSetter||r;e.exports=function(e){return s(u,e)||(u[e]=a&&s(c,e)?c[e]:d("Symbol."+e)),u[e]}},c04e:function(e,t,n){var i=n("c65b"),o=n("861d"),s=n("d9b5"),r=n("dc4a"),a=n("485a"),l=n("b622"),c=TypeError,u=l("toPrimitive");e.exports=function(e,t){if(!o(e)||s(e))return e;var n,l=r(e,u);if(l){if(void 0===t&&(t="default"),n=i(l,e,t),!o(n)||s(n))return n;throw c("Can't convert object to primitive value")}return void 0===t&&(t="number"),a(e,t)}},c430:function(e,t){e.exports=!1},c65b:function(e,t,n){var i=n("40d5"),o=Function.prototype.call;e.exports=i?o.bind(o):function(){return o.apply(o,arguments)}},c6b6:function(e,t,n){var i=n("e330"),o=i({}.toString),s=i("".slice);e.exports=function(e){return s(o(e),8,-1)}},c6cd:function(e,t,n){var i=n("da84"),o=n("6374"),s="__core-js_shared__",r=i[s]||o(s,{});e.exports=r},c8ba:function(e,t){var n;n=function(){return this}();try{n=n||new Function("return this")()}catch(i){"object"===typeof window&&(n=window)}e.exports=n},ca84:function(e,t,n){var i=n("e330"),o=n("1a2d"),s=n("fc6a"),r=n("4d64").indexOf,a=n("d012"),l=i([].push);e.exports=function(e,t){var n,i=s(e),c=0,u=[];for(n in i)!o(a,n)&&o(i,n)&&l(u,n);while(t.length>c)o(i,n=t[c++])&&(~r(u,n)||l(u,n));return u}},cae8:function(e,t,n){"use strict";n("dbdc")},cb2d:function(e,t,n){var i=n("1626"),o=n("9bf2"),s=n("13d2"),r=n("6374");e.exports=function(e,t,n,a){a||(a={});var l=a.enumerable,c=void 0!==a.name?a.name:t;if(i(n)&&s(n,c,a),a.global)l?e[t]=n:r(t,n);else{try{a.unsafe?e[t]&&(l=!0):delete e[t]}catch(u){}l?e[t]=n:o.f(e,t,{value:n,enumerable:!1,configurable:!a.nonConfigurable,writable:!a.nonWritable})}return e}},cc12:function(e,t,n){var i=n("da84"),o=n("861d"),s=i.document,r=o(s)&&o(s.createElement);e.exports=function(e){return r?s.createElement(e):{}}},cdce:function(e,t,n){var i=n("da84"),o=n("1626"),s=i.WeakMap;e.exports=o(s)&&/native code/.test(String(s))},d012:function(e,t){e.exports={}},d01f:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));const i="undefined"===typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__},d039:function(e,t){e.exports=function(e){try{return!!e()}catch(t){return!0}}},d066:function(e,t,n){var i=n("da84"),o=n("1626"),s=function(e){return o(e)?e:void 0};e.exports=function(e,t){return arguments.length<2?s(i[e]):i[e]&&i[e][t]}},d1e7:function(e,t,n){"use strict";var i={}.propertyIsEnumerable,o=Object.getOwnPropertyDescriptor,s=o&&!i.call({1:2},1);t.f=s?function(e){var t=o(this,e);return!!t&&t.enumerable}:i},d882:function(e,t,n){},d920:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return l}));var i=n("710b"),o=n("3a18"),s=n("73a0"),r=n("2c81");function a(e,t=100,n=1/0){try{return c("",e,t,n)}catch(i){return{ERROR:`**non-serializable** (${i})`}}}function l(e,t=3,n=102400){const i=a(e,t);return p(i)>n?l(e,t-1,n):i}function c(e,t,n=1/0,i=1/0,r=Object(o["a"])()){const[a,l]=r;if(null==t||["boolean","string"].includes(typeof t)||"number"===typeof t&&Number.isFinite(t))return t;const d=u(e,t);if(!d.startsWith("[object "))return d;if(t["__sentry_skip_normalization__"])return t;const h="number"===typeof t["__sentry_override_normalization_depth__"]?t["__sentry_override_normalization_depth__"]:n;if(0===h)return d.replace("object ","");if(a(t))return"[Circular ~]";const p=t;if(p&&"function"===typeof p.toJSON)try{const e=p.toJSON();return c("",e,h-1,i,r)}catch(v){}const m=Array.isArray(t)?[]:{};let f=0;const g=Object(s["b"])(t);for(const o in g){if(!Object.prototype.hasOwnProperty.call(g,o))continue;if(f>=i){m[o]="[MaxProperties ~]";break}const e=g[o];m[o]=c(o,e,h-1,i,r),f++}return l(t),m}function u(t,n){try{if("domain"===t&&n&&"object"===typeof n&&n._events)return"[Domain]";if("domainEmitter"===t)return"[DomainEmitter]";if("undefined"!==typeof e&&n===e)return"[Global]";if("undefined"!==typeof window&&n===window)return"[Window]";if("undefined"!==typeof document&&n===document)return"[Document]";if(Object(i["o"])(n))return"[VueViewModel]";if(Object(i["m"])(n))return"[SyntheticEvent]";if("number"===typeof n&&!Number.isFinite(n))return`[${n}]`;if("function"===typeof n)return`[Function: ${Object(r["d"])(n)}]`;if("symbol"===typeof n)return`[${String(n)}]`;if("bigint"===typeof n)return`[BigInt: ${String(n)}]`;const o=d(n);return/^HTML(\w*)Element$/.test(o)?`[HTMLElement: ${o}]`:`[object ${o}]`}catch(o){return`**non-serializable** (${o})`}}function d(e){const t=Object.getPrototypeOf(e);return t?t.constructor.name:"null prototype"}function h(e){return~-encodeURI(e).split(/%..|./).length}function p(e){return h(JSON.stringify(e))}}).call(this,n("c8ba"))},d96b:function(e,t,n){"use strict";n.d(t,"a",(function(){return o})),n.d(t,"b",(function(){return s}));var i=n("11ec");const o=globalThis;function s(e,t,n){const s=n||o,r=s.__SENTRY__=s.__SENTRY__||{},a=r[i["a"]]=r[i["a"]]||{};return a[e]||(a[e]=t())}},d9b5:function(e,t,n){var i=n("d066"),o=n("1626"),s=n("3a9b"),r=n("fdbf"),a=Object;e.exports=r?function(e){return"symbol"==typeof e}:function(e){var t=i("Symbol");return o(t)&&s(t.prototype,a(e))}},da84:function(e,t,n){(function(t){var n=function(e){return e&&e.Math==Math&&e};e.exports=n("object"==typeof globalThis&&globalThis)||n("object"==typeof window&&window)||n("object"==typeof self&&self)||n("object"==typeof t&&t)||function(){return this}()||Function("return this")()}).call(this,n("c8ba"))},dbdc:function(e,t,n){},dc4a:function(e,t,n){var i=n("59ed"),o=n("7234");e.exports=function(e,t){var n=e[t];return o(n)?void 0:i(n)}},e330:function(e,t,n){var i=n("40d5"),o=Function.prototype,s=o.call,r=i&&o.bind.bind(s,s);e.exports=i?r:function(e){return function(){return s.apply(e,arguments)}}},e893:function(e,t,n){var i=n("1a2d"),o=n("56ef"),s=n("06cf"),r=n("9bf2");e.exports=function(e,t,n){for(var a=o(t),l=r.f,c=s.f,u=0;u<a.length;u++){var d=a[u];i(e,d)||n&&i(n,d)||l(e,d,c(t,d))}}},e8b5:function(e,t,n){var i=n("c6b6");e.exports=Array.isArray||function(e){return"Array"==i(e)}},ef74:function(e,t,n){"use strict";n("921d")},f04e:function(e,t,n){"use strict";n("fcf5")},f37b:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return l})),n.d(t,"c",(function(){return u})),n.d(t,"d",(function(){return a}));var i=n("d01f"),o=n("d96b");const s="Sentry Logger ",r=["debug","info","warn","error","log","assert","trace"],a={};function l(e){if(!("console"in o["a"]))return e();const t=o["a"].console,n={},i=Object.keys(a);i.forEach(e=>{const i=a[e];n[e]=t[e],t[e]=i});try{return e()}finally{i.forEach(e=>{t[e]=n[e]})}}function c(){let e=!1;const t={enable:()=>{e=!0},disable:()=>{e=!1},isEnabled:()=>e};return i["a"]?r.forEach(n=>{t[n]=(...t)=>{e&&l(()=>{o["a"].console[n](`${s}[${n}]:`,...t)})}}):r.forEach(e=>{t[e]=()=>{}}),t}const u=Object(o["b"])("logger",c)},f772:function(e,t,n){var i=n("5692"),o=n("90e3"),s=i("keys");e.exports=function(e){return s[e]||(s[e]=o(e))}},f9d5:function(e,t,n){(function(t){!function(t,n){e.exports=n()}(0,(function(){"use strict";var e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof t?t:"undefined"!=typeof self?self:{},n={exports:{}};n.exports=function(){const e=e=>{const t=[];for(let n=0;n<e.length;n++)-1===t.indexOf(e[n])&&t.push(e[n]);return t},t=e=>e.charAt(0).toUpperCase()+e.slice(1),n=e=>Array.prototype.slice.call(e),i=e=>{},o=e=>{},s=[],r=e=>{s.includes(e)||(s.push(e),i(e))},a=(e,t)=>{r('"'.concat(e,'" is deprecated and will be removed in the next major release. Please use "').concat(t,'" instead.'))},l=e=>"function"==typeof e?e():e,c=e=>e&&"function"==typeof e.toPromise,u=e=>c(e)?e.toPromise():Promise.resolve(e),d=e=>e&&Promise.resolve(e)===e,h={title:"",titleText:"",text:"",html:"",footer:"",icon:void 0,iconColor:void 0,iconHtml:void 0,template:void 0,toast:!1,showClass:{popup:"swal2-show",backdrop:"swal2-backdrop-show",icon:"swal2-icon-show"},hideClass:{popup:"swal2-hide",backdrop:"swal2-backdrop-hide",icon:"swal2-icon-hide"},customClass:{},target:"body",color:void 0,backdrop:!0,heightAuto:!0,allowOutsideClick:!0,allowEscapeKey:!0,allowEnterKey:!0,stopKeydownPropagation:!0,keydownListenerCapture:!1,showConfirmButton:!0,showDenyButton:!1,showCancelButton:!1,preConfirm:void 0,preDeny:void 0,confirmButtonText:"OK",confirmButtonAriaLabel:"",confirmButtonColor:void 0,denyButtonText:"No",denyButtonAriaLabel:"",denyButtonColor:void 0,cancelButtonText:"Cancel",cancelButtonAriaLabel:"",cancelButtonColor:void 0,buttonsStyling:!0,reverseButtons:!1,focusConfirm:!0,focusDeny:!1,focusCancel:!1,returnFocus:!0,showCloseButton:!1,closeButtonHtml:"&times;",closeButtonAriaLabel:"Close this dialog",loaderHtml:"",showLoaderOnConfirm:!1,showLoaderOnDeny:!1,imageUrl:void 0,imageWidth:void 0,imageHeight:void 0,imageAlt:"",timer:void 0,timerProgressBar:!1,width:void 0,padding:void 0,background:void 0,input:void 0,inputPlaceholder:"",inputLabel:"",inputValue:"",inputOptions:{},inputAutoTrim:!0,inputAttributes:{},inputValidator:void 0,returnInputValueOnDeny:!1,validationMessage:void 0,grow:!1,position:"center",progressSteps:[],currentProgressStep:void 0,progressStepsDistance:void 0,willOpen:void 0,didOpen:void 0,didRender:void 0,willClose:void 0,didClose:void 0,didDestroy:void 0,scrollbarPadding:!0},p=["allowEscapeKey","allowOutsideClick","background","buttonsStyling","cancelButtonAriaLabel","cancelButtonColor","cancelButtonText","closeButtonAriaLabel","closeButtonHtml","color","confirmButtonAriaLabel","confirmButtonColor","confirmButtonText","currentProgressStep","customClass","denyButtonAriaLabel","denyButtonColor","denyButtonText","didClose","didDestroy","footer","hideClass","html","icon","iconColor","iconHtml","imageAlt","imageHeight","imageUrl","imageWidth","preConfirm","preDeny","progressSteps","returnFocus","reverseButtons","showCancelButton","showCloseButton","showConfirmButton","showDenyButton","text","title","titleText","willClose"],m={},f=["allowOutsideClick","allowEnterKey","backdrop","focusConfirm","focusDeny","focusCancel","returnFocus","heightAuto","keydownListenerCapture"],g=e=>Object.prototype.hasOwnProperty.call(h,e),v=e=>-1!==p.indexOf(e),b=e=>m[e],_=e=>{g(e)||i('Unknown parameter "'.concat(e,'"'))},y=e=>{f.includes(e)&&i('The parameter "'.concat(e,'" is incompatible with toasts'))},w=e=>{b(e)&&a(e,b(e))},k=e=>{!e.backdrop&&e.allowOutsideClick&&i('"allowOutsideClick" parameter requires `backdrop` parameter to be set to `true`');for(const t in e)_(t),e.toast&&y(t),w(t)},x="swal2-",j=e=>{const t={};for(const n in e)t[e[n]]=x+e[n];return t},O=j(["container","shown","height-auto","iosfix","popup","modal","no-backdrop","no-transition","toast","toast-shown","show","hide","close","title","html-container","actions","confirm","deny","cancel","default-outline","footer","icon","icon-content","image","input","file","range","select","radio","checkbox","label","textarea","inputerror","input-label","validation-message","progress-steps","active-progress-step","progress-step","progress-step-line","loader","loading","styled","top","top-start","top-end","top-left","top-right","center","center-start","center-end","center-left","center-right","bottom","bottom-start","bottom-end","bottom-left","bottom-right","grow-row","grow-column","grow-fullscreen","rtl","timer-progress-bar","timer-progress-bar-container","scrollbar-measure","icon-success","icon-warning","icon-info","icon-question","icon-error"]),E=j(["success","warning","info","question","error"]),C=()=>document.body.querySelector(".".concat(O.container)),S=e=>{const t=C();return t?t.querySelector(e):null},I=e=>S(".".concat(e)),P=()=>I(O.popup),T=()=>I(O.icon),A=()=>I(O.title),L=()=>I(O["html-container"]),D=()=>I(O.image),z=()=>I(O["progress-steps"]),q=()=>I(O["validation-message"]),N=()=>S(".".concat(O.actions," .").concat(O.confirm)),M=()=>S(".".concat(O.actions," .").concat(O.deny)),B=()=>I(O["input-label"]),V=()=>S(".".concat(O.loader)),F=()=>S(".".concat(O.actions," .").concat(O.cancel)),R=()=>I(O.actions),U=()=>I(O.footer),$=()=>I(O["timer-progress-bar"]),W=()=>I(O.close),H='\n  a[href],\n  area[href],\n  input:not([disabled]),\n  select:not([disabled]),\n  textarea:not([disabled]),\n  button:not([disabled]),\n  iframe,\n  object,\n  embed,\n  [tabindex="0"],\n  [contenteditable],\n  audio[controls],\n  video[controls],\n  summary\n',K=()=>{const t=n(P().querySelectorAll('[tabindex]:not([tabindex="-1"]):not([tabindex="0"])')).sort((e,t)=>{const n=parseInt(e.getAttribute("tabindex")),i=parseInt(t.getAttribute("tabindex"));return n>i?1:n<i?-1:0}),i=n(P().querySelectorAll(H)).filter(e=>"-1"!==e.getAttribute("tabindex"));return e(t.concat(i)).filter(e=>pe(e))},G=()=>!Q(document.body,O["toast-shown"])&&!Q(document.body,O["no-backdrop"]),J=()=>P()&&Q(P(),O.toast),Y=()=>P().hasAttribute("data-loading"),Z={previousBodyPadding:null},X=(e,t)=>{if(e.textContent="",t){const i=(new DOMParser).parseFromString(t,"text/html");n(i.querySelector("head").childNodes).forEach(t=>{e.appendChild(t)}),n(i.querySelector("body").childNodes).forEach(t=>{e.appendChild(t)})}},Q=(e,t)=>{if(!t)return!1;const n=t.split(/\s+/);for(let i=0;i<n.length;i++)if(!e.classList.contains(n[i]))return!1;return!0},ee=(e,t)=>{n(e.classList).forEach(n=>{Object.values(O).includes(n)||Object.values(E).includes(n)||Object.values(t.showClass).includes(n)||e.classList.remove(n)})},te=(e,t,n)=>{if(ee(e,t),t.customClass&&t.customClass[n]){if("string"!=typeof t.customClass[n]&&!t.customClass[n].forEach)return i("Invalid type of customClass.".concat(n,'! Expected string or iterable object, got "').concat(typeof t.customClass[n],'"'));se(e,t.customClass[n])}},ne=(e,t)=>{if(!t)return null;switch(t){case"select":case"textarea":case"file":return e.querySelector(".".concat(O.popup," > .").concat(O[t]));case"checkbox":return e.querySelector(".".concat(O.popup," > .").concat(O.checkbox," input"));case"radio":return e.querySelector(".".concat(O.popup," > .").concat(O.radio," input:checked"))||e.querySelector(".".concat(O.popup," > .").concat(O.radio," input:first-child"));case"range":return e.querySelector(".".concat(O.popup," > .").concat(O.range," input"));default:return e.querySelector(".".concat(O.popup," > .").concat(O.input))}},ie=e=>{if(e.focus(),"file"!==e.type){const t=e.value;e.value="",e.value=t}},oe=(e,t,n)=>{e&&t&&("string"==typeof t&&(t=t.split(/\s+/).filter(Boolean)),t.forEach(t=>{Array.isArray(e)?e.forEach(e=>{n?e.classList.add(t):e.classList.remove(t)}):n?e.classList.add(t):e.classList.remove(t)}))},se=(e,t)=>{oe(e,t,!0)},re=(e,t)=>{oe(e,t,!1)},ae=(e,t)=>{const i=n(e.childNodes);for(let n=0;n<i.length;n++)if(Q(i[n],t))return i[n]},le=(e,t,n)=>{n==="".concat(parseInt(n))&&(n=parseInt(n)),n||0===parseInt(n)?e.style[t]="number"==typeof n?"".concat(n,"px"):n:e.style.removeProperty(t)},ce=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"flex";e.style.display=t},ue=e=>{e.style.display="none"},de=(e,t,n,i)=>{const o=e.querySelector(t);o&&(o.style[n]=i)},he=(e,t,n)=>{t?ce(e,n):ue(e)},pe=e=>!(!e||!(e.offsetWidth||e.offsetHeight||e.getClientRects().length)),me=()=>!pe(N())&&!pe(M())&&!pe(F()),fe=e=>!!(e.scrollHeight>e.clientHeight),ge=e=>{const t=window.getComputedStyle(e),n=parseFloat(t.getPropertyValue("animation-duration")||"0"),i=parseFloat(t.getPropertyValue("transition-duration")||"0");return n>0||i>0},ve=function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const n=$();pe(n)&&(t&&(n.style.transition="none",n.style.width="100%"),setTimeout(()=>{n.style.transition="width ".concat(e/1e3,"s linear"),n.style.width="0%"},10))},be=()=>{const e=$(),t=parseInt(window.getComputedStyle(e).width);e.style.removeProperty("transition"),e.style.width="100%";const n=t/parseInt(window.getComputedStyle(e).width)*100;e.style.removeProperty("transition"),e.style.width="".concat(n,"%")},_e=()=>"undefined"==typeof window||"undefined"==typeof document,ye=100,we={},ke=()=>{we.previousActiveElement&&we.previousActiveElement.focus?(we.previousActiveElement.focus(),we.previousActiveElement=null):document.body&&document.body.focus()},xe=e=>new Promise(t=>{if(!e)return t();const n=window.scrollX,i=window.scrollY;we.restoreFocusTimeout=setTimeout(()=>{ke(),t()},ye),window.scrollTo(n,i)}),je='\n <div aria-labelledby="'.concat(O.title,'" aria-describedby="').concat(O["html-container"],'" class="').concat(O.popup,'" tabindex="-1">\n   <button type="button" class="').concat(O.close,'"></button>\n   <ul class="').concat(O["progress-steps"],'"></ul>\n   <div class="').concat(O.icon,'"></div>\n   <img class="').concat(O.image,'" />\n   <h2 class="').concat(O.title,'" id="').concat(O.title,'"></h2>\n   <div class="').concat(O["html-container"],'" id="').concat(O["html-container"],'"></div>\n   <input class="').concat(O.input,'" />\n   <input type="file" class="').concat(O.file,'" />\n   <div class="').concat(O.range,'">\n     <input type="range" />\n     <output></output>\n   </div>\n   <select class="').concat(O.select,'"></select>\n   <div class="').concat(O.radio,'"></div>\n   <label for="').concat(O.checkbox,'" class="').concat(O.checkbox,'">\n     <input type="checkbox" />\n     <span class="').concat(O.label,'"></span>\n   </label>\n   <textarea class="').concat(O.textarea,'"></textarea>\n   <div class="').concat(O["validation-message"],'" id="').concat(O["validation-message"],'"></div>\n   <div class="').concat(O.actions,'">\n     <div class="').concat(O.loader,'"></div>\n     <button type="button" class="').concat(O.confirm,'"></button>\n     <button type="button" class="').concat(O.deny,'"></button>\n     <button type="button" class="').concat(O.cancel,'"></button>\n   </div>\n   <div class="').concat(O.footer,'"></div>\n   <div class="').concat(O["timer-progress-bar-container"],'">\n     <div class="').concat(O["timer-progress-bar"],'"></div>\n   </div>\n </div>\n').replace(/(^|\n)\s*/g,""),Oe=()=>{const e=C();return!!e&&(e.remove(),re([document.documentElement,document.body],[O["no-backdrop"],O["toast-shown"],O["has-column"]]),!0)},Ee=()=>{we.currentInstance.resetValidationMessage()},Ce=()=>{const e=P(),t=ae(e,O.input),n=ae(e,O.file),i=e.querySelector(".".concat(O.range," input")),o=e.querySelector(".".concat(O.range," output")),s=ae(e,O.select),r=e.querySelector(".".concat(O.checkbox," input")),a=ae(e,O.textarea);t.oninput=Ee,n.onchange=Ee,s.onchange=Ee,r.onchange=Ee,a.oninput=Ee,i.oninput=()=>{Ee(),o.value=i.value},i.onchange=()=>{Ee(),i.nextSibling.value=i.value}},Se=e=>"string"==typeof e?document.querySelector(e):e,Ie=e=>{const t=P();t.setAttribute("role",e.toast?"alert":"dialog"),t.setAttribute("aria-live",e.toast?"polite":"assertive"),e.toast||t.setAttribute("aria-modal","true")},Pe=e=>{"rtl"===window.getComputedStyle(e).direction&&se(C(),O.rtl)},Te=e=>{const t=Oe();if(_e())return void o("SweetAlert2 requires document to initialize");const n=document.createElement("div");n.className=O.container,t&&se(n,O["no-transition"]),X(n,je);const i=Se(e.target);i.appendChild(n),Ie(e),Pe(i),Ce()},Ae=(e,t)=>{e instanceof HTMLElement?t.appendChild(e):"object"==typeof e?Le(e,t):e&&X(t,e)},Le=(e,t)=>{e.jquery?De(t,e):X(t,e.toString())},De=(e,t)=>{if(e.textContent="",0 in t)for(let n=0;n in t;n++)e.appendChild(t[n].cloneNode(!0));else e.appendChild(t.cloneNode(!0))},ze=(()=>{if(_e())return!1;const e=document.createElement("div"),t={WebkitAnimation:"webkitAnimationEnd",animation:"animationend"};for(const n in t)if(Object.prototype.hasOwnProperty.call(t,n)&&void 0!==e.style[n])return t[n];return!1})(),qe=()=>{const e=document.createElement("div");e.className=O["scrollbar-measure"],document.body.appendChild(e);const t=e.getBoundingClientRect().width-e.clientWidth;return document.body.removeChild(e),t},Ne=(e,t)=>{const n=R(),i=V();t.showConfirmButton||t.showDenyButton||t.showCancelButton?ce(n):ue(n),te(n,t,"actions"),Me(n,i,t),X(i,t.loaderHtml),te(i,t,"loader")};function Me(e,t,n){const i=N(),o=M(),s=F();Ve(i,"confirm",n),Ve(o,"deny",n),Ve(s,"cancel",n),Be(i,o,s,n),n.reverseButtons&&(n.toast?(e.insertBefore(s,i),e.insertBefore(o,i)):(e.insertBefore(s,t),e.insertBefore(o,t),e.insertBefore(i,t)))}function Be(e,t,n,i){if(!i.buttonsStyling)return re([e,t,n],O.styled);se([e,t,n],O.styled),i.confirmButtonColor&&(e.style.backgroundColor=i.confirmButtonColor,se(e,O["default-outline"])),i.denyButtonColor&&(t.style.backgroundColor=i.denyButtonColor,se(t,O["default-outline"])),i.cancelButtonColor&&(n.style.backgroundColor=i.cancelButtonColor,se(n,O["default-outline"]))}function Ve(e,n,i){he(e,i["show".concat(t(n),"Button")],"inline-block"),X(e,i["".concat(n,"ButtonText")]),e.setAttribute("aria-label",i["".concat(n,"ButtonAriaLabel")]),e.className=O[n],te(e,i,"".concat(n,"Button")),se(e,i["".concat(n,"ButtonClass")])}function Fe(e,t){"string"==typeof t?e.style.background=t:t||se([document.documentElement,document.body],O["no-backdrop"])}function Re(e,t){t in O?se(e,O[t]):(i('The "position" parameter is not valid, defaulting to "center"'),se(e,O.center))}function Ue(e,t){if(t&&"string"==typeof t){const n="grow-".concat(t);n in O&&se(e,O[n])}}const $e=(e,t)=>{const n=C();n&&(Fe(n,t.backdrop),Re(n,t.position),Ue(n,t.grow),te(n,t,"container"))};var We={awaitingPromise:new WeakMap,promise:new WeakMap,innerParams:new WeakMap,domCache:new WeakMap};const He=["input","file","range","select","radio","checkbox","textarea"],Ke=(e,t)=>{const n=P(),i=We.innerParams.get(e),o=!i||t.input!==i.input;He.forEach(e=>{const i=O[e],s=ae(n,i);Ye(e,t.inputAttributes),s.className=i,o&&ue(s)}),t.input&&(o&&Ge(t),Ze(t))},Ge=e=>{if(!tt[e.input])return o('Unexpected type of input! Expected "text", "email", "password", "number", "tel", "select", "radio", "checkbox", "textarea", "file" or "url", got "'.concat(e.input,'"'));const t=et(e.input),n=tt[e.input](t,e);ce(n),setTimeout(()=>{ie(n)})},Je=e=>{for(let t=0;t<e.attributes.length;t++){const n=e.attributes[t].name;["type","value","style"].includes(n)||e.removeAttribute(n)}},Ye=(e,t)=>{const n=ne(P(),e);if(n){Je(n);for(const e in t)n.setAttribute(e,t[e])}},Ze=e=>{const t=et(e.input);e.customClass&&se(t,e.customClass.input)},Xe=(e,t)=>{e.placeholder&&!t.inputPlaceholder||(e.placeholder=t.inputPlaceholder)},Qe=(e,t,n)=>{if(n.inputLabel){e.id=O.input;const i=document.createElement("label"),o=O["input-label"];i.setAttribute("for",e.id),i.className=o,se(i,n.customClass.inputLabel),i.innerText=n.inputLabel,t.insertAdjacentElement("beforebegin",i)}},et=e=>{const t=O[e]?O[e]:O.input;return ae(P(),t)},tt={};tt.text=tt.email=tt.password=tt.number=tt.tel=tt.url=(e,t)=>("string"==typeof t.inputValue||"number"==typeof t.inputValue?e.value=t.inputValue:d(t.inputValue)||i('Unexpected type of inputValue! Expected "string", "number" or "Promise", got "'.concat(typeof t.inputValue,'"')),Qe(e,e,t),Xe(e,t),e.type=t.input,e),tt.file=(e,t)=>(Qe(e,e,t),Xe(e,t),e),tt.range=(e,t)=>{const n=e.querySelector("input"),i=e.querySelector("output");return n.value=t.inputValue,n.type=t.input,i.value=t.inputValue,Qe(n,e,t),e},tt.select=(e,t)=>{if(e.textContent="",t.inputPlaceholder){const n=document.createElement("option");X(n,t.inputPlaceholder),n.value="",n.disabled=!0,n.selected=!0,e.appendChild(n)}return Qe(e,e,t),e},tt.radio=e=>(e.textContent="",e),tt.checkbox=(e,t)=>{const n=ne(P(),"checkbox");n.value="1",n.id=O.checkbox,n.checked=Boolean(t.inputValue);const i=e.querySelector("span");return X(i,t.inputPlaceholder),e},tt.textarea=(e,t)=>{e.value=t.inputValue,Xe(e,t),Qe(e,e,t);const n=e=>parseInt(window.getComputedStyle(e).marginLeft)+parseInt(window.getComputedStyle(e).marginRight);return setTimeout(()=>{if("MutationObserver"in window){const t=parseInt(window.getComputedStyle(P()).width);new MutationObserver(()=>{const i=e.offsetWidth+n(e);P().style.width=i>t?"".concat(i,"px"):null}).observe(e,{attributes:!0,attributeFilter:["style"]})}}),e};const nt=(e,t)=>{const n=L();te(n,t,"htmlContainer"),t.html?(Ae(t.html,n),ce(n,"block")):t.text?(n.textContent=t.text,ce(n,"block")):ue(n),Ke(e,t)},it=(e,t)=>{const n=U();he(n,t.footer),t.footer&&Ae(t.footer,n),te(n,t,"footer")},ot=(e,t)=>{const n=W();X(n,t.closeButtonHtml),te(n,t,"closeButton"),he(n,t.showCloseButton),n.setAttribute("aria-label",t.closeButtonAriaLabel)},st=(e,t)=>{const n=We.innerParams.get(e),i=T();return n&&t.icon===n.icon?(ut(i,t),void rt(i,t)):t.icon||t.iconHtml?t.icon&&-1===Object.keys(E).indexOf(t.icon)?(o('Unknown icon! Expected "success", "error", "warning", "info" or "question", got "'.concat(t.icon,'"')),ue(i)):(ce(i),ut(i,t),rt(i,t),void se(i,t.showClass.icon)):ue(i)},rt=(e,t)=>{for(const n in E)t.icon!==n&&re(e,E[n]);se(e,E[t.icon]),dt(e,t),at(),te(e,t,"icon")},at=()=>{const e=P(),t=window.getComputedStyle(e).getPropertyValue("background-color"),n=e.querySelectorAll("[class^=swal2-success-circular-line], .swal2-success-fix");for(let i=0;i<n.length;i++)n[i].style.backgroundColor=t},lt='\n  <div class="swal2-success-circular-line-left"></div>\n  <span class="swal2-success-line-tip"></span> <span class="swal2-success-line-long"></span>\n  <div class="swal2-success-ring"></div> <div class="swal2-success-fix"></div>\n  <div class="swal2-success-circular-line-right"></div>\n',ct='\n  <span class="swal2-x-mark">\n    <span class="swal2-x-mark-line-left"></span>\n    <span class="swal2-x-mark-line-right"></span>\n  </span>\n',ut=(e,t)=>{e.textContent="",t.iconHtml?X(e,ht(t.iconHtml)):"success"===t.icon?X(e,lt):"error"===t.icon?X(e,ct):X(e,ht({question:"?",warning:"!",info:"i"}[t.icon]))},dt=(e,t)=>{if(t.iconColor){e.style.color=t.iconColor,e.style.borderColor=t.iconColor;for(const n of[".swal2-success-line-tip",".swal2-success-line-long",".swal2-x-mark-line-left",".swal2-x-mark-line-right"])de(e,n,"backgroundColor",t.iconColor);de(e,".swal2-success-ring","borderColor",t.iconColor)}},ht=e=>'<div class="'.concat(O["icon-content"],'">').concat(e,"</div>"),pt=(e,t)=>{const n=D();if(!t.imageUrl)return ue(n);ce(n,""),n.setAttribute("src",t.imageUrl),n.setAttribute("alt",t.imageAlt),le(n,"width",t.imageWidth),le(n,"height",t.imageHeight),n.className=O.image,te(n,t,"image")},mt=e=>{const t=document.createElement("li");return se(t,O["progress-step"]),X(t,e),t},ft=e=>{const t=document.createElement("li");return se(t,O["progress-step-line"]),e.progressStepsDistance&&(t.style.width=e.progressStepsDistance),t},gt=(e,t)=>{const n=z();if(!t.progressSteps||0===t.progressSteps.length)return ue(n);ce(n),n.textContent="",t.currentProgressStep>=t.progressSteps.length&&i("Invalid currentProgressStep parameter, it should be less than progressSteps.length (currentProgressStep like JS arrays starts from 0)"),t.progressSteps.forEach((e,i)=>{const o=mt(e);if(n.appendChild(o),i===t.currentProgressStep&&se(o,O["active-progress-step"]),i!==t.progressSteps.length-1){const e=ft(t);n.appendChild(e)}})},vt=(e,t)=>{const n=A();he(n,t.title||t.titleText,"block"),t.title&&Ae(t.title,n),t.titleText&&(n.innerText=t.titleText),te(n,t,"title")},bt=(e,t)=>{const n=C(),i=P();t.toast?(le(n,"width",t.width),i.style.width="100%",i.insertBefore(V(),T())):le(i,"width",t.width),le(i,"padding",t.padding),t.color&&(i.style.color=t.color),t.background&&(i.style.background=t.background),ue(q()),_t(i,t)},_t=(e,t)=>{e.className="".concat(O.popup," ").concat(pe(e)?t.showClass.popup:""),t.toast?(se([document.documentElement,document.body],O["toast-shown"]),se(e,O.toast)):se(e,O.modal),te(e,t,"popup"),"string"==typeof t.customClass&&se(e,t.customClass),t.icon&&se(e,O["icon-".concat(t.icon)])},yt=(e,t)=>{bt(e,t),$e(e,t),gt(e,t),st(e,t),pt(e,t),vt(e,t),ot(e,t),nt(e,t),Ne(e,t),it(e,t),"function"==typeof t.didRender&&t.didRender(P())},wt=Object.freeze({cancel:"cancel",backdrop:"backdrop",close:"close",esc:"esc",timer:"timer"}),kt=()=>{n(document.body.children).forEach(e=>{e===C()||e.contains(C())||(e.hasAttribute("aria-hidden")&&e.setAttribute("data-previous-aria-hidden",e.getAttribute("aria-hidden")),e.setAttribute("aria-hidden","true"))})},xt=()=>{n(document.body.children).forEach(e=>{e.hasAttribute("data-previous-aria-hidden")?(e.setAttribute("aria-hidden",e.getAttribute("data-previous-aria-hidden")),e.removeAttribute("data-previous-aria-hidden")):e.removeAttribute("aria-hidden")})},jt=["swal-title","swal-html","swal-footer"],Ot=e=>{const t="string"==typeof e.template?document.querySelector(e.template):e.template;if(!t)return{};const n=t.content;return At(n),Object.assign(Et(n),Ct(n),St(n),It(n),Pt(n),Tt(n,jt))},Et=e=>{const t={};return n(e.querySelectorAll("swal-param")).forEach(e=>{Lt(e,["name","value"]);const n=e.getAttribute("name"),i=e.getAttribute("value");"boolean"==typeof h[n]&&"false"===i&&(t[n]=!1),"object"==typeof h[n]&&(t[n]=JSON.parse(i))}),t},Ct=e=>{const i={};return n(e.querySelectorAll("swal-button")).forEach(e=>{Lt(e,["type","color","aria-label"]);const n=e.getAttribute("type");i["".concat(n,"ButtonText")]=e.innerHTML,i["show".concat(t(n),"Button")]=!0,e.hasAttribute("color")&&(i["".concat(n,"ButtonColor")]=e.getAttribute("color")),e.hasAttribute("aria-label")&&(i["".concat(n,"ButtonAriaLabel")]=e.getAttribute("aria-label"))}),i},St=e=>{const t={},n=e.querySelector("swal-image");return n&&(Lt(n,["src","width","height","alt"]),n.hasAttribute("src")&&(t.imageUrl=n.getAttribute("src")),n.hasAttribute("width")&&(t.imageWidth=n.getAttribute("width")),n.hasAttribute("height")&&(t.imageHeight=n.getAttribute("height")),n.hasAttribute("alt")&&(t.imageAlt=n.getAttribute("alt"))),t},It=e=>{const t={},n=e.querySelector("swal-icon");return n&&(Lt(n,["type","color"]),n.hasAttribute("type")&&(t.icon=n.getAttribute("type")),n.hasAttribute("color")&&(t.iconColor=n.getAttribute("color")),t.iconHtml=n.innerHTML),t},Pt=e=>{const t={},i=e.querySelector("swal-input");i&&(Lt(i,["type","label","placeholder","value"]),t.input=i.getAttribute("type")||"text",i.hasAttribute("label")&&(t.inputLabel=i.getAttribute("label")),i.hasAttribute("placeholder")&&(t.inputPlaceholder=i.getAttribute("placeholder")),i.hasAttribute("value")&&(t.inputValue=i.getAttribute("value")));const o=e.querySelectorAll("swal-input-option");return o.length&&(t.inputOptions={},n(o).forEach(e=>{Lt(e,["value"]);const n=e.getAttribute("value"),i=e.innerHTML;t.inputOptions[n]=i})),t},Tt=(e,t)=>{const n={};for(const i in t){const o=t[i],s=e.querySelector(o);s&&(Lt(s,[]),n[o.replace(/^swal-/,"")]=s.innerHTML.trim())}return n},At=e=>{const t=jt.concat(["swal-param","swal-button","swal-image","swal-icon","swal-input","swal-input-option"]);n(e.children).forEach(e=>{const n=e.tagName.toLowerCase();-1===t.indexOf(n)&&i("Unrecognized element <".concat(n,">"))})},Lt=(e,t)=>{n(e.attributes).forEach(n=>{-1===t.indexOf(n.name)&&i(['Unrecognized attribute "'.concat(n.name,'" on <').concat(e.tagName.toLowerCase(),">."),"".concat(t.length?"Allowed attributes are: ".concat(t.join(", ")):"To set the value, use HTML within the element.")])})};var Dt={email:(e,t)=>/^[a-zA-Z0-9.+_-]+@[a-zA-Z0-9.-]+\.[a-zA-Z0-9-]{2,24}$/.test(e)?Promise.resolve():Promise.resolve(t||"Invalid email address"),url:(e,t)=>/^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\.[a-z]{2,63}\b([-a-zA-Z0-9@:%_+.~#?&/=]*)$/.test(e)?Promise.resolve():Promise.resolve(t||"Invalid URL")};function zt(e){e.inputValidator||Object.keys(Dt).forEach(t=>{e.input===t&&(e.inputValidator=Dt[t])})}function qt(e){(!e.target||"string"==typeof e.target&&!document.querySelector(e.target)||"string"!=typeof e.target&&!e.target.appendChild)&&(i('Target parameter is not valid, defaulting to "body"'),e.target="body")}function Nt(e){zt(e),e.showLoaderOnConfirm&&!e.preConfirm&&i("showLoaderOnConfirm is set to true, but preConfirm is not defined.\nshowLoaderOnConfirm should be used together with preConfirm, see usage example:\nhttps://sweetalert2.github.io/#ajax-request"),qt(e),"string"==typeof e.title&&(e.title=e.title.split("\n").join("<br />")),Te(e)}class Mt{constructor(e,t){this.callback=e,this.remaining=t,this.running=!1,this.start()}start(){return this.running||(this.running=!0,this.started=new Date,this.id=setTimeout(this.callback,this.remaining)),this.remaining}stop(){return this.running&&(this.running=!1,clearTimeout(this.id),this.remaining-=(new Date).getTime()-this.started.getTime()),this.remaining}increase(e){const t=this.running;return t&&this.stop(),this.remaining+=e,t&&this.start(),this.remaining}getTimerLeft(){return this.running&&(this.stop(),this.start()),this.remaining}isRunning(){return this.running}}const Bt=()=>{null===Z.previousBodyPadding&&document.body.scrollHeight>window.innerHeight&&(Z.previousBodyPadding=parseInt(window.getComputedStyle(document.body).getPropertyValue("padding-right")),document.body.style.paddingRight="".concat(Z.previousBodyPadding+qe(),"px"))},Vt=()=>{null!==Z.previousBodyPadding&&(document.body.style.paddingRight="".concat(Z.previousBodyPadding,"px"),Z.previousBodyPadding=null)},Ft=()=>{if((/iPad|iPhone|iPod/.test(navigator.userAgent)&&!window.MSStream||"MacIntel"===navigator.platform&&navigator.maxTouchPoints>1)&&!Q(document.body,O.iosfix)){const e=document.body.scrollTop;document.body.style.top="".concat(-1*e,"px"),se(document.body,O.iosfix),Ut(),Rt()}},Rt=()=>{const e=navigator.userAgent,t=!!e.match(/iPad/i)||!!e.match(/iPhone/i),n=!!e.match(/WebKit/i);if(t&&n&&!e.match(/CriOS/i)){const e=44;P().scrollHeight>window.innerHeight-e&&(C().style.paddingBottom="".concat(e,"px"))}},Ut=()=>{const e=C();let t;e.ontouchstart=e=>{t=$t(e)},e.ontouchmove=e=>{t&&(e.preventDefault(),e.stopPropagation())}},$t=e=>{const t=e.target,n=C();return!(Wt(e)||Ht(e)||t!==n&&(fe(n)||"INPUT"===t.tagName||"TEXTAREA"===t.tagName||fe(L())&&L().contains(t)))},Wt=e=>e.touches&&e.touches.length&&"stylus"===e.touches[0].touchType,Ht=e=>e.touches&&e.touches.length>1,Kt=()=>{if(Q(document.body,O.iosfix)){const e=parseInt(document.body.style.top,10);re(document.body,O.iosfix),document.body.style.top="",document.body.scrollTop=-1*e}},Gt=10,Jt=e=>{const t=C(),n=P();"function"==typeof e.willOpen&&e.willOpen(n);const i=window.getComputedStyle(document.body).overflowY;Qt(t,n,e),setTimeout(()=>{Zt(t,n)},Gt),G()&&(Xt(t,e.scrollbarPadding,i),kt()),J()||we.previousActiveElement||(we.previousActiveElement=document.activeElement),"function"==typeof e.didOpen&&setTimeout(()=>e.didOpen(n)),re(t,O["no-transition"])},Yt=e=>{const t=P();if(e.target!==t)return;const n=C();t.removeEventListener(ze,Yt),n.style.overflowY="auto"},Zt=(e,t)=>{ze&&ge(t)?(e.style.overflowY="hidden",t.addEventListener(ze,Yt)):e.style.overflowY="auto"},Xt=(e,t,n)=>{Ft(),t&&"hidden"!==n&&Bt(),setTimeout(()=>{e.scrollTop=0})},Qt=(e,t,n)=>{se(e,n.showClass.backdrop),t.style.setProperty("opacity","0","important"),ce(t,"grid"),setTimeout(()=>{se(t,n.showClass.popup),t.style.removeProperty("opacity")},Gt),se([document.documentElement,document.body],O.shown),n.heightAuto&&n.backdrop&&!n.toast&&se([document.documentElement,document.body],O["height-auto"])},en=e=>{let t=P();t||new $i,t=P();const n=V();J()?ue(T()):tn(t,e),ce(n),t.setAttribute("data-loading",!0),t.setAttribute("aria-busy",!0),t.focus()},tn=(e,t)=>{const n=R(),i=V();!t&&pe(N())&&(t=N()),ce(n),t&&(ue(t),i.setAttribute("data-button-to-replace",t.className)),i.parentNode.insertBefore(i,t),se([e,n],O.loading)},nn=(e,t)=>{"select"===t.input||"radio"===t.input?ln(e,t):["text","email","number","tel","textarea"].includes(t.input)&&(c(t.inputValue)||d(t.inputValue))&&(en(N()),cn(e,t))},on=(e,t)=>{const n=e.getInput();if(!n)return null;switch(t.input){case"checkbox":return sn(n);case"radio":return rn(n);case"file":return an(n);default:return t.inputAutoTrim?n.value.trim():n.value}},sn=e=>e.checked?1:0,rn=e=>e.checked?e.value:null,an=e=>e.files.length?null!==e.getAttribute("multiple")?e.files:e.files[0]:null,ln=(e,t)=>{const n=P(),i=e=>un[t.input](n,dn(e),t);c(t.inputOptions)||d(t.inputOptions)?(en(N()),u(t.inputOptions).then(t=>{e.hideLoading(),i(t)})):"object"==typeof t.inputOptions?i(t.inputOptions):o("Unexpected type of inputOptions! Expected object, Map or Promise, got ".concat(typeof t.inputOptions))},cn=(e,t)=>{const n=e.getInput();ue(n),u(t.inputValue).then(i=>{n.value="number"===t.input?parseFloat(i)||0:"".concat(i),ce(n),n.focus(),e.hideLoading()}).catch(t=>{o("Error in inputValue promise: ".concat(t)),n.value="",ce(n),n.focus(),e.hideLoading()})},un={select:(e,t,n)=>{const i=ae(e,O.select),o=(e,t,i)=>{const o=document.createElement("option");o.value=i,X(o,t),o.selected=hn(i,n.inputValue),e.appendChild(o)};t.forEach(e=>{const t=e[0],n=e[1];if(Array.isArray(n)){const e=document.createElement("optgroup");e.label=t,e.disabled=!1,i.appendChild(e),n.forEach(t=>o(e,t[1],t[0]))}else o(i,n,t)}),i.focus()},radio:(e,t,n)=>{const i=ae(e,O.radio);t.forEach(e=>{const t=e[0],o=e[1],s=document.createElement("input"),r=document.createElement("label");s.type="radio",s.name=O.radio,s.value=t,hn(t,n.inputValue)&&(s.checked=!0);const a=document.createElement("span");X(a,o),a.className=O.label,r.appendChild(s),r.appendChild(a),i.appendChild(r)});const o=i.querySelectorAll("input");o.length&&o[0].focus()}},dn=e=>{const t=[];return"undefined"!=typeof Map&&e instanceof Map?e.forEach((e,n)=>{let i=e;"object"==typeof i&&(i=dn(i)),t.push([n,i])}):Object.keys(e).forEach(n=>{let i=e[n];"object"==typeof i&&(i=dn(i)),t.push([n,i])}),t},hn=(e,t)=>t&&t.toString()===e.toString(),pn=e=>{const t=We.innerParams.get(e);e.disableButtons(),t.input?gn(e,"confirm"):wn(e,!0)},mn=e=>{const t=We.innerParams.get(e);e.disableButtons(),t.returnInputValueOnDeny?gn(e,"deny"):bn(e,!1)},fn=(e,t)=>{e.disableButtons(),t(wt.cancel)},gn=(e,n)=>{const i=We.innerParams.get(e);if(!i.input)return o('The "input" parameter is needed to be set when using returnInputValueOn'.concat(t(n)));const s=on(e,i);i.inputValidator?vn(e,s,n):e.getInput().checkValidity()?"deny"===n?bn(e,s):wn(e,s):(e.enableButtons(),e.showValidationMessage(i.validationMessage))},vn=(e,t,n)=>{const i=We.innerParams.get(e);e.disableInput(),Promise.resolve().then(()=>u(i.inputValidator(t,i.validationMessage))).then(i=>{e.enableButtons(),e.enableInput(),i?e.showValidationMessage(i):"deny"===n?bn(e,t):wn(e,t)})},bn=(e,t)=>{const n=We.innerParams.get(e||void 0);n.showLoaderOnDeny&&en(M()),n.preDeny?(We.awaitingPromise.set(e||void 0,!0),Promise.resolve().then(()=>u(n.preDeny(t,n.validationMessage))).then(n=>{!1===n?e.hideLoading():e.closePopup({isDenied:!0,value:void 0===n?t:n})}).catch(t=>yn(e||void 0,t))):e.closePopup({isDenied:!0,value:t})},_n=(e,t)=>{e.closePopup({isConfirmed:!0,value:t})},yn=(e,t)=>{e.rejectPromise(t)},wn=(e,t)=>{const n=We.innerParams.get(e||void 0);n.showLoaderOnConfirm&&en(),n.preConfirm?(e.resetValidationMessage(),We.awaitingPromise.set(e||void 0,!0),Promise.resolve().then(()=>u(n.preConfirm(t,n.validationMessage))).then(n=>{pe(q())||!1===n?e.hideLoading():_n(e,void 0===n?t:n)}).catch(t=>yn(e||void 0,t))):_n(e,t)},kn=(e,t,n)=>{We.innerParams.get(e).toast?xn(e,t,n):(En(t),Cn(t),Sn(e,t,n))},xn=(e,t,n)=>{t.popup.onclick=()=>{const t=We.innerParams.get(e);t&&(jn(t)||t.timer||t.input)||n(wt.close)}},jn=e=>e.showConfirmButton||e.showDenyButton||e.showCancelButton||e.showCloseButton;let On=!1;const En=e=>{e.popup.onmousedown=()=>{e.container.onmouseup=function(t){e.container.onmouseup=void 0,t.target===e.container&&(On=!0)}}},Cn=e=>{e.container.onmousedown=()=>{e.popup.onmouseup=function(t){e.popup.onmouseup=void 0,(t.target===e.popup||e.popup.contains(t.target))&&(On=!0)}}},Sn=(e,t,n)=>{t.container.onclick=i=>{const o=We.innerParams.get(e);On?On=!1:i.target===t.container&&l(o.allowOutsideClick)&&n(wt.backdrop)}},In=()=>pe(P()),Pn=()=>N()&&N().click(),Tn=()=>M()&&M().click(),An=()=>F()&&F().click(),Ln=(e,t,n,i)=>{t.keydownTarget&&t.keydownHandlerAdded&&(t.keydownTarget.removeEventListener("keydown",t.keydownHandler,{capture:t.keydownListenerCapture}),t.keydownHandlerAdded=!1),n.toast||(t.keydownHandler=t=>Nn(e,t,i),t.keydownTarget=n.keydownListenerCapture?window:P(),t.keydownListenerCapture=n.keydownListenerCapture,t.keydownTarget.addEventListener("keydown",t.keydownHandler,{capture:t.keydownListenerCapture}),t.keydownHandlerAdded=!0)},Dn=(e,t,n)=>{const i=K();if(i.length)return(t+=n)===i.length?t=0:-1===t&&(t=i.length-1),i[t].focus();P().focus()},zn=["ArrowRight","ArrowDown"],qn=["ArrowLeft","ArrowUp"],Nn=(e,t,n)=>{const i=We.innerParams.get(e);i&&(i.stopKeydownPropagation&&t.stopPropagation(),"Enter"===t.key?Mn(e,t,i):"Tab"===t.key?Bn(t,i):[...zn,...qn].includes(t.key)?Vn(t.key):"Escape"===t.key&&Fn(t,i,n))},Mn=(e,t,n)=>{if(l(n.allowEnterKey)&&!t.isComposing&&t.target&&e.getInput()&&t.target.outerHTML===e.getInput().outerHTML){if(["textarea","file"].includes(n.input))return;Pn(),t.preventDefault()}},Bn=(e,t)=>{const n=e.target,i=K();let o=-1;for(let s=0;s<i.length;s++)if(n===i[s]){o=s;break}e.shiftKey?Dn(t,o,-1):Dn(t,o,1),e.stopPropagation(),e.preventDefault()},Vn=e=>{if(![N(),M(),F()].includes(document.activeElement))return;const t=zn.includes(e)?"nextElementSibling":"previousElementSibling",n=document.activeElement[t];n instanceof HTMLElement&&n.focus()},Fn=(e,t,n)=>{l(t.allowEscapeKey)&&(e.preventDefault(),n(wt.esc))},Rn=e=>"object"==typeof e&&e.jquery,Un=e=>e instanceof Element||Rn(e),$n=e=>{const t={};return"object"!=typeof e[0]||Un(e[0])?["title","html","icon"].forEach((n,i)=>{const s=e[i];"string"==typeof s||Un(s)?t[n]=s:void 0!==s&&o("Unexpected type of ".concat(n,'! Expected "string" or "Element", got ').concat(typeof s))}):Object.assign(t,e[0]),t};function Wn(){const e=this;for(var t=arguments.length,n=new Array(t),i=0;i<t;i++)n[i]=arguments[i];return new e(...n)}function Hn(e){class t extends(this){_main(t,n){return super._main(t,Object.assign({},e,n))}}return t}const Kn=()=>we.timeout&&we.timeout.getTimerLeft(),Gn=()=>{if(we.timeout)return be(),we.timeout.stop()},Jn=()=>{if(we.timeout){const e=we.timeout.start();return ve(e),e}},Yn=()=>{const e=we.timeout;return e&&(e.running?Gn():Jn())},Zn=e=>{if(we.timeout){const t=we.timeout.increase(e);return ve(t,!0),t}},Xn=()=>we.timeout&&we.timeout.isRunning();let Qn=!1;const ei={};function ti(){ei[arguments.length>0&&void 0!==arguments[0]?arguments[0]:"data-swal-template"]=this,Qn||(document.body.addEventListener("click",ni),Qn=!0)}const ni=e=>{for(let t=e.target;t&&t!==document;t=t.parentNode)for(const e in ei){const n=t.getAttribute(e);if(n)return void ei[e].fire({template:n})}};var ii=Object.freeze({isValidParameter:g,isUpdatableParameter:v,isDeprecatedParameter:b,argsToParams:$n,isVisible:In,clickConfirm:Pn,clickDeny:Tn,clickCancel:An,getContainer:C,getPopup:P,getTitle:A,getHtmlContainer:L,getImage:D,getIcon:T,getInputLabel:B,getCloseButton:W,getActions:R,getConfirmButton:N,getDenyButton:M,getCancelButton:F,getLoader:V,getFooter:U,getTimerProgressBar:$,getFocusableElements:K,getValidationMessage:q,isLoading:Y,fire:Wn,mixin:Hn,showLoading:en,enableLoading:en,getTimerLeft:Kn,stopTimer:Gn,resumeTimer:Jn,toggleTimer:Yn,increaseTimer:Zn,isTimerRunning:Xn,bindClickHandler:ti});function oi(){const e=We.innerParams.get(this);if(!e)return;const t=We.domCache.get(this);ue(t.loader),J()?e.icon&&ce(T()):si(t),re([t.popup,t.actions],O.loading),t.popup.removeAttribute("aria-busy"),t.popup.removeAttribute("data-loading"),t.confirmButton.disabled=!1,t.denyButton.disabled=!1,t.cancelButton.disabled=!1}const si=e=>{const t=e.popup.getElementsByClassName(e.loader.getAttribute("data-button-to-replace"));t.length?ce(t[0],"inline-block"):me()&&ue(e.actions)};function ri(e){const t=We.innerParams.get(e||this),n=We.domCache.get(e||this);return n?ne(n.popup,t.input):null}var ai={swalPromiseResolve:new WeakMap,swalPromiseReject:new WeakMap};function li(e,t,n,i){J()?bi(e,i):(xe(n).then(()=>bi(e,i)),we.keydownTarget.removeEventListener("keydown",we.keydownHandler,{capture:we.keydownListenerCapture}),we.keydownHandlerAdded=!1),/^((?!chrome|android).)*safari/i.test(navigator.userAgent)?(t.setAttribute("style","display:none !important"),t.removeAttribute("class"),t.innerHTML=""):t.remove(),G()&&(Vt(),Kt(),xt()),ci()}function ci(){re([document.documentElement,document.body],[O.shown,O["height-auto"],O["no-backdrop"],O["toast-shown"]])}function ui(e){e=fi(e);const t=ai.swalPromiseResolve.get(this),n=hi(this);this.isAwaitingPromise()?e.isDismissed||(mi(this),t(e)):n&&t(e)}function di(){return!!We.awaitingPromise.get(this)}const hi=e=>{const t=P();if(!t)return!1;const n=We.innerParams.get(e);if(!n||Q(t,n.hideClass.popup))return!1;re(t,n.showClass.popup),se(t,n.hideClass.popup);const i=C();return re(i,n.showClass.backdrop),se(i,n.hideClass.backdrop),gi(e,t,n),!0};function pi(e){const t=ai.swalPromiseReject.get(this);mi(this),t&&t(e)}const mi=e=>{e.isAwaitingPromise()&&(We.awaitingPromise.delete(e),We.innerParams.get(e)||e._destroy())},fi=e=>void 0===e?{isConfirmed:!1,isDenied:!1,isDismissed:!0}:Object.assign({isConfirmed:!1,isDenied:!1,isDismissed:!1},e),gi=(e,t,n)=>{const i=C(),o=ze&&ge(t);"function"==typeof n.willClose&&n.willClose(t),o?vi(e,t,i,n.returnFocus,n.didClose):li(e,i,n.returnFocus,n.didClose)},vi=(e,t,n,i,o)=>{we.swalCloseEventFinishedCallback=li.bind(null,e,n,i,o),t.addEventListener(ze,(function(e){e.target===t&&(we.swalCloseEventFinishedCallback(),delete we.swalCloseEventFinishedCallback)}))},bi=(e,t)=>{setTimeout(()=>{"function"==typeof t&&t.bind(e.params)(),e._destroy()})};function _i(e,t,n){const i=We.domCache.get(e);t.forEach(e=>{i[e].disabled=n})}function yi(e,t){if(!e)return!1;if("radio"===e.type){const n=e.parentNode.parentNode.querySelectorAll("input");for(let e=0;e<n.length;e++)n[e].disabled=t}else e.disabled=t}function wi(){_i(this,["confirmButton","denyButton","cancelButton"],!1)}function ki(){_i(this,["confirmButton","denyButton","cancelButton"],!0)}function xi(){return yi(this.getInput(),!1)}function ji(){return yi(this.getInput(),!0)}function Oi(e){const t=We.domCache.get(this),n=We.innerParams.get(this);X(t.validationMessage,e),t.validationMessage.className=O["validation-message"],n.customClass&&n.customClass.validationMessage&&se(t.validationMessage,n.customClass.validationMessage),ce(t.validationMessage);const i=this.getInput();i&&(i.setAttribute("aria-invalid",!0),i.setAttribute("aria-describedby",O["validation-message"]),ie(i),se(i,O.inputerror))}function Ei(){const e=We.domCache.get(this);e.validationMessage&&ue(e.validationMessage);const t=this.getInput();t&&(t.removeAttribute("aria-invalid"),t.removeAttribute("aria-describedby"),re(t,O.inputerror))}function Ci(){return We.domCache.get(this).progressSteps}function Si(e){const t=P(),n=We.innerParams.get(this);if(!t||Q(t,n.hideClass.popup))return i("You're trying to update the closed or closing popup, that won't work. Use the update() method in preConfirm parameter or show a new popup.");const o=Ii(e),s=Object.assign({},n,o);yt(this,s),We.innerParams.set(this,s),Object.defineProperties(this,{params:{value:Object.assign({},this.params,e),writable:!1,enumerable:!0}})}const Ii=e=>{const t={};return Object.keys(e).forEach(n=>{v(n)?t[n]=e[n]:i('Invalid parameter to update: "'.concat(n,'". Updatable params are listed here: https://github.com/sweetalert2/sweetalert2/blob/master/src/utils/params.js\n\nIf you think this parameter should be updatable, request it here: https://github.com/sweetalert2/sweetalert2/issues/new?template=02_feature_request.md'))}),t};function Pi(){const e=We.domCache.get(this),t=We.innerParams.get(this);t?(e.popup&&we.swalCloseEventFinishedCallback&&(we.swalCloseEventFinishedCallback(),delete we.swalCloseEventFinishedCallback),we.deferDisposalTimer&&(clearTimeout(we.deferDisposalTimer),delete we.deferDisposalTimer),"function"==typeof t.didDestroy&&t.didDestroy(),Ti(this)):Ai(this)}const Ti=e=>{Ai(e),delete e.params,delete we.keydownHandler,delete we.keydownTarget,delete we.currentInstance},Ai=e=>{e.isAwaitingPromise()?(Li(We,e),We.awaitingPromise.set(e,!0)):(Li(ai,e),Li(We,e))},Li=(e,t)=>{for(const n in e)e[n].delete(t)};var Di=Object.freeze({hideLoading:oi,disableLoading:oi,getInput:ri,close:ui,isAwaitingPromise:di,rejectPromise:pi,closePopup:ui,closeModal:ui,closeToast:ui,enableButtons:wi,disableButtons:ki,enableInput:xi,disableInput:ji,showValidationMessage:Oi,resetValidationMessage:Ei,getProgressSteps:Ci,update:Si,_destroy:Pi});let zi;class qi{constructor(){if("undefined"==typeof window)return;zi=this;for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];const i=Object.freeze(this.constructor.argsToParams(t));Object.defineProperties(this,{params:{value:i,writable:!1,enumerable:!0,configurable:!0}});const o=this._main(this.params);We.promise.set(this,o)}_main(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};k(Object.assign({},t,e)),we.currentInstance&&(we.currentInstance._destroy(),G()&&xt()),we.currentInstance=this;const n=Mi(e,t);Nt(n),Object.freeze(n),we.timeout&&(we.timeout.stop(),delete we.timeout),clearTimeout(we.restoreFocusTimeout);const i=Bi(this);return yt(this,n),We.innerParams.set(this,n),Ni(this,i,n)}then(e){return We.promise.get(this).then(e)}finally(e){return We.promise.get(this).finally(e)}}const Ni=(e,t,n)=>new Promise((i,o)=>{const s=t=>{e.closePopup({isDismissed:!0,dismiss:t})};ai.swalPromiseResolve.set(e,i),ai.swalPromiseReject.set(e,o),t.confirmButton.onclick=()=>pn(e),t.denyButton.onclick=()=>mn(e),t.cancelButton.onclick=()=>fn(e,s),t.closeButton.onclick=()=>s(wt.close),kn(e,t,s),Ln(e,we,n,s),nn(e,n),Jt(n),Vi(we,n,s),Fi(t,n),setTimeout(()=>{t.container.scrollTop=0})}),Mi=(e,t)=>{const n=Ot(e),i=Object.assign({},h,t,n,e);return i.showClass=Object.assign({},h.showClass,i.showClass),i.hideClass=Object.assign({},h.hideClass,i.hideClass),i},Bi=e=>{const t={popup:P(),container:C(),actions:R(),confirmButton:N(),denyButton:M(),cancelButton:F(),loader:V(),closeButton:W(),validationMessage:q(),progressSteps:z()};return We.domCache.set(e,t),t},Vi=(e,t,n)=>{const i=$();ue(i),t.timer&&(e.timeout=new Mt(()=>{n("timer"),delete e.timeout},t.timer),t.timerProgressBar&&(ce(i),te(i,t,"timerProgressBar"),setTimeout(()=>{e.timeout&&e.timeout.running&&ve(t.timer)})))},Fi=(e,t)=>{if(!t.toast)return l(t.allowEnterKey)?void(Ri(e,t)||Dn(t,-1,1)):Ui()},Ri=(e,t)=>t.focusDeny&&pe(e.denyButton)?(e.denyButton.focus(),!0):t.focusCancel&&pe(e.cancelButton)?(e.cancelButton.focus(),!0):!(!t.focusConfirm||!pe(e.confirmButton)||(e.confirmButton.focus(),0)),Ui=()=>{document.activeElement instanceof HTMLElement&&"function"==typeof document.activeElement.blur&&document.activeElement.blur()};Object.assign(qi.prototype,Di),Object.assign(qi,ii),Object.keys(Di).forEach(e=>{qi[e]=function(){if(zi)return zi[e](...arguments)}}),qi.DismissReason=wt,qi.version="11.4.0";const $i=qi;return $i.default=$i,$i}(),void 0!==e&&e.Sweetalert2&&(e.swal=e.sweetAlert=e.Swal=e.SweetAlert=e.Sweetalert2);var i=n.exports;return class{static install(e,t={}){var n;const o=i.mixin(t),s=function(...e){return o.fire.call(o,...e)};Object.assign(s,i),Object.keys(i).filter(e=>"function"==typeof i[e]).forEach(e=>{s[e]=o[e].bind(o)}),(null==(n=e.config)?void 0:n.globalProperties)&&!e.config.globalProperties.$swal?(e.config.globalProperties.$swal=s,e.provide("$swal",s)):Object.prototype.hasOwnProperty.call(e,"$swal")||(e.prototype.$swal=s,e.swal=s)}}}))}).call(this,n("c8ba"))},fc6a:function(e,t,n){var i=n("44ad"),o=n("1d80");e.exports=function(e){return i(o(e))}},fcc9:function(e,t,n){"use strict";n("0920")},fcd2:function(e,t,n){},fcf5:function(e,t,n){},fdbf:function(e,t,n){var i=n("04f8");e.exports=i&&!Symbol.sham&&"symbol"==typeof Symbol.iterator}});
//# sourceMappingURL=wizard.min.js.map
//# debugId=4be673d6-e390-5285-a2ff-dd33181994c9
