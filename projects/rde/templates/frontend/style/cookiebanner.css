#cookie-banner {
  display: none;
  z-index: 999999999;
  overflow-y: auto;
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

#cookie-banner > div {
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  height: 80dvh;
}

#cookie-banner > div .cookie-glass {
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  position: fixed;
  background-color: black;
  opacity: 0.8;
}

#cookie-banner > div .cookie-glass > div {
  opacity: 0.5;
  background-color: black;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

#cookie-banner > div .cookie-container {
  width: min(500px, 100%);
  max-height: 100%;
  display: inline-block;
  vertical-align: middle;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
  --tw-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  box-shadow: 0 0 #0000, 0 0 #0000, var(--tw-shadow);
  text-align: left;
  border-radius: 0.5rem;
  overflow: auto;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translateX(var(--tw-translate-x)) translateY(var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

#cookie-banner > div .cookie-container .cookie-header {
  position: sticky;
  top: 0;
  text-align: center;
  padding: 25px;
  background-color: #ce000c;
  color: white;
  display: flex;
}

#cookie-banner > div .cookie-container .cookie-header > img {
  width: 120px;
}

#cookie-banner > div .cookie-container .cookie-header .cookie-title {
  margin-left: auto;
  display: flex;
  align-items: center;
}

#cookie-banner > div .cookie-container .cookie-header .cookie-title > svg {
  margin-right: 12px;
}

#cookie-banner > div .cookie-container .cookie-header .cookie-title h2 {
  display: none;
}

#cookie-banner > div .cookie-container .cookie-content {
  background: white;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

#cookie-banner > div .cookie-container .cookie-content .cookie-about {
  padding: 24px;
}

#cookie-banner > div .cookie-container .cookie-content .cookie-btns {
  position: sticky;
  bottom: 0;
  background-color: white;
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  justify-content: space-evenly;
  padding: 12px 12px 16px 12px;
  gap: 4px;
  border-top: 1px solid #ce000c;
}

#cookie-banner > div .cookie-container .cookie-content .cookie-btns button {
  flex-grow: 1;
  width: 100%;
}

#cookie-banner > div .cookie-container .cookie-content .cookie-btns button:hover {
  background-color: var(--gsd-btn-bg-color-hover);
  color: var(--gsd-btn-text-color-hover);
}

#cookie-banner > div .cookie-container .cookie-content .cookie-btns button.primary {
  background-color: var(--gsd-btn-primary-bg-color);
  color: var(--gsd-btn-primary-text-color);
}

#cookie-banner > div .cookie-container .cookie-content .cookie-btns button.primary:hover {
  background-color: var(--gsd-btn-primary-bg-color-hover);
  color: var(--gsd-btn-primary-text-color-hover);
}

#tooltip {
  z-index: 9999999999999999;
  background-color: unset;
  border-radius: 4px;
}

#tooltip #tooltip-content {
  background-color: #ce000c;
  color: white;
  border-radius: 4px;
}

#tooltip #tooltip-arrow,
#tooltip #tooltip-arrow::before {
  background-color: #ce000c;
  border: none !important;
}

.btn-reverse {
  border: 1px solid #ce000c;
  background-color: white;
  color: #ce000c;
}

.btn-reverse:hover {
  border: 1px solid #ce000c;
  background-color: #ce000c;
  color: white;
}

@media screen and (min-width: 830px) {
  #cookie-banner > div .cookie-container {
    max-height: 80%;
  }

  #cookie-banner > div .cookie-container .cookie-header .cookie-title h2 {
    display: inline;
  }

  #cookie-banner > div .cookie-container .cookie-content .cookie-btns button {
    width: fit-content;
  }
}
