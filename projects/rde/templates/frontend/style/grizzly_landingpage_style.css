#ifrm { display: none; }
.usp{
  background-color:#f4f4f4;
}
.usp-wrapper{
  padding:20px 0;
}
.usp-column{
  font-size:20px;
}
.usp-wrapper i{
  color: #ce000c;
  margin-right:10px;
}
.contenttxt h2,.contenttxt h3,.contenttxt h4,.contenttxt h5,.contenttxt h6{
  margin-top:30px !important;
}

.breadcr{
  display: block;
}
.breadcr ul{
  padding:0;
  margin-top:0;
}
.breadcr ul li{
  display: inline-block;
}

.breadcr ul li:after{
  content: " / ";
}

.breadcr ul li:last-child:after{
  content: "  ";
}
.link-structuur ul li{
  display:inline-block;
  list-style-type: none;

}
.link-structuur ul{
  padding-left:0;
}

#contact {
  width: 100%;
  font-family: sans-serif;
}

#contact .box-field input, #contact .box-field textarea, #contact .box-field button{
  font-family: sans-serif;
  font-size: 15px;
}

.half {
  width: 50%;
  display: inline-block;
}

.full {
  width: 100%;
}

.left {
  float: left;
}

.right {
  float: right;
}

.center {
  text-align: center;
}

.bold {
  font-weight: bold;
}

.e-mail-url {
  visibility: hidden !important;
  height: 0px !important;
  padding: 0px !important;
  margin: 0px !important;
  width: 0px !important;
  position: absolute !important;
}

.bedankt{
  padding:50px 0;
}
.bedankt h1{
  margin-bottom:20px;
}

.box-field {
  margin: 0 0 25px 0;
}

textarea {
  resize: vertical;
  min-height: 175px;
}

.box-field input, .box-field textarea{
  padding: 15px 20px;
  box-sizing: border-box;
  background-color: #f4f4f4;
  border: 1px solid #f1f1f1;
  border-radius: 0px;
  box-shadow: 0px 4px 2px -3px #d2d2d2;
  color: #6d7779;
  display: block;
}

.box-field input::placeholder, .box-field textarea::placeholder{
  color: #6d7779;
}

.box-field input:focus, .box-field textarea:focus{
  outline: none;
}

.box-field.half{
  width: 49%;
}

.half-left {
  padding-right: 1%;
}

.half-right {
  margin-left: 1%;
}

button.send-submit {
  background-color: #ce000c;
  cursor: pointer;
  color: #FFF;
  border: none;
  padding: 15px 40px;
  transition: .5s all;
}

button.send-submit:hover {
  opacity: .9;
}

label {
  margin: 0 0 2px 0;
}

.error {
  -webkit-transition: all 0.3s ease-out;
  -moz-transition: all 0.3s ease-out;
  -o-transition: all 0.3s ease-out;
  transition: all 0.3s ease-out;
  box-shadow: 0 4px 8px -4px #DE0000 !important;
}

.error:focus {
  box-shadow: 0 4px 8px -4px #DE0000 !important;
}

label.error {
  display: none !important;
}

@media screen and (max-width: 500px){
  .box-field{
    margin-bottom: 25px;
  }
  .box-field.half,  #details.half, #message.half{
    width: 100%;
  }

  .half-left {
    padding-right: 0;
  }

  .half-right {
    margin-left: 0;
  }
}


@media only screen and (max-width: 670px){
  .m-r-md{
    margin-bottom:10px;
  }
}

@media only screen and (min-width: 640px){
  .usp-wrapper{
    display:flex;
    justify-content: space-around;
  }
}
@media only screen and (max-width: 639px){

}
@media only screen and (max-width: 860px){
  .usp-wrapper {
    padding: 20px 0 10px 20px;
  }
  .usp-column{
    font-size:18px;
    margin-bottom:10px;
  }
}
@media only screen and (max-width: 1100px) and (min-width: 670px){
  .col8-s {
    width: 100%;
  }
}
