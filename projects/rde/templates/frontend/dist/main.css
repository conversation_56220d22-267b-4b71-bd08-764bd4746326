/*!**************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[0].use[1]!./node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[0].use[2]!./node_modules/simplelightbox/dist/simple-lightbox.min.css ***!
  \**************************************************************************************************************************************************************************************************************/
/*!
	By André Rinas, www.andrerinas.de
	Documentation, www.simplelightbox.de
	Available for use under the MIT License
	Version 2.10.4
*/
body.hidden-scroll{overflow:hidden}
.sl-overlay{position:fixed;left:0;right:0;top:0;bottom:0;background:#fff;display:none;z-index:1035}
.sl-wrapper{z-index:1040;width:100%;height:100%;left:0;top:0;position:fixed}
.sl-wrapper *{box-sizing:border-box}
.sl-wrapper button{border:0 none;background:transparent;font-size:28px;padding:0;cursor:pointer}
.sl-wrapper button:hover{opacity:0.7}
.sl-wrapper .sl-close{display:none;position:fixed;right:30px;top:30px;z-index:10060;margin-top:-14px;margin-right:-14px;height:44px;width:44px;line-height:44px;font-family:Arial,Baskerville,monospace;color:#000;font-size:3rem}
.sl-wrapper .sl-counter{display:none;position:fixed;top:30px;left:30px;z-index:10060;color:#000;font-size:1rem}
.sl-wrapper .sl-navigation{width:100%;display:none}
.sl-wrapper .sl-navigation button{position:fixed;top:50%;margin-top:-22px;height:44px;width:22px;line-height:44px;text-align:center;display:block;z-index:10060;font-family:Arial,Baskerville,monospace;color:#000}
.sl-wrapper .sl-navigation button.sl-next{right:5px;font-size:2rem}
.sl-wrapper .sl-navigation button.sl-prev{left:5px;font-size:2rem}
@media (min-width: 35.5em){.sl-wrapper .sl-navigation button{width:44px}.sl-wrapper .sl-navigation button.sl-next{right:10px;font-size:3rem}.sl-wrapper .sl-navigation button.sl-prev{left:10px;font-size:3rem}}
@media (min-width: 50em){.sl-wrapper .sl-navigation button{width:44px}.sl-wrapper .sl-navigation button.sl-next{right:20px;font-size:3rem}.sl-wrapper .sl-navigation button.sl-prev{left:20px;font-size:3rem}}
.sl-wrapper.sl-dir-rtl .sl-navigation{direction:ltr}
.sl-wrapper .sl-image{position:fixed;-ms-touch-action:none;touch-action:none;z-index:10000}
.sl-wrapper .sl-image img{margin:0;padding:0;display:block;border:0 none;width:100%;height:auto}
@media (min-width: 35.5em){.sl-wrapper .sl-image img{border:0 none}}
@media (min-width: 50em){.sl-wrapper .sl-image img{border:0 none}}
.sl-wrapper .sl-image iframe{background:#000;border:0 none}
@media (min-width: 35.5em){.sl-wrapper .sl-image iframe{border:0 none}}
@media (min-width: 50em){.sl-wrapper .sl-image iframe{border:0 none}}
.sl-wrapper .sl-image .sl-caption{display:none;padding:10px;color:#fff;background:rgba(0,0,0,0.8);font-size:1rem;position:absolute;bottom:0;left:0;right:0}
.sl-wrapper .sl-image .sl-caption.pos-top{bottom:auto;top:0}
.sl-wrapper .sl-image .sl-caption.pos-outside{bottom:auto}
.sl-wrapper .sl-image .sl-download{display:none;position:absolute;bottom:5px;right:5px;color:#000;z-index:1060}
.sl-spinner{display:none;border:5px solid #333;border-radius:40px;height:40px;left:50%;margin:-20px 0 0 -20px;opacity:0;position:fixed;top:50%;width:40px;z-index:1007;-webkit-animation:pulsate 1s ease-out infinite;-moz-animation:pulsate 1s ease-out infinite;-ms-animation:pulsate 1s ease-out infinite;-o-animation:pulsate 1s ease-out infinite;animation:pulsate 1s ease-out infinite}
.sl-scrollbar-measure{position:absolute;top:-9999px;width:50px;height:50px;overflow:scroll}
.sl-transition{transition:-moz-transform ease 200ms;transition:-ms-transform ease 200ms;transition:-o-transform ease 200ms;transition:-webkit-transform ease 200ms;transition:transform ease 200ms}
@-webkit-keyframes pulsate{0%{transform:scale(0.1);opacity:0.0}50%{opacity:1}100%{transform:scale(1.2);opacity:0}}
@keyframes pulsate{0%{transform:scale(0.1);opacity:0.0}50%{opacity:1}100%{transform:scale(1.2);opacity:0}}
@-moz-keyframes pulsate{0%{transform:scale(0.1);opacity:0.0}50%{opacity:1}100%{transform:scale(1.2);opacity:0}}
@-o-keyframes pulsate{0%{transform:scale(0.1);opacity:0.0}50%{opacity:1}100%{transform:scale(1.2);opacity:0}}
@-ms-keyframes pulsate{0%{transform:scale(0.1);opacity:0.0}50%{opacity:1}100%{transform:scale(1.2);opacity:0}}

/*!**********************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[0].use[1]!./node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[0].use[2]!./node_modules/simple-line-icons/dist/styles/simple-line-icons.css ***!
  \**********************************************************************************************************************************************************************************************************************/
/*
* Font Face
*/
@font-face {
  font-family: 'simple-line-icons';
  src: url(/projects/rde/templates/frontend/dist/fonts/Simple-Line-Icons.eot);
  src: url(/projects/rde/templates/frontend/dist/fonts/Simple-Line-Icons.eot#iefix) format('embedded-opentype'), url(/projects/rde/templates/frontend/dist/fonts/Simple-Line-Icons.woff2) format('woff2'), url(/projects/rde/templates/frontend/dist/fonts/Simple-Line-Icons.ttf) format('truetype'), url(/projects/rde/templates/frontend/dist/fonts/Simple-Line-Icons.woff) format('woff'), url(/projects/rde/templates/frontend/dist/95a776adb65bebc85439.svg?v=2.4.0#simple-line-icons) format('svg');
  font-weight: normal;
  font-style: normal;
}
/*
 Use the following code if you want to have a class per icon.
 Instead of a list of all class selectors, you can use the generic [class*="icon-"] selector,
 but it's slower.
*/
.icon-user,
.icon-people,
.icon-user-female,
.icon-user-follow,
.icon-user-following,
.icon-user-unfollow,
.icon-login,
.icon-logout,
.icon-emotsmile,
.icon-phone,
.icon-call-end,
.icon-call-in,
.icon-call-out,
.icon-map,
.icon-location-pin,
.icon-direction,
.icon-directions,
.icon-compass,
.icon-layers,
.icon-menu,
.icon-list,
.icon-options-vertical,
.icon-options,
.icon-arrow-down,
.icon-arrow-left,
.icon-arrow-right,
.icon-arrow-up,
.icon-arrow-up-circle,
.icon-arrow-left-circle,
.icon-arrow-right-circle,
.icon-arrow-down-circle,
.icon-check,
.icon-clock,
.icon-plus,
.icon-minus,
.icon-close,
.icon-event,
.icon-exclamation,
.icon-organization,
.icon-trophy,
.icon-screen-smartphone,
.icon-screen-desktop,
.icon-plane,
.icon-notebook,
.icon-mustache,
.icon-mouse,
.icon-magnet,
.icon-energy,
.icon-disc,
.icon-cursor,
.icon-cursor-move,
.icon-crop,
.icon-chemistry,
.icon-speedometer,
.icon-shield,
.icon-screen-tablet,
.icon-magic-wand,
.icon-hourglass,
.icon-graduation,
.icon-ghost,
.icon-game-controller,
.icon-fire,
.icon-eyeglass,
.icon-envelope-open,
.icon-envelope-letter,
.icon-bell,
.icon-badge,
.icon-anchor,
.icon-wallet,
.icon-vector,
.icon-speech,
.icon-puzzle,
.icon-printer,
.icon-present,
.icon-playlist,
.icon-pin,
.icon-picture,
.icon-handbag,
.icon-globe-alt,
.icon-globe,
.icon-folder-alt,
.icon-folder,
.icon-film,
.icon-feed,
.icon-drop,
.icon-drawer,
.icon-docs,
.icon-doc,
.icon-diamond,
.icon-cup,
.icon-calculator,
.icon-bubbles,
.icon-briefcase,
.icon-book-open,
.icon-basket-loaded,
.icon-basket,
.icon-bag,
.icon-action-undo,
.icon-action-redo,
.icon-wrench,
.icon-umbrella,
.icon-trash,
.icon-tag,
.icon-support,
.icon-frame,
.icon-size-fullscreen,
.icon-size-actual,
.icon-shuffle,
.icon-share-alt,
.icon-share,
.icon-rocket,
.icon-question,
.icon-pie-chart,
.icon-pencil,
.icon-note,
.icon-loop,
.icon-home,
.icon-grid,
.icon-graph,
.icon-microphone,
.icon-music-tone-alt,
.icon-music-tone,
.icon-earphones-alt,
.icon-earphones,
.icon-equalizer,
.icon-like,
.icon-dislike,
.icon-control-start,
.icon-control-rewind,
.icon-control-play,
.icon-control-pause,
.icon-control-forward,
.icon-control-end,
.icon-volume-1,
.icon-volume-2,
.icon-volume-off,
.icon-calendar,
.icon-bulb,
.icon-chart,
.icon-ban,
.icon-bubble,
.icon-camrecorder,
.icon-camera,
.icon-cloud-download,
.icon-cloud-upload,
.icon-envelope,
.icon-eye,
.icon-flag,
.icon-heart,
.icon-info,
.icon-key,
.icon-link,
.icon-lock,
.icon-lock-open,
.icon-magnifier,
.icon-magnifier-add,
.icon-magnifier-remove,
.icon-paper-clip,
.icon-paper-plane,
.icon-power,
.icon-refresh,
.icon-reload,
.icon-settings,
.icon-star,
.icon-symbol-female,
.icon-symbol-male,
.icon-target,
.icon-credit-card,
.icon-paypal,
.icon-social-tumblr,
.icon-social-twitter,
.icon-social-facebook,
.icon-social-instagram,
.icon-social-linkedin,
.icon-social-pinterest,
.icon-social-github,
.icon-social-google,
.icon-social-reddit,
.icon-social-skype,
.icon-social-dribbble,
.icon-social-behance,
.icon-social-foursqare,
.icon-social-soundcloud,
.icon-social-spotify,
.icon-social-stumbleupon,
.icon-social-youtube,
.icon-social-dropbox,
.icon-social-vkontakte,
.icon-social-steam {
  font-family: 'simple-line-icons';
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.icon-user:before {
  content: "\e005";
}
.icon-people:before {
  content: "\e001";
}
.icon-user-female:before {
  content: "\e000";
}
.icon-user-follow:before {
  content: "\e002";
}
.icon-user-following:before {
  content: "\e003";
}
.icon-user-unfollow:before {
  content: "\e004";
}
.icon-login:before {
  content: "\e066";
}
.icon-logout:before {
  content: "\e065";
}
.icon-emotsmile:before {
  content: "\e021";
}
.icon-phone:before {
  content: "\e600";
}
.icon-call-end:before {
  content: "\e048";
}
.icon-call-in:before {
  content: "\e047";
}
.icon-call-out:before {
  content: "\e046";
}
.icon-map:before {
  content: "\e033";
}
.icon-location-pin:before {
  content: "\e096";
}
.icon-direction:before {
  content: "\e042";
}
.icon-directions:before {
  content: "\e041";
}
.icon-compass:before {
  content: "\e045";
}
.icon-layers:before {
  content: "\e034";
}
.icon-menu:before {
  content: "\e601";
}
.icon-list:before {
  content: "\e067";
}
.icon-options-vertical:before {
  content: "\e602";
}
.icon-options:before {
  content: "\e603";
}
.icon-arrow-down:before {
  content: "\e604";
}
.icon-arrow-left:before {
  content: "\e605";
}
.icon-arrow-right:before {
  content: "\e606";
}
.icon-arrow-up:before {
  content: "\e607";
}
.icon-arrow-up-circle:before {
  content: "\e078";
}
.icon-arrow-left-circle:before {
  content: "\e07a";
}
.icon-arrow-right-circle:before {
  content: "\e079";
}
.icon-arrow-down-circle:before {
  content: "\e07b";
}
.icon-check:before {
  content: "\e080";
}
.icon-clock:before {
  content: "\e081";
}
.icon-plus:before {
  content: "\e095";
}
.icon-minus:before {
  content: "\e615";
}
.icon-close:before {
  content: "\e082";
}
.icon-event:before {
  content: "\e619";
}
.icon-exclamation:before {
  content: "\e617";
}
.icon-organization:before {
  content: "\e616";
}
.icon-trophy:before {
  content: "\e006";
}
.icon-screen-smartphone:before {
  content: "\e010";
}
.icon-screen-desktop:before {
  content: "\e011";
}
.icon-plane:before {
  content: "\e012";
}
.icon-notebook:before {
  content: "\e013";
}
.icon-mustache:before {
  content: "\e014";
}
.icon-mouse:before {
  content: "\e015";
}
.icon-magnet:before {
  content: "\e016";
}
.icon-energy:before {
  content: "\e020";
}
.icon-disc:before {
  content: "\e022";
}
.icon-cursor:before {
  content: "\e06e";
}
.icon-cursor-move:before {
  content: "\e023";
}
.icon-crop:before {
  content: "\e024";
}
.icon-chemistry:before {
  content: "\e026";
}
.icon-speedometer:before {
  content: "\e007";
}
.icon-shield:before {
  content: "\e00e";
}
.icon-screen-tablet:before {
  content: "\e00f";
}
.icon-magic-wand:before {
  content: "\e017";
}
.icon-hourglass:before {
  content: "\e018";
}
.icon-graduation:before {
  content: "\e019";
}
.icon-ghost:before {
  content: "\e01a";
}
.icon-game-controller:before {
  content: "\e01b";
}
.icon-fire:before {
  content: "\e01c";
}
.icon-eyeglass:before {
  content: "\e01d";
}
.icon-envelope-open:before {
  content: "\e01e";
}
.icon-envelope-letter:before {
  content: "\e01f";
}
.icon-bell:before {
  content: "\e027";
}
.icon-badge:before {
  content: "\e028";
}
.icon-anchor:before {
  content: "\e029";
}
.icon-wallet:before {
  content: "\e02a";
}
.icon-vector:before {
  content: "\e02b";
}
.icon-speech:before {
  content: "\e02c";
}
.icon-puzzle:before {
  content: "\e02d";
}
.icon-printer:before {
  content: "\e02e";
}
.icon-present:before {
  content: "\e02f";
}
.icon-playlist:before {
  content: "\e030";
}
.icon-pin:before {
  content: "\e031";
}
.icon-picture:before {
  content: "\e032";
}
.icon-handbag:before {
  content: "\e035";
}
.icon-globe-alt:before {
  content: "\e036";
}
.icon-globe:before {
  content: "\e037";
}
.icon-folder-alt:before {
  content: "\e039";
}
.icon-folder:before {
  content: "\e089";
}
.icon-film:before {
  content: "\e03a";
}
.icon-feed:before {
  content: "\e03b";
}
.icon-drop:before {
  content: "\e03e";
}
.icon-drawer:before {
  content: "\e03f";
}
.icon-docs:before {
  content: "\e040";
}
.icon-doc:before {
  content: "\e085";
}
.icon-diamond:before {
  content: "\e043";
}
.icon-cup:before {
  content: "\e044";
}
.icon-calculator:before {
  content: "\e049";
}
.icon-bubbles:before {
  content: "\e04a";
}
.icon-briefcase:before {
  content: "\e04b";
}
.icon-book-open:before {
  content: "\e04c";
}
.icon-basket-loaded:before {
  content: "\e04d";
}
.icon-basket:before {
  content: "\e04e";
}
.icon-bag:before {
  content: "\e04f";
}
.icon-action-undo:before {
  content: "\e050";
}
.icon-action-redo:before {
  content: "\e051";
}
.icon-wrench:before {
  content: "\e052";
}
.icon-umbrella:before {
  content: "\e053";
}
.icon-trash:before {
  content: "\e054";
}
.icon-tag:before {
  content: "\e055";
}
.icon-support:before {
  content: "\e056";
}
.icon-frame:before {
  content: "\e038";
}
.icon-size-fullscreen:before {
  content: "\e057";
}
.icon-size-actual:before {
  content: "\e058";
}
.icon-shuffle:before {
  content: "\e059";
}
.icon-share-alt:before {
  content: "\e05a";
}
.icon-share:before {
  content: "\e05b";
}
.icon-rocket:before {
  content: "\e05c";
}
.icon-question:before {
  content: "\e05d";
}
.icon-pie-chart:before {
  content: "\e05e";
}
.icon-pencil:before {
  content: "\e05f";
}
.icon-note:before {
  content: "\e060";
}
.icon-loop:before {
  content: "\e064";
}
.icon-home:before {
  content: "\e069";
}
.icon-grid:before {
  content: "\e06a";
}
.icon-graph:before {
  content: "\e06b";
}
.icon-microphone:before {
  content: "\e063";
}
.icon-music-tone-alt:before {
  content: "\e061";
}
.icon-music-tone:before {
  content: "\e062";
}
.icon-earphones-alt:before {
  content: "\e03c";
}
.icon-earphones:before {
  content: "\e03d";
}
.icon-equalizer:before {
  content: "\e06c";
}
.icon-like:before {
  content: "\e068";
}
.icon-dislike:before {
  content: "\e06d";
}
.icon-control-start:before {
  content: "\e06f";
}
.icon-control-rewind:before {
  content: "\e070";
}
.icon-control-play:before {
  content: "\e071";
}
.icon-control-pause:before {
  content: "\e072";
}
.icon-control-forward:before {
  content: "\e073";
}
.icon-control-end:before {
  content: "\e074";
}
.icon-volume-1:before {
  content: "\e09f";
}
.icon-volume-2:before {
  content: "\e0a0";
}
.icon-volume-off:before {
  content: "\e0a1";
}
.icon-calendar:before {
  content: "\e075";
}
.icon-bulb:before {
  content: "\e076";
}
.icon-chart:before {
  content: "\e077";
}
.icon-ban:before {
  content: "\e07c";
}
.icon-bubble:before {
  content: "\e07d";
}
.icon-camrecorder:before {
  content: "\e07e";
}
.icon-camera:before {
  content: "\e07f";
}
.icon-cloud-download:before {
  content: "\e083";
}
.icon-cloud-upload:before {
  content: "\e084";
}
.icon-envelope:before {
  content: "\e086";
}
.icon-eye:before {
  content: "\e087";
}
.icon-flag:before {
  content: "\e088";
}
.icon-heart:before {
  content: "\e08a";
}
.icon-info:before {
  content: "\e08b";
}
.icon-key:before {
  content: "\e08c";
}
.icon-link:before {
  content: "\e08d";
}
.icon-lock:before {
  content: "\e08e";
}
.icon-lock-open:before {
  content: "\e08f";
}
.icon-magnifier:before {
  content: "\e090";
}
.icon-magnifier-add:before {
  content: "\e091";
}
.icon-magnifier-remove:before {
  content: "\e092";
}
.icon-paper-clip:before {
  content: "\e093";
}
.icon-paper-plane:before {
  content: "\e094";
}
.icon-power:before {
  content: "\e097";
}
.icon-refresh:before {
  content: "\e098";
}
.icon-reload:before {
  content: "\e099";
}
.icon-settings:before {
  content: "\e09a";
}
.icon-star:before {
  content: "\e09b";
}
.icon-symbol-female:before {
  content: "\e09c";
}
.icon-symbol-male:before {
  content: "\e09d";
}
.icon-target:before {
  content: "\e09e";
}
.icon-credit-card:before {
  content: "\e025";
}
.icon-paypal:before {
  content: "\e608";
}
.icon-social-tumblr:before {
  content: "\e00a";
}
.icon-social-twitter:before {
  content: "\e009";
}
.icon-social-facebook:before {
  content: "\e00b";
}
.icon-social-instagram:before {
  content: "\e609";
}
.icon-social-linkedin:before {
  content: "\e60a";
}
.icon-social-pinterest:before {
  content: "\e60b";
}
.icon-social-github:before {
  content: "\e60c";
}
.icon-social-google:before {
  content: "\e60d";
}
.icon-social-reddit:before {
  content: "\e60e";
}
.icon-social-skype:before {
  content: "\e60f";
}
.icon-social-dribbble:before {
  content: "\e00d";
}
.icon-social-behance:before {
  content: "\e610";
}
.icon-social-foursqare:before {
  content: "\e611";
}
.icon-social-soundcloud:before {
  content: "\e612";
}
.icon-social-spotify:before {
  content: "\e613";
}
.icon-social-stumbleupon:before {
  content: "\e614";
}
.icon-social-youtube:before {
  content: "\e008";
}
.icon-social-dropbox:before {
  content: "\e00c";
}
.icon-social-vkontakte:before {
  content: "\e618";
}
.icon-social-steam:before {
  content: "\e620";
}

/*!********************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[0].use[1]!./node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[0].use[2]!./node_modules/font-awesome/css/font-awesome.min.css ***!
  \********************************************************************************************************************************************************************************************************/
/*!
 *  Font Awesome 4.7.0 by @davegandy - http://fontawesome.io - @fontawesome
 *  License - http://fontawesome.io/license (Font: SIL OFL 1.1, CSS: MIT License)
 */@font-face{font-family:'FontAwesome';src:url(/projects/rde/templates/frontend/dist/fonts/fontawesome-webfont.eot);src:url(/projects/rde/templates/frontend/dist/fonts/fontawesome-webfont.eot?#iefix&v=4.7.0) format('embedded-opentype'),url(/projects/rde/templates/frontend/dist/fonts/fontawesome-webfont.woff2) format('woff2'),url(/projects/rde/templates/frontend/dist/fonts/fontawesome-webfont.woff) format('woff'),url(/projects/rde/templates/frontend/dist/fonts/fontawesome-webfont.ttf) format('truetype'),url(/projects/rde/templates/frontend/dist/0e266bf6f9d07469ad71.svg?v=4.7.0#fontawesomeregular) format('svg');font-weight:normal;font-style:normal}.fa{display:inline-block;font:normal normal normal 14px/1 FontAwesome;font-size:inherit;text-rendering:auto;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.fa-lg{font-size:1.33333333em;line-height:.75em;vertical-align:-15%}.fa-2x{font-size:2em}.fa-3x{font-size:3em}.fa-4x{font-size:4em}.fa-5x{font-size:5em}.fa-fw{width:1.28571429em;text-align:center}.fa-ul{padding-left:0;margin-left:2.14285714em;list-style-type:none}.fa-ul>li{position:relative}.fa-li{position:absolute;left:-2.14285714em;width:2.14285714em;top:.14285714em;text-align:center}.fa-li.fa-lg{left:-1.85714286em}.fa-border{padding:.2em .25em .15em;border:solid .08em #eee;border-radius:.1em}.fa-pull-left{float:left}.fa-pull-right{float:right}.fa.fa-pull-left{margin-right:.3em}.fa.fa-pull-right{margin-left:.3em}.pull-right{float:right}.pull-left{float:left}.fa.pull-left{margin-right:.3em}.fa.pull-right{margin-left:.3em}.fa-spin{-webkit-animation:fa-spin 2s infinite linear;animation:fa-spin 2s infinite linear}.fa-pulse{-webkit-animation:fa-spin 1s infinite steps(8);animation:fa-spin 1s infinite steps(8)}@-webkit-keyframes fa-spin{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(359deg);transform:rotate(359deg)}}@keyframes fa-spin{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(359deg);transform:rotate(359deg)}}.fa-rotate-90{-ms-filter:"progid:DXImageTransform.Microsoft.BasicImage(rotation=1)";-webkit-transform:rotate(90deg);-ms-transform:rotate(90deg);transform:rotate(90deg)}.fa-rotate-180{-ms-filter:"progid:DXImageTransform.Microsoft.BasicImage(rotation=2)";-webkit-transform:rotate(180deg);-ms-transform:rotate(180deg);transform:rotate(180deg)}.fa-rotate-270{-ms-filter:"progid:DXImageTransform.Microsoft.BasicImage(rotation=3)";-webkit-transform:rotate(270deg);-ms-transform:rotate(270deg);transform:rotate(270deg)}.fa-flip-horizontal{-ms-filter:"progid:DXImageTransform.Microsoft.BasicImage(rotation=0, mirror=1)";-webkit-transform:scale(-1, 1);-ms-transform:scale(-1, 1);transform:scale(-1, 1)}.fa-flip-vertical{-ms-filter:"progid:DXImageTransform.Microsoft.BasicImage(rotation=2, mirror=1)";-webkit-transform:scale(1, -1);-ms-transform:scale(1, -1);transform:scale(1, -1)}:root .fa-rotate-90,:root .fa-rotate-180,:root .fa-rotate-270,:root .fa-flip-horizontal,:root .fa-flip-vertical{filter:none}.fa-stack{position:relative;display:inline-block;width:2em;height:2em;line-height:2em;vertical-align:middle}.fa-stack-1x,.fa-stack-2x{position:absolute;left:0;width:100%;text-align:center}.fa-stack-1x{line-height:inherit}.fa-stack-2x{font-size:2em}.fa-inverse{color:#fff}.fa-glass:before{content:"\f000"}.fa-music:before{content:"\f001"}.fa-search:before{content:"\f002"}.fa-envelope-o:before{content:"\f003"}.fa-heart:before{content:"\f004"}.fa-star:before{content:"\f005"}.fa-star-o:before{content:"\f006"}.fa-user:before{content:"\f007"}.fa-film:before{content:"\f008"}.fa-th-large:before{content:"\f009"}.fa-th:before{content:"\f00a"}.fa-th-list:before{content:"\f00b"}.fa-check:before{content:"\f00c"}.fa-remove:before,.fa-close:before,.fa-times:before{content:"\f00d"}.fa-search-plus:before{content:"\f00e"}.fa-search-minus:before{content:"\f010"}.fa-power-off:before{content:"\f011"}.fa-signal:before{content:"\f012"}.fa-gear:before,.fa-cog:before{content:"\f013"}.fa-trash-o:before{content:"\f014"}.fa-home:before{content:"\f015"}.fa-file-o:before{content:"\f016"}.fa-clock-o:before{content:"\f017"}.fa-road:before{content:"\f018"}.fa-download:before{content:"\f019"}.fa-arrow-circle-o-down:before{content:"\f01a"}.fa-arrow-circle-o-up:before{content:"\f01b"}.fa-inbox:before{content:"\f01c"}.fa-play-circle-o:before{content:"\f01d"}.fa-rotate-right:before,.fa-repeat:before{content:"\f01e"}.fa-refresh:before{content:"\f021"}.fa-list-alt:before{content:"\f022"}.fa-lock:before{content:"\f023"}.fa-flag:before{content:"\f024"}.fa-headphones:before{content:"\f025"}.fa-volume-off:before{content:"\f026"}.fa-volume-down:before{content:"\f027"}.fa-volume-up:before{content:"\f028"}.fa-qrcode:before{content:"\f029"}.fa-barcode:before{content:"\f02a"}.fa-tag:before{content:"\f02b"}.fa-tags:before{content:"\f02c"}.fa-book:before{content:"\f02d"}.fa-bookmark:before{content:"\f02e"}.fa-print:before{content:"\f02f"}.fa-camera:before{content:"\f030"}.fa-font:before{content:"\f031"}.fa-bold:before{content:"\f032"}.fa-italic:before{content:"\f033"}.fa-text-height:before{content:"\f034"}.fa-text-width:before{content:"\f035"}.fa-align-left:before{content:"\f036"}.fa-align-center:before{content:"\f037"}.fa-align-right:before{content:"\f038"}.fa-align-justify:before{content:"\f039"}.fa-list:before{content:"\f03a"}.fa-dedent:before,.fa-outdent:before{content:"\f03b"}.fa-indent:before{content:"\f03c"}.fa-video-camera:before{content:"\f03d"}.fa-photo:before,.fa-image:before,.fa-picture-o:before{content:"\f03e"}.fa-pencil:before{content:"\f040"}.fa-map-marker:before{content:"\f041"}.fa-adjust:before{content:"\f042"}.fa-tint:before{content:"\f043"}.fa-edit:before,.fa-pencil-square-o:before{content:"\f044"}.fa-share-square-o:before{content:"\f045"}.fa-check-square-o:before{content:"\f046"}.fa-arrows:before{content:"\f047"}.fa-step-backward:before{content:"\f048"}.fa-fast-backward:before{content:"\f049"}.fa-backward:before{content:"\f04a"}.fa-play:before{content:"\f04b"}.fa-pause:before{content:"\f04c"}.fa-stop:before{content:"\f04d"}.fa-forward:before{content:"\f04e"}.fa-fast-forward:before{content:"\f050"}.fa-step-forward:before{content:"\f051"}.fa-eject:before{content:"\f052"}.fa-chevron-left:before{content:"\f053"}.fa-chevron-right:before{content:"\f054"}.fa-plus-circle:before{content:"\f055"}.fa-minus-circle:before{content:"\f056"}.fa-times-circle:before{content:"\f057"}.fa-check-circle:before{content:"\f058"}.fa-question-circle:before{content:"\f059"}.fa-info-circle:before{content:"\f05a"}.fa-crosshairs:before{content:"\f05b"}.fa-times-circle-o:before{content:"\f05c"}.fa-check-circle-o:before{content:"\f05d"}.fa-ban:before{content:"\f05e"}.fa-arrow-left:before{content:"\f060"}.fa-arrow-right:before{content:"\f061"}.fa-arrow-up:before{content:"\f062"}.fa-arrow-down:before{content:"\f063"}.fa-mail-forward:before,.fa-share:before{content:"\f064"}.fa-expand:before{content:"\f065"}.fa-compress:before{content:"\f066"}.fa-plus:before{content:"\f067"}.fa-minus:before{content:"\f068"}.fa-asterisk:before{content:"\f069"}.fa-exclamation-circle:before{content:"\f06a"}.fa-gift:before{content:"\f06b"}.fa-leaf:before{content:"\f06c"}.fa-fire:before{content:"\f06d"}.fa-eye:before{content:"\f06e"}.fa-eye-slash:before{content:"\f070"}.fa-warning:before,.fa-exclamation-triangle:before{content:"\f071"}.fa-plane:before{content:"\f072"}.fa-calendar:before{content:"\f073"}.fa-random:before{content:"\f074"}.fa-comment:before{content:"\f075"}.fa-magnet:before{content:"\f076"}.fa-chevron-up:before{content:"\f077"}.fa-chevron-down:before{content:"\f078"}.fa-retweet:before{content:"\f079"}.fa-shopping-cart:before{content:"\f07a"}.fa-folder:before{content:"\f07b"}.fa-folder-open:before{content:"\f07c"}.fa-arrows-v:before{content:"\f07d"}.fa-arrows-h:before{content:"\f07e"}.fa-bar-chart-o:before,.fa-bar-chart:before{content:"\f080"}.fa-twitter-square:before{content:"\f081"}.fa-facebook-square:before{content:"\f082"}.fa-camera-retro:before{content:"\f083"}.fa-key:before{content:"\f084"}.fa-gears:before,.fa-cogs:before{content:"\f085"}.fa-comments:before{content:"\f086"}.fa-thumbs-o-up:before{content:"\f087"}.fa-thumbs-o-down:before{content:"\f088"}.fa-star-half:before{content:"\f089"}.fa-heart-o:before{content:"\f08a"}.fa-sign-out:before{content:"\f08b"}.fa-linkedin-square:before{content:"\f08c"}.fa-thumb-tack:before{content:"\f08d"}.fa-external-link:before{content:"\f08e"}.fa-sign-in:before{content:"\f090"}.fa-trophy:before{content:"\f091"}.fa-github-square:before{content:"\f092"}.fa-upload:before{content:"\f093"}.fa-lemon-o:before{content:"\f094"}.fa-phone:before{content:"\f095"}.fa-square-o:before{content:"\f096"}.fa-bookmark-o:before{content:"\f097"}.fa-phone-square:before{content:"\f098"}.fa-twitter:before{content:"\f099"}.fa-facebook-f:before,.fa-facebook:before{content:"\f09a"}.fa-github:before{content:"\f09b"}.fa-unlock:before{content:"\f09c"}.fa-credit-card:before{content:"\f09d"}.fa-feed:before,.fa-rss:before{content:"\f09e"}.fa-hdd-o:before{content:"\f0a0"}.fa-bullhorn:before{content:"\f0a1"}.fa-bell:before{content:"\f0f3"}.fa-certificate:before{content:"\f0a3"}.fa-hand-o-right:before{content:"\f0a4"}.fa-hand-o-left:before{content:"\f0a5"}.fa-hand-o-up:before{content:"\f0a6"}.fa-hand-o-down:before{content:"\f0a7"}.fa-arrow-circle-left:before{content:"\f0a8"}.fa-arrow-circle-right:before{content:"\f0a9"}.fa-arrow-circle-up:before{content:"\f0aa"}.fa-arrow-circle-down:before{content:"\f0ab"}.fa-globe:before{content:"\f0ac"}.fa-wrench:before{content:"\f0ad"}.fa-tasks:before{content:"\f0ae"}.fa-filter:before{content:"\f0b0"}.fa-briefcase:before{content:"\f0b1"}.fa-arrows-alt:before{content:"\f0b2"}.fa-group:before,.fa-users:before{content:"\f0c0"}.fa-chain:before,.fa-link:before{content:"\f0c1"}.fa-cloud:before{content:"\f0c2"}.fa-flask:before{content:"\f0c3"}.fa-cut:before,.fa-scissors:before{content:"\f0c4"}.fa-copy:before,.fa-files-o:before{content:"\f0c5"}.fa-paperclip:before{content:"\f0c6"}.fa-save:before,.fa-floppy-o:before{content:"\f0c7"}.fa-square:before{content:"\f0c8"}.fa-navicon:before,.fa-reorder:before,.fa-bars:before{content:"\f0c9"}.fa-list-ul:before{content:"\f0ca"}.fa-list-ol:before{content:"\f0cb"}.fa-strikethrough:before{content:"\f0cc"}.fa-underline:before{content:"\f0cd"}.fa-table:before{content:"\f0ce"}.fa-magic:before{content:"\f0d0"}.fa-truck:before{content:"\f0d1"}.fa-pinterest:before{content:"\f0d2"}.fa-pinterest-square:before{content:"\f0d3"}.fa-google-plus-square:before{content:"\f0d4"}.fa-google-plus:before{content:"\f0d5"}.fa-money:before{content:"\f0d6"}.fa-caret-down:before{content:"\f0d7"}.fa-caret-up:before{content:"\f0d8"}.fa-caret-left:before{content:"\f0d9"}.fa-caret-right:before{content:"\f0da"}.fa-columns:before{content:"\f0db"}.fa-unsorted:before,.fa-sort:before{content:"\f0dc"}.fa-sort-down:before,.fa-sort-desc:before{content:"\f0dd"}.fa-sort-up:before,.fa-sort-asc:before{content:"\f0de"}.fa-envelope:before{content:"\f0e0"}.fa-linkedin:before{content:"\f0e1"}.fa-rotate-left:before,.fa-undo:before{content:"\f0e2"}.fa-legal:before,.fa-gavel:before{content:"\f0e3"}.fa-dashboard:before,.fa-tachometer:before{content:"\f0e4"}.fa-comment-o:before{content:"\f0e5"}.fa-comments-o:before{content:"\f0e6"}.fa-flash:before,.fa-bolt:before{content:"\f0e7"}.fa-sitemap:before{content:"\f0e8"}.fa-umbrella:before{content:"\f0e9"}.fa-paste:before,.fa-clipboard:before{content:"\f0ea"}.fa-lightbulb-o:before{content:"\f0eb"}.fa-exchange:before{content:"\f0ec"}.fa-cloud-download:before{content:"\f0ed"}.fa-cloud-upload:before{content:"\f0ee"}.fa-user-md:before{content:"\f0f0"}.fa-stethoscope:before{content:"\f0f1"}.fa-suitcase:before{content:"\f0f2"}.fa-bell-o:before{content:"\f0a2"}.fa-coffee:before{content:"\f0f4"}.fa-cutlery:before{content:"\f0f5"}.fa-file-text-o:before{content:"\f0f6"}.fa-building-o:before{content:"\f0f7"}.fa-hospital-o:before{content:"\f0f8"}.fa-ambulance:before{content:"\f0f9"}.fa-medkit:before{content:"\f0fa"}.fa-fighter-jet:before{content:"\f0fb"}.fa-beer:before{content:"\f0fc"}.fa-h-square:before{content:"\f0fd"}.fa-plus-square:before{content:"\f0fe"}.fa-angle-double-left:before{content:"\f100"}.fa-angle-double-right:before{content:"\f101"}.fa-angle-double-up:before{content:"\f102"}.fa-angle-double-down:before{content:"\f103"}.fa-angle-left:before{content:"\f104"}.fa-angle-right:before{content:"\f105"}.fa-angle-up:before{content:"\f106"}.fa-angle-down:before{content:"\f107"}.fa-desktop:before{content:"\f108"}.fa-laptop:before{content:"\f109"}.fa-tablet:before{content:"\f10a"}.fa-mobile-phone:before,.fa-mobile:before{content:"\f10b"}.fa-circle-o:before{content:"\f10c"}.fa-quote-left:before{content:"\f10d"}.fa-quote-right:before{content:"\f10e"}.fa-spinner:before{content:"\f110"}.fa-circle:before{content:"\f111"}.fa-mail-reply:before,.fa-reply:before{content:"\f112"}.fa-github-alt:before{content:"\f113"}.fa-folder-o:before{content:"\f114"}.fa-folder-open-o:before{content:"\f115"}.fa-smile-o:before{content:"\f118"}.fa-frown-o:before{content:"\f119"}.fa-meh-o:before{content:"\f11a"}.fa-gamepad:before{content:"\f11b"}.fa-keyboard-o:before{content:"\f11c"}.fa-flag-o:before{content:"\f11d"}.fa-flag-checkered:before{content:"\f11e"}.fa-terminal:before{content:"\f120"}.fa-code:before{content:"\f121"}.fa-mail-reply-all:before,.fa-reply-all:before{content:"\f122"}.fa-star-half-empty:before,.fa-star-half-full:before,.fa-star-half-o:before{content:"\f123"}.fa-location-arrow:before{content:"\f124"}.fa-crop:before{content:"\f125"}.fa-code-fork:before{content:"\f126"}.fa-unlink:before,.fa-chain-broken:before{content:"\f127"}.fa-question:before{content:"\f128"}.fa-info:before{content:"\f129"}.fa-exclamation:before{content:"\f12a"}.fa-superscript:before{content:"\f12b"}.fa-subscript:before{content:"\f12c"}.fa-eraser:before{content:"\f12d"}.fa-puzzle-piece:before{content:"\f12e"}.fa-microphone:before{content:"\f130"}.fa-microphone-slash:before{content:"\f131"}.fa-shield:before{content:"\f132"}.fa-calendar-o:before{content:"\f133"}.fa-fire-extinguisher:before{content:"\f134"}.fa-rocket:before{content:"\f135"}.fa-maxcdn:before{content:"\f136"}.fa-chevron-circle-left:before{content:"\f137"}.fa-chevron-circle-right:before{content:"\f138"}.fa-chevron-circle-up:before{content:"\f139"}.fa-chevron-circle-down:before{content:"\f13a"}.fa-html5:before{content:"\f13b"}.fa-css3:before{content:"\f13c"}.fa-anchor:before{content:"\f13d"}.fa-unlock-alt:before{content:"\f13e"}.fa-bullseye:before{content:"\f140"}.fa-ellipsis-h:before{content:"\f141"}.fa-ellipsis-v:before{content:"\f142"}.fa-rss-square:before{content:"\f143"}.fa-play-circle:before{content:"\f144"}.fa-ticket:before{content:"\f145"}.fa-minus-square:before{content:"\f146"}.fa-minus-square-o:before{content:"\f147"}.fa-level-up:before{content:"\f148"}.fa-level-down:before{content:"\f149"}.fa-check-square:before{content:"\f14a"}.fa-pencil-square:before{content:"\f14b"}.fa-external-link-square:before{content:"\f14c"}.fa-share-square:before{content:"\f14d"}.fa-compass:before{content:"\f14e"}.fa-toggle-down:before,.fa-caret-square-o-down:before{content:"\f150"}.fa-toggle-up:before,.fa-caret-square-o-up:before{content:"\f151"}.fa-toggle-right:before,.fa-caret-square-o-right:before{content:"\f152"}.fa-euro:before,.fa-eur:before{content:"\f153"}.fa-gbp:before{content:"\f154"}.fa-dollar:before,.fa-usd:before{content:"\f155"}.fa-rupee:before,.fa-inr:before{content:"\f156"}.fa-cny:before,.fa-rmb:before,.fa-yen:before,.fa-jpy:before{content:"\f157"}.fa-ruble:before,.fa-rouble:before,.fa-rub:before{content:"\f158"}.fa-won:before,.fa-krw:before{content:"\f159"}.fa-bitcoin:before,.fa-btc:before{content:"\f15a"}.fa-file:before{content:"\f15b"}.fa-file-text:before{content:"\f15c"}.fa-sort-alpha-asc:before{content:"\f15d"}.fa-sort-alpha-desc:before{content:"\f15e"}.fa-sort-amount-asc:before{content:"\f160"}.fa-sort-amount-desc:before{content:"\f161"}.fa-sort-numeric-asc:before{content:"\f162"}.fa-sort-numeric-desc:before{content:"\f163"}.fa-thumbs-up:before{content:"\f164"}.fa-thumbs-down:before{content:"\f165"}.fa-youtube-square:before{content:"\f166"}.fa-youtube:before{content:"\f167"}.fa-xing:before{content:"\f168"}.fa-xing-square:before{content:"\f169"}.fa-youtube-play:before{content:"\f16a"}.fa-dropbox:before{content:"\f16b"}.fa-stack-overflow:before{content:"\f16c"}.fa-instagram:before{content:"\f16d"}.fa-flickr:before{content:"\f16e"}.fa-adn:before{content:"\f170"}.fa-bitbucket:before{content:"\f171"}.fa-bitbucket-square:before{content:"\f172"}.fa-tumblr:before{content:"\f173"}.fa-tumblr-square:before{content:"\f174"}.fa-long-arrow-down:before{content:"\f175"}.fa-long-arrow-up:before{content:"\f176"}.fa-long-arrow-left:before{content:"\f177"}.fa-long-arrow-right:before{content:"\f178"}.fa-apple:before{content:"\f179"}.fa-windows:before{content:"\f17a"}.fa-android:before{content:"\f17b"}.fa-linux:before{content:"\f17c"}.fa-dribbble:before{content:"\f17d"}.fa-skype:before{content:"\f17e"}.fa-foursquare:before{content:"\f180"}.fa-trello:before{content:"\f181"}.fa-female:before{content:"\f182"}.fa-male:before{content:"\f183"}.fa-gittip:before,.fa-gratipay:before{content:"\f184"}.fa-sun-o:before{content:"\f185"}.fa-moon-o:before{content:"\f186"}.fa-archive:before{content:"\f187"}.fa-bug:before{content:"\f188"}.fa-vk:before{content:"\f189"}.fa-weibo:before{content:"\f18a"}.fa-renren:before{content:"\f18b"}.fa-pagelines:before{content:"\f18c"}.fa-stack-exchange:before{content:"\f18d"}.fa-arrow-circle-o-right:before{content:"\f18e"}.fa-arrow-circle-o-left:before{content:"\f190"}.fa-toggle-left:before,.fa-caret-square-o-left:before{content:"\f191"}.fa-dot-circle-o:before{content:"\f192"}.fa-wheelchair:before{content:"\f193"}.fa-vimeo-square:before{content:"\f194"}.fa-turkish-lira:before,.fa-try:before{content:"\f195"}.fa-plus-square-o:before{content:"\f196"}.fa-space-shuttle:before{content:"\f197"}.fa-slack:before{content:"\f198"}.fa-envelope-square:before{content:"\f199"}.fa-wordpress:before{content:"\f19a"}.fa-openid:before{content:"\f19b"}.fa-institution:before,.fa-bank:before,.fa-university:before{content:"\f19c"}.fa-mortar-board:before,.fa-graduation-cap:before{content:"\f19d"}.fa-yahoo:before{content:"\f19e"}.fa-google:before{content:"\f1a0"}.fa-reddit:before{content:"\f1a1"}.fa-reddit-square:before{content:"\f1a2"}.fa-stumbleupon-circle:before{content:"\f1a3"}.fa-stumbleupon:before{content:"\f1a4"}.fa-delicious:before{content:"\f1a5"}.fa-digg:before{content:"\f1a6"}.fa-pied-piper-pp:before{content:"\f1a7"}.fa-pied-piper-alt:before{content:"\f1a8"}.fa-drupal:before{content:"\f1a9"}.fa-joomla:before{content:"\f1aa"}.fa-language:before{content:"\f1ab"}.fa-fax:before{content:"\f1ac"}.fa-building:before{content:"\f1ad"}.fa-child:before{content:"\f1ae"}.fa-paw:before{content:"\f1b0"}.fa-spoon:before{content:"\f1b1"}.fa-cube:before{content:"\f1b2"}.fa-cubes:before{content:"\f1b3"}.fa-behance:before{content:"\f1b4"}.fa-behance-square:before{content:"\f1b5"}.fa-steam:before{content:"\f1b6"}.fa-steam-square:before{content:"\f1b7"}.fa-recycle:before{content:"\f1b8"}.fa-automobile:before,.fa-car:before{content:"\f1b9"}.fa-cab:before,.fa-taxi:before{content:"\f1ba"}.fa-tree:before{content:"\f1bb"}.fa-spotify:before{content:"\f1bc"}.fa-deviantart:before{content:"\f1bd"}.fa-soundcloud:before{content:"\f1be"}.fa-database:before{content:"\f1c0"}.fa-file-pdf-o:before{content:"\f1c1"}.fa-file-word-o:before{content:"\f1c2"}.fa-file-excel-o:before{content:"\f1c3"}.fa-file-powerpoint-o:before{content:"\f1c4"}.fa-file-photo-o:before,.fa-file-picture-o:before,.fa-file-image-o:before{content:"\f1c5"}.fa-file-zip-o:before,.fa-file-archive-o:before{content:"\f1c6"}.fa-file-sound-o:before,.fa-file-audio-o:before{content:"\f1c7"}.fa-file-movie-o:before,.fa-file-video-o:before{content:"\f1c8"}.fa-file-code-o:before{content:"\f1c9"}.fa-vine:before{content:"\f1ca"}.fa-codepen:before{content:"\f1cb"}.fa-jsfiddle:before{content:"\f1cc"}.fa-life-bouy:before,.fa-life-buoy:before,.fa-life-saver:before,.fa-support:before,.fa-life-ring:before{content:"\f1cd"}.fa-circle-o-notch:before{content:"\f1ce"}.fa-ra:before,.fa-resistance:before,.fa-rebel:before{content:"\f1d0"}.fa-ge:before,.fa-empire:before{content:"\f1d1"}.fa-git-square:before{content:"\f1d2"}.fa-git:before{content:"\f1d3"}.fa-y-combinator-square:before,.fa-yc-square:before,.fa-hacker-news:before{content:"\f1d4"}.fa-tencent-weibo:before{content:"\f1d5"}.fa-qq:before{content:"\f1d6"}.fa-wechat:before,.fa-weixin:before{content:"\f1d7"}.fa-send:before,.fa-paper-plane:before{content:"\f1d8"}.fa-send-o:before,.fa-paper-plane-o:before{content:"\f1d9"}.fa-history:before{content:"\f1da"}.fa-circle-thin:before{content:"\f1db"}.fa-header:before{content:"\f1dc"}.fa-paragraph:before{content:"\f1dd"}.fa-sliders:before{content:"\f1de"}.fa-share-alt:before{content:"\f1e0"}.fa-share-alt-square:before{content:"\f1e1"}.fa-bomb:before{content:"\f1e2"}.fa-soccer-ball-o:before,.fa-futbol-o:before{content:"\f1e3"}.fa-tty:before{content:"\f1e4"}.fa-binoculars:before{content:"\f1e5"}.fa-plug:before{content:"\f1e6"}.fa-slideshare:before{content:"\f1e7"}.fa-twitch:before{content:"\f1e8"}.fa-yelp:before{content:"\f1e9"}.fa-newspaper-o:before{content:"\f1ea"}.fa-wifi:before{content:"\f1eb"}.fa-calculator:before{content:"\f1ec"}.fa-paypal:before{content:"\f1ed"}.fa-google-wallet:before{content:"\f1ee"}.fa-cc-visa:before{content:"\f1f0"}.fa-cc-mastercard:before{content:"\f1f1"}.fa-cc-discover:before{content:"\f1f2"}.fa-cc-amex:before{content:"\f1f3"}.fa-cc-paypal:before{content:"\f1f4"}.fa-cc-stripe:before{content:"\f1f5"}.fa-bell-slash:before{content:"\f1f6"}.fa-bell-slash-o:before{content:"\f1f7"}.fa-trash:before{content:"\f1f8"}.fa-copyright:before{content:"\f1f9"}.fa-at:before{content:"\f1fa"}.fa-eyedropper:before{content:"\f1fb"}.fa-paint-brush:before{content:"\f1fc"}.fa-birthday-cake:before{content:"\f1fd"}.fa-area-chart:before{content:"\f1fe"}.fa-pie-chart:before{content:"\f200"}.fa-line-chart:before{content:"\f201"}.fa-lastfm:before{content:"\f202"}.fa-lastfm-square:before{content:"\f203"}.fa-toggle-off:before{content:"\f204"}.fa-toggle-on:before{content:"\f205"}.fa-bicycle:before{content:"\f206"}.fa-bus:before{content:"\f207"}.fa-ioxhost:before{content:"\f208"}.fa-angellist:before{content:"\f209"}.fa-cc:before{content:"\f20a"}.fa-shekel:before,.fa-sheqel:before,.fa-ils:before{content:"\f20b"}.fa-meanpath:before{content:"\f20c"}.fa-buysellads:before{content:"\f20d"}.fa-connectdevelop:before{content:"\f20e"}.fa-dashcube:before{content:"\f210"}.fa-forumbee:before{content:"\f211"}.fa-leanpub:before{content:"\f212"}.fa-sellsy:before{content:"\f213"}.fa-shirtsinbulk:before{content:"\f214"}.fa-simplybuilt:before{content:"\f215"}.fa-skyatlas:before{content:"\f216"}.fa-cart-plus:before{content:"\f217"}.fa-cart-arrow-down:before{content:"\f218"}.fa-diamond:before{content:"\f219"}.fa-ship:before{content:"\f21a"}.fa-user-secret:before{content:"\f21b"}.fa-motorcycle:before{content:"\f21c"}.fa-street-view:before{content:"\f21d"}.fa-heartbeat:before{content:"\f21e"}.fa-venus:before{content:"\f221"}.fa-mars:before{content:"\f222"}.fa-mercury:before{content:"\f223"}.fa-intersex:before,.fa-transgender:before{content:"\f224"}.fa-transgender-alt:before{content:"\f225"}.fa-venus-double:before{content:"\f226"}.fa-mars-double:before{content:"\f227"}.fa-venus-mars:before{content:"\f228"}.fa-mars-stroke:before{content:"\f229"}.fa-mars-stroke-v:before{content:"\f22a"}.fa-mars-stroke-h:before{content:"\f22b"}.fa-neuter:before{content:"\f22c"}.fa-genderless:before{content:"\f22d"}.fa-facebook-official:before{content:"\f230"}.fa-pinterest-p:before{content:"\f231"}.fa-whatsapp:before{content:"\f232"}.fa-server:before{content:"\f233"}.fa-user-plus:before{content:"\f234"}.fa-user-times:before{content:"\f235"}.fa-hotel:before,.fa-bed:before{content:"\f236"}.fa-viacoin:before{content:"\f237"}.fa-train:before{content:"\f238"}.fa-subway:before{content:"\f239"}.fa-medium:before{content:"\f23a"}.fa-yc:before,.fa-y-combinator:before{content:"\f23b"}.fa-optin-monster:before{content:"\f23c"}.fa-opencart:before{content:"\f23d"}.fa-expeditedssl:before{content:"\f23e"}.fa-battery-4:before,.fa-battery:before,.fa-battery-full:before{content:"\f240"}.fa-battery-3:before,.fa-battery-three-quarters:before{content:"\f241"}.fa-battery-2:before,.fa-battery-half:before{content:"\f242"}.fa-battery-1:before,.fa-battery-quarter:before{content:"\f243"}.fa-battery-0:before,.fa-battery-empty:before{content:"\f244"}.fa-mouse-pointer:before{content:"\f245"}.fa-i-cursor:before{content:"\f246"}.fa-object-group:before{content:"\f247"}.fa-object-ungroup:before{content:"\f248"}.fa-sticky-note:before{content:"\f249"}.fa-sticky-note-o:before{content:"\f24a"}.fa-cc-jcb:before{content:"\f24b"}.fa-cc-diners-club:before{content:"\f24c"}.fa-clone:before{content:"\f24d"}.fa-balance-scale:before{content:"\f24e"}.fa-hourglass-o:before{content:"\f250"}.fa-hourglass-1:before,.fa-hourglass-start:before{content:"\f251"}.fa-hourglass-2:before,.fa-hourglass-half:before{content:"\f252"}.fa-hourglass-3:before,.fa-hourglass-end:before{content:"\f253"}.fa-hourglass:before{content:"\f254"}.fa-hand-grab-o:before,.fa-hand-rock-o:before{content:"\f255"}.fa-hand-stop-o:before,.fa-hand-paper-o:before{content:"\f256"}.fa-hand-scissors-o:before{content:"\f257"}.fa-hand-lizard-o:before{content:"\f258"}.fa-hand-spock-o:before{content:"\f259"}.fa-hand-pointer-o:before{content:"\f25a"}.fa-hand-peace-o:before{content:"\f25b"}.fa-trademark:before{content:"\f25c"}.fa-registered:before{content:"\f25d"}.fa-creative-commons:before{content:"\f25e"}.fa-gg:before{content:"\f260"}.fa-gg-circle:before{content:"\f261"}.fa-tripadvisor:before{content:"\f262"}.fa-odnoklassniki:before{content:"\f263"}.fa-odnoklassniki-square:before{content:"\f264"}.fa-get-pocket:before{content:"\f265"}.fa-wikipedia-w:before{content:"\f266"}.fa-safari:before{content:"\f267"}.fa-chrome:before{content:"\f268"}.fa-firefox:before{content:"\f269"}.fa-opera:before{content:"\f26a"}.fa-internet-explorer:before{content:"\f26b"}.fa-tv:before,.fa-television:before{content:"\f26c"}.fa-contao:before{content:"\f26d"}.fa-500px:before{content:"\f26e"}.fa-amazon:before{content:"\f270"}.fa-calendar-plus-o:before{content:"\f271"}.fa-calendar-minus-o:before{content:"\f272"}.fa-calendar-times-o:before{content:"\f273"}.fa-calendar-check-o:before{content:"\f274"}.fa-industry:before{content:"\f275"}.fa-map-pin:before{content:"\f276"}.fa-map-signs:before{content:"\f277"}.fa-map-o:before{content:"\f278"}.fa-map:before{content:"\f279"}.fa-commenting:before{content:"\f27a"}.fa-commenting-o:before{content:"\f27b"}.fa-houzz:before{content:"\f27c"}.fa-vimeo:before{content:"\f27d"}.fa-black-tie:before{content:"\f27e"}.fa-fonticons:before{content:"\f280"}.fa-reddit-alien:before{content:"\f281"}.fa-edge:before{content:"\f282"}.fa-credit-card-alt:before{content:"\f283"}.fa-codiepie:before{content:"\f284"}.fa-modx:before{content:"\f285"}.fa-fort-awesome:before{content:"\f286"}.fa-usb:before{content:"\f287"}.fa-product-hunt:before{content:"\f288"}.fa-mixcloud:before{content:"\f289"}.fa-scribd:before{content:"\f28a"}.fa-pause-circle:before{content:"\f28b"}.fa-pause-circle-o:before{content:"\f28c"}.fa-stop-circle:before{content:"\f28d"}.fa-stop-circle-o:before{content:"\f28e"}.fa-shopping-bag:before{content:"\f290"}.fa-shopping-basket:before{content:"\f291"}.fa-hashtag:before{content:"\f292"}.fa-bluetooth:before{content:"\f293"}.fa-bluetooth-b:before{content:"\f294"}.fa-percent:before{content:"\f295"}.fa-gitlab:before{content:"\f296"}.fa-wpbeginner:before{content:"\f297"}.fa-wpforms:before{content:"\f298"}.fa-envira:before{content:"\f299"}.fa-universal-access:before{content:"\f29a"}.fa-wheelchair-alt:before{content:"\f29b"}.fa-question-circle-o:before{content:"\f29c"}.fa-blind:before{content:"\f29d"}.fa-audio-description:before{content:"\f29e"}.fa-volume-control-phone:before{content:"\f2a0"}.fa-braille:before{content:"\f2a1"}.fa-assistive-listening-systems:before{content:"\f2a2"}.fa-asl-interpreting:before,.fa-american-sign-language-interpreting:before{content:"\f2a3"}.fa-deafness:before,.fa-hard-of-hearing:before,.fa-deaf:before{content:"\f2a4"}.fa-glide:before{content:"\f2a5"}.fa-glide-g:before{content:"\f2a6"}.fa-signing:before,.fa-sign-language:before{content:"\f2a7"}.fa-low-vision:before{content:"\f2a8"}.fa-viadeo:before{content:"\f2a9"}.fa-viadeo-square:before{content:"\f2aa"}.fa-snapchat:before{content:"\f2ab"}.fa-snapchat-ghost:before{content:"\f2ac"}.fa-snapchat-square:before{content:"\f2ad"}.fa-pied-piper:before{content:"\f2ae"}.fa-first-order:before{content:"\f2b0"}.fa-yoast:before{content:"\f2b1"}.fa-themeisle:before{content:"\f2b2"}.fa-google-plus-circle:before,.fa-google-plus-official:before{content:"\f2b3"}.fa-fa:before,.fa-font-awesome:before{content:"\f2b4"}.fa-handshake-o:before{content:"\f2b5"}.fa-envelope-open:before{content:"\f2b6"}.fa-envelope-open-o:before{content:"\f2b7"}.fa-linode:before{content:"\f2b8"}.fa-address-book:before{content:"\f2b9"}.fa-address-book-o:before{content:"\f2ba"}.fa-vcard:before,.fa-address-card:before{content:"\f2bb"}.fa-vcard-o:before,.fa-address-card-o:before{content:"\f2bc"}.fa-user-circle:before{content:"\f2bd"}.fa-user-circle-o:before{content:"\f2be"}.fa-user-o:before{content:"\f2c0"}.fa-id-badge:before{content:"\f2c1"}.fa-drivers-license:before,.fa-id-card:before{content:"\f2c2"}.fa-drivers-license-o:before,.fa-id-card-o:before{content:"\f2c3"}.fa-quora:before{content:"\f2c4"}.fa-free-code-camp:before{content:"\f2c5"}.fa-telegram:before{content:"\f2c6"}.fa-thermometer-4:before,.fa-thermometer:before,.fa-thermometer-full:before{content:"\f2c7"}.fa-thermometer-3:before,.fa-thermometer-three-quarters:before{content:"\f2c8"}.fa-thermometer-2:before,.fa-thermometer-half:before{content:"\f2c9"}.fa-thermometer-1:before,.fa-thermometer-quarter:before{content:"\f2ca"}.fa-thermometer-0:before,.fa-thermometer-empty:before{content:"\f2cb"}.fa-shower:before{content:"\f2cc"}.fa-bathtub:before,.fa-s15:before,.fa-bath:before{content:"\f2cd"}.fa-podcast:before{content:"\f2ce"}.fa-window-maximize:before{content:"\f2d0"}.fa-window-minimize:before{content:"\f2d1"}.fa-window-restore:before{content:"\f2d2"}.fa-times-rectangle:before,.fa-window-close:before{content:"\f2d3"}.fa-times-rectangle-o:before,.fa-window-close-o:before{content:"\f2d4"}.fa-bandcamp:before{content:"\f2d5"}.fa-grav:before{content:"\f2d6"}.fa-etsy:before{content:"\f2d7"}.fa-imdb:before{content:"\f2d8"}.fa-ravelry:before{content:"\f2d9"}.fa-eercast:before{content:"\f2da"}.fa-microchip:before{content:"\f2db"}.fa-snowflake-o:before{content:"\f2dc"}.fa-superpowers:before{content:"\f2dd"}.fa-wpexplorer:before{content:"\f2de"}.fa-meetup:before{content:"\f2e0"}.sr-only{position:absolute;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0, 0, 0, 0);border:0}.sr-only-focusable:active,.sr-only-focusable:focus{position:static;width:auto;height:auto;margin:0;overflow:visible;clip:auto}

/*!********************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[0].use[1]!./node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[0].use[2]!../../projects/rde/resources/frontend/style/main.css ***!
  \********************************************************************************************************************************************************************************************************/
html, body, div, span, applet, object, iframe,
h1, h2, h3, h4, h5, h6, p, blockquote, pre,
a, abbr, acronym, address, big, cite, code,
del, dfn, em, font, img, ins, kbd, q, s, samp,
small, strike, strong, sub, sup, tt, var,
b, u, i, center,
dl, dt, dd,
fieldset, form, label, legend,
table, caption, tbody, tfoot, thead, tr, th, td {
  background: transparent;
  border: 0;
  margin: 0;
  padding: 0;
  vertical-align: baseline;
}

html {
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  font-family: sans-serif;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
}

*,
*:before,
*:after {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

*:focus {
  outline: 0 !important;
}

select,
input[type="text"],
input[type="email"],
input[type="password"],
input[type="submit"],
textarea {
  -webkit-appearance: none;
  -webkit-border-radius: 0;
}

body {
  line-height: 1;
}

p {
  margin: 0 0 18px 0;
}

h1, h2, h3, h4, h5, h6 {
  clear: both;
  font-weight: normal;
}

blockquote {
  quotes: none;
}

blockquote:before, blockquote:after {
  content: none;
}

del {
  text-decoration: line-through;
}

/* tables still need 'cellspacing="0"' in the markup */

table {
  border-collapse: collapse;
  border-spacing: 0;
}

img {
  max-width: 100%;
  height: auto;
  vertical-align: top;
}

a img {
  border: none;
}

a {
  text-decoration: none;
  color: #ce000c
}

a.white {
  color: #fff;
}

/*template sheet*/

body {
  font-family: 'Roboto', sans-serif;
  -webkit-font-smoothing: antialiased;
  font-size: 16px;
  line-height: 22px;
  font-weight: 300;
  margin: 0;
  color: #333;
  background: #fff;
  height: 100%;
  overflow-y: scroll
}

.wrap-sm {
  max-width: 800px;
  padding: 0 30px;
  margin: 0 auto;
}

.wrap {
  max-width: 1100px;
  padding: 0 30px;
  margin: 0 auto;
}

.wrap-lg {
  max-width: 1200px;
  padding: 0 30px;
  margin: 0 auto;
}

.container {
  position: relative;
}

/* HEADER */

.header-top {
  padding-top: 20px;
  padding-bottom: 20px;
}

.header-bottom {
  height: 70px;
}

.header .contact {
  padding-top: 15px;
}

.header .contact .text {
  font-size: 18px;
  color: #777777;
  font-weight: 500;
}

.header .action {
  display: inline-block;
}

/* MENU */

.nav > ul {
  margin: 0;
  padding: 0;
  display: flex;
  justify-content: space-between;
}

.nav > ul li {
  list-style: none;
  height: 70px;
  display: block;
}

.nav > ul li a {
  line-height: 70px;
  padding: 0 5px;
  color: #fff;
  font-weight: 400;
  font-size: 18px;
  display: block;
  transition: all 0.3s ease;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
}

.nav > ul li a:hover {
  color: rgba(255, 255, 255, 0.8)
}

.nav > ul li.first-child a {
  padding-left: 0
}

.nav > ul li.first-child a:before {
  font-family: 'simple-line-icons';
  content: "\e069";
  margin-left: 10px
}

.nav > ul li.first-child a span {
  display: none;
}

.nav > ul li.haschild {
  position: relative;
}

.nav > ul li.haschild a {
  -webkit-transition: none;
  -moz-transition: none;
  -o-transition: none;
  transition: none;
}

.nav > ul li.haschild > a:after {
  font-family: 'FontAwesome';
  content: '\f107';
  margin-left: 5px;
}

.nav > ul li.haschild:hover > a {
  color: #ebebeb;
  position: relative;
  z-index: 999;
}

.nav > ul li .child {
  position: absolute;
  top: 100%;
  left: -15px;
  width: 280px;
  padding: 20px 0;
  margin: 0;
  z-index: 100;
  display: none;
  background: #fff;
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.3);
}

.nav > ul li .child li {
  float: none;
  height: 36px;
}

.nav > ul li .child li a {
  font-size: 16px;
  line-height: 36px;
  color: #151515;
  padding: 0 20px;
}

.nav > ul li .child li a:hover {
  color: #999
}

.nav > ul li .child li.current a {
  color: #ce000c
}

/* BANNER */

.banner {
  position: relative;
}

.banner .badge-container {
  position: absolute;
  z-index: 70;
  right: 0;
  bottom: 10%;
}

.badge-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.badge-wrapper .wrap-lg {
  position: relative;
  height: 100%;
}

.banner .badge {
  width: 240px;
  height: 240px;
  border-radius: 100%;
  -moz-border-radius: 100%;
  padding: 94px 0 0 50px;
}

.banner .badge .text {
  font-size: 24px;
  font-weight: bold;
  padding-top: 10px;
}

.banner .badge-sm {
  position: absolute;
  right: -5px;
  top: -5px;
  width: 85px;
  height: 85px;
  line-height: 85px;
  color: #ce000c;
  font-size: 24px;
  text-align: center;
  border-radius: 100%;
  -moz-border-radius: 100%;
}

/* FOOTER */

.footer .widget {
  width: 20%;
  float: left;
}

.footer .widget.first-child {
  width: 40%;
  padding-right: 50px
}

.footer .widget_header h3 {
  font-size: 20px;
  font-weight: bold;
  padding-bottom: 15px;
}

.footer .widget_content ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer .widget_content ul li {
}

.footer-top a {
  color: #444;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  transition: all 0.3s ease;
}

.footer-top a:hover {
  color: #ce000c
}

.footer-bottom ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-bottom ul li {
  display: inline-block;
  padding-left: 20px;
}

/* SCROLLTOTOP */

.scrollToTop {
  width: 50px;
  height: 50px;
  line-height: 50px;
  text-align: center;
  background: rgba(206, 0, 12, 0.8);
  color: #fff;
  text-decoration: none;
  position: fixed;
  z-index: 75;
  bottom: 20px;
  right: 20px;
  display: none;
  cursor: pointer;
  border-radius: 2px;
  -moz-border-radius: 2px;
  -webkit-border-radius: 2px;
  -webkit-transition: all 0.2s ease-out 0s;
  -moz-transition: all 0.2s ease-out 0s;
  -o-transition: all 0.2s ease-out 0s;
  transition: all 0.2s ease-out 0s;
}

.scrollToTop:hover {
  background: rgba(206, 0, 12, 1);
}

/* BREADCRUMB */

.breadcrumb-bar,
.breadcrumb-bar a {
  color: #222;
  font-size: 14px;
}

.breadcrumb-bar ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.breadcrumb-bar ul li {
  display: inline-block;
}

.breadcrumb-bar ul li:after {
  content: '\/';
  padding: 0 3px;
}

.breadcrumb-bar ul li:last-child:after {
  display: none;
}

/* POSTS */

.post {
  position: relative;
  border-bottom: 1px solid #ddd;
  margin: 0 0 20px 0;
  padding: 0 0 50px 0;
}

.post .entry-date {
  color: #999;
  display: inline-block;
  padding: 5px 0 15px 0
}

/* TABLE */

.table {
  width: 100%;
  margin: 0 0 20px 0
}

.table th {
  padding: 5px;
  text-align: left;
}

.table td {
  padding: 5px;
  border-top: 1px solid #ddd
}

/* SPACEERS */

.bg-spacer-t {
  position: absolute;
  bottom: 50%;
  top: 0;
  left: 0;
  right: 0;
  z-index: -1;
}

.bg-spacer-b {
  position: absolute;
  top: 50%;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: -1;
}

/* USPS */

.usp-icon i {
  font-size: 40px;
  color: #666464
}

/* HEADING */

.heading .title {
  font-size: 24px;
  font-weight: 900;
}

.heading-sm .title {
  font-size: 20px;
  font-weight: 900;
}

/* OFFER */

.offer {
  position: relative;
  padding-left: 60px;
}

.offer:before {
  position: absolute;
  left: 0;
  font-family: 'simple-line-icons';
  content: "\e049";
  font-size: 46px;
  color: #fff;
  line-height: 100%;
}

/* BORDERS */

.b-t {
  border-top: 1px solid #b8b8b8
}

.btn {
  font-family: Arial, Helvetica, sans-serif;
  min-height: 42px;
  line-height: 42px;
  padding: 0 10px;
  background-color: #ce000c;
  font-size: 18px;
  font-weight: 500;
  color: #fff;
  display: inline-block;
  cursor: pointer;
  border: 0;
  border-radius: 2px;
  -moz-border-radius: 2px;
  -webkit-border-radius: 2px;
  text-transform: uppercase;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}

.btn i {
  margin-right: 10px;
}

.btn:hover {
  box-shadow: 2px 2px 4px #dddbdb;
}

#next_top:hover i, #prev_top:hover i {
  transition: color 0.3s ease;
  color: #6b6b6b;
}

.btn-lt {
  background: none;
  border: 1px solid #fff;
}

.btn-lt:hover {
  background: #fff;
  color: #ce000c
}

.btn-lg {
  width: 100%;
}

.l-h-xl {
  line-height: 40px
}

/* FORMS */

.input,
.select select {
  height: 40px;
  line-height: 40px;
  background: #fff;
  border-radius: 2px;
  -moz-border-radius: 2px;
  -webkit-border-radius: 2px;
  width: 100%;
  border: 1px solid #d3d3d3;
  padding: 0 5px;
  color: #5d5d5d;
  font-family: 'Roboto', sans-serif;
  font-size: 15px;
}

textarea.input {
  resize: vertical;
  min-height: 100px;
}

.select {
  position: relative;
}

.select:after {
  content: '\f0d7';
  font-family: FontAwesome;
  font-weight: normal;
  font-style: normal;
  position: absolute;
  right: 10px;
  top: 0;
  line-height: 40px;
  pointer-events: none;
}

.select select {
  padding: 0 10px;
}

input[type="checkbox"],
input[type="radio"] {
  background: #fff;
  border: 1px solid #e3dfd8;
  color: #555;
  clear: none;
  cursor: pointer;
  display: inline-block;
  line-height: 0;
  height: 16px;
  margin: -2px 10px 0 0;
  outline: 0;
  padding: 0 !important;
  text-align: center;
  vertical-align: middle;
  width: 16px;
  min-width: 16px;
  -webkit-appearance: none;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-transition: .05s border-color ease-in-out;
  transition: .05s border-color ease-in-out;
}

input[type="radio"] {
  border-radius: 50px;
}

input[type="checkbox"]:checked:before {
  content: '\f00c';
  font-family: FontAwesome;
  font-style: normal;
  font-weight: normal;
  color: #ce000c;
  font-size: 20px;
  display: inline-block;
  vertical-align: middle;
  width: 16px;
  speak: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  margin: 5px 0 0 0;
}

input[type="radio"]:checked:before {
  content: '\f111';
  font-family: FontAwesome;
  text-indent: -9999px;
  -webkit-border-radius: 50px;
  border-radius: 50px;
  width: 6px;
  height: 6px;
  margin: 3px;
  line-height: 16px;
  color: #ce000c;
  font-size: 11px;
  text-align: center;
}

/* READ MORE */

.read-more {
  background: none;
  border: 0;
  text-transform: uppercase;
  height: 40px;
  line-height: 40px;
  position: absolute;
  z-index: 14;
  padding: 0 0 0 10px;
  right: 0;
  bottom: 0;
  margin: 0;
  cursor: pointer;
  -webkit-transition: color 0.2s ease-out 0s;
  -moz-transition: color 0.2s ease-out 0s;
  -o-transition: color 0.2s ease-out 0s;
  transition: color 0.2s ease-out 0s;
}

.read-more-lt {
  color: #fff
}

.read-more.pull-left {
  left: 0;
  right: auto;
  padding: 0 10px 0 0
}

.read-more:before {
  content: "";
  clear: both;
}

.read-more:hover {
  color: #fff;
}

.read-more:after {
  content: "";
  background-color: #ce000c;
  width: 40px;
  height: 40px;
  display: block;
  position: absolute;
  top: 0;
  right: 0;
  z-index: -1;
  -webkit-transition: width 0.2s ease-in 0s;
  -moz-transition: width 0.2s ease-in 0s;
  -o-transition: width 0.2s ease-in 0s;
  transition: width 0.2s ease-in 0s;
}

.read-more.pull-left:after {
  left: 0;
  right: auto;
}

.read-more span {
  float: right;
  width: 40px;
  height: 40px;
  color: #fff;
  margin-left: 10px;
  background-color: #ce000c;
  text-align: center
}

.read-more.pull-left span {
  float: left;
  margin: 0 10px 0 0
}

.read-more:hover:after {
  width: 100%
}

/* MORE INFO */

.more-info:before {
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
  font-family: 'FontAwesome';
  content: '\f105';
  margin-right: 5px;
}

.more-info:hover:before {
  margin-right: 10px;
}

/* SERVICE */

.service-content {
  position: relative;
}

/* SIDEBAR */

.sidebar .widget {
  margin: 0 0 30px 0;
}

.sidebar .widget_header h3 {
  font-size: 20px;
  font-weight: 900;
  padding-bottom: 10px;
}

.sidebar .widget_content ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.sidebar .widget_content ul li a {
  display: block;
  position: relative;
  line-height: 28px;
  padding-left: 15px;
  color: #333;
}

.sidebar .widget_content ul li li a {
  padding-left: 30px;
}

.sidebar .widget_content ul li a:hover {
  color: #ce000c
}

.sidebar .widget_content ul li.current a {
  color: #ce000c
}

.sidebar .widget_content ul li a:before {
  font-family: 'FontAwesome';
  content: '\f105';
  position: absolute;
  left: 0;
}

.sidebar .widget_content ul li li a:before {
  font-family: 'FontAwesome';
  content: '\f105';
  position: absolute;
  left: 15px;
}

table.download {
  width: 100%;
}

table.download th,
table.download td {
  padding: 5px;
  text-align: left;
}

table.download th {
  padding: 7px 5px;
  background: #ebebeb;
}

/* VISIBILITY */

.hidden {
  display: none;
}

.quote-icon {
  background-image: url(/projects/rde/templates/frontend/dist/78064c843eedcdf5a9c7.svg);
  background-size: cover;
  background-position: center;
  width: 62px;
  height: 55px;
}

/* BG COLORS */

.bg-primary {
  background-color: #ce000c;
}

.bg-dk {
  background-color: #111
}

.bg-lt {
  background-color: #ebebeb;
}

.bg-lter {
  background-color: #fff;
}

.bg-image {
  background-size: cover;
  background-position: center center;
  padding-bottom: 120%;
  position: relative;
}

.bg-image .inner {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
}

.bg-shadow {
  box-shadow: 0 0 2px rgba(0, 0, 0, 0.2);
}

/* BORDER RADIUSES */

.r {
  border-radius: 2px 2px 2px 2px;
}

.r-2x {
  border-radius: 4px;
}

.r-3x {
  border-radius: 6px;
}

.r-l {
  border-radius: 2px 0 0 2px;
}

.r-r {
  border-radius: 0 2px 2px 0;
}

.r-t {
  border-radius: 2px 2px 0 0;
}

.r-b {
  border-radius: 0 0 2px 2px;
}

/* paddingS */

.p-xxs {
  padding: 2px 4px;
}

.p-xs {
  padding: 5px;
}

.p-sm {
  padding: 10px;
}

.p {
  padding: 15px;
}

.p-md {
  padding: 20px;
}

.p-lg {
  padding: 30px;
}

.p-xl {
  padding: 50px;
}

.p-n {
  padding: 0 !important;
}

.p-l-none {
  padding-left: 0 !important;
}

.p-l-xs {
  padding-left: 5px;
}

.p-l-sm {
  padding-left: 10px;
}

.p-l {
  padding-left: 15px;
}

.p-l-md {
  padding-left: 20px;
}

.p-l-lg {
  padding-left: 30px;
}

.p-l-xl {
  padding-left: 40px;
}

.p-l-xxl {
  padding-left: 50px;
}

.p-l-n-xxs {
  padding-left: -1px;
}

.p-l-n-xs {
  padding-left: -5px;
}

.p-l-n-sm {
  padding-left: -10px;
}

.p-l-n {
  padding-left: -15px;
}

.p-l-n-md {
  padding-left: -20px;
}

.p-l-n-lg {
  padding-left: -30px;
}

.p-l-n-xl {
  padding-left: -40px;
}

.p-l-n-xxl {
  padding-left: -50px;
}

.p-t-none {
  padding-top: 0 !important;
}

.p-t-xxs {
  padding-top: 1px;
}

.p-t-xs {
  padding-top: 5px;
}

.p-t-sm {
  padding-top: 10px;
}

.p-t {
  padding-top: 15px;
}

.p-t-md {
  padding-top: 20px;
}

.p-t-lg {
  padding-top: 30px;
}

.p-t-xl {
  padding-top: 40px;
}

.p-t-xxl {
  padding-top: 50px;
}

.p-t-n-xxs {
  padding-top: -1px;
}

.p-t-n-xs {
  padding-top: -5px;
}

.p-t-n-sm {
  padding-top: -10px;
}

.p-t-n {
  padding-top: -15px;
}

.p-t-n-md {
  padding-top: -20px;
}

.p-t-n-lg {
  padding-top: -30px;
}

.p-t-n-xl {
  padding-top: -40px;
}

.p-t-n-xxl {
  padding-top: -50px;
}

.p-r-none {
  padding-right: 0 !important;
}

.p-r-xxs {
  padding-right: 1px;
}

.p-r-xs {
  padding-right: 5px;
}

.p-r-sm {
  padding-right: 10px;
}

.p-r {
  padding-right: 15px;
}

.p-r-md {
  padding-right: 20px;
}

.p-r-lg {
  padding-right: 30px;
}

.p-r-xl {
  padding-right: 40px;
}

.p-r-xxl {
  padding-right: 50px;
}

.p-r-n-xxs {
  padding-right: -1px;
}

.p-r-n-xs {
  padding-right: -5px;
}

.p-r-n-sm {
  padding-right: -10px;
}

.p-r-n {
  padding-right: -15px;
}

.p-r-n-md {
  padding-right: -20px;
}

.p-r-n-lg {
  padding-right: -30px;
}

.p-r-n-xl {
  padding-right: -40px;
}

.p-r-n-xxl {
  padding-right: -50px;
}

.p-b-none {
  padding-bottom: 0 !important;
}

.p-b-xxs {
  padding-bottom: 1px;
}

.p-b-xs {
  padding-bottom: 5px;
}

.p-b-sm {
  padding-bottom: 10px;
}

.p-b {
  padding-bottom: 15px;
}

.p-b-md {
  padding-bottom: 20px;
}

.p-b-lg {
  padding-bottom: 30px;
}

.p-b-xl {
  padding-bottom: 40px;
}

.p-b-xxl {
  padding-bottom: 50px;
}

.p-b-n-xxs {
  padding-bottom: -1px;
}

.p-b-n-xs {
  padding-bottom: -5px;
}

.p-b-n-sm {
  padding-bottom: -10px;
}

.p-b-n {
  padding-bottom: -15px;
}

.p-b-n-md {
  padding-bottom: -20px;
}

.p-b-n-lg {
  padding-bottom: -30px;
}

.p-b-n-xl {
  padding-bottom: -40px;
}

.p-b-n-xxl {
  padding-bottom: -50px;
}

/* MARGINS */

.m-xxs {
  margin: 2px 4px;
}

.m-xs {
  margin: 5px;
}

.m-sm {
  margin: 10px;
}

.m {
  margin: 15px;
}

.m-md {
  margin: 20px;
}

.m-lg {
  margin: 30px;
}

.m-xl {
  margin: 50px;
}

.m-n {
  margin: 0 !important;
}

.m-l-none {
  margin-left: 0 !important;
}

.m-l-xs {
  margin-left: 5px;
}

.m-l-sm {
  margin-left: 10px;
}

.m-l {
  margin-left: 15px;
}

.m-l-md {
  margin-left: 20px;
}

.m-l-lg {
  margin-left: 30px;
}

.m-l-xl {
  margin-left: 40px;
}

.m-l-xxl {
  margin-left: 50px;
}

.m-l-n-xxs {
  margin-left: -1px;
}

.m-l-n-xs {
  margin-left: -5px;
}

.m-l-n-sm {
  margin-left: -10px;
}

.m-l-n {
  margin-left: -15px;
}

.m-l-n-md {
  margin-left: -20px;
}

.m-l-n-lg {
  margin-left: -30px;
}

.m-l-n-xl {
  margin-left: -40px;
}

.m-l-n-xxl {
  margin-left: -50px;
}

.m-t-none {
  margin-top: 0 !important;
}

.m-t-xxs {
  margin-top: 1px;
}

.m-t-xs {
  margin-top: 5px;
}

.m-t-sm {
  margin-top: 10px;
}

.m-t {
  margin-top: 15px;
}

.m-t-md {
  margin-top: 20px;
}

.m-t-lg {
  margin-top: 30px;
}

.m-t-xl {
  margin-top: 40px;
}

.m-t-xxl {
  margin-top: 50px;
}

.m-t-n-xxs {
  margin-top: -1px;
}

.m-t-n-xs {
  margin-top: -5px;
}

.m-t-n-sm {
  margin-top: -10px;
}

.m-t-n {
  margin-top: -15px;
}

.m-t-n-md {
  margin-top: -20px;
}

.m-t-n-lg {
  margin-top: -30px;
}

.m-t-n-xl {
  margin-top: -40px;
}

.m-t-n-xxl {
  margin-top: -50px;
}

.m-r-none {
  margin-right: 0 !important;
}

.m-r-xxs {
  margin-right: 1px;
}

.m-r-xs {
  margin-right: 5px;
}

.m-r-sm {
  margin-right: 10px;
}

.m-r {
  margin-right: 15px;
}

.m-r-md {
  margin-right: 20px;
}

.m-r-lg {
  margin-right: 30px;
}

.m-r-xl {
  margin-right: 40px;
}

.m-r-xxl {
  margin-right: 50px;
}

.m-r-n-xxs {
  margin-right: -1px;
}

.m-r-n-xs {
  margin-right: -5px;
}

.m-r-n-sm {
  margin-right: -10px;
}

.m-r-n {
  margin-right: -15px;
}

.m-r-n-md {
  margin-right: -20px;
}

.m-r-n-lg {
  margin-right: -30px;
}

.m-r-n-xl {
  margin-right: -40px;
}

.m-r-n-xxl {
  margin-right: -50px;
}

.m-b-none {
  margin-bottom: 0 !important;
}

.m-b-xxs {
  margin-bottom: 1px;
}

.m-b-xs {
  margin-bottom: 5px;
}

.m-b-sm {
  margin-bottom: 10px;
}

.m-b {
  margin-bottom: 15px;
}

.m-b-md {
  margin-bottom: 20px;
}

.m-b-lg {
  margin-bottom: 30px;
}

.m-b-xl {
  margin-bottom: 40px;
}

.m-b-xxl {
  margin-bottom: 50px;
}

.m-b-n-xxs {
  margin-bottom: -1px;
}

.m-b-n-xs {
  margin-bottom: -5px;
}

.m-b-n-sm {
  margin-bottom: -10px;
}

.m-b-n {
  margin-bottom: -15px;
}

.m-b-n-md {
  margin-bottom: -20px;
}

.m-b-n-lg {
  margin-bottom: -30px;
}

.m-b-n-xl {
  margin-bottom: -40px;
}

.m-b-n-xxl {
  margin-bottom: -50px;
}

/* TEXT */

.text-italic {
  font-style: italic;
}

.text-center {
  text-align: center;
}

.text-lter {
  color: #fff;
}

/* COLS */

.row {
  margin: 0 -10px !important
}

.row:after {
  clear: both;
}

.row:before,
.row:after {
  display: table;
  content: "";
  line-height: 0;
}

.col1, .col2, .col3, .col4, .col5, .col6, .col7, .col8, .col9, .col10, .col11, .col12,
.col1-s, .col2-s, .col3-s, .col4-s, .col5-s, .col6-s, .col7-s, .col8-s, .col9-s, .col10-s, .col11-s, .col12-s,
.col1-xs, .col2-xs, .col3-xxs, .col4-s, .col5-xs, .col6-xs, .col7-xs, .col8-xs, .col9-xs, .col10-xs, .col11-xs, .col12x-s {
  float: left;
  min-height: 1px;
  padding-left: 10px;
  padding-right: 10px;
  box-sizing: border-box;
}

.col12 {
  width: 100%
}

.col11 {
  width: 91.66666667%
}

.col10 {
  width: 83.33333333%
}

.col9 {
  width: 75%
}

.col8 {
  width: 66.66666667%
}

.col7 {
  width: 58.33333333%
}

.col6 {
  width: 50%
}

.col5 {
  width: 41.66666667%
}

.col4 {
  width: 33.33333333%
}

.col3 {
  width: 25%
}

.col2 {
  width: 16.66666667%
}

.col1 {
  width: 8.33333333%
}

.pull-left {
  float: left;
}

.pull-right {
  float: right;
}

/*defaults iMod*/

.alignnone {
  margin: 5px 20px 20px 0;
}

.aligncenter, div.aligncenter {
  display: block;
  margin: 5px auto 5px auto;
}

.alignright {
  float: right;
  margin: 5px 0 20px 20px;
}

.alignleft {
  float: left;
  margin: 5px 20px 20px 0;
}

.aligncenter {
  display: block;
  margin: 5px auto 5px auto;
}

a img.alignright {
  float: right;
  margin: 5px 0 20px 20px;
}

a img.alignnone {
  margin: 5px 20px 20px 0;
}

a img.alignleft {
  float: left;
  margin: 5px 20px 20px 0;
}

a img.aligncenter {
  display: block;
  margin-left: auto;
  margin-right: auto
}

.caption {
  background: #fff;
  border: 1px solid #f0f0f0;
  max-width: 96%; /* Image does not overflow the content area */
  padding: 5px 3px 10px;
  text-align: center;
}

.caption.alignnone {
  margin: 5px 20px 20px 0;
}

.caption.alignleft {
  margin: 5px 20px 20px 0;
}

.caption.alignright {
  margin: 5px 0 20px 20px;
}

.caption img {
  border: 0 none;
  height: auto;
  margin: 0;
  max-width: 98.5%;
  padding: 0;
  width: auto;
}

.caption p.caption-text {
  font-size: 11px;
  line-height: 17px;
  margin: 0;
  padding: 0 4px 5px;
}

.widget, #content {
  position: relative
}

.edit_page_link, .edit_menu_link, .edit_widget_link {
  position: absolute;
  right: 10px;
  top: 10px
}

.clear {
  clear: both;
}

.clearfix:after {
  visibility: hidden;
  display: block;
  font-size: 0;
  content: " ";
  clear: both;
  height: 0;
}

.clearfix {
  display: inline-block;
}

* html .clearfix {
  height: 1%;
}

.clearfix {
  display: block;
}

[data-href] {
  cursor: pointer;
}

.gallery-item .text {
  min-height: 120px;
}

.mceContentBody {
}

.social-icons img {
  width: 40px;
  float: left;
  display: inline-block;
  margin-right: 10px;
  border-radius: 6px;
}

.text-center {
  text-align: center;
}

table.product-table {
  width: 100%;
  margin: 10px 0;
}

.product-table td {
  width: 50%;
}

.product-table img {
  width: 75%;
  height: auto;
}

table.product-table h2 {
  font-size: 18px;
}

ul.check {
  list-style: none;
  margin: 10px 0;
  padding-left: 0 !important;
}

ul.check li:before {
  font-family: 'FontAwesome';
  margin-right: 5px;
  content: '\f00c';
  color: #d93744;
}

img.alignright.img-margin {
  margin: 5px 0 0px 20px;
}

.faq-item {
  border-bottom: 1px solid #b8b8b8;

}

.faq-question {
  position: relative;
  padding: 15px 15px 15px 30px;
  text-transform: uppercase;
}

.faq-question:before {
  font-family: 'FontAwesome';
  content: '\f067';
  position: absolute;
  left: 0;
}

.faq-question.collapsed:before {
  font-family: 'FontAwesome';
  content: '\f068';
}

.faq-question:hover {
  cursor: pointer
}

.faq-answer {
  padding: 0 15px 15px 30px;
  display: none;
  color: #777
}

/* FORM COLUMNS */

.form-row {
  margin: 0 0 0.2em 0;
  display: inline-block;
  width: 100%;
}

.form-row .col-1,
.form-row .col-2,
.form-row .col-3,
.form-row .col-4,
.form-row .col-4-3 {
  box-sizing: border-box;
  float: left;
  line-height: 3.0em;
}

.form-row .col-1 {
  width: 100%;
}

.form-row .col-2 {
  width: 50%;
}

.form-row .col-3 {
  width: 33.33%;
}

.form-row .col-4 {
  width: 25%;
}

.form-row .col-4-3 {
  width: 75%;
}

/* FORM FIELDS */

.form-input {
  width: 100%;
  box-sizing: border-box;
  border: 1px solid #ccc;
  min-height: 3.0em;
  resize: vertical;
  padding: 0 4%;
  border-radius: 2px;
  -moz-border-radius: 2px;
  -webkit-border-radius: 2px;
  -webkit-appearance: none;
}

.form-input, textarea.form-input {
  color: #666;
  font-family: Arial, Helvetica, sans-serif;
}

textarea.form-input {
  min-height: 100px;
  padding: 5px 4%;
}

select {
  box-sizing: border-box;
  border: 1px solid #ccc;
  min-height: 3.0em;
  resize: vertical;
  padding: 0 4%;
  border-radius: 1px;
  -moz-border-radius: 1px;
  -webkit-border-radius: 1px;
  width: 100%;
}

select.hour,
select.minute {
  width: 49%;
  margin-right: 1%;
}

select.minute {
  margin-right: 0;
  margin-left: 1%;
}

/* OTHER */

.form-arterisk {
  color: red;
  font-size: 18px;
  line-height: 12px
}

/* THANKYOU */

.thanks {
  border: 3px solid #9aca74;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
  display: inline-block;
  width: 100%;
  margin: 1.0em 0 1.0em 0;
  box-sizing: border-box;
  padding: 2.0em 2% 2.0em 2%;

}

.thanks_l {
  float: left;
  width: 20%;
}

.thanks_r {
  float: left;
  width: 75%;
  padding-top: 2%;
}

.thanks h2 {
  font-size: 1.5em;
  margin-bottom: 0.2em;
}

.thanks p {
  font-size: 16px;
  margin-bottom: 0.5em;
}

.thanks .fa {
  color: #9aca74;
  font-size: 6em;
}

/** SLIDER **/

.slider,
#slider_container {
  position: relative;
}

.slider .slide {
  position: absolute;
  z-index: 50;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background-size: cover;
  background-position: center
}

.slider img {
  position: absolute;
  z-index: 55;
  top: 0;
  left: 0;
}

.slider .slide .wrap {
  position: relative;
}

.slider .slide .slide-content {
  position: absolute;
  z-index: 80;
  padding: 5% 20% 0 0;
}

.home .slider .slide .slide-title {
  width: 60%
}

.landing .slider .slide .slide-content {
  padding: 25px 25px 0 0;
}

.slider .slide .slide-title {
  color: #fff;
  font-weight: 900;
  font-size: 48px;
  line-height: 60px;
  position: relative;
  padding-bottom: 30px;
}

.slider .slide .slide-description {
  color: #242729;
  font-weight: 900;
  font-size: 210%;
  line-height: 100%;
}

/*navigation*/

.slider-navigation {
  position: absolute;
  bottom: 20px;
  height: 11px;
  z-index: 70;
  list-style: none;
  margin: 0;
  padding: 0;
  left: 0;
  right: 0;
  text-align: center;
}

.slider-navigation li {
  cursor: pointer;
  display: inline-block;
  margin: 0 5px;
  width: 11px;
  height: 11px;
  border-radius: 100%;
  background: #fff
}

.slider-navigation li.current {
  background: #242729;
}

/* SLIDER THUMBS */

.slider-thumbnails {
  list-style: none;
  margin: 0;
  padding: 30px 0 0 0;
  height: 80px;
}

.slider-thumbnails li {
  float: left;
  margin: 0 15px 0 0
}

.slider-thumbnails li img {
  cursor: pointer;
  border: 2px solid #ccc
}

.slider-thumbnails li.current img {
  border-color: #087da9
}

#search {
  display: inline-block;
  vertical-align: middle;
  line-height: 12px;
}

.nav.action {
  vertical-align: middle;
}

#search .form-row {
  margin: 0;
}

#search .btn i {
  margin: 0;
}

#search_result {
}

#search_result .teaser {
  margin-bottom: 10px;
}

#search_result .search_item {
  background-color: #EFEFEF;
  padding: 20px;
  margin-bottom: 10px;
}

#search_result .highlight {
  color: #CE000C;
}

.x-scroller {
  overflow-x: auto;
}

.breadcrumb-bar ol {
  list-style: none;
  padding: 0;
  margin: 0;
}

.breadcrumb-bar ol li {
  display: inline-block;
}

.breadcrumb-bar ol li:after {
  content: '\/';
  padding: 0 3px;
}

.breadcrumb-bar ol li:last-child:after {
  display: none;
}

.contenttxt h1, .headerfade h1 {
  font-size: 24px;
  font-weight: 900;
  margin: 15px 0;
}

.contenttxt h2, #tags_result h2 {
  margin: 15px 0;
}

.contenttxt h3, #tags_result h2 {
  margin: 10px 0;
}

.contenttxt h1:first-child, .contenttxt h2:first-child {
  margin-top: 0;
}

.contenttxt img {
  height: auto !important;
}

.contenttxt ul {
  padding-left: 25px;
}

#tags_result > div {
  margin-bottom: 15px;
  border-top: 1px solid #dddddd;
  padding-top: 5px;
}

.hidden {
  display: none;
}

.bold {
  font-weight: bold;
}

.form-input {
  padding: 0 15px;
}

.inputerror {
  color: #e9322d;
  border: 1px solid #e9322d !important;
}

.error {
  color: Red;
}

.alert {
  padding: 10px;
  margin-bottom: 10px;
  text-shadow: 0 1px 0 rgba(255, 255, 255, 0.5);
  border-radius: 4px;
  border: 1px solid transparent;
}

.alert .fa:first-child {
  padding-right: 5px;
}

tr.alert td {
  color: red;
}

label.error {
  color: #e9322d !important;
  font-weight: bold;
}

.form-input.error {
  border-color: #e9322d !important;
}

.form-row {
  margin: 0 0 0.2em 0;
}

.btn[disabled], #prev, .disabled, .btn-secondary {
  background-color: grey;
}

.alert-dismissable, .alert-dismissible {
  padding-right: 35px;
}

.alert.alert-danger {
  background-color: #F2DEDE;
  border-color: #EED3D7;
  color: #B94A48;
}

.alert.alert-warning {
  color: #8a6d3b;
  background-color: #fcf8e3;
  border-color: #faebcc;
}

.alert.alert-success {
  color: black;
  background-color: #d9edf7;
  border-color: #bce8f1;
}

.alert .alert-link {
  font-weight: 700;
}

.alert-danger .alert-link {
  color: #843534;
}

.alert-warning .alert-link {
  color: #66512c;
}

.alert-success .alert-link {
  color: #245269;
}

div.errorbox ul,
div.warningbox ul,
div.alert ul {
  padding: 0 20px;
  margin: 5px 0 0 0;
}

.inputerror {
  border: 1px solid red !important;
}

.messagered {
  padding: 10px;
  margin: 5px 0;
  text-shadow: 0 1px 0 rgba(255, 255, 255, 0.5);
  background-color: #F2DEDE;
  border: 1px solid #EED3D7;
  border-radius: 4px;
  color: #B94A48;
  font-size: 12px;
}

.message {
  padding: 10px;
  margin: 5px 0;
  background-color: #E6F1F6;
  border: 1px solid #BCE8F1;
  border-radius: 4px;
  color: #29235C;
  font-size: 12px;
}

.header-bar-link {
  color: #ffffff;
  margin-top: 2px;
}

.header-bar-link:hover,
.header-bar-link:focus {
  color: #F4F4F5;
  text-decoration: underline;
}

.header-bar-link img {
  margin-top: -3px;
  margin-right: 2px;
}

.question-mark {
  display: inline-block;
  float: left;
  margin-top: 8px;
  text-align: center;
  color: #888888;
  font-size: 20px;
}

.question-mark-inline {
  display: inline-block;
  text-align: center;
  color: #888888;
  font-size: 20px;
}

.passwordrow {
  display: none;
}

#offertetable .fa {
  font-size: 18px;
}

#startnewquotation {
  float: right;
  font-size: 18px;
  display: inline-block;
  padding-top: 2px;
  padding-right: 10px;
}

#popupbg {
  overflow: auto;
  overflow-y: scroll;
  position: fixed;
  bottom: 0;
  right: 0;
  width: auto;
  height: auto;
  top: 0;
  left: 0;
  z-index: 900;
  background: #0e0e0ebd;
  display: none;
}

::placeholder {
  color: #ccc;
  opacity: 1;
}

#legenda {
  display: none;
}

#legenda_top {
  float: right;
  margin-top: -30px;
}

#legenda_table {
}

#legenda_table td {
  padding: 3px 5px;
}

.label-line-height {
  line-height: 2em !important
}

.quotation_info_row {
  display: none;
  background-color: #f9f9f9;
}

.quotation_info_row td {
  position: relative;
}

.show_info_rb {
  position: absolute;
  right: 0;
  bottom: 0;
  padding: 10px;
}

.delete_rb {
  position: absolute;
  width: 155px;
  right: 0;
  top: 33px;
  padding: 10px;
}

.pdf_rb {
  position: absolute;
  width: 155px;
  right: 0;
  top: 0;
  padding: 10px;
}

.invoicelate td {
  color: #ce000c;
  /*font-weight: bold;*/
}

div.icondot {
  background-color: #ce000c;
  z-index: 10;
  position: absolute;
  color: white;
  text-align: center;
  padding: 0;
  font-weight: bold;
  border-radius: 15px;
  font-size: 12px;
  right: 0;
  top: 3px;
  width: 20px;
  height: 20px;
  line-height: 21px;
}

tr.imessage_unread td {
  font-weight: bold;
}

hr {
  border-top: 1px solid #EBEBEB;
}

#mainnews {
  background-color: #EBEBEB;
  padding: 20px 15px 1px 15px;
  margin-bottom: 25px;
}

ul.pagination {
  margin: 0;
  padding: 0;
}

.pagination > li {
  display: inline-block;
}

.pagination-block {
  padding: 0 0 15px 0;
}

.pagination > li > a, .pagination > li > span {
  position: relative;
  float: left;
  padding: 6px 12px;
  margin-left: -1px;
  line-height: 1.42857143;
  color: #0e0e0ebd;
  text-decoration: none;
  background-color: #fff;
  border: 1px solid #ddd;
}

.pagination > .active > a,
.pagination > .active > span,
.pagination > .active > a:hover,
.pagination > .active > span:hover,
.pagination > .active > a:focus,
.pagination > .active > span:focus {
  background: #ce000c;
  border-color: #ce000c;
  color: white;
}

.pagination > li > a:focus, .pagination > li > a:hover, .pagination > li > span:focus, .pagination > li > span:hover {
  z-index: 3;
  color: #0e0e0ebd;
  background-color: #eee;
  border-color: #ddd;
}

.pagination .prev, .pagination .next {
  width: 43.5px;
  text-align: center;
}

.prev.disabled, .next.disabled {
  color: #ddd;
}

.prev.disabled:hover, .next.disabled:hover {
  background-color: white;
  color: #ddd;
}

#prev_top {
  margin-right: 15px;
  font-size: 25px;
}

#next_top {
  margin-left: 15px;
  font-size: 25px;
}

.trhover:hover {
  background-color: #f9f9f9;
}

.category_show {
  color: #333;
  display: block;
}

aside {
  padding-bottom: 15px;
}

.sidebar .widget_content ul.dropdown-menu {
  padding-left: 10px;
}

.sidebar .widget_content ul li.current a {
  color: #333;
}

.sidebar .widget_content ul li.current > a {
  color: #ce000c;
  font-weight: 500;
}

.submenutoggle {
  display: none;
  background: #ebebeb;
  padding: 12px 15px 10px 15px;
  position: relative;
  color: black;
  font-weight: 500;
  border-bottom: 1px solid white;
}

.submenutoggle .c-hamburger {
  display: inline-block;
  position: absolute;
  right: 10px;
  top: 4px;
  margin: 0;
  float: right;
  color: black;
}

.submenutoggle .c-hamburger span::before, .submenutoggle .c-hamburger span::after, .submenutoggle .c-hamburger span {
  background-color: black;
}

.submenutoggle .c-hamburger--htx.is-active span {
  background: none;
}

.sidebar.submenu-active {
  visibility: visible;
  opacity: 1;
  height: auto;
}

.select:after {
  top: 1px;
  background-color: unset;
}

.select option[disabled] {
  color: #ccc;
}

.slider_usp {
  margin: 0;
  padding: 0;
  padding-top: 10px;
  list-style: none;
  color: white;
  font-weight: 400;
}

.slider_usp li {
  padding: 5px 0;
  font-size: 18px;
}

.service-text {
  min-height: 96px;
  padding-bottom: 30px;
}

.gallery-item .title {
  padding: 5px 0;
}

#usps h2:before {
  content: "\f00c"; /* this is your text. You can also use UTF-8 character codes as I do here */
  font-family: FontAwesome;
  position: relative;
  margin: 0 10px 0 0;
  font-weight: normal;
  color: #CE000C;
}

#widgetBar-1 {
  padding: 0 15px;
}

.gallery-item {
  padding-bottom: 40px;
}

.gallery-item img {
  border: 1px solid #e0e0e0;
}

#gmap {
  height: 360px;
  width: 100%;
  margin-bottom: 15px;
}

#gdirections {
  width: 480px;
}

/* Resposive Youtube */

#youtube .col-md-6 {
  padding-top: 30px;
}

.youtube-container {
  position: relative;
  padding-bottom: 56.25%;
  padding-top: 30px;
  height: 0;
  overflow: hidden;
  width: 100%;
}

.youtube-container iframe,
.youtube-container object,
.youtube-container embed {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

/* Resposive Vimeo */

.vimeo-container {
  position: relative;
  padding-bottom: 56.25%;
  padding-top: 30px;
  height: 0;
  overflow: hidden;
  width: 100%;
}

.vimeo-container iframe,
.vimeo-container object,
.vimeo-container embed {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

/********************* CONTACT MAP *********************/

#gmap {
  position: relative;
  padding-bottom: 56.25%;
  padding-top: 30px;
  height: 0;
  overflow: hidden;
}

#gmap iframe,
#gmap object,
#gmap embed {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

#gdirections {
  width: 100%;
}

.adp-text {
  padding: 15px 30px;
}

.adp-marker {
  margin-left: -30px;
}

.header .contact .icon-sm {
  font-size: 28px;
  display: inline-block;
  min-width: 40px;
}

.header-item {
  display: flex;
  align-items: center;
}

.header .contact .icon-sm i {
  color: #ce000c;
}

.header .icon-content-sm {
  display: inline-block;
}

.header .basket_txt {
  padding-left: 15px;
}

/*.nav.action > ul li.haschild:hover > a {*/

/*  box-shadow: 0 -1px 1px rgba(0, 0, 0, 0.3)*/

/*}*/

.nav.action > ul li {
  height: 58px;
}

.nav.action > ul li a {
  height: 58px;
  line-height: 58px;
}

.nav.action > ul li.haschild > a:after {
  color: #777777;
}

.hcart a, .hcart li {
  height: auto !important;
  padding: 0 15px;
}

.hcart a.btn {
  color: white !important;
  margin-bottom: 5px;
  text-align: center;
}

.nav > ul li .child li a.hchart_product_link {
  line-height: 20px;
  padding: 0;
}

.hcart .table {
  margin-bottom: 10px;
}

.hcart .text-right {
  text-align: right;
}

a.mobile_icon {
  font-size: 23px !important;
  padding: 0 15px 0 0;
}

/* TAG CLOUD*/

#tags {
  margin: 0 1em 0 0;
  padding: 0;
}

#tags li {
  margin: 0;
  padding: 0;
  list-style: none;
}

#tags li a {
  text-decoration: none;
  padding: 0 4px 0 0;
}

#tags li a:hover {
  color: #CE000C;
}

.tag1 {
  font-size: 100%;
}

.tag2 {
  font-size: 100%;
}

.tag3 {
  font-size: 100%;
}

.tag4 {
  font-size: 100%;
}

.tag5 {
  font-size: 100%;
}

.link-structuur {
  padding-top: 20px;
}

.link-structuur ul {
  padding-left: 0;
}

.link-structuur ul li {
  display: inline-block;
  list-style-type: none;
  padding-right: 10px;
}

.klantevertellen-wrapper iframe {
  border: 1px solid #ebebeb;
  border-radius: 5px;
  max-width: 100%;
  width: 185px;
  height: 222px;
  margin-left: 15px;
}

.review_star {
  position: absolute;
  z-index: 2;
  right: 3px;
}

.review_star span.review_star_star {
  font-size: 66px;
  color: #F9B403;
  line-height: 35px;
  /*transform: rotate(-90deg);*/
  display: block;
  text-shadow: 1px 1px 0 rgba(0, 0, 0, .15);
}

.review_star span.review_star_score {
  font-size: 14px;
  position: absolute;
  color: black;
  padding-top: 11px;
  display: inline-block;
  text-align: center;
  width: 100%;
  font-weight: bold;
  top: 0;
}

.homereview_item {
  padding: 0 0 15px 0;
}

.homereview_item p {
  padding-bottom: 0;
}

.homereview_item > div {
  position: relative;
  padding: 15px 45px 15px 15px;
  background: #a6a3a3;
}

.homereview_title {
  padding: 1rem;
  margin: 0;
  font-size: 1.7rem;
  line-height: 1.7rem;
  z-index: 1;
  position: relative;
  font-weight: 700;
  color: white;
}

.homereview_teaser {
  z-index: 1;
  padding: 1rem;
  position: relative;
  color: white;
  font-size: 1.2rem;
}

.scroller-x {
  overflow-x: auto;
}

.header .contact .icon-sm.icon-whatsapp {
  min-width: auto;
}

.icon-whatsapp img {
  width: 28px;
  margin-top: 13px;
}

.mobile_icon.icon-whatsapp img {
  width: 25px;
  margin: 4px 0 0 5px;
}

.rdecontact {
  display: flex;
  justify-content: space-between;
}

#message_development {
  background-color: #f44336;
  text-align: center;
  color: white;
  font-weight: bold;
  padding: 5px;
}

#message_development {
  background-color: #f44336;
  text-align: center;
  color: white;
  font-weight: bold;
  padding: 5px;
  display: block;
}

.product-col {
  padding: 15px 15px 50px;
  margin-bottom: 20px;
  background: #fff;
  border: 1px solid #e8e8e8;
  position: relative;
  min-height: 285px;
}

.product-col-a {
  text-align: center;
  display: block;
  height: 152px;
}

.col6 .product-col-a {
  height: 200px;
}

.discountflag {
  padding: 5px 10px;
  font-weight: bold;
  position: absolute;
  top: 20px;
  left: 40px;
  border-radius: 5px;
}

.price-dot-circle {
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  border-radius: 38px;
  height: 75px;
  width: 75px;
  position: absolute;
  top: -12px;
  right: -12px;
  color: white;
}

.price-on-request {
  font-size: 15px;
}

.product-col .caption h4.ft-green {
  height: 60px;
}

.product-col .caption .description {
  height: 46px;
}

.price-2-digits-ft-size {
  font-size: 24px;
}

.price-3-digits-ft-size {
  font-size: 24px;
}

.price-4-digits-ft-size {
  font-size: 20px;
}

.price-5-digits-ft-size {
  font-size: 20px;
}

.price-6-digits-ft-size {
  font-size: 18px;
}

.bg-green {
  background-color: #67993a;
}

.bg-blue {
  background-color: #00467f;
}

.bg-yellow {
  background-color: #f6c500;
}

.bg-grey {
  background-color: #524d49 !important;
}

.bg-red {
  background-color: #CE000C;
}

.btn-cart.bg-moreinfo {
  background-color: #ebebeb;
  color: black;
  position: absolute;
  bottom: 15px;
  width: calc(100% - 30px);
}

.btn-cart.bg-moreinfo:hover .fa.bg-grey {
  background-color: #CE000C !important;
  transition: all 0.3s ease;
}

.bg-white {
  background-color: white;
  color: black;
  box-shadow: 0 1px 3px 0 rgba(189, 189, 189, 1)
}

.cart-button {
  text-align: left;
}

.btn-cart {
  padding: 0 0 0 15px;
  font-size: 18px;
  color: #fff;
  text-transform: uppercase;
  border: none;
  height: auto;
  line-height: initial;
  font-weight: 500;
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: space-between;
}

.btn i.fa.fa-chevron-right {
  margin-left: 10px;
  margin-right: 0;
}

.btn-cart:hover {
  background-color: #f7f7f7;
  color: #CE000C;
}

.btn-cart:hover .fa {
}

.btn-cart .fa {
  margin: 0;
  margin-left: 10px;
  padding: 10px;
  color: white;
  border: 1px solid transparent
}

a.product-img-a {
  display: block;
}

.product-col .prcaption h4 {
  padding: 15px 5px;
  height: 90px;
}

.product-col .prcaption .description {
  height: 50px;
  padding: 15px;
}

.manufacturer {
  list-style: none;
}

.hide {
  display: none;
}

.product-addtocart-div {
  display: flex;
  align-items: center;
  background-color: #EBEBEB;
  padding: 15px;
}

#productpage .productimage {
  border: 1px solid #EBEBEB;
  display: inline-block;
  margin-bottom: 15px;
}

#productpage img {
}

#product_orderform {
  background-color: #EBEBEB;
  padding: 10px;
}

.product-stone-div {
  display: flex;
  align-items: center;
  padding: 5px;
  justify-content: space-between;
}

.call-to-action {
  width: 100%;
  text-align: center;
  margin-bottom: 15px;
  height: auto;
}

#usp {
  font-size: 18px;
}

#usp h4 {
  font-weight: bold;
  padding-bottom: 15px;
}

#usp ul {
  margin: 0;
  padding: 0;
  list-style: none;
}

#usp li {
  padding: 8px 20px 8px 0;
}

#usp li i {
  font-weight: bold;
  margin-right: 10px;
}

#productprops td {
  padding: 5px;
}

#productprops td:first-child {
  font-weight: bolder;
}

#basket .alert.alert-danger {
  margin-top: 10px;
}

#basket_row_header {
  padding-top: 10px;
  padding-bottom: 10px;
  background-color: #EAEBEC;
  margin-top: 15px !important;
  margin-bottom: 15px !important;
  border-radius: 5px;
  font-weight: bold;
}

.basket_pieceprice, .basket_price, .basket_subtotal {
  text-align: right;
}

.basket-form .fa {
  padding: 8px 5px 13px 5px;
}

#shippingbar {
  background-color: #EAEBEC;
  padding: 15px;
  font-weight: bold;
}

.shop_top_message {
  background-color: #ebebeb;
  color: #CE000C;
  font-weight: 500;
  text-align: center;
  font-size: 17px;
  padding-top: 10px;
  padding-bottom: 10px;
  margin-bottom: 15px;
  border-radius: 3px;
}

.payment_logo {
  padding: 5px 5px;
  background: white;
  text-align: center;
  display: block;
}

.payment_logo img {
  max-height: 50px;
}

.basket-step-2 .form-row {
  margin: 0 0 0.4em 0;
  display: flex;
  align-items: center;
}

.price-on-request {
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.vi-lazyload {
  width: 100%;
  position: relative !important;
  overflow: hidden;
  cursor: pointer;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.vi-lazyload::before {
  content: '';
  width: 100%;
  display: block;
  position: relative;
  padding-top: 56.25%; /*16:9 ratio*/
  background-color: #000;
}

.vi-lazyload-wrap {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  padding: inherit;
}

.vi-lazyload-content {
  width: 100%;
  height: 100%;
  position: relative;
  background-color: #000;
  /* --vi-lazyload-img wil be replaced with thumb */
  background-image: var(--vi-lazyload-img);
  background-size: cover;
  background-position: 50%;
  background-repeat: no-repeat;
}

.vi-lazyload-playbtn {
  display: block;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1;
  background-image: url("data:image/svg+xml,%3Csvg xmlns=%27http://www.w3.org/2000/svg%27 viewBox=%270 0 13 8%27%3E%3Crect fill=%27rgba%280,0,0,.65%29%27 width=%2713%27 height=%278%27 rx=%271%27 ry=%271%27/%3E%3Cpolygon fill=%27%23fff%27 points=%275 6 9 4 5 2%27/%3E%3C/svg%3E");
  background-position: 50%;
  background-size: calc(35px + 10%) auto;
  background-repeat: no-repeat;
}

.vi-lazyload-playbtn:hover {
  background-image: url("data:image/svg+xml,%3Csvg xmlns=%27http://www.w3.org/2000/svg%27 viewBox=%270 0 13 8%27%3E%3Crect fill=%27%23B43929%27 width=%2713%27 height=%278%27 rx=%271%27 ry=%271%27/%3E%3Cpolygon fill=%27%23fff%27 points=%275 6 9 4 5 2%27/%3E%3C/svg%3E");
}

.vi-lazyload iframe {
  width: 100% !important;
  height: 100% !important;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 3;
  border: 0;
  background-color: #000;
}

#c-cookies {
  bottom: 0;
  position: fixed;
  width: 100%;
  z-index: 100;
  display: none;
}

#c-cookies .cookies {
  text-align: center;
}

#c-cookies .cookies_wrapper {
  background-color: rgba(44, 51, 54, .9);
  color: #fff;
  padding: 15px 30px;
  display: block;
}

#c-cookies p {
  color: #fff;
  position: relative;
  padding-right: 10px;
  margin: 0;
}

a.cc-privacy-link {
  color: white;
  text-decoration: underline;
}

#c-cookies .cc-btn-close {
  padding: 0 15px;
  border-radius: 5px;
  z-index: 1000000;
  white-space: nowrap;
  display: inline-block;
  margin: 5px 0;
  border: 0;
}

#c-cookies .cc-btn-close:hover {
  background-color: #ce000c;
  color: white;
  box-shadow: 2px 2px 4px #050505;
}

.qtip-default.qtip-def {
  border: 1px solid #B94A48;
  color: #67767d;
  font-size: 14px;
  line-height: 18px;
  background-color: #ffffff;
  border-radius: 5px 5px;
  padding: 5px;
}

.qtip {
  max-width: 400px !important;
}

.qtip-content ul {
  padding: 0 15px;
  margin: 5px 0;
}

.header-bar {
  padding: 10px;
  margin-top: 20px;
  background: #ce000c;
  color: #ffffff;
}

.qtip-default .qtip-titlebar {
  background: none;
}

#tooltip {
  border-radius: 4px 4px 0 0;
  background-color: white;
  display: none;
  box-shadow: 2px 5px 10px rgb(0 0 0 / 30%);
}

#tooltip-title {
  background-color: #ce000c;
  color: white;
  padding: 10px 15px;
  border-radius: 4px 4px 0 0;
  font-size: 13px;
  font-weight: bold;
}

#tooltip-content {
  background-color: white;
  color: black;
  font-size: 13px;
  padding: 10px 15px;
  border-radius: 4px 4px 0 0;
}

#tooltip-arrow,
#tooltip-arrow::before {
  position: absolute;
  width: 12px;
  height: 12px;
  background: inherit;
  border: 1px solid #b4b4b4;
  z-index: -1;
}

#tooltip-arrow {
  visibility: hidden;
}

#tooltip-arrow::before {
  visibility: visible;
  content: '';
  transform: rotate(45deg);
}

#tooltip[data-popper-placement^='top'] > #tooltip-arrow {
  bottom: -6px;
}

#tooltip[data-popper-placement^='bottom'] > #tooltip-arrow {
  top: -6px;
}

#tooltip[data-popper-placement^='left'] > #tooltip-arrow {
  right: -6px;
}

#tooltip[data-popper-placement^='right'] > #tooltip-arrow {
  left: -6px;
}

.quotation_icon {
  width: 16px;
  height: 16px;
  border-radius: 8px;
  box-shadow: 1px 1px 4px #dddbdb;
  border: 1px solid #dddbdb;
  color: white;
  font-size: 12px !important;
  padding: 1px;
}

.quotation_icon.fa.fa-bolt {
  padding: 1px 5px;
  color: black;
}

.quotation_icon_10 {
  background-color: white;
}

.quotation_icon_20, .quotation_icon_21 {
  background-color: #65CF0F;
}

.quotation_icon_30 {
  background-color: #DEC300;
}

.quotation_icon_31, .quotation_icon_35, .quotation_icon_38 {
  background-color: #de6d00;
}

.quotation_icon_40 {
  background-color: #CF0F1C;
}

.quotation_icon_50 {
  background-color: #9D0091;
}

.quotation_icon_53 {
  background-color: #69DBCF;
}

.quotation_icon_55 {
  background-color: #4E00DE;
}

.quotation_icon_60, .quotation_icon_61, .quotation_icon_62, .quotation_icon_63 {
  background-color: black;
}

.quotation_icon_70 {
  background-color: #D6D9BA;
}

@media screen and (max-width: 425px) {
  .text-center a {
    font-size: 14px;
    width: 100%;
    margin: 10px 0;
  }

  .product-table td {
    display: inline-block;
    width: 100%;
    float: left;
  }

  td.mob-hide {
    display: none;
  }
}

@media (max-width: 895px) {
  .form-row .col-4 {
    width: 40%;
  }

  .form-row .col-4-3 {
    width: 60%;
  }
}

@media (max-width: 670px) {

  .form-row {
    margin: 0;
  }

  .form-row .col-1,
  .form-row .col-2,
  .form-row .col-3,
  .form-row .col-4,
  .form-row .col-4-3 {
    float: none;
    width: 100%;
  }

  .footer .widget_content ul li {
    padding: 5px 0;
  }

  .footer #tags li {
    padding: 5px 0;
  }

}

@media (max-width: 1100px) {

  .hidden-s {
    display: none;
  }

  .slider .slide .slide-title {

    line-height: 50px;
  }

  .visible-s {
    display: block;
  }

  .wrapper {
    overflow-x: hidden;
  }

  .wrap,
  .wrap-lg {
    padding-left: 20px;
    padding-right: 20px;
  }

  .c-hamburger {
    display: block;
    position: relative;
    overflow: hidden;
    padding: 0;
    width: 24px !important;
    height: 36px;
    font-size: 0;
    text-indent: -9999px;
    appearance: none;
    box-shadow: none;
    border-radius: unset;
    cursor: pointer;
    background: none;
    border: 0;
    margin: 17px 0 0 20px;
    /* transition: background 0.3s; */
  }

  .c-hamburger span {
    display: block;
    position: absolute;
    top: 16px;
    left: 0;
    right: 0;
    height: 2px;
    background: #fff;
  }

  .c-hamburger span::before,
  .c-hamburger span::after {
    position: absolute;
    display: block;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: #fff;
    content: "";
  }

  .btn-stroke:hover.c-hamburger span,
  .btn-stroke:hover.c-hamburger span::before,
  .btn-stroke:hover.c-hamburger span::after {
    background-color: #A72072
  }

  .btn-stroke:hover.c-hamburger--htx.is-active span {
    background: none;
  }

  .c-hamburger span::before {
    top: -7px;
  }

  .c-hamburger span::after {
    bottom: -7px;
  }


  .c-hamburger--htx span {
    transition: background 0s 0.3s;
  }

  .c-hamburger--htx span::before,
  .c-hamburger--htx span::after {
    transition-duration: 0.3s, 0.3s;
    transition-delay: 0.3s, 0s;
  }

  .c-hamburger--htx span::before {
    transition-property: top, transform;
  }

  .c-hamburger--htx span::after {
    transition-property: bottom, transform;
  }

  /* active state, i.e. menu open */
  .c-hamburger--htx.is-active {

  }

  .c-hamburger--htx.is-active span {
    background: none;
  }

  .c-hamburger--htx.is-active span::before {
    top: 0;
    transform: rotate(45deg);
  }

  .c-hamburger--htx.is-active span::after {
    bottom: 0;
    transform: rotate(-45deg);
  }

  .c-hamburger--htx.is-active span::before,
  .c-hamburger--htx.is-active span::after {
    transition-delay: 0s, 0.3s;
  }


  #mobilemenu {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  #mobilemenu li {
    line-height: 40px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.15);
  }

  #mobilemenu li a {
    color: #fff;
    font-size: 18px;
    display: block;
  }

  #mobilemenu li.haschild > a:after {
    content: '\f105';
    font-family: FontAwesome;
    font-weight: normal;
    font-style: normal;
    font-size: 17px;
    margin: 0 10px 0 0;
    float: right;
  }

  #mobilemenu li a:hover {
    /*color:#999;*/
  }

  #mobilemenu li.current > a {
    color: #ce000c;
  }

  #mobilemenu .child {
    display: none;
    list-style: none;
    margin: 0;
    padding: 0 0 0 20px
  }

  #mobilemenu .haschild.active .child {
    display: block;

  }

  #mobilemenu .haschild {
    left: -81%;
    overflow-x: hidden;
    overflow-y: auto;
    visibility: visible;
    -webkit-overflow-scrolling: touch;
    -moz-transition: left 0.3s ease;
    -webkit-transition: left 0.3s ease;
    transition: left 0.3s ease;
    -webkit-transition-timing-function: cubic-bezier(0.420, 0.000, 0.580, 1.000);
    -moz-transition-timing-function: cubic-bezier(0.420, 0.000, 0.580, 1.000);
    -o-transition-timing-function: cubic-bezier(0.420, 0.000, 0.580, 1.000);
    transition-timing-function: cubic-bezier(0.420, 0.000, 0.580, 1.000); /* ease-in-out */
  }

  #mobilemenu .haschild.active {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    padding: 0;
    margin: 0;
    background-color: #111;
    border-bottom: 0;
    padding-top: 11px;
    z-index: 2;
  }

  #mobilemenu .haschild.active > a {
    margin-left: 20px;
    color: #96c322;
    border-bottom: 1px solid rgba(255, 255, 255, 0.15);
    /* background-color: white; */
    font-weight: bold;
  }

  #mobilemenu .haschild.active > a:before {
    display: none;
  }

  #mobilemenu .first-child .mobile-exit,
  #mobilemenu .haschild.active .mobile-close {
    position: absolute;
    z-index: 2;
    float: right;
    font-size: 30px;
    background: #ce000c;
    top: 0;
    right: -1px;
    height: 51px;
    width: 50px;
    line-height: 50px;
    text-align: center;
    color: #fff;
  }

  #mobilemenu .mobile-exit .c-hamburger {
    margin-left: 14px;
    margin-top: 10px;
  }

  #mobilemenu .mobile-exit .c-hamburger span::before,
  #mobilemenu .mobile-exit .c-hamburger span::after {
    background-color: #ffffff;
  }

  #mobilemenu .haschild.active .mobile-close i {
    pointer-events: None;
  }

  #mobilemenu .child li a {
    text-transform: none; /* color:#d2d2d2 */
  }

  .container-wrapper {
    -webkit-transition: -webkit-transform 0.3s ease;
    -moz-transition: -moz-transform 0.3s ease;
    -o-transition: -o-transform 0.3s ease;
    transition: transform 0.3s ease;
    -webkit-transition-timing-function: cubic-bezier(0.420, 0.000, 0.580, 1.000);
    -moz-transition-timing-function: cubic-bezier(0.420, 0.000, 0.580, 1.000);
    -o-transition-timing-function: cubic-bezier(0.420, 0.000, 0.580, 1.000);
    transition-timing-function: cubic-bezier(0.420, 0.000, 0.580, 1.000); /* ease-in-out */

  }

  .mobile-menu-container {
    background-color: #111;
    padding: 10px 0 20px 20px;
    position: fixed;
    top: 0;
    left: -71%;
    bottom: 0;
    z-index: 101;
    display: block !important;
    width: 71% !important;
    overflow-x: hidden;
    overflow-y: auto;
    visibility: visible;
    -webkit-overflow-scrolling: touch;
    -moz-transition: left 0.3s ease;
    -webkit-transition: left 0.3s ease;
    transition: left 0.3s ease;
    -webkit-transition-timing-function: cubic-bezier(0.420, 0.000, 0.580, 1.000);
    -moz-transition-timing-function: cubic-bezier(0.420, 0.000, 0.580, 1.000);
    -o-transition-timing-function: cubic-bezier(0.420, 0.000, 0.580, 1.000);
    transition-timing-function: cubic-bezier(0.420, 0.000, 0.580, 1.000); /* ease-in-out */
  }

  .off-screen {
    left: 0;
  }

  .off-screen + * {
    position: relative;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 5;
    width: 100%;
    overflow: hidden;
    -webkit-transform: translate3d(70%, 0, 0);
    transform: translate3d(70%, 0, 0);
    -webkit-transition: -webkit-transform .3s ease;
    -moz-transition: -moz-transform .3s ease;
    -o-transition: -o-transform .3s ease;
    transition: transform .3s ease;
    -webkit-backface-visibility: hidden;
    -moz-backface-visibility: hidden;
    backface-visibility: hidden;
    -webkit-transition-delay: 0.1s; /* Safari */
    transition-delay: 0.1s;
  }

  /* USPS */
  .usps {
    position: relative;
    padding-left: 55px;
  }

  .usps .usp-icon {
    position: absolute;
    left: 0;
  }

}

@media only screen
and (min-width: 670px)
and (max-width: 1100px) {

  /* GENERAL */
  .visible-s {
    display: block !important;
  }

  .logo {
    width: 30%;
    padding-right: 15px;
  }

  .contact {
    width: 70%;
  }

  .hidden-s {
    display: none;
  }

  .gallery .gallery-item {
    height: 350px;
  }

  .mobile-menu-container {
    left: -36%;
    width: 35% !important;
  }

  .off-screen {
    left: 0;
  }

  .off-screen + * {
    -webkit-transform: translate3d(35%, 0, 0);
    transform: translate3d(35%, 0, 0);
  }

  /* SLIDER */
  .slider {
    min-height: 150px;
  }

  .home .slider {
    min-height: 350px;
  }

  .slider .slide .slide-title {
    width: 100%
  }

  /* FOOTER */
  .footer .widget {
    width: 33.33%;
    padding: 0 0 30px 0 !important;
  }

  .footer .widget.first-child {
    width: 100% !Important
  }

  /* COLUMNS */
  .col12-s {
    width: 100%
  }

  .col11-s {
    width: 91.66666667%
  }

  .col10-s {
    width: 83.33333333%
  }

  .col9-s {
    width: 75%
  }

  .col8-s {
    width: 66.66666667%
  }

  .col7-s {
    width: 58.33333333%
  }

  .col6-s {
    width: 50%
  }

  .col5-s {
    width: 41.66666667%
  }

  .col4-s {
    width: 33.33333333%
  }

  .col3-s {
    width: 25%
  }

  .col2-s {
    width: 16.66666667%
  }

  .col1-s {
    width: 8.33333333%
  }

}

@media only screen
and (min-device-width: 768px)
and (max-device-width: 1024px)
and (orientation: portrait) {

}

@media (max-width: 670px) {

  .read-more span {

    width: 25px;
  }

  .read-more:after {
    width: 25px;
  }

  .hidden-xs {
    display: none;
  }

  .visible-xs {
    display: block;
  }

  /* HEADER */
  .logo {
    width: 60%;
  }

  .header .contact .action {
    padding-left: 0;
    margin-left: 15px;

  }

  .badge-wrapper {
    display: none;
  }

  .header .contact .icon-sm {
    position: relative;
  }

  .no-padding-responsive {
    padding-left: 20px;
  }

  .btn-login {
    font-size: 15px;
  }

  /* OFFER */
  .offer .btn {
    width: 100%;
    margin-top: 20px;
  }

  #img_cont {
    display: block !important;
    flex-direction: row !important;
    width: 100% !important;
    margin-left: 0 !important;
  }

  #img_cont img {
    width: 100%;
    height: 100%;
    vertical-align: initial;
  }

  /* SLIDER */
  .slider {
    min-height: 150px;
  }

  .slider .slide .slide-title {
    font-size: 28px;
    line-height: 30px;
    width: 100%;
  }

  .form-row .col-4 {
    padding-top: 5px;
    line-height: 1;
  }

  /* FOOTER */
  .footer .widget {
    width: 100% !Important;
    float: none;
    padding: 0 0 30px 0 !Important
  }

  .footer-bottom {
    text-align: center;
  }

  .footer-bottom a {
    padding-top: 10px;
    display: block;
  }

  /* OFFER */
  .offer {
    padding-left: 0;
  }

  .offer:before {
    position: relative;
    margin-bottom: 20px;
    display: block;
  }

  /* COLUMNS */
  .col12-xs {
    width: 100%
  }

  .col11-xs {
    width: 91.66666667%
  }

  .col10-xs {
    width: 83.33333333%
  }

  .col9-xs {
    width: 75%
  }

  .col8-xs {
    width: 66.66666667%
  }

  .col7-xs {
    width: 58.33333333%
  }

  .col6-xs {
    width: 50%
  }

  .col5-xs {
    width: 41.66666667%
  }

  .col4-xs {
    width: 33.33333333%
  }

  .col3-xs {
    width: 25%
  }

  .col2-xs {
    width: 16.66666667%
  }

  .col1-xs {
    width: 8.33333333%
  }


}

@media (max-width: 1600px) {
  .slider_usp li {
    float: left;
    padding-left: 10px;
  }
}

@media (max-width: 1300px) {
  .slider .slide .slide-title {
    font-size: 30px;
    line-height: 40px
  }
}

@media (min-width: 992px) {
  #city {
    width: calc(50% - 20px);
  }
}

@media (max-width: 992px) {
  .mobile-menu.hidden {
    display: block;
  }
}

@media (max-width: 670px) {
  label.col-form-label {
    display: block;
    width: 100%;
    font-weight: bold;
    padding-top: 10px !important;
    padding-bottom: 5px;
  }

  .wizard label.col-form-label {
    padding-top: 0 !important;
    padding-bottom: 5px;
  }

  label.mobileshow {
    display: block;
  }

  .slider_usp {
    display: none;
  }

  .home .slider .slide .slide-title {
    font-size: 20px;
    line-height: 25px;
    width: 100%;
    padding-bottom: 15px;
  }

  .slide-description .btn {
    margin-right: 5px;
  }

  .slider .slide .slide-content {
    padding: 5% 15px 0 0;
  }

  .sidebar {
    visibility: hidden;
    height: 0;
    opacity: 0;
    transition: visibility 0s, opacity 0.3s ease;
    transition-timing-function: cubic-bezier(0.420, 0.000, 0.580, 1.000);
  }

  .submenutoggle {
    display: block;
  }

  #startnewquotation {
    float: none;
    margin-top: 10px;
  }

  .steps {
    display: block;
  }

  .steps div {
    display: inline-block;
    margin-right: -4px;
  }

  .product-addtocart-div {
    display: block;
  }

  .product-col {
    min-height: auto;
  }

  .product-col .prcaption h4 {
    height: auto;
  }

  .product-col > div {
    text-align: center;
  }

  .rdecontact {
    display: block;
    padding-bottom: 30px;
  }
}

@media (max-width: 1100px) {
  #mobilemenu li:not(.active) {
    border-bottom-width: 0;
  }

  #mobilemenu li.active li, .off-screen #mobilemenu li {
    border-bottom-width: 1px;
  }

  #basket .btn {
    margin-right: 5px;
    margin-bottom: 5px;
    float: left !important;
  }

  #mobilemenu {
    display: inline-block;
    margin: 17px 0 0 0
  }

  .off-screen #mobilemenu {
    display: block;
  }

  #mobilemenu > li {
    display: inline-block;
    vertical-align: top;
  }

  .off-screen #mobilemenu li {
    display: block;
  }

  #mobilemenu li.haschild > a:after {
    content: '';
    margin: 0;
  }

  .off-screen #mobilemenu li.haschild > a:after {
    content: '\f105';
    margin: 0 10px 0 0;
  }
}

@media (max-width: 895px) {
  .header .contact {
    display: none;
  }
}

/**
  THIS IS THE DEFAULT GSDEDITOR.CSS
  Do not change this file, so we can copy it if new features are added
*/

.gsd-editor {
  width: 100%;
  background-color: white;
}

.gsd-container {
  width: 100%;
  margin: 0 auto;
  padding: 50px 0 50px 0;
}

.gsd-container > div {
  width: 100%;
  margin: 0 auto;
  padding: 0 15px;
  display: flex;
  max-width: 1097px;
  gap: 30px;
}

/** one column grids **/

.gsd-container.gsd-image,
.gsd-container.gsd-wysiwyg,
.gsd-container.gsd-image > div,
.gsd-container.gsd-wysiwyg > div {
  width: 100%;
}

/** two column grids **/

.gsd-container.gsd-wysiwyg-wysiwyg > div > div,
.gsd-container.gsd-wysiwyg-image > div > div,
.gsd-container.gsd-image-wysiwyg > div > div,
.gsd-container.gsd-image-image > div > div {
  width: 50%;
}

/** three column grids **/

.gsd-container.gsd-wysiwyg-wysiwyg-wysiwyg > div > div,
.gsd-container.gsd-image-image-image > div > div {
  flex: 33.33%;
}

/** some default styling **/

.gsdblock-image-block img {
  max-width: 100%;
  /*border-radius: 20px;*/
}

.gsdblock-image-block.image-block-backgroundimage {
  min-height: 250px;
}

.image-block-backgroundimage {
  background-size: cover;
  background-position: center;
}

.gsd-wysiwyg .gsdblock-wysiwyg-block {
  padding: 15px;
  width: 100%;
}

.gsd-wysiwyg-image .gsdblock-wysiwyg-block {
  margin-right: 50px;
}

.gsd-image-wysiwyg .gsdblock-wysiwyg-block {
  margin-left: 50px;
}

.gsdblock-wysiwyg-block h1 {
  font-size: 4rem;
  line-height: 5rem;
  margin-top: 0;
}

.gsdblock-wysiwyg-block h2 {
  font-size: 3rem;
  line-height: 4rem;
  margin-top: 0;
}

.gsdblock-wysiwyg-block h3 {
  font-size: 2rem;
  line-height: 3rem;
  margin-top: 0;
}

.image-block-title {
  padding: 10px 15px;
  font-weight: bold;
  background-color: #ebebeb;
}

.image-block-description {
  padding: 10px 15px;
  border: 1px solid #ebebeb;
}

.text-middle-width > div {
  max-width: 760px;
}

/** responsive **/

@media (max-width: 767px) {

  .gsd-container > div {
    flex-direction: column;
    padding: 0;
    align-items: center;
    gap: 20px;
  }

  /** two column grids **/
  .gsd-container.gsd-wysiwyg-wysiwyg > div > div,
  .gsd-container.gsd-wysiwyg-image > div > div,
  .gsd-container.gsd-image-wysiwyg > div > div,
  .gsd-container.gsd-image-image > div > div {
    width: 94%;
  }

  /** three column grids **/
  .gsd-container.gsd-wysiwyg-wysiwyg-wysiwyg > div > div,
  .gsd-container.gsd-image-image-image > div > div {
    flex: 100%;
  }

}

.gsd-editor section:first-child.gsd-container {
  padding-top: 0;
}

.gsd-container {
  padding: 15px 0;
}

.gsd-container > div {
  padding: 0;
}

.gsdblock-wysiwyg-block h1,
.gsdblock-wysiwyg-block h2,
.gsdblock-wysiwyg-block h3 {
  font-size: 24px;
  line-height: inherit;
  font-weight: 900;
  margin: 15px 0;
}

.gsd-editor h1:first-child, .gsd-editor h2:first-child {
  margin-top: 0;
}

.gsd-wysiwyg .gsdblock-wysiwyg-block {
  padding: 15px 0;
}

.gsd-editor .gsd-wysiwyg .gsdblock-wysiwyg-block:first-child {
  padding-top: 0;
}

.wizard .form-row {
  background-color: #EBEBEB;
  padding: 0.6em 0;
}

.wizard .header-row {
  font-weight: bold;
}

.wizard table td {
  padding: 0 10px 10px 10px;

}

.wizard .col-form-label {
  line-height: 2.5em;
}

input[readonly].inputreadonly_cust, input[disabled].inputreadonly_cust {
  color: #666;
  border: 1px solid #ccc;
  background: #F0F0F0;
}

#elementtable {
  width: 100%;
}

.form-row.disabled {
  background-color: #BEC4C7 !important;
}

#laststep {
  font-weight: bold;
  display: inline-block;
  float: right;
  padding: 15px;
  color: #B43929;
}

.steps {
  margin-top: 15px;
  margin-bottom: 15px;
  width: 100%;
  display: flex;
}

.steps div:first-child {
  border-radius: 5px 0 0 5px;
}

.steps div:last-child {
  border-radius: 0 5px 5px 0;
}

.steps div {
  text-align: center;
  padding: 7px;
  flex-grow: 1;
}

div.step_on {
  background-color: #333;
  color: white;
}

div.step_on a {
  color: white;
  text-decoration: none;
}

div.step_off {
  background-color: #EAEBEC;
  color: black;
}

.input-row {
  background-color: #EBEBEB;
  padding: 10px;
}

.input-validation-icon {
  padding-top: 13px;
  text-align: right;
  min-width: 20px;
}

.input-error-message {
  margin: 0;
  margin-top: 8px;
  color: #f65e3f;
}

.input-warning-message {
  margin: 0;
  margin-top: 8px;
  color: #057fcc;
}

.notification {
  color: #057fcc;
}

[v-cloak] > * {
  display: none
}

[v-cloak]::before {
  content: " ";
  display: block;
  width: 16px;
  height: 16px;
  background-image: url(data:image/gif;base64,R0lGODlhEAAQAPIAAP///wAAAMLCwkJCQgAAAGJiYoKCgpKSkiH/C05FVFNDQVBFMi4wAwEAAAAh/hpDcmVhdGVkIHdpdGggYWpheGxvYWQuaW5mbwAh+QQJCgAAACwAAAAAEAAQAAADMwi63P4wyklrE2MIOggZnAdOmGYJRbExwroUmcG2LmDEwnHQLVsYOd2mBzkYDAdKa+dIAAAh+QQJCgAAACwAAAAAEAAQAAADNAi63P5OjCEgG4QMu7DmikRxQlFUYDEZIGBMRVsaqHwctXXf7WEYB4Ag1xjihkMZsiUkKhIAIfkECQoAAAAsAAAAABAAEAAAAzYIujIjK8pByJDMlFYvBoVjHA70GU7xSUJhmKtwHPAKzLO9HMaoKwJZ7Rf8AYPDDzKpZBqfvwQAIfkECQoAAAAsAAAAABAAEAAAAzMIumIlK8oyhpHsnFZfhYumCYUhDAQxRIdhHBGqRoKw0R8DYlJd8z0fMDgsGo/IpHI5TAAAIfkECQoAAAAsAAAAABAAEAAAAzIIunInK0rnZBTwGPNMgQwmdsNgXGJUlIWEuR5oWUIpz8pAEAMe6TwfwyYsGo/IpFKSAAAh+QQJCgAAACwAAAAAEAAQAAADMwi6IMKQORfjdOe82p4wGccc4CEuQradylesojEMBgsUc2G7sDX3lQGBMLAJibufbSlKAAAh+QQJCgAAACwAAAAAEAAQAAADMgi63P7wCRHZnFVdmgHu2nFwlWCI3WGc3TSWhUFGxTAUkGCbtgENBMJAEJsxgMLWzpEAACH5BAkKAAAALAAAAAAQABAAAAMyCLrc/jDKSatlQtScKdceCAjDII7HcQ4EMTCpyrCuUBjCYRgHVtqlAiB1YhiCnlsRkAAAOwAAAAAAAAAAAA==);
}

.stoneimage {
  border: 1px solid #d6d5d5;
  border-radius: 3px;
}

.wizard_inleiding {
  padding: 5px 5px 15px 5px;
}

input.inputnumber {
  text-align: right;
}

.wizard .form-row.elementrow {
  padding: 0;
}

.select_custom_depth, .select_custom_thickness, .select_custom_height, .select_custom_width_click, .select_custom_height_click {
  width: 120px;
  display: inline-block;
}

.select_custom_wrapper {
  display: flex;
}

.select_custom {
  display: flex;
  flex-direction: column;
}

.select_custom > div {
  padding-bottom: 5px;
}

.select_custom > div > label {
  width: 25px;
  display: inline-block;
}

.custom_img img {
  width: 268px;
  margin-left: 15px;
  border: 1px solid #d6d5d5;
}

#step3.wizard .elementrow .form-row, #step3.wizard .form-row {
  background: white;
}

#step3.wizard .elementrow .elementrow-header {
  background: #EAEBEC;
}

.elementtable_fa {
  padding: 0 0 0 5px;
  display: inline-block;
}

.wizard_elementLength {
  width: calc(100% - 45px);
}

.wizard_inputmm {
  width: calc(100% - 35px);
  min-width: 60px;
}

.wizard a.imagegallery {
  display: block;
  padding: 3px;
}

.wizard a.imagegallery img {
  max-width: 80px;
  border: 1px solid white;
  border-radius: 3px;
}

.wizard a.imagegallery:hover img {
  border-color: #ccc;
  transition: 0.3s;
}

.questionbox {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  margin: auto;
  z-index: 1000;
  height: 412px;
  width: 600px;
  background-color: #FFFFFF;
  border: 1px solid #000000;
  border-radius: 5px;
  /*display: none;*/
}

#flagWindowbox {
  height: 500px !important;
  width: 400px;
}

.questionbox_title {
  background-color: #ce000c;
  color: white;
  padding: 15px 15px;
  font-weight: bold;
  border-radius: 4px 4px 0 0;
}

.questionbox_content {
  padding: 10px 15px;
}

.questionbox img.exampleimg {
  border: none;
  vertical-align: middle;
  padding: 10px 4px;
}

.center {
  text-align: center;
}

.form-btn {
  border: 1px solid #ccc;
  display: inline-block;
  cursor: pointer;
  text-transform: uppercase;
  transition: all 0.3s ease;
}

.form-btn:hover {
  border: 1px solid #666;
  color: black;
}

.form-input[readonly], .form-input[disabled], .select select[disabled] {
  background-color: #eaeaea;
  color: #666;
}

.mitre_div_parent {
  width: 380px;
}

.mitre_div {
  padding: 10px 0;
  background: white;
}

.mitre_middle {
  width: 200px;
  height: 60px;
}

.speling {
  border: 1px solid #ccc;
  color: #ccc;
  margin: 9px 0;
  padding: 1px 5px;
  display: inline-block;
  width: 54px;
}

.vlagkozijnlbl {
  color: #ccc;
  font-size: 16px;
  padding: 0 5px;
}

#wizard_delivery {
  margin: 0 0 0.6em 0;
}

#wizard_delivery .form-row {
  margin: 0 0 0.2em 0;
}

textarea.form-input {
  padding: 15px;
}

.wizard .col-form-label .wizard_stonealert {
  line-height: 1.5em;
  display: inline-block;
  color: #ce000c;
}

.material {
  border: 1px solid #e8e8e8;
  border-radius: 5px 5px 0 0;
  cursor: pointer;
  margin-bottom: 15px;
}

.material:hover {
  box-shadow: 2px 2px 4px #dddbdb;
}

#material_type {
  display: none;
}

.material_selected {
  box-shadow: 2px 2px 4px #dddbdb;
}

.material_disabled {
  opacity: 0.4;
}

.material img {
  border-radius: 5px 5px 0 0;
}

.material_title {
  font-size: 21px;
  font-weight: 900;
  text-align: center;
  padding: 15px;
}

.verstekhoekImgAfstand {
  padding-bottom: 10px;
}

.mitre_example, .endstone_example {
  overflow: hidden;
  display: inline-block;
  vertical-align: middle;
}

.mitre_example > div, .endstone_example > div {
  width: 200px;
  height: 40px;
  border: 1px solid grey;
  position: relative;
}

.mitre_line {
  border-top: 1px solid grey;
  margin-top: 5px;
}

.mitre_of {
  display: inline-block;
  padding: 10px 4px;
  font-size: 18px;
}

.spekband.brand1 .mitre_of, .spekband.brand1 .mitre_example_left_top, .spekband.brand1 .mitre_example_right_top, .spekband.brand1 .mitre_example_leftright_top {
  display: none;
}

.mitre_schuin_left {
  transform: skew(-45deg, 0deg);
  position: absolute;
  border-right: 1px solid grey;
  height: 40px;
  width: 40px;
  top: -1px;
  left: -20px;
  background: white;
  z-index: 1;
}

.mitre_schuin_left_top {
  transform: skew(45deg, 0deg);
  position: absolute;
  border-right: 1px solid grey;
  height: 40px;
  width: 40px;
  top: -1px;
  left: -21px;
  background: white;
  z-index: 1;
}

.mitre_schuin_right {
  transform: skew(0deg, 45deg);
  position: absolute;
  border-bottom: 1px solid grey;
  height: 40px;
  width: 40px;
  top: -21px;
  right: -1px;
  background: white;
  z-index: 1;
}

.mitre_schuin_right_top {
  transform: skew(-45deg, 0deg);
  position: absolute;
  border-left: 1px solid grey;
  height: 40px;
  width: 40px;
  top: 0;
  right: -21px;
  background: white;
  z-index: 1;
}

.spekband .mitre_line {
  margin-top: 23px;
}

.muurafdekker .mitre_line {
  margin-top: 18px;
}

.muurafdekker.no-ezelsrug .mitre_line {
  border: 0;
}

.balkjes .mitre_line {
  border: 0;
}

.spekband.brand1 .mitre_example_left .mitre_schuin_left,
.spekband.brand1 .mitre_example_leftright .mitre_schuin_left {
  transform: none;
  background: white;
  top: 0;
  left: 0;
  height: 24px;
  width: 15px;
  border-right: 1px solid grey;
}

.spekband.brand1 .mitre_example_right .mitre_schuin_right,
.spekband.brand1 .mitre_example_leftright .mitre_schuin_right {
  transform: none;
  background: white;
  top: 0;
  right: 0;
  height: 24px;
  width: 15px;
  border-left: 1px solid grey;
  border-bottom: 0;
}

.spekband.brand1 .mitre_example.mitre_example_left > div {
  border-radius: 0 0 0 15px;
}

.spekband.brand1 .mitre_example.mitre_example_right > div {
  border-radius: 0 0 15px 0;
}

.spekband.brand1 .mitre_example.mitre_example_leftright > div {
  border-radius: 0 0 15px 15px;
}

.mitre_label {
  width: 70px;
  display: inline-block;
  text-align: left;
  padding-left: 10px;
}

.spekband.brand1 .verstekhoekImgAfstand {
  text-align: center;
  padding-right: 30px;
}

.endstone_line {
  border-top: 1px solid grey;
  margin-top: 23px;
}

.endStoneRow {
  padding-bottom: 10px;
}

.endstone_example .endstone_left {
  position: absolute;
  transform: none;
  background: white;
  top: 0;
  left: 0;
  height: 24px;
  width: 5px;
  border-right: 1px solid grey;
  border-bottom: 0;
}

.endstone_example .endstone_right {
  position: absolute;
  transform: none;
  background: white;
  top: 0;
  right: 0;
  height: 24px;
  width: 5px;
  border-left: 1px solid grey;
  border-bottom: 0;
}

.endstone_example .line_left, .endstone_example .line_right {
  display: none;
}

.muurafdekker .endstone_line {
  margin-top: 19px;
}

.muurafdekker.no-ezelsrug .endstone_line {
  border: 0;
}

.muurafdekker.no-ezelsrug .endstone_example .endstone_right,
.muurafdekker.no-ezelsrug .endstone_example .endstone_left {
  border: 0;
}

.endstone_example .endstone_left {
  left: -15px;
  width: 30px;
  background: white;
  border-top: 1px solid grey;
  top: 4px;
  transform: rotate(45deg);
  height: 30px;
}

.muurafdekker .endstone_example .endstone_right {
  right: -15px;
  width: 30px;
  background: white;
  border-top: 1px solid grey;
  top: 4px;
  transform: rotate(-45deg);
  height: 30px;
}

.muurafdekker .endstone_example .line_left {
  display: block;
  top: 0;
  left: 0;
  border-left: 1px solid grey;
  height: 40px;
  position: absolute;
}

.muurafdekker .endstone_example .line_right {
  display: block;
  top: 0;
  right: 0;
  border-left: 1px solid grey;
  height: 40px;
  position: absolute;
}

.wizard .confirmoptions .col-form-label {
  line-height: 1.5em;
}

.wizard .producsextrahead td {
  font-weight: bold;
  background-color: #EBEBEB;
  padding: 5px 15px;
  margin-bottom: 5px;
}

@media (max-width: 992px) {
  .steps div:first-child {
    border-radius: 0;
  }

  .steps div:last-child {
    border-radius: 0;
  }
}

.wizard .splitcopy {
  padding-left: 0;
  padding-right: 0;
}

.splitcopy a {
  color: black;
  user-select: none;
}

.splitcopy a:hover {
  color: #ce000c;
}

.copy_left {
  padding: 5px 0 5px 5px;
}

.copy_all_left {
  padding: 5px;
}

.copy_all_left .fa {
  margin-right: -3px;
}

.copy_right {
  padding: 5px 5px 5px 0;
}

.copy_all_right {
  padding: 5px;
}

.copy_all_right .fa {
  margin-left: -3px;
}

