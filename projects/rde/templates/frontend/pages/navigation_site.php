<?php 

function getLink($item) {
  return PageMap::getUrl($item->getPageId());
}
	/**
	 * @param $paid
	 * @param Navigation $nav
	 */
function writeNav($paid, $nav, $id) {

  $site = Context::getSite();
  global $cats,$catparents_active;

	$out = '<ul class="menu" id="'.$id.'">';
  $items = Navigation::getItem($paid)->getChildren();
	
	$key=0;
	foreach($items as $item) {

    if(!$item->hasRight() || !$item->isShow() || in_array($item->getPageId(),[224,277,220])) continue;

    $subitems = Navigation::getItem($item->getPageId())->getChildren();
    if(!$subitems) $subitems = [];

    $link = "";
    if(!$item->isDisabled()) {
      if($site->site_host->homepage_id==$item->getPageId()) {
        $link = '/';
      }
      else {
        $link = getLink($item);
      }
    }

    $showsubs = false;
    foreach($subitems as $subitem) {
      if($subitem->hasRight() && $subitem->isShow() && $subitem->parentId!=1) {
        $showsubs = true;
        break;
      }
    }

    $out .= '<li class="';
    if($showsubs || $item->pageId==216) {
      $out .= 'haschild ';
    }
    if($item->isActive()) {
      $out .= 'current ';
    }
    if($item->pageId==220) {
      $out .= 'first-child ';
    }
    $out .= '">';
    $out .= '<a href="'.$link.'" ';
    if($item->getTarget()!='') {
      $out .= 'target="'.$item->getTarget().'"';
    }
    $out .= ' class="';
//			if($item->isActive()) {
//				$out .= 'current ';
//			}
    if($showsubs) {
      $out .= 'dropdown-toggle" data-toggle="dropdown';
    }
    $out .= '">';
    $out .= '<span>'.$item->getName().'</span>';
    $out .= '</a>';

    if($item->pageId==216 && isset($cats)) {
      $out .= '<ul class="child">';
      foreach($cats as $cat) {
        $out .= '<li ';
        $out .= ' class="';
        if(isset($catparents_active[$cat->id])) {
          $out .= 'current ';
        }
        $out .= '"';
        $out .= '>';
        $out .= '<a href="'.$cat->getShopUrl().'" ';
        if($subitem->getTarget()!='') {
          $out .= 'target="'.$subitem->getTarget().'"';
        }
        $out .= '>';
        $out .= escapeSafe($cat->getName($_SESSION['lang']));
        $out .= '</a>';

      }
      $out .= '</ul>';
    }


    if($showsubs) {
      $out .= '<ul class="child">';
      foreach($subitems as $subitem) {

        if($item->pageId==1) {
          if(isset($_SESSION["userObject"])) {
            if($subitem->pageId == 207 || $subitem->pageId == 209 || $subitem->pageId == 217) {
              continue;
            }
          }
          else {
            if($subitem->pageId != 207 && $subitem->pageId != 208) {
              continue;
            }
          }
        }


        if($subitem->hasRight() && $subitem->isShow()) {
          $subsubitems = Navigation::getItem($subitem->getPageId())->getChildren();

          $link = "";
          if(!$subitem->isDisabled()) {
             $link = getLink($subitem);
          }
          $out .= '<li ';
          if(count($subsubitems)>0) {
            $out .= 'class="haschild" ';
          }
          $out .= '>';
          $out .= '<a href="'.$link.'" ';
          if($subitem->getTarget()!='') {
            $out .= 'target="'.$subitem->getTarget().'"';
          }
          $out .= ' class="';
          if($subitem->isActive()) {
            $out .= 'current ';
          }
          if(count($subsubitems)>0) {
            $out .= 'dropdown-toggle" data-toggle="dropdown';
          }
          $out .= '">';

          $out .= $subitem->getName();
          $out .= '</a>';

          if(count($subsubitems)>0) {
            $out .= '<ul class="child">';
            foreach($subsubitems as $subsubitem) {
              if($subsubitem->hasRight() && $subsubitem->isShow() && $subitem->isShow()) {
                if(!$subsubitem->disabled) {
                    $link = getLink($subsubitem);
                }
                $out .= '<li>';
                $out .= '<a href="'.$link.'" ';
                if($subsubitem->getTarget()!='') {
                  $out .= 'target="'.$subsubitem->getTarget().'"';
                }
                if($subsubitem->isActive()) {
                  //$out .= ' style="color: #681821;" ';
                }

                $out .= '>';
                $out .= htmlentities($subsubitem->getName());
                $out .= '</a>';

                $items3 = Navigation::getItem($subsubitem->getPageId())->getChildren();
                if(count($items3)>0) {
                  $out .= '<ul class="child">';
                  foreach($items3 as $item3) {
                    if($item3->hasRight() && $item3->isShow()) {
                      if(!$item3->isDisabled()) {
                         $link = getLink($item3);
                      }
                      $out .= '<li>';
                      $out .= '<a href="'.$link.'" ';
                      if($item3->getTarget()!='') {
                        $out .= 'target="'.$item3->getTarget().'"';
                      }
                      if($item3->isActive()) {
                        $out .= ' style="color: #681821;" ';
                      }

                      $out .= '>';
                      $out .= htmlentities($item3->getName());
                      $out .= '</a>';

                      $out .= '</li>';

                    }
                  }
                  $out .= '</ul>';
                }
                $out .= '</li>';
              }
            }
            $out .= '</ul>';
          }
          $out .= '</li>';
        }
      }
      $out .= '</ul>';
    }
    $out .= '</li>';

    $key++;
	}
	$out .= '</ul>';
	echo $out;
}
