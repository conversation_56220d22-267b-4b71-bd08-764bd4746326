<?php

function writeLeftNav($paid, $nav) {
  $site = Context::getSite();
  $items = Navigation::getItem($paid)->getChildren();

  $firstitem = array_values($items)[0];
  $parent = Navigation::getItem($firstitem->parentId);

  $out = '';
  if(isset($parent->page) && $parent->page->id==1) {
    $out .= '<div class="widget_header"><h3>Mijn account</h3></div>';
  }
  elseif(isset($parent->page) && $parent->page->id==277) {
    $out .= '<div class="widget_header"><h3>Mijn account</h3></div>';
  }
  else {
    $out .= '<div class="widget_header"><h3>'.$parent->getName().'</h3></div>';
  }
  $out .= '<ul class="menu nav nav-pills" id="mainNav">';

	$key=0;
	foreach($items as $item) {

    if(isset($_SESSION["userObject"])) {
      if($item->pageId == 290 && !(isset($_SESSION["userObject"]->company) && $_SESSION["userObject"]->company->payInAdvance==1)) { //vooruitbetalen alleen tonen wanneer aangvinkt
        continue;
      }
    }
    if($item->pageId==1 || $item->parentId==1) {

      if(isset($_SESSION["userObject"])) {
//        if($item->pageId == 207 || $item->pageId == 349) {
          continue;
//        }
      }
      else {
        if($item->pageId != 207 && $item->pageId != 349) {
          continue;
        }
      }
    }

		if($item->hasRight() && $item->isShow()) {
      $subitems = Navigation::getItem($item->getPageId())->getChildren();
      if(!$subitems) $subitems = [];
		  
			$link = "";
			$actstr = "";
			if(!$item->isDisabled()) {
				if($site->site_host->homepage_id==$item->getPageId()) {
					$link = '/';
				}
				else {
					$link = getLink($item);
				}
			}

      $showsubs = false;
      foreach($subitems as $subitem) {
				if($subitem->hasRight() && $subitem->isShow()) {
          $showsubs = true;
          break;
        }
      }
      if($item->pageId==224) {
        $showsubs = false;
      }

      $out .= '<li class="';
      if($showsubs) {
        $out .= 'dropdown ';
      }
      if($item->isActive()) {
        $out .= 'current ';
      }
      $out .= '">';
			$out .= '<a href="'.$link.'" ';
			if($item->getTarget()!='') {
				$out .= 'target="'.$item->getTarget().'"';
			}
      $out .= ' class="';
//			if($item->isActive()) {
//				$out .= 'current ';
//			}
      if($showsubs) {
        $out .= 'dropdown-toggle" data-toggle="dropdown';
      }
      $out .= '">';

			$out .= $item->getName();

			if(isset($_SESSION['userObject'])) {
        if($item->pageId == 213) { //invoices
          $invoicecount = Invoices::getOpeninvoiceCount();
          if($invoicecount != 0) {
            $ic = $invoicecount;
            if($invoicecount > 9) {
              $ic = "!";
            }
            $out .= '<div class="icondot" title="' . $invoicecount . ' openstaande facturen">' . $ic . '</div>';
          }
        }
        elseif($item->pageId == 212) { //containers
          $containeraway = ContainersQuotations::getContainersAway();
          if($containeraway != 0) {
            $ic = $containeraway;
            if($containeraway > 9) {
              $ic = "!";
            }
            $out .= '<div class="icondot" title="' . $containeraway . ' bakken/rekken niet retour, meer dan 45 dagen op locatie">' . $ic . '</div>';
          }
        }
        elseif($item->pageId == 215) { //berichten
          $imescount = Imessage::unreadMessagesCount();
          if($imescount > 0) {
            $ic = $imescount;
            if($imescount > 9) {
              $ic = "!";
            }
            $out .= '<div class="icondot" title="' . $imescount . ' ongelezen berichten">' . $ic . '</div>';
          }
        }
        elseif($item->pageId == 290) { //vooruitbetalen
          $prepaidcount = count(Quotations::getQuotationsPrepaid($_SESSION["userObject"]));
          if($prepaidcount > 0) {
            $ic = $prepaidcount;
            if($prepaidcount > 9) {
              $ic = "!";
            }
            $out .= '<div class="icondot" title="' . $prepaidcount . ' bestellingen vooruit te betalen">' . $ic . '</div>';
          }
        }

      }
			$out .= '</a>';

			if($showsubs) {
				$out .= '<ul class="dropdown-menu">';
				foreach($subitems as $subitem) {
					if($subitem->hasRight() && $subitem->isShow()) {
            $subsubitems = Navigation::getItem($subitem->getPageId())->getChildren();
						
						$link = "";
						if(!$subitem->isDisabled()) {
							 $link = getLink($subitem);
						}
						$out .= '<li class="';
            if(count($subsubitems)>0) {
              $out .= 'dropdown ';
            }
            if($subitem->isActive()) {
              $out .= 'current ';
            }
            $out .= '">';
						$out .= '<a href="'.$link.'" ';
						if($subitem->getTarget()!='') {
							$out .= 'target="'.$subitem->getTarget().'"';
						}
            $out .= ' class="';
						if($subitem->isActive()) {
              $out .= 'current ';
						}						
            if(count($subsubitems)>0) {
              $out .= 'dropdown-toggle" data-toggle="dropdown';
            }
            $out .= '">';

						$out .= $subitem->getName();
						$out .= '</a>';
	
						if(count($subsubitems)>0) {
							$out .= '<ul class="dropdown-menu">';
							foreach($subsubitems as $subsubitem) {
								if($subsubitem->hasRight() && $subsubitem->isShow() && $subitem->isShow()) {
									if(!$subsubitem->disabled) {
										  $link = getLink($subsubitem);
									}
									$out .= '<li>';
									$out .= '<a href="'.$link.'" ';
									if($subsubitem->getTarget()!='') {
										$out .= 'target="'.$subsubitem->getTarget().'"';
									}
									if($subsubitem->isActive()) {
										//$out .= ' style="color: #681821;" ';
									}						
																		
									$out .= '>';
									$out .= htmlentities($subsubitem->getName());
									$out .= '</a>';
									
									$items3 = Navigation::getItem($subsubitem->getPageId())->getChildren();
									if(count($items3)>0) {
										$out .= '<ul class="dropdown-menu">';
										foreach($items3 as $item3) {
											if($item3->hasRight() && $item3->isShow()) {
												if(!$item3->isDisabled()) {
													 $link = getLink($item3);
												}
  											$out .= '<li>';
												$out .= '<a href="'.$link.'" ';
												if($item3->getTarget()!='') {
													$out .= 'target="'.$item3->getTarget().'"';
												}
												if($item3->isActive()) {
													$out .= ' style="color: #681821;" ';
												}						
																					
												$out .= '>';
												$out .= htmlentities($item3->getName());
												$out .= '</a>';
												
												$out .= '</li>';

											}
										}
										$out .= '</ul>';
									}
									$out .= '</li>';
								}
							}
							$out .= '</ul>';
						}
						$out .= '</li>';						
					}
				}
				$out .= '</ul>';
			}
			$out .= '</li>';
			
			$key++;
		}
	}
	$out .= '</ul>';
	echo $out;
}

