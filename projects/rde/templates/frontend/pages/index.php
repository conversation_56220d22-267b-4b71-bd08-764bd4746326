<?php

  include(DIR_INCLUDES . "headercode_frontend.inc.php");

  if ($pageId == 228 || $pageId == 219) { //18-02-2025: hoofdmappen kun je niet aanroepen (oude url fix). Mag na 6 maanden weg.
    ResponseHelper::redirect301("/");
  }

  include("navigation_site.php");
  include("_leftmenu.php");

  if(!isset($seo_title) || $seo_title=="") {
    $seo_title = Navigation::getItem($pageId)->getName();
  }
  if(isset($seo_description) && $seo_description != null) {
    $seo_description = StringHelper::cleanAndEscape($seo_description);
  }

  Context::addMetatag("description",$seo_description, true);
  Context::addMetatag("author", "Raamdorpelelementen BV", true);
  Context::addMetatag("copyright", "Raamdorpelelementen BV", true);
  Context::addMetatag("robots", "All", true);

  $site->addOpengraphMeta('Raamdorpelelementen BV', $seo_title, $seo_description, $seo_image);

  $myacount_pages = [];
  if(isset($_SESSION["userObject"])) {
    $myacount_pages = Navigation::getItem(277)->getChildren();
    if(!(isset($_SESSION["userObject"]->company) && $_SESSION["userObject"]->company->payInAdvance==1)) {
      //vooruitbetalen alleen tonen wanneer aangvinkt
      unset($myacount_pages[290]);
    }
  }

?>
<!DOCTYPE html>
<html dir="ltr" lang="nl-NL">
<head>
  <meta charset="utf-8"/>

  <title><?php echo escapeSafe($seo_title); ?></title>

  <?php Context::printMetatags(); ?>
  <?php Context::printCanonicals($site); ?>

  <?php $site->writeStructuredDataScript(); ?>

  <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no">
  <link rel="icon" type="image/x-ico" href="<?php echo $site->getTemplateUrl() ?>images/favicon.ico"/>

  <?php if(!DEVELOPMENT): ?>
    <!-- Google Tag Manager -->
    <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
          new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
        j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
        'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
      })(window,document,'script','dataLayer','GTM-MJMTJWF');</script>
    <!-- End Google Tag Manager -->
  <?php endif; ?>

  <link href='https://fonts.googleapis.com/css?family=Roboto:300,400,400i,700,900&display=swap' rel='stylesheet' type='text/css'/>

  <?php echo TemplateHelper::includeJavascript($site->getTemplateUrl() . 'dist/deploy', true); ?>
  <?php echo TemplateHelper::includeStylesheet($site->getTemplateUrl() . 'dist/main', true); ?>
  <?php echo TemplateHelper::includeStylesheet($site->getTemplateUrl() . 'style/cookiebanner', false); ?>

  <?php Context::printStylesheets(); ?>

  <?php echo TemplateHelper::includeJavascript('/gsdfw/includes/jsscripts/popper', true) ?>

  <script src="https://cdn.jsdelivr.net/npm/promise-polyfill@7.1.0/dist/promise.min.js"></script>
  <?php echo TemplateHelper::includeJavascript('/gsdfw/includes/jsscripts/general', true); ?>

  <?php Context::printJavascripts(); ?>

  <script>
    addErrorHandler();
  </script>

  <?php if(false && !DEVELOPMENT): ?>
    <!-- Hotjar Tracking Code for https://www.raamdorpel.nl -->
    <script>
      (function(h,o,t,j,a,r){
        h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
        h._hjSettings={hjid:2043362,hjsv:6};
        a=o.getElementsByTagName('head')[0];
        r=o.createElement('script');r.async=1;
        r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
        a.appendChild(r);
      })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');
    </script>
  <?php endif; ?>
</head>

<body>
  
  <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-MJMTJWF" height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>

  <?php TemplateHelper::writeStagingMessage() ?>

  <?php if (in_array($site->id, [1,2]) && $pageId !== 274): ?>
    <?php TemplateHelper::includeComponent('cookiebanner', 'site'); ?>
  <?php endif; ?>

  <div class="wrapper">

    <header class="header">

      <div class="header-top">

        <div class="wrap clearfix">

          <div class="logo pull-left">
            <a href="/">
              <img width="300" src="<?php echo $site->getTemplateUrl() ?>images/logo.svg" alt="Raamdorpel"/>
            </a>
          </div>

          <div class="contact pull-right">
            <div id="search">
              <form method="get" action="<?php echo PageMap::getUrl("M_SEARCH") ?>">
                <div class="form-row">
                  <input type="text" class="form-input col-4-3" name="topsearch" placeholder="Zoeken..." autocomplete="off">
                  <button class="btn col2" title="Zoeken op de website">
                    <i class="fa fa-search ft-white"></i>
                  </button>
                </div>
              </form>
            </div>
            <div class="nav action">
              <ul>
                <li>
                  <a class="header-item" href="/contact" title="Chat met ons via WhatsApp">
                    <span class="icon-sm icon-whatsapp"><img src="<?php echo $site->getTemplateUrl() ?>images/whatsapp_red.svg" alt="Chat met ons via WhatsApp"/> </span>
                  </a>
                </li>
                <li>
                  <a class="icon-sm" href="tel:0497360791" title="Bel raamdorpelelementen"><i class="icon-phone"></i></a>
                </li>
                <li>
                  <a class="icon-sm" href="mailto:<EMAIL>" title="E-mail raamdorpelelementen"><i class="icon-envelope"></i></a>
                </li>
                <?php if(isset($_SESSION["userObject"])): ?>
                  <li class="haschild">
                    <a class="header-item dropdown-toggle" data-toggle="dropdown" href="#">
                      <span class="icon-sm"><i class="icon-user"></i></span>
                    </a>
                    <ul class="child">
                      <li  style="font-weight: 600;padding: 0 15px;border-bottom: 1px solid #e8e8e8;">
                        <span class="fa fa-user"></span> &nbsp;<?php echo $_SESSION["userObject"]->getNaam() ?>
                      </li>
                      <?php foreach ($myacount_pages as $navchild): ?>
                        <li class="<?php if($pageId==$navchild->pageId) echo 'current' ?>"><a href="<?php echo PageMap::getUrl($navchild->pageId) ?>"><?php echo $navchild->getName() ?></a></li>
                      <?php endforeach; ?>
                      <?php if(SandboxUsers::isAdmin()): ?>
                        <li><a href="/offerte?adminclear=1">Uitloggen ADMIN</a></li>
                      <?php endif; ?>
                    </ul>
                  </li>
                <?php else: ?>
                  <li>
                    <a class="header-item dropdown-toggle" data-toggle="dropdown" href="/inloggen">
                      <span class="icon-sm"><i class="icon-user"></i></span>
                      <span class="text hidden-xs">Inloggen</span>
                    </a>
                  </li>
                <?php endif; ?>
                <li class="haschild">
                  <a class="header-item dropdown-toggle" data-toggle="dropdown" href="#" title="Bekijk winkelmandje">
                    <span class="icon-sm"><i class="icon-basket"></i></span>
                  </a>
                  <?php TemplateHelper::includeComponent('basketPopup', 'basket'); ?>
                </li>
              </ul>
            </div>
          </div>

        </div>

      </div>

      <div class="header-bottom bg-primary">

        <div class="wrap clearfix">

          <nav class="nav hidden-s hidden-xs">
            <?php writeNav('219',Navigation::getInstance(),'mainmenu'); ?>
          </nav>

          <div class="mobile-menu hidden visible-s visible-xs pull-left">
            <ul id="mobilemenu">
              <li class="haschild">
                <a href="#" class="mobile_icon" title="Naar mijn account">
                  <i class="icon-user"></i>
                </a>
                <ul class="child">
                  <?php if(isset($_SESSION["userObject"])): ?>
                    <?php foreach ($myacount_pages as $navchild): ?>
                      <li class="<?php if($pageId==$navchild->pageId) echo 'current' ?>"><a href="<?php echo PageMap::getUrl($navchild->pageId) ?>"><?php echo $navchild->getName() ?></a></li>
                    <?php endforeach; ?>
                    <?php if(SandboxUsers::isAdmin()): ?>
                      <li><a href="/offerte?adminclear=1">Uitloggen ADMIN</a></li>
                    <?php endif; ?>
                  <?php else: ?>
                    <li><a href="/offerte">Offerte & bestellen</a></li>
                    <li><a href="/registreren">Registreren</a></li>
                  <?php endif; ?>
                </ul>
              </li>
              <li>
                <a class="mobile_icon" href="<?php echo PageMap::getUrl("M_BASKET") ?>" title="Naar winkelmandje">
                  <i class="icon-basket"></i>
                </a>
              </li>
              <li>
                <a class="mobile_icon" href="tel:0497360791"  title="Bel raamdorpelelementen voor meer informatie"><i class="icon-phone"></i></a>
              </li>
              <li>
                <a class="mobile_icon" href="mailto:<EMAIL>" title="E-mail raamdorpelementen voor meer informatie"><i class="icon-envelope"></i></a>
              </li>
              <li>
                <a class="mobile_icon icon-whatsapp" href="/contact" title="Chat met raamdorpelelementen voor al uw vragen">
                  <img src="<?php echo $site->getTemplateUrl() ?>images/whatsapp.svg" alt="Chat met Raamdorpelelementen"/>
                </a>
              </li>
            </ul>
          </div>

          <div class="mobile-menu hidden visible-s visible-xs pull-right">

            <button data-toggle-offscreen=".mobile-menu-container" class="c-hamburger c-hamburger--htx">
              <span>toggle menu</span>
            </button>
          </div>

        </div>

      </div>

    </header>


    <div class="mobile-menu-container bg-dk hidden">
      <?php writeNav('219',Navigation::getInstance(),"mobilemenu"); ?>
      <?php if(isset($_SESSION["userObject"])): ?>

      <?php else: ?>
        <div class="p-t-md p-r-md">
          <a class="btn btn-lg btn-login" href="/inloggen"><i class="fa fa-lock"></i>Inloggen</a>
        </div>
      <?php endif; ?>
    </div>

    <div class="container-wrapper">

      <?php if($pageId==220): //homepage ?>

        <?php
          MessageFlashCoordinator::setClassByType("alert","alert alert-danger alerttop");
          MessageFlashCoordinator::setClassByType("default","alert alert-success alerttop");
          MessageFlashCoordinator::showMessages();

          if(isset($_SESSION['flash_message_swal'])):
            $_SESSION['flash_message_swal']->showMessage();
            unset($_SESSION['flash_message_swal']);
          endif;
        ?>

        <?php include($current_action->getTemplatePath()); ?>

      <?php else: ?>

        <?php if(isset($landing)): ?>
          <?php TemplateHelper::includePartial("_topslidersmall.php",'site',["site"=>$site,'titel'=>$page->content->title]) ?>
        <?php endif; ?>

        <div class="container p-t-md">

          <div class="wrap">

            <div class="row">


              <aside class="col3 col4-s col12-xs">
                <?php $hasSubmenu = isset($page) && ($page->parent_id!=219 || $page->id==216) || $pageId=="M_BASKET"; ?>
                <?php if($hasSubmenu): ?>
                  <a href="#" class="submenutoggle"> SUBMENU
                    <button class="c-hamburger c-hamburger--htx">
                      <span>toggle menu</span>
                    </button>
                  </a>
                <?php endif; ?>
                <div class="sidebar">

                  <?php if(isset($page) && ($page->parent_id==277 || $page->parent_id==1 || $page->id==208 || $page->parent_id==216 || $page->id==216)): ?>
                    <a href="<?php echo PageMap::getUrl(208); ?>?action=wizard" class="btn btn-primary" style="width: 100%;margin-bottom: 15px;"><i class="icon-calculator"></i> START WIZARD</a>
                  <?php endif; ?>

                  <?php if($hasSubmenu): ?>
                    <div class="submenu-items widget bg-lt p-md">
                      <?php if($pageId=="M_BASKET" || $page->parent_id==216 || $page->id==216): ?>
                        <?php if(isset($cats)): ?>
                          <div class="widget_content">
                            <div class="widget_header">
                              <h3><a href="<?php echo PageMap::getUrl(216) ?>" style="color: #333;">Webshop</a></h3>
                            </div>
                            <div class="widget_content">
                              <?php
                                $out = '<ul class="menu nav nav-pills">';
                                foreach($cats as $cat) {
                                  $out .= '<li ';
                                  $out .= ' class="';
                                  if(isset($catparents_active[$cat->id])) {
                                    $out .= 'current ';
                                  }
                                  $out .= '"';
                                  $out .= '>';
                                  $out .= '<a href="'.$cat->getShopUrl().'" ';
                                  $out .= '>';
                                  $out .= escapeSafe($cat->getName($_SESSION['lang']));
                                  $out .= '</a>';

                                  //children
                                  if(isset($catparents_active[$cat->id]) && isset($cat->children)) {
                                    $out .= '<ul>';
                                    foreach ($cat->children as $cat_child) {
                                      $out .= '<li ';
                                      $out .= ' class="';
                                      if(isset($catparents_active[$cat_child->id])) {
                                        $out .= 'current ';
                                      }
                                      $out .= '"';
                                      $out .= '>';
                                      $out .= '<a href="' . $cat_child->getShopUrl() . '" ';
                                      $out .= '>';
                                      $out .= escapeSafe($cat_child->getName($_SESSION['lang']));
                                      $out .= '</a>';
                                    }
                                    $out .= '</ul>';
                                  }

                                }
                                $out .= '</ul>';
                                echo $out;
                              ?>
                            </div>
                          </div>
                        <?php endif; ?>
                      <?php elseif($pageId=="M_BASKET" || $page->parent_id==1 || $page->parent_id==216 || $page->id==216 || $page->parent_id==277): ?>

                        <div class="widget_content">
                          <?php writeLeftNav(isset($_SESSION["userObject"])?277:1,Navigation::getInstance()); ?>
                        </div>

                      <?php else: ?>
                        <div class="widget_content">
                          <?php
                            $parentId = $page->parent_id;
                            if($parentId==278) {
                              $parentId = 228;
                            }
                            writeLeftNav($parentId,Navigation::getInstance());
                          ?>
                        </div>
                      <?php endif; ?>
                    </div>
                  <?php endif; ?>

                  <div class='widgetBar' id='widgetBar-1'>
                    <div id='widget_23' class='widget width-1-1 first-child last-child'>
                      <div class='widget-inner'>
                        <div class='widget_header'><h3><span>Contact</span></h3></div>
                        <div class='widget_content'>
                          <p>Raamdorpelelementen BV</p>
                          <p>Raambrug 9<br/>5531 AG Bladel</p>
                          <p><a href="tel:+31497360791">0497-36 07 91</a><br/>
                            <a href="mailto:<EMAIL>"><EMAIL></a>
                          </p>

                        </div>
                        <div class='widget_footer'></div>
                      </div>
                    </div>
                  </div>

                  <div class="klantevertellen-wrapper">
                    <iframe frameborder="0" allowtransparency="true" src="https://www.klantenvertellen.nl/retrieve-widget.html?color=white&button=true&lang=nl&tenantId=99&locationId=1062537" title="Reviews van Raamdorpelelementen"></iframe>
                  </div>


                </div>
              </aside>

              <div class="col9 col8-s col12-xs pull-right p-b-lg content">

                <div class="breadcrumb-bar p-b">
                  <?php echo BreadCrumbs::writeBreadcrumbs() ?>
                </div>

                <?php
                  MessageFlashCoordinator::setClassByType("alert", "alert alert-danger alerttop");
                  MessageFlashCoordinator::setClassByType("default", "alert alert-success alerttop");
                  MessageFlashCoordinator::showMessages();
                ?>

                <?php
                  if (isset($_SESSION['flash_message_swal'])):
                    $_SESSION['flash_message_swal']->showMessage();
                    unset($_SESSION['flash_message_swal']);
                  endif;
                ?>

                <?php include($current_action->getTemplatePath()); ?>

              </div>

            </div>

          </div>

        </div>

      <?php endif; ?>


      <?php TemplateHelper::includePartial("_footer.php","site",["site"=>$site]) ?>

    </div>

  </div>


  <div class="scrollToTop hidden visible-s visible-xs"><i class="fa fa-chevron-up"></i></div>

  <script>
    <?php if (in_array($site->id, [1,2]) && $pageId !== 274): ?>
    $(document).ready(function () {

      if (!localStorage.getItem("consentSet")) {
        $("body").css('overflow', 'hidden')

        $("#cookie-banner").css('display', 'block');
        $("#consent-deny, #consent-allow-all").on('click', function () {
          updateCookieBanner();
        })
      }

    })
    <?php endif; ?>
  </script>
</body>
</html>