<ifmodule mod_deflate.c>
  AddOutputFilterByType DEFLATE text/plain
  AddOutputFilterByType DEFLATE text/html
  AddOutputFilterByType DEFLATE text/xml
  AddOutputFilterByType DEFLATE text/css
  AddOutputFilterByType DEFLATE text/javascript
  AddOutputFilterByType DEFLATE application/xml
  AddOutputFilterByType DEFLATE application/xhtml+xml
  AddOutputFilterByType DEFLATE application/rss+xml
  AddOutputFilterByType DEFLATE application/javascript
  AddOutputFilterByType DEFLATE application/x-javascript

  # Or, compress certain file types by extension:
  <Files *.html>
  SetOutputFilter DEFLATE
  </Files>
</ifmodule>

<ifmodule mod_expires.c>
	ExpiresActive On
	ExpiresByType text/html "access plus 1 day"
	ExpiresByType image/gif "access plus 10 years"
	ExpiresByType image/jpeg "access plus 10 years"
	ExpiresByType image/png "access plus 10 years"
	ExpiresByType text/css "access plus 10 years"
	ExpiresByType text/javascript "access plus 10 years"
	ExpiresByType application/x-javascript "access plus 10 years"
</ifmodule>

RewriteEngine on

#errors
ErrorDocument 404 /index.php?pageId=M_ERROR
ErrorDocument 401 /index.php?pageId=M_ERROR_RIGHTS
RewriteRule ^error404$ index.php?pageId=M_ERROR [QSA,L]
RewriteRule ^error401$ index.php?pageId=M_ERROR_RIGHTS [QSA,L]

#seo friendly image urls
RewriteRule ^uploads/([a-zA-Z_-]+)/s(\d+)/(.*)/([^.]+)\.(png|jpg|jpeg|gif|webp|bmp)$ /uploads/$1/sites/$2/images/$3.$5 [QSA,L]
RewriteRule ^uploads/([a-zA-Z_-]+)/(p|pi|c|pc)/(.*)/([^.]+)\.(png|jpg|jpeg|gif|webp|bmp)$ /uploads/$1/images/catalog/$3.$5 [QSA,L]
RewriteRule ^uploads/([a-zA-Z_-]+)/vehicles/(.*)/(.*)$ /uploads/$1/vehicles/$2  [QSA,L]
RewriteRule ^uploads/([a-zA-Z_-]+)/images/catalog/(.*)/(.*)$ /uploads/$1/images/catalog/$2 [QSA,L]
RewriteRule ^uploads/([a-zA-Z_-]+)/images/brands/(.*)/(.*)$ /uploads/$1/images/brands/$2 [QSA,L]

#move of /images/
RewriteRule ^images/(.*)$ /gsdfw/images/$1 [QSA,L]

#robots.txt dynamic
RewriteRule ^robots.txt index.php?action=robotstxt [QSA,L]

#cmd/cron (new 16-10-2024)
RewriteRule ^cmd.php /index.php?gsdloader=cmd [QSA,L]
RewriteRule ^cron.php /index.php?gsdloader=cron [QSA,L]

#payment postbacks
RewriteRule ^payment_postback.php /nl/external?action=paymentpostback [QSA,L]
RewriteRule ^payment_postback_hovenier.php /nl/external?action=paymentpostback&payvariant=internetkassa [QSA,L]
RewriteRule ^payment_postback_howik.php /nl/external?action=paymentpostback&payvariant=buckaroo [QSA,L]
RewriteRule ^payment_postback_omnikassa.php /nl/external?action=paymentpostback&payvariant=omnikassa [QSA,L]
RewriteRule ^payment_postback_paypal.php /nl/external?action=paymentpostback&payvariant=paypal [QSA,L]
RewriteRule ^payment_postback_mollie.php /nl/external?action=paymentpostback&payvariant=mollie [QSA,L]

#standard redirects
RewriteRule ^(nl|en|de|fr|es|da|pl)/$ index.php?lang=$1 [QSA]
RewriteRule ^(nl|en|de|fr|es|da|pl)/(\d+)/(.*)$ index.php?lang=$1&pageId=$2&rest=$3 [QSA]
RewriteRule ^(nl|en|de|fr|es|da|pl)/(m\d+)/(.*)$ index.php?lang=$1&pageId=$2&rest=$3 [QSA]
RewriteRule ^(nl|en|de|fr|es|da|pl)/([a-zA-Z_-]+)/(\d+)/(.*)$ index.php?lang=$1&var1=$2&var2=$3&rest=$4 [QSA]
RewriteRule ^(nl|en|de|fr|es|da|pl)/([a-zA-Z0-9_-]+)(.*)$ index.php?lang=$1&pageId=$2&rest=$3 [QSA]
RewriteRule ^(nl|en|de|fr|es|da|pl)/(.*)$ index.php?lang=$1&pageId=$2 [QSA]

# RDE
RewriteRule ^src/assets/(.*)$ /projects/rde/resources/vueapps/map/src/assets/$1 [QSA,L]
RewriteRule ^projects/rde/templates/map/node_modules/(.*)$ /projects/rde/resources/vueapps/map/node_modules/$1 [QSA,L]

#31-01-2019 - ROBERT - alle niet gereserveerde mappen/bestanden gaan naar de index.php. zodat we alle url's kunnen maken (page/product/categorie url's)
RewriteCond %{REQUEST_URI}  !^/gsd/cache/
RewriteCond %{REQUEST_URI}  !^/gsd/downloads/
RewriteCond %{REQUEST_URI}  !^/gsd/gsdfw/
RewriteCond %{REQUEST_URI}  !^/gsd/projects/
RewriteCond %{REQUEST_URI}  !^/gsd/temp/
RewriteCond %{REQUEST_URI}  !^/gsd/images/
RewriteCond %{REQUEST_URI}  !^/gsd/uploads/
RewriteCond %{REQUEST_URI}  !^/gsd/cmd.php
RewriteCond %{REQUEST_URI}  !^/gsd/cron.php
RewriteCond %{REQUEST_URI}  !^/gsd/error.php
RewriteCond %{REQUEST_URI}  !^/gsd/error_rights.php
RewriteCond %{REQUEST_URI}  !^/gsd/index_down.php
RewriteCond %{REQUEST_URI}  !^/gsd/index_temp.php
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php?rest=$1 [QSA]